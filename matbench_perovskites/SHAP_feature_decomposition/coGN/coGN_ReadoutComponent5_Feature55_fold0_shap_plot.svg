<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="785.946156pt" height="679.5765pt" viewBox="0 0 785.946156 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T10:08:12.725686</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 785.946156 679.5765 
L 785.946156 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
L 522.851156 27.789 
L 416.875156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 455.258835 638.149 
L 455.258835 27.789 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 416.875156 609.084238 
L 522.851156 609.084238 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 416.875156 580.019476 
L 522.851156 580.019476 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 416.875156 550.954714 
L 522.851156 550.954714 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 416.875156 521.889952 
L 522.851156 521.889952 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 416.875156 492.82519 
L 522.851156 492.82519 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 416.875156 463.760429 
L 522.851156 463.760429 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 416.875156 434.695667 
L 522.851156 434.695667 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 416.875156 405.630905 
L 522.851156 405.630905 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 416.875156 376.566143 
L 522.851156 376.566143 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 416.875156 347.501381 
L 522.851156 347.501381 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 416.875156 318.436619 
L 522.851156 318.436619 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 416.875156 289.371857 
L 522.851156 289.371857 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 416.875156 260.307095 
L 522.851156 260.307095 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 416.875156 231.242333 
L 522.851156 231.242333 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 416.875156 202.177571 
L 522.851156 202.177571 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 416.875156 173.11281 
L 522.851156 173.11281 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 416.875156 144.048048 
L 522.851156 144.048048 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 416.875156 114.983286 
L 522.851156 114.983286 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 416.875156 85.918524 
L 522.851156 85.918524 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 416.875156 56.853762 
L 522.851156 56.853762 
" clip-path="url(#p1c4f50117b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m6ad3fb32cf" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m6ad3fb32cf" x="418.310005" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(404.954458 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m6ad3fb32cf" x="455.258835" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(446.512117 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m6ad3fb32cf" x="492.207666" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(483.460947 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(347.18175 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- VoronoiFingerprint_std_dev_Voro_area_maximum -->
      <g style="fill: #333333" transform="translate(74.302344 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-61" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-78" x="2109.220703"/>
       <use xlink:href="#DejaVuSans-69" x="2168.400391"/>
       <use xlink:href="#DejaVuSans-6d" x="2196.183594"/>
       <use xlink:href="#DejaVuSans-75" x="2293.595703"/>
       <use xlink:href="#DejaVuSans-6d" x="2356.974609"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ValenceOrbital_frac_d_valence_electrons -->
      <g style="fill: #333333" transform="translate(128.593594 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-61" x="60.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="121.9375"/>
       <use xlink:href="#DejaVuSans-65" x="149.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="211.244141"/>
       <use xlink:href="#DejaVuSans-63" x="274.623047"/>
       <use xlink:href="#DejaVuSans-65" x="329.603516"/>
       <use xlink:href="#DejaVuSans-4f" x="391.126953"/>
       <use xlink:href="#DejaVuSans-72" x="469.837891"/>
       <use xlink:href="#DejaVuSans-62" x="510.951172"/>
       <use xlink:href="#DejaVuSans-69" x="574.427734"/>
       <use xlink:href="#DejaVuSans-74" x="602.210938"/>
       <use xlink:href="#DejaVuSans-61" x="641.419922"/>
       <use xlink:href="#DejaVuSans-6c" x="702.699219"/>
       <use xlink:href="#DejaVuSans-5f" x="730.482422"/>
       <use xlink:href="#DejaVuSans-66" x="780.482422"/>
       <use xlink:href="#DejaVuSans-72" x="815.6875"/>
       <use xlink:href="#DejaVuSans-61" x="856.800781"/>
       <use xlink:href="#DejaVuSans-63" x="918.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="973.060547"/>
       <use xlink:href="#DejaVuSans-64" x="1023.060547"/>
       <use xlink:href="#DejaVuSans-5f" x="1086.537109"/>
       <use xlink:href="#DejaVuSans-76" x="1136.537109"/>
       <use xlink:href="#DejaVuSans-61" x="1195.716797"/>
       <use xlink:href="#DejaVuSans-6c" x="1256.996094"/>
       <use xlink:href="#DejaVuSans-65" x="1284.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1346.302734"/>
       <use xlink:href="#DejaVuSans-63" x="1409.681641"/>
       <use xlink:href="#DejaVuSans-65" x="1464.662109"/>
       <use xlink:href="#DejaVuSans-5f" x="1526.185547"/>
       <use xlink:href="#DejaVuSans-65" x="1576.185547"/>
       <use xlink:href="#DejaVuSans-6c" x="1637.708984"/>
       <use xlink:href="#DejaVuSans-65" x="1665.492188"/>
       <use xlink:href="#DejaVuSans-63" x="1727.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1781.996094"/>
       <use xlink:href="#DejaVuSans-72" x="1821.205078"/>
       <use xlink:href="#DejaVuSans-6f" x="1860.068359"/>
       <use xlink:href="#DejaVuSans-6e" x="1921.25"/>
       <use xlink:href="#DejaVuSans-73" x="1984.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_1_0_1_0 -->
      <g style="fill: #333333" transform="translate(74.992969 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-5f" x="2335.466797"/>
       <use xlink:href="#DejaVuSans-30" x="2385.466797"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=2_89e+00 -->
      <g style="fill: #333333" transform="translate(90.121719 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-32" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-38" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-39" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(97.064531 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_4_0_1_0 -->
      <g style="fill: #333333" transform="translate(74.992969 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-34" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-5f" x="2335.466797"/>
       <use xlink:href="#DejaVuSans-30" x="2385.466797"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- CrystalNNFingerprint_std_dev_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(95.266875 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-54" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1583.527344"/>
       <use xlink:href="#DejaVuSans-73" x="1619.611328"/>
       <use xlink:href="#DejaVuSans-68" x="1671.710938"/>
       <use xlink:href="#DejaVuSans-61" x="1735.089844"/>
       <use xlink:href="#DejaVuSans-70" x="1796.369141"/>
       <use xlink:href="#DejaVuSans-65" x="1859.845703"/>
       <use xlink:href="#DejaVuSans-64" x="1921.369141"/>
       <use xlink:href="#DejaVuSans-5f" x="1984.845703"/>
       <use xlink:href="#DejaVuSans-43" x="2034.845703"/>
       <use xlink:href="#DejaVuSans-4e" x="2104.669922"/>
       <use xlink:href="#DejaVuSans-5f" x="2179.474609"/>
       <use xlink:href="#DejaVuSans-33" x="2229.474609"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(189.28125 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- CrystalNNFingerprint_mean_q2_CN_12 -->
      <g style="fill: #333333" transform="translate(143.09875 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-71" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-32" x="1489.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="1553.296875"/>
       <use xlink:href="#DejaVuSans-43" x="1603.296875"/>
       <use xlink:href="#DejaVuSans-4e" x="1673.121094"/>
       <use xlink:href="#DejaVuSans-5f" x="1747.925781"/>
       <use xlink:href="#DejaVuSans-31" x="1797.925781"/>
       <use xlink:href="#DejaVuSans-32" x="1861.548828"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- GaussianSymmFunc_std_dev_G2_4_0 -->
      <g style="fill: #333333" transform="translate(150.620469 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-34" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=5_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-35" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_avg_dev_CovalentRadius -->
      <g style="fill: #333333" transform="translate(31.128125 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-43" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6f" x="2077.640625"/>
       <use xlink:href="#DejaVuSans-76" x="2138.822266"/>
       <use xlink:href="#DejaVuSans-61" x="2198.001953"/>
       <use xlink:href="#DejaVuSans-6c" x="2259.28125"/>
       <use xlink:href="#DejaVuSans-65" x="2287.064453"/>
       <use xlink:href="#DejaVuSans-6e" x="2348.587891"/>
       <use xlink:href="#DejaVuSans-74" x="2411.966797"/>
       <use xlink:href="#DejaVuSans-52" x="2451.175781"/>
       <use xlink:href="#DejaVuSans-61" x="2518.408203"/>
       <use xlink:href="#DejaVuSans-64" x="2579.6875"/>
       <use xlink:href="#DejaVuSans-69" x="2643.164062"/>
       <use xlink:href="#DejaVuSans-75" x="2670.947266"/>
       <use xlink:href="#DejaVuSans-73" x="2734.326172"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(81.135469 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(108.967656 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(195.783281 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(204.054531 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(95.004844 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(273.681719 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(152.16625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(110.5825 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageeae16a056e" transform="scale(1 -1) translate(0 -578.16)" x="419.04" y="-43.2" width="101.52" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature55_fold0 -->
    <g transform="translate(160.980156 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.474656 638.149 
L 537.104156 638.149 
L 537.104156 27.789 
L 529.474656 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image71e75814c5" transform="scale(1 -1) translate(0 -609.84)" x="529.2" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(540.604156 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(540.604156 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.005094 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p1c4f50117b">
   <rect x="416.875156" y="27.789" width="105.976" height="610.36"/>
  </clipPath>
 </defs>
</svg>
