<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.657906pt" height="679.5765pt" viewBox="0 0 794.657906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:55:45.295091</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.657906 679.5765 
L 794.657906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
L 525.730906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 475.810742 638.149 
L 475.810742 27.789 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.730906 609.084238 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.730906 580.019476 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.730906 550.954714 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.730906 521.889952 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.730906 492.82519 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.730906 463.760429 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.730906 434.695667 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.730906 405.630905 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.730906 376.566143 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.730906 347.501381 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.730906 318.436619 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.730906 289.371857 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.730906 260.307095 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.730906 231.242333 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.730906 202.177571 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.730906 173.11281 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.730906 144.048048 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.730906 114.983286 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.730906 85.918524 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.730906 56.853762 
" clip-path="url(#p5ca14e1e76)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m79455ad6fa" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m79455ad6fa" x="434.093782" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(425.985579 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m79455ad6fa" x="475.810742" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(472.311367 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m79455ad6fa" x="517.527702" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(514.028327 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8935 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- BondOrientationParameter_mean_BOOP_Q_l=4 -->
      <g style="fill: #333333" transform="translate(100.655781 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-6d" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-65" x="1488.859375"/>
       <use xlink:href="#DejaVuSans-61" x="1550.382812"/>
       <use xlink:href="#DejaVuSans-6e" x="1611.662109"/>
       <use xlink:href="#DejaVuSans-5f" x="1675.041016"/>
       <use xlink:href="#DejaVuSans-42" x="1725.041016"/>
       <use xlink:href="#DejaVuSans-4f" x="1791.894531"/>
       <use xlink:href="#DejaVuSans-4f" x="1870.605469"/>
       <use xlink:href="#DejaVuSans-50" x="1949.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.619141"/>
       <use xlink:href="#DejaVuSans-51" x="2059.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="2138.330078"/>
       <use xlink:href="#DejaVuSans-6c" x="2188.330078"/>
       <use xlink:href="#DejaVuSans-3d" x="2216.113281"/>
       <use xlink:href="#DejaVuSans-34" x="2299.902344"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(75.179844 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-76" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2073.441406"/>
       <use xlink:href="#DejaVuSans-6c" x="2134.623047"/>
       <use xlink:href="#DejaVuSans-75" x="2162.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="2225.785156"/>
       <use xlink:href="#DejaVuSans-65" x="2323.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="2384.720703"/>
       <use xlink:href="#DejaVuSans-70" x="2434.720703"/>
       <use xlink:href="#DejaVuSans-61" x="2498.197266"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(88.876563 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_hexagonal_planar_CN_6 -->
      <g style="fill: #333333" transform="translate(66.861875 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-68" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-78" x="1549.349609"/>
       <use xlink:href="#DejaVuSans-61" x="1608.529297"/>
       <use xlink:href="#DejaVuSans-67" x="1669.808594"/>
       <use xlink:href="#DejaVuSans-6f" x="1733.285156"/>
       <use xlink:href="#DejaVuSans-6e" x="1794.466797"/>
       <use xlink:href="#DejaVuSans-61" x="1857.845703"/>
       <use xlink:href="#DejaVuSans-6c" x="1919.125"/>
       <use xlink:href="#DejaVuSans-5f" x="1946.908203"/>
       <use xlink:href="#DejaVuSans-70" x="1996.908203"/>
       <use xlink:href="#DejaVuSans-6c" x="2060.384766"/>
       <use xlink:href="#DejaVuSans-61" x="2088.167969"/>
       <use xlink:href="#DejaVuSans-6e" x="2149.447266"/>
       <use xlink:href="#DejaVuSans-61" x="2212.826172"/>
       <use xlink:href="#DejaVuSans-72" x="2274.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="2315.21875"/>
       <use xlink:href="#DejaVuSans-43" x="2365.21875"/>
       <use xlink:href="#DejaVuSans-4e" x="2435.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="2509.847656"/>
       <use xlink:href="#DejaVuSans-36" x="2559.847656"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- OPSiteFingerprint_mean_sgl_bd_CN_1 -->
      <g style="fill: #333333" transform="translate(162.7775 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="923.511719"/>
       <use xlink:href="#DejaVuSans-65" x="1020.923828"/>
       <use xlink:href="#DejaVuSans-61" x="1082.447266"/>
       <use xlink:href="#DejaVuSans-6e" x="1143.726562"/>
       <use xlink:href="#DejaVuSans-5f" x="1207.105469"/>
       <use xlink:href="#DejaVuSans-73" x="1257.105469"/>
       <use xlink:href="#DejaVuSans-67" x="1309.205078"/>
       <use xlink:href="#DejaVuSans-6c" x="1372.681641"/>
       <use xlink:href="#DejaVuSans-5f" x="1400.464844"/>
       <use xlink:href="#DejaVuSans-62" x="1450.464844"/>
       <use xlink:href="#DejaVuSans-64" x="1513.941406"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.417969"/>
       <use xlink:href="#DejaVuSans-43" x="1627.417969"/>
       <use xlink:href="#DejaVuSans-4e" x="1697.242188"/>
       <use xlink:href="#DejaVuSans-5f" x="1772.046875"/>
       <use xlink:href="#DejaVuSans-31" x="1822.046875"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(218.598281 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_maximum_NpUnfilled -->
      <g style="fill: #333333" transform="translate(68.358906 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-70" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(188.675938 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_range_CovalentRadius -->
      <g style="fill: #333333" transform="translate(62.230625 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-76" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2070.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2131.9375"/>
       <use xlink:href="#DejaVuSans-65" x="2159.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="2221.244141"/>
       <use xlink:href="#DejaVuSans-74" x="2284.623047"/>
       <use xlink:href="#DejaVuSans-52" x="2323.832031"/>
       <use xlink:href="#DejaVuSans-61" x="2391.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2452.34375"/>
       <use xlink:href="#DejaVuSans-69" x="2515.820312"/>
       <use xlink:href="#DejaVuSans-75" x="2543.603516"/>
       <use xlink:href="#DejaVuSans-73" x="2606.982422"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_avg_dev_Column -->
      <g style="fill: #333333" transform="translate(97.109219 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-43" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6f" x="2077.640625"/>
       <use xlink:href="#DejaVuSans-6c" x="2138.822266"/>
       <use xlink:href="#DejaVuSans-75" x="2166.605469"/>
       <use xlink:href="#DejaVuSans-6d" x="2229.984375"/>
       <use xlink:href="#DejaVuSans-6e" x="2327.396484"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- CrystalNNFingerprint_mean_wt_CN_1 -->
      <g style="fill: #333333" transform="translate(166.71 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-31" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(97.9075 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-69" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-6e" x="2035.347656"/>
       <use xlink:href="#DejaVuSans-69" x="2098.726562"/>
       <use xlink:href="#DejaVuSans-6d" x="2126.509766"/>
       <use xlink:href="#DejaVuSans-75" x="2223.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="2287.300781"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_172 -->
      <g style="fill: #333333" transform="translate(210.327031 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(16.330469 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_maximum_CovalentRadius -->
      <g style="fill: #333333" transform="translate(34.5325 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-43" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-6f" x="2163.382812"/>
       <use xlink:href="#DejaVuSans-76" x="2224.564453"/>
       <use xlink:href="#DejaVuSans-61" x="2283.744141"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.023438"/>
       <use xlink:href="#DejaVuSans-65" x="2372.806641"/>
       <use xlink:href="#DejaVuSans-6e" x="2434.330078"/>
       <use xlink:href="#DejaVuSans-74" x="2497.708984"/>
       <use xlink:href="#DejaVuSans-52" x="2536.917969"/>
       <use xlink:href="#DejaVuSans-61" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-64" x="2665.429688"/>
       <use xlink:href="#DejaVuSans-69" x="2728.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2756.689453"/>
       <use xlink:href="#DejaVuSans-73" x="2820.068359"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- MEGNet_OFMEncoded_v1_169 -->
      <g style="fill: #333333" transform="translate(210.327031 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-39" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(216.128281 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(288.225469 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image27c676156b" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature85_fold0 -->
    <g transform="translate(169.691906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-38" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.625406 638.149 
L 539.254906 638.149 
L 539.254906 27.789 
L 531.625406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image8af56bbeaf" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(542.754906 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(542.754906 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(577.155844 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p5ca14e1e76">
   <rect x="431.418906" y="27.789" width="94.312" height="610.36"/>
  </clipPath>
 </defs>
</svg>
