<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="774.599937pt" height="679.5765pt" viewBox="0 0 774.599937 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:09:27.501332</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 774.599937 679.5765 
L 774.599937 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 398.400937 638.149 
L 518.632938 638.149 
L 518.632938 27.789 
L 398.400937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 446.028983 638.149 
L 446.028983 27.789 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 398.400937 609.084238 
L 518.632938 609.084238 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 398.400937 580.019476 
L 518.632938 580.019476 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 398.400937 550.954714 
L 518.632938 550.954714 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 398.400937 521.889952 
L 518.632938 521.889952 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 398.400937 492.82519 
L 518.632938 492.82519 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 398.400937 463.760429 
L 518.632938 463.760429 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 398.400937 434.695667 
L 518.632938 434.695667 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 398.400937 405.630905 
L 518.632938 405.630905 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 398.400937 376.566143 
L 518.632938 376.566143 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 398.400937 347.501381 
L 518.632938 347.501381 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 398.400937 318.436619 
L 518.632938 318.436619 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 398.400937 289.371857 
L 518.632938 289.371857 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 398.400937 260.307095 
L 518.632938 260.307095 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 398.400937 231.242333 
L 518.632938 231.242333 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 398.400937 202.177571 
L 518.632938 202.177571 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 398.400937 173.11281 
L 518.632938 173.11281 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 398.400937 144.048048 
L 518.632938 144.048048 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 398.400937 114.983286 
L 518.632938 114.983286 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 398.400937 85.918524 
L 518.632938 85.918524 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 398.400937 56.853762 
L 518.632938 56.853762 
" clip-path="url(#pd381d44df3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mef2eea1abb" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mef2eea1abb" x="404.364737" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.25 -->
      <g style="fill: #333333" transform="translate(387.509815 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-32" x="179.199219"/>
       <use xlink:href="#DejaVuSans-35" x="242.822266"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mef2eea1abb" x="446.028983" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(433.782889 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mef2eea1abb" x="487.693228" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(475.447134 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(335.835531 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ValenceOrbital_frac_s_valence_electrons -->
      <g style="fill: #333333" transform="translate(111.600156 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-61" x="60.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="121.9375"/>
       <use xlink:href="#DejaVuSans-65" x="149.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="211.244141"/>
       <use xlink:href="#DejaVuSans-63" x="274.623047"/>
       <use xlink:href="#DejaVuSans-65" x="329.603516"/>
       <use xlink:href="#DejaVuSans-4f" x="391.126953"/>
       <use xlink:href="#DejaVuSans-72" x="469.837891"/>
       <use xlink:href="#DejaVuSans-62" x="510.951172"/>
       <use xlink:href="#DejaVuSans-69" x="574.427734"/>
       <use xlink:href="#DejaVuSans-74" x="602.210938"/>
       <use xlink:href="#DejaVuSans-61" x="641.419922"/>
       <use xlink:href="#DejaVuSans-6c" x="702.699219"/>
       <use xlink:href="#DejaVuSans-5f" x="730.482422"/>
       <use xlink:href="#DejaVuSans-66" x="780.482422"/>
       <use xlink:href="#DejaVuSans-72" x="815.6875"/>
       <use xlink:href="#DejaVuSans-61" x="856.800781"/>
       <use xlink:href="#DejaVuSans-63" x="918.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="973.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1023.060547"/>
       <use xlink:href="#DejaVuSans-5f" x="1075.160156"/>
       <use xlink:href="#DejaVuSans-76" x="1125.160156"/>
       <use xlink:href="#DejaVuSans-61" x="1184.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1245.619141"/>
       <use xlink:href="#DejaVuSans-65" x="1273.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1334.925781"/>
       <use xlink:href="#DejaVuSans-63" x="1398.304688"/>
       <use xlink:href="#DejaVuSans-65" x="1453.285156"/>
       <use xlink:href="#DejaVuSans-5f" x="1514.808594"/>
       <use xlink:href="#DejaVuSans-65" x="1564.808594"/>
       <use xlink:href="#DejaVuSans-6c" x="1626.332031"/>
       <use xlink:href="#DejaVuSans-65" x="1654.115234"/>
       <use xlink:href="#DejaVuSans-63" x="1715.638672"/>
       <use xlink:href="#DejaVuSans-74" x="1770.619141"/>
       <use xlink:href="#DejaVuSans-72" x="1809.828125"/>
       <use xlink:href="#DejaVuSans-6f" x="1848.691406"/>
       <use xlink:href="#DejaVuSans-6e" x="1909.873047"/>
       <use xlink:href="#DejaVuSans-73" x="1973.251953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(133.692031 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(119.99125 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(133.692031 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_0 -->
      <g style="fill: #333333" transform="translate(62.66125 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-30" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(108.084063 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_NdUnfilled -->
      <g style="fill: #333333" transform="translate(63.973437 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-64" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-55" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2084.769531"/>
       <use xlink:href="#DejaVuSans-66" x="2148.148438"/>
       <use xlink:href="#DejaVuSans-69" x="2183.353516"/>
       <use xlink:href="#DejaVuSans-6c" x="2211.136719"/>
       <use xlink:href="#DejaVuSans-6c" x="2238.919922"/>
       <use xlink:href="#DejaVuSans-65" x="2266.703125"/>
       <use xlink:href="#DejaVuSans-64" x="2328.226562"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(112.648281 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(185.580312 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_range_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-64" x="2091.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2155.130859"/>
       <use xlink:href="#DejaVuSans-6c" x="2216.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2244.4375"/>
       <use xlink:href="#DejaVuSans-65" x="2305.960938"/>
       <use xlink:href="#DejaVuSans-76" x="2367.484375"/>
       <use xlink:href="#DejaVuSans-4e" x="2426.664062"/>
       <use xlink:href="#DejaVuSans-75" x="2501.46875"/>
       <use xlink:href="#DejaVuSans-6d" x="2564.847656"/>
       <use xlink:href="#DejaVuSans-62" x="2662.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2725.736328"/>
       <use xlink:href="#DejaVuSans-72" x="2787.259766"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(55.858594 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_trigonal_planar_CN_3 -->
      <g style="fill: #333333" transform="translate(51.462969 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-70" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6c" x="1924.878906"/>
       <use xlink:href="#DejaVuSans-61" x="1952.662109"/>
       <use xlink:href="#DejaVuSans-6e" x="2013.941406"/>
       <use xlink:href="#DejaVuSans-61" x="2077.320312"/>
       <use xlink:href="#DejaVuSans-72" x="2138.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="2179.712891"/>
       <use xlink:href="#DejaVuSans-43" x="2229.712891"/>
       <use xlink:href="#DejaVuSans-4e" x="2299.537109"/>
       <use xlink:href="#DejaVuSans-5f" x="2374.341797"/>
       <use xlink:href="#DejaVuSans-33" x="2424.341797"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(77.207031 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(90.493437 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_maximum_GSbandgap -->
      <g style="fill: #333333" transform="translate(27.226094 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-62" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-61" x="2298.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="2359.28125"/>
       <use xlink:href="#DejaVuSans-64" x="2422.660156"/>
       <use xlink:href="#DejaVuSans-67" x="2486.136719"/>
       <use xlink:href="#DejaVuSans-61" x="2549.613281"/>
       <use xlink:href="#DejaVuSans-70" x="2610.892578"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(38.365469 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(183.110312 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(123.885156 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(255.2075 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(62.66125 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 398.400937 638.149 
L 518.632938 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageb9be95bc4d" transform="scale(1 -1) translate(0 -578.16)" x="401.76" y="-43.2" width="113.76" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature65_fold0 -->
    <g transform="translate(149.633938 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 526.147437 638.149 
L 533.776937 638.149 
L 533.776937 27.789 
L 526.147437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagedf63cded3e" transform="scale(1 -1) translate(0 -609.84)" x="526.32" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(537.276937 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(537.276937 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(571.677875 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pd381d44df3">
   <rect x="398.400937" y="27.789" width="120.232" height="610.36"/>
  </clipPath>
 </defs>
</svg>
