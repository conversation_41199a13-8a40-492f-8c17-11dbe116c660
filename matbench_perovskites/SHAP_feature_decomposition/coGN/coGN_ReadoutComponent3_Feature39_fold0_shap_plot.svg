<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:40:55.510806</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 452.023768 638.149 
L 452.023768 27.789 
" clip-path="url(#p017b685b34)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#p017b685b34)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m5ff1cba76c" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m5ff1cba76c" x="452.023768" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(443.277049 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m5ff1cba76c" x="494.906452" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.2 -->
      <g style="fill: #333333" transform="translate(486.159733 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(43.983906 584.958461) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(50.14875 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_range_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(31.0875 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-64" x="2091.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2155.130859"/>
       <use xlink:href="#DejaVuSans-6c" x="2216.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2244.4375"/>
       <use xlink:href="#DejaVuSans-65" x="2305.960938"/>
       <use xlink:href="#DejaVuSans-76" x="2367.484375"/>
       <use xlink:href="#DejaVuSans-4e" x="2426.664062"/>
       <use xlink:href="#DejaVuSans-75" x="2501.46875"/>
       <use xlink:href="#DejaVuSans-6d" x="2564.847656"/>
       <use xlink:href="#DejaVuSans-62" x="2662.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2725.736328"/>
       <use xlink:href="#DejaVuSans-72" x="2787.259766"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(66.049375 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-76" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2073.441406"/>
       <use xlink:href="#DejaVuSans-6c" x="2134.623047"/>
       <use xlink:href="#DejaVuSans-75" x="2162.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="2225.785156"/>
       <use xlink:href="#DejaVuSans-65" x="2323.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="2384.720703"/>
       <use xlink:href="#DejaVuSans-70" x="2434.720703"/>
       <use xlink:href="#DejaVuSans-61" x="2498.197266"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(147.772656 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_range_CovalentRadius -->
      <g style="fill: #333333" transform="translate(53.100156 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-76" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2070.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2131.9375"/>
       <use xlink:href="#DejaVuSans-65" x="2159.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="2221.244141"/>
       <use xlink:href="#DejaVuSans-74" x="2284.623047"/>
       <use xlink:href="#DejaVuSans-52" x="2323.832031"/>
       <use xlink:href="#DejaVuSans-61" x="2391.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2452.34375"/>
       <use xlink:href="#DejaVuSans-69" x="2515.820312"/>
       <use xlink:href="#DejaVuSans-75" x="2543.603516"/>
       <use xlink:href="#DejaVuSans-73" x="2606.982422"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_11 -->
      <g style="fill: #333333" transform="translate(209.467812 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(79.746094 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(147.772656 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mode_Electronegativity -->
      <g style="fill: #333333" transform="translate(44.91625 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(62.252969 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_176 -->
      <g style="fill: #333333" transform="translate(201.196562 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-36" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_mean_CovalentRadius -->
      <g style="fill: #333333" transform="translate(54.034531 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-76" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-61" x="2063.480469"/>
       <use xlink:href="#DejaVuSans-6c" x="2124.759766"/>
       <use xlink:href="#DejaVuSans-65" x="2152.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="2214.066406"/>
       <use xlink:href="#DejaVuSans-74" x="2277.445312"/>
       <use xlink:href="#DejaVuSans-52" x="2316.654297"/>
       <use xlink:href="#DejaVuSans-61" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-64" x="2445.166016"/>
       <use xlink:href="#DejaVuSans-69" x="2508.642578"/>
       <use xlink:href="#DejaVuSans-75" x="2536.425781"/>
       <use xlink:href="#DejaVuSans-73" x="2599.804688"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(234.119062 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(147.772656 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- AverageBondLength_mean_Average_bond_length -->
      <g style="fill: #333333" transform="translate(76.806875 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-6d" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-65" x="1159.611328"/>
       <use xlink:href="#DejaVuSans-61" x="1221.134766"/>
       <use xlink:href="#DejaVuSans-6e" x="1282.414062"/>
       <use xlink:href="#DejaVuSans-5f" x="1345.792969"/>
       <use xlink:href="#DejaVuSans-41" x="1395.792969"/>
       <use xlink:href="#DejaVuSans-76" x="1458.326172"/>
       <use xlink:href="#DejaVuSans-65" x="1517.505859"/>
       <use xlink:href="#DejaVuSans-72" x="1579.029297"/>
       <use xlink:href="#DejaVuSans-61" x="1620.142578"/>
       <use xlink:href="#DejaVuSans-67" x="1681.421875"/>
       <use xlink:href="#DejaVuSans-65" x="1744.898438"/>
       <use xlink:href="#DejaVuSans-5f" x="1806.421875"/>
       <use xlink:href="#DejaVuSans-62" x="1856.421875"/>
       <use xlink:href="#DejaVuSans-6f" x="1919.898438"/>
       <use xlink:href="#DejaVuSans-6e" x="1981.080078"/>
       <use xlink:href="#DejaVuSans-64" x="2044.458984"/>
       <use xlink:href="#DejaVuSans-5f" x="2107.935547"/>
       <use xlink:href="#DejaVuSans-6c" x="2157.935547"/>
       <use xlink:href="#DejaVuSans-65" x="2185.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="2247.242188"/>
       <use xlink:href="#DejaVuSans-67" x="2310.621094"/>
       <use xlink:href="#DejaVuSans-74" x="2374.097656"/>
       <use xlink:href="#DejaVuSans-68" x="2413.306641"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- GeneralizedRDF_mean_Gaussian_center=1_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(26.314062 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-31" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imaged580ea78b7" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature39_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-39" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imageb9410f0866" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p017b685b34">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
