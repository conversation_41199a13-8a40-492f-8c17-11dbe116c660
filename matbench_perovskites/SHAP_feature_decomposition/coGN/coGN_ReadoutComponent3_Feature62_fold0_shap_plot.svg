<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:03:07.794597</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 445.092037 638.149 
L 445.092037 27.789 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#p6e2c661c0f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="me438161747" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#me438161747" x="445.092037" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(441.592662 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#me438161747" x="507.976446" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(504.477071 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(278.319062 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(62.252969 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(147.772656 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(143.87875 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(122.835 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(143.87875 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(44.918281 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- XRDPowderPattern_xrd_38 -->
      <g style="fill: #333333" transform="translate(225.916875 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-38" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CrystalNNFingerprint_std_dev_pentagonal_pyramidal_CN_6 -->
      <g style="fill: #333333" transform="translate(14.165156 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-70" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1595.044922"/>
       <use xlink:href="#DejaVuSans-6e" x="1656.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1719.947266"/>
       <use xlink:href="#DejaVuSans-61" x="1759.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1820.435547"/>
       <use xlink:href="#DejaVuSans-6f" x="1883.912109"/>
       <use xlink:href="#DejaVuSans-6e" x="1945.09375"/>
       <use xlink:href="#DejaVuSans-61" x="2008.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="2069.751953"/>
       <use xlink:href="#DejaVuSans-5f" x="2097.535156"/>
       <use xlink:href="#DejaVuSans-70" x="2147.535156"/>
       <use xlink:href="#DejaVuSans-79" x="2211.011719"/>
       <use xlink:href="#DejaVuSans-72" x="2270.191406"/>
       <use xlink:href="#DejaVuSans-61" x="2311.304688"/>
       <use xlink:href="#DejaVuSans-6d" x="2372.583984"/>
       <use xlink:href="#DejaVuSans-69" x="2469.996094"/>
       <use xlink:href="#DejaVuSans-64" x="2497.779297"/>
       <use xlink:href="#DejaVuSans-61" x="2561.255859"/>
       <use xlink:href="#DejaVuSans-6c" x="2622.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="2650.318359"/>
       <use xlink:href="#DejaVuSans-43" x="2700.318359"/>
       <use xlink:href="#DejaVuSans-4e" x="2770.142578"/>
       <use xlink:href="#DejaVuSans-5f" x="2844.947266"/>
       <use xlink:href="#DejaVuSans-36" x="2894.947266"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(157.579531 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(234.119062 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(80.65 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(20.096406 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(147.772656 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(86.54875 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(90.028281 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6f" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-63" x="1592.75"/>
       <use xlink:href="#DejaVuSans-74" x="1647.730469"/>
       <use xlink:href="#DejaVuSans-61" x="1686.939453"/>
       <use xlink:href="#DejaVuSans-68" x="1748.21875"/>
       <use xlink:href="#DejaVuSans-65" x="1811.597656"/>
       <use xlink:href="#DejaVuSans-64" x="1873.121094"/>
       <use xlink:href="#DejaVuSans-72" x="1936.597656"/>
       <use xlink:href="#DejaVuSans-61" x="1977.710938"/>
       <use xlink:href="#DejaVuSans-6c" x="2038.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2066.773438"/>
       <use xlink:href="#DejaVuSans-43" x="2116.773438"/>
       <use xlink:href="#DejaVuSans-4e" x="2186.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2261.402344"/>
       <use xlink:href="#DejaVuSans-36" x="2311.402344"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(206.997812 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image61c22430f8" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature62_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image2a52debe42" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p6e2c661c0f">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
