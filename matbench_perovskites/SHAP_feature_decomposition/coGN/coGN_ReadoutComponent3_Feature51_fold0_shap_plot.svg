<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="781.271031pt" height="679.5765pt" viewBox="0 0 781.271031 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T16:09:08.142385</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 781.271031 679.5765 
L 781.271031 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
L 520.984031 27.789 
L 409.392031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 436.873957 638.149 
L 436.873957 27.789 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 409.392031 609.084238 
L 520.984031 609.084238 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 409.392031 580.019476 
L 520.984031 580.019476 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 409.392031 550.954714 
L 520.984031 550.954714 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 409.392031 521.889952 
L 520.984031 521.889952 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 409.392031 492.82519 
L 520.984031 492.82519 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 409.392031 463.760429 
L 520.984031 463.760429 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 409.392031 434.695667 
L 520.984031 434.695667 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 409.392031 405.630905 
L 520.984031 405.630905 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 409.392031 376.566143 
L 520.984031 376.566143 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 409.392031 347.501381 
L 520.984031 347.501381 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 409.392031 318.436619 
L 520.984031 318.436619 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 409.392031 289.371857 
L 520.984031 289.371857 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 409.392031 260.307095 
L 520.984031 260.307095 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 409.392031 231.242333 
L 520.984031 231.242333 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 409.392031 202.177571 
L 520.984031 202.177571 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 409.392031 173.11281 
L 520.984031 173.11281 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 409.392031 144.048048 
L 520.984031 144.048048 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 409.392031 114.983286 
L 520.984031 114.983286 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 409.392031 85.918524 
L 520.984031 85.918524 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 409.392031 56.853762 
L 520.984031 56.853762 
" clip-path="url(#p468ee36c53)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mee80c2d605" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mee80c2d605" x="436.873957" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(424.627863 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mee80c2d605" x="475.911662" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(463.665568 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mee80c2d605" x="514.949367" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.50 -->
      <g style="fill: #333333" transform="translate(502.703274 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(342.506625 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(88.198125 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(265.422656 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(85.457969 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(10.945625 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(37.252344 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(130.982344 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_128 -->
      <g style="fill: #333333" transform="translate(188.300156 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-38" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(57.759844 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(77.125781 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(263.442187 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(73.652344 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(73.652344 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_avg_dev_NpValence -->
      <g style="fill: #333333" transform="translate(56.039375 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-56" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-61" x="2206.755859"/>
       <use xlink:href="#DejaVuSans-6c" x="2268.035156"/>
       <use xlink:href="#DejaVuSans-65" x="2295.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="2357.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2420.720703"/>
       <use xlink:href="#DejaVuSans-65" x="2475.701172"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- OxidationStates_minimum_oxidation_state -->
      <g style="fill: #333333" transform="translate(107.954063 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-6d" x="848.779297"/>
       <use xlink:href="#DejaVuSans-69" x="946.191406"/>
       <use xlink:href="#DejaVuSans-6e" x="973.974609"/>
       <use xlink:href="#DejaVuSans-69" x="1037.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1065.136719"/>
       <use xlink:href="#DejaVuSans-75" x="1162.548828"/>
       <use xlink:href="#DejaVuSans-6d" x="1225.927734"/>
       <use xlink:href="#DejaVuSans-5f" x="1323.339844"/>
       <use xlink:href="#DejaVuSans-6f" x="1373.339844"/>
       <use xlink:href="#DejaVuSans-78" x="1431.396484"/>
       <use xlink:href="#DejaVuSans-69" x="1490.576172"/>
       <use xlink:href="#DejaVuSans-64" x="1518.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
       <use xlink:href="#DejaVuSans-69" x="1682.324219"/>
       <use xlink:href="#DejaVuSans-6f" x="1710.107422"/>
       <use xlink:href="#DejaVuSans-6e" x="1771.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1834.667969"/>
       <use xlink:href="#DejaVuSans-73" x="1884.667969"/>
       <use xlink:href="#DejaVuSans-74" x="1936.767578"/>
       <use xlink:href="#DejaVuSans-61" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-74" x="2037.255859"/>
       <use xlink:href="#DejaVuSans-65" x="2076.464844"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CrystalNNFingerprint_mean_wt_CN_5 -->
      <g style="fill: #333333" transform="translate(144.683125 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-35" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_maximum_GSbandgap -->
      <g style="fill: #333333" transform="translate(38.217187 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-62" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-61" x="2298.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="2359.28125"/>
       <use xlink:href="#DejaVuSans-64" x="2422.660156"/>
       <use xlink:href="#DejaVuSans-67" x="2486.136719"/>
       <use xlink:href="#DejaVuSans-61" x="2549.613281"/>
       <use xlink:href="#DejaVuSans-70" x="2610.892578"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(194.101406 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(196.571406 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(49.356562 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagee461d485ae" transform="scale(1 -1) translate(0 -578.16)" x="411.84" y="-43.2" width="106.56" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature51_fold0 -->
    <g transform="translate(156.305031 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.958531 638.149 
L 535.588031 638.149 
L 535.588031 27.789 
L 527.958531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image2373650264" transform="scale(1 -1) translate(0 -609.84)" x="527.76" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(539.088031 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(539.088031 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(573.488969 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p468ee36c53">
   <rect x="409.392031" y="27.789" width="111.592" height="610.36"/>
  </clipPath>
 </defs>
</svg>
