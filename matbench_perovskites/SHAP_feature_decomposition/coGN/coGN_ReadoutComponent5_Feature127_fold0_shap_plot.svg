<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="772.995531pt" height="679.5765pt" viewBox="0 0 772.995531 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T03:25:36.066898</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 772.995531 679.5765 
L 772.995531 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 385.504531 638.149 
L 516.104531 638.149 
L 516.104531 27.789 
L 385.504531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 442.592647 638.149 
L 442.592647 27.789 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 385.504531 609.084238 
L 516.104531 609.084238 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 385.504531 580.019476 
L 516.104531 580.019476 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 385.504531 550.954714 
L 516.104531 550.954714 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 385.504531 521.889952 
L 516.104531 521.889952 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 385.504531 492.82519 
L 516.104531 492.82519 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 385.504531 463.760429 
L 516.104531 463.760429 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 385.504531 434.695667 
L 516.104531 434.695667 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 385.504531 405.630905 
L 516.104531 405.630905 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 385.504531 376.566143 
L 516.104531 376.566143 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 385.504531 347.501381 
L 516.104531 347.501381 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 385.504531 318.436619 
L 516.104531 318.436619 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 385.504531 289.371857 
L 516.104531 289.371857 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 385.504531 260.307095 
L 516.104531 260.307095 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 385.504531 231.242333 
L 516.104531 231.242333 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 385.504531 202.177571 
L 516.104531 202.177571 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 385.504531 173.11281 
L 516.104531 173.11281 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 385.504531 144.048048 
L 516.104531 144.048048 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 385.504531 114.983286 
L 516.104531 114.983286 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 385.504531 85.918524 
L 516.104531 85.918524 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 385.504531 56.853762 
L 516.104531 56.853762 
" clip-path="url(#pfe18c3e8f4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m5f72a5e37a" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m5f72a5e37a" x="388.536014" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2.5 -->
      <g style="fill: #333333" transform="translate(375.180468 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m5f72a5e37a" x="442.592647" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(433.845929 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m5f72a5e37a" x="496.64928" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 2.5 -->
      <g style="fill: #333333" transform="translate(487.902561 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(328.123125 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(107.094844 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_pentagonal_planar_CN_5 -->
      <g style="fill: #333333" transform="translate(15.065 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-70" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1489.673828"/>
       <use xlink:href="#DejaVuSans-6e" x="1551.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1614.576172"/>
       <use xlink:href="#DejaVuSans-61" x="1653.785156"/>
       <use xlink:href="#DejaVuSans-67" x="1715.064453"/>
       <use xlink:href="#DejaVuSans-6f" x="1778.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="1839.722656"/>
       <use xlink:href="#DejaVuSans-61" x="1903.101562"/>
       <use xlink:href="#DejaVuSans-6c" x="1964.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1992.164062"/>
       <use xlink:href="#DejaVuSans-70" x="2042.164062"/>
       <use xlink:href="#DejaVuSans-6c" x="2105.640625"/>
       <use xlink:href="#DejaVuSans-61" x="2133.423828"/>
       <use xlink:href="#DejaVuSans-6e" x="2194.703125"/>
       <use xlink:href="#DejaVuSans-61" x="2258.082031"/>
       <use xlink:href="#DejaVuSans-72" x="2319.361328"/>
       <use xlink:href="#DejaVuSans-5f" x="2360.474609"/>
       <use xlink:href="#DejaVuSans-43" x="2410.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="2480.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2555.103516"/>
       <use xlink:href="#DejaVuSans-35" x="2605.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- BondOrientationParameter_std_dev_BOOP_Q_l=1 -->
      <g style="fill: #333333" transform="translate(41.040625 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-73" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-74" x="1443.546875"/>
       <use xlink:href="#DejaVuSans-64" x="1482.755859"/>
       <use xlink:href="#DejaVuSans-5f" x="1546.232422"/>
       <use xlink:href="#DejaVuSans-64" x="1596.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1659.708984"/>
       <use xlink:href="#DejaVuSans-76" x="1721.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="1780.412109"/>
       <use xlink:href="#DejaVuSans-42" x="1830.412109"/>
       <use xlink:href="#DejaVuSans-4f" x="1897.265625"/>
       <use xlink:href="#DejaVuSans-4f" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-50" x="2054.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="2114.990234"/>
       <use xlink:href="#DejaVuSans-51" x="2164.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2243.701172"/>
       <use xlink:href="#DejaVuSans-6c" x="2293.701172"/>
       <use xlink:href="#DejaVuSans-3d" x="2321.484375"/>
       <use xlink:href="#DejaVuSans-31" x="2405.273438"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- VoronoiFingerprint_std_dev_Voro_vol_maximum -->
      <g style="fill: #333333" transform="translate(52.655312 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-76" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-6f" x="1736.763672"/>
       <use xlink:href="#DejaVuSans-6c" x="1797.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1825.728516"/>
       <use xlink:href="#DejaVuSans-6d" x="1875.728516"/>
       <use xlink:href="#DejaVuSans-61" x="1973.140625"/>
       <use xlink:href="#DejaVuSans-78" x="2034.419922"/>
       <use xlink:href="#DejaVuSans-69" x="2093.599609"/>
       <use xlink:href="#DejaVuSans-6d" x="2121.382812"/>
       <use xlink:href="#DejaVuSans-75" x="2218.794922"/>
       <use xlink:href="#DejaVuSans-6d" x="2282.173828"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- XRDPowderPattern_xrd_20 -->
      <g style="fill: #333333" transform="translate(189.132969 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-30" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(164.412656 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(157.910625 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(142.761562 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(22.657812 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(172.683906 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(77.597031 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(164.412656 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(172.683906 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(49.764844 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=4_43e+00 -->
      <g style="fill: #333333" transform="translate(16.454375 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-34" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-34" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-33" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2b" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2530.761719"/>
       <use xlink:href="#DejaVuSans-30" x="2594.384766"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(63.634219 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(242.311094 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(120.795625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(79.211875 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 385.504531 638.149 
L 516.104531 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAKwAAAMjCAYAAAA4P/tIAABX90lEQVR4nO3dd5wkdZ3/8de3qjpOjpsDbGCXHJack4iCYT0DZz4PFeN5jCiCqICCMup5h+n8gZ4BBWQPRQ9RAUEyS3Zh2Zx3ZmdnJ/RMx6r6/v6ont0JPdWzu7NT29Of5+MxD5jq9Jnad3/7W9Xf+n6V1hrhb7267gch3I87qMQcfUNN0PWUMyWB9bdBXet00WxkCGPiUEcXEdLWLH2jE3Rt5cgIuoCD2WZ1beUOphkJ4mQJkSJKG83kMLJB11auJLA++tHbsoQAtXubi0k3tWr0R4kDyQq6gINZFsfSaJrpQqEBRYIYBpLXoEhgfYRQ5mzaaGYzMfrJEqGdWSSIB11a2ZIugY84rprKBmL0AxAmwwzWEke6sEGRFtZHmJQKkaWDGSSpJkyaerYToy/o0sqWBNZHlhBtzMJEEyJFN7X0UEuURNCllS0JrI8ojllHLwbeueomdrKeeWSoCLiy8iV9WB9VJNVAWAEUmim0YZEOsKryJoH1oQvsHgMHE/l2MCgSWB8ZwiO29VONiR1ANQKkD+vLJEuKCBY2CpdeGsgSwpTTWoGRFtaHiU2MHUTYSZROdGQnH333m3i9oVG+6gqIjNbykVLv1zH6aa9q5KEFZ9ATq2Z2+1Y+/OZ38Z7HX+Hho+f+78u3Hbo06DrLiXQJfOSIkI0afPWiT/HilCY0isO6Epy9dSeGhiO2d7096BrLjXQJRrFW3TSjn1ruOuY8bjt6IU9Nb+Dp6fX8etEsVCSEGzY5fl0bSz689rWgay0nEtjRpRPUcscRJ5Iz9+ymnGnw/LQ6GvpSKBe0Um0B1lh2JLCjmKev7oyRokeN/FYrpRRzt3exelotz91+6LkBlFe2JLA+qujl/H+sH7G9sTdBVySsHzl8zhkBlFXW5KDLR4YI5/xjAzsr4yw7ZREaxbkrNnDuqtX6s1veYXw26ALLkJzW8rFDfVJvZBFVZHEwMNCkCKNx9Qn6M/LpFABpYX0oFFVkMdGYeBfJVpEmLWMJAiOthI8YqYIDXSryVyCIiSeB9eGgUbgjtpvkAqhGgATWVxd1bpgs7G5lNSFyGLLbAiN9WB8uhhPGNUxSuw+6DDSuDC8MjATWR5Q0OWrpJ5YfVuhQSZKYDC8MjAS2iG4qcfK7ycWgi2pcCWxgpDPmo4N61xnxnlb0yUQagZHA+oiSdgudcQ3JedjASGB9aNxEeMTHv6aCTCD1CAmsr0X6lilVJKmknxA5YqSooxeHjJwmCIgcdBXVnw5TEY2QQwMGWT1bf33k5bRiQkgLW8Rc/Y2YRU8kRNfTUXZ8aLb+muyzAMloLVFSpLUQJUUCK0qKBFaUFAnsGD07+yuyrw4C8o9QxGVvW375ffO+oY/cutLpCb9P/+eS20YOkBUTRs4SFPHb+a26LzKTFHG0ghnpbTw1rdm96e9LzaBrK0fyxUERXfFZHNO2BiNkYzkOO6wmFu3qkU+mgEhgi5jdu52fnn0puyq8JWaP3LqauVu3BVxV+ZKWooi/LV5CT6yKeDZHPJvj1WnzSFbEgi6rbEkLW8S22ilUZ7K71z6MOA7/mDYv0JrKmbSwRURte8hCnQpwTBn7EhQJbBFWgbMohgzgDowE1sctZ/yhMuQWOO2qZMb4oEgf1seLlU1VjdkMU3d1ozVopVDA6zPqgy6tbElgfXRWVkYXbt9BU1cCK9/SZiwLs05WQgyKdAl8fOTev14QTed2hxUgYtvM3tkbYFXlTQLrY4GT/LHSI/uwlusEUI0ACawvE5Ot9d7Hv2bPDFsvzp4SWE3lTvqwBWxXX63S6Fv6Q/VqXX0tIW2wsybECVvWU9frMn/ndmx1XbeF1Yz+jUwDM4GkhR1mu/paDbB1J/pj/aEoieo4K6fW0NTdz7KjjmPFISE++MKjaKprNG4G9Z5Q0DWXE2lhR9BfyWFVZajk+akzuOyJFzjvNW9hjo8+vJyfn34stXY7LiZgonGvVHBzsDWXD2lhR1qgUThYVLi53WEd8M9PvsTz0w7DwMX1dt/JgVRZpiSwI/3ExCFMH4u3dYy40XI1sVQMFwOFjULdEECNZUsCO8w0/dXfm7g/itOvK7Np0tbQXlNnVQW90Uos+lHwOPo3zwdUalmSwBYwTX/1ihosy0TTUVnDrqoKXKC9robXZs/mLwuOwMQ1lL5TFpabYBLYUUzTX3FtDN0djmPnQrw6czZ9Zhxta9yQCfo3MmQrAHKWwIdNv/vQcaeblZkcMzq72TCtgdUzpzC7rT3o0sqWBNZH60UXzD+yu2/9qwtm8uqh0wGIpzLsikQDrqx8SWB9TE0mlQ4rLnrqFdrrqlFAfX8/Ty2cE3RpZUv6sD6662upT6boaawiUxsnWx0lXRGhuk9WQgyKtLA+fn3Mgg2H7eqmY0rj7qsMumqqqN6xK+DKype0sD7012K6r6pyyCUxrmmSk8u8AyOBLcKxRs5IlAvLeJegSGCLmLdz3YhtltMTQCUCpA9b1Dteuo+nEjt4Zs5xROwM57/+GG2xCHB20KWVJQlsEf3hGG975QHe9soDu7f94qgLAqyovEmXoIjvLXkT7qCDrs5YNT85UoYQBEVa2CJeqj/UPf29Xzb+ZcWT7IxW8ONjzuTIjR2vBV1XuZIJjcfgzW99obcvFq2K52wdSSdvu/ePJ18edE3lSgIrSor0YUVJkcCKkiKBFSVFAitKipzWGgN1i+0w8OZWPKxbrPOCrah8yVmCItQtOXfoDMYaUK/pz1uHB1ZUGZMuQXFqxK9aLwqmFCGB3RdqeIjFRJE+7FgMjqf0oAIlLezekrY1UBJYUVIksHtLugSBkj7sWAwPqYQ2MNLC+lCttvRYDzISWD/OKE2pxDgw0iXwM/B2Hh5Q6RIERlpYHx++/2H5RusgI4H1MStjrAi6BjGUBHYUT8RvNWZ2dXqdAa2HriwnAiOBHcV3zz/FntnXRrgvAzk3H1o9+oGYmBAS2ALO/9i2+OUv/1ndccypZC0LQqY3wlApMBS4GnWLvTPoOsuRBLaw45vSKVw7BGaBXeQNj62b6KKEBHY069JWmBPXtkOB1bzz7IksSHgksAU8+OPp27K2QY2b5U1Pr/H6rrtpbwz3561IYAWWMQnsKJ6eNvvb57b9ldpc1uu3KvI/8jVXkCSwo7jqxfe3dFRGueO8o4IuRQwigfXxneMuXmu6o/ZhRQBkLIGP35x3xgJAEnsQkRbWj/RXDzoSWD8yZ8NBRwLrQ38+JIk9yEhgRUmRwIqSIoEVJUUCK0qKBFaUFAmsKCkS2L3lXS4jQwsDIoEtRqkLdl8eozWgtL4qJMt5B0Rm4BYlRVpYUVIksKKkSGBFSZHAipIiA7jH4F8ueDpUn+jdnlNG2/eeOv/IoOspZ3KWoIivH3HPhh9dfMacbVU1aAVLtmzm2Ffbjv/vx858IejaypG0sEU8tGThnFO37uLo9jXkTIPHZk0hUpF5DulOBUICW8S0pM2nnr6Pw9tXkrEiLJl9Iv+76Ci5diYgEtgiPvbsnzhl83Lvl2wfl678E70hEzg10LrKlXysFXHM9tdGbDtt4z8CqESABLY4t8Auyk18GcIjgS3ihZqjSFDDRhazmcPooZYHZp8YdFllS/qwPtQ3spvu1VNYRcxbdB5oZxaOHQ+6tLIlgfWjmBnO5PjahSfxp4VziNk2739uJRev3Bh0ZWVLAutHwU9PPJz7Dj+Uubt6yZkG3z/9GGrSWd4cdG1lSgJbxLOzp9D6xyc4tCsBwAvTGvjTYXP4esB1lSsJrD/1ptc37Q4rwHHbO1nXVBNgSeVNzhL40bBwUFgHHNneGUAxAqSFLerJhTP4ySHHsb65llkdvXzmj8/w2symoMsqWzJaaxTqG9mfYagPKgP0oJEDpuNSu6ufzprKqL4mnAmuwvIkXYLRKD6IyodV7/lxTANTaU57blUq4ArLkgS2AHVT9nPeCt565HKdGpKRENnqsFJfz1YFUF5Zk8AW5i1p5BbuLtWnMtSn0zByYXpxgElgC9BXh2/2WlY1chZurYn0Z3GSOfQ14d4g6itnEtjRaO5DgZnM7Qmt1lh9WXaEojx48lEyoCAAcpbAh/pmVptdaRwAywBbg+MStx36v1sv3YEAyHlYPxovrC6Qza9+pBTZkBlgUeVNugR+NNT1p0dsrkuN3CYmhgTWn/7gs0Mvh4llc7znxZGXzYiJIV0CPya8adVG5u3s5o+LD6Uim+Ofn3+NrvqKoCsrWxJYPxqeWzSXS558mXPWbgbANgweOEzGEgRFAuvHoe3JRTOmoQym7urBMRSrZ05l0bbXg66sbEkf1of+Unj62x55jrBr01VbQW91nCm93cxZ2xd0aWVLAltMNDbs+1eDzmkyhCAoEtgikrHwiG1aOlKBkcAWsb16ZGu6tcA2MTEksEW8MK2ZtfW1OEqRMU1enNbM5uraoMsqW/LhVsS87Z08sWgeT82e4W1Qiret3SQDMAIiLWwR8zZuaF+8aTsKCNsOp61Yw4zezjcFXVe5ktFaY3DPzJ9Vb6yq3h6xcz2fXP3u6UHXU84ksKKkSJdAlBQJrCgpElhRUiSwoqTIedgijvrw2r9fuHrbGYZlYWZzvDSj3vnT3UfKfguInCUo4kNveUlvmdKIUgoXqE0m2WBne5/7zeEyhWEApEvgY977Vu7aMLUR11DsioZIWwY98ThTtCmDCQIiH20+IqFwRSIa5pWmKnKmAVozM5FmjmMHXVrZkhbWh+PkeK2h0gsrgFJsqY7RI5d5B0YC6yMdjZEqEM5kNBRANQKkS+DL0A7K1XsmMFSAUkQdOVANirSwPvotC+1qby9Zygus7ZI0pUsQFGlhfXQ21VhEDDAHva9NF9twgyuqzEkL68O1rKFhBTANuitjwRQkpIX1E3Idclp7k8ENrN1pQGZ4iMWEkT3vw9V40xcOHGPlf69IylocQZHA+lCGKjgHrKFlqvigSJfAh60MQHPypjaO2N5JVzzCI/NmsqtaJt8Oigx+GUWHuvas5huueeT89VsI25qsaXDBqtXUpLP0WZ1c8fSj2iKHBbjkesP6rtqgay4H0sIO06GuNYB7+iu73qbQPHboTKYldvHbX36HE7auJ2tYdEVrqaRXaUJoYhhYNba6TFv619JVOMCkDzvSRWmDt33lnAvQlkkmZPHjZT/hhK3rAQi7NlOSO/MnDXKADSgMwjhq6eNBFl4OJLAjLU7G4N6jTgPD2z3nrf2Hz90HvkRQKDj+gFdX5iSwIz1c2+/yg3t/unvDa80zfO4+8DWtC/DdA1iXQAI7QpO+8QUD48pjt3Xn0BoU/PubP0DK2jNCSwMuCpcwXmBdbNKOoZd9Kai6y4WcJRhFh7q2ovnGaxOYhsJQNCe6eeOql9ger+HIjVv5ysN35QzMjhDZKovcBZZe9kzQNZcDCawP44aUq8OmYtj3B5WJNIkbquSMQACkS+BDoxkeVgBtSFaDIoH1o7UuuDhy1paPpYBIYP2YRn6k1p7FkXEhEZZLZIIigfXjAobKnxbQ3n8NRX0iGXBh5UsC66O6K0EkZ3v92PxPyHY4c+P2oEsrWxJYH9GsbZNxwHG9FtZ2ydmwrUqW7gyKDH7xkQgZZAyT+q1dJA2DuO2Qqo7z4gxZujMoElgfNTmbqrYudsSjAKQti3AySywuV80GRboEPnaZ5u6wDshaJseuawuoIiGB9TE1nSHkOCO2NyXTAVQjQALra5fWz56/fuuQbTN7+rALLOcpJoaMJSjibe9+TU/b3snK+hqakynmZDL8eWbTrS/+atGng66tHElgi6j6an/4mM0d6cZURqVCFtvRz7/8P/NPCLquciWBFSVF+rCipEhgRUmRwIqSIoEVJUW+mh2DyJd60woVUdp107FQg74u3h10TeVKAltE6Jo+bYdD3vBCrQ2VdbpAJoMLinQJfFhf6n7SDpnelQchAywDbZmEr+6RdY8CIi2sj5Crlzhh0wvrAEsR6XfkjR4Q2fE+YtmstxjHYKaBMXybmDASWB9dVfGCl3knIjL4JSgSWD9ZJz9v/CBao3OyikxQpA/rJ2SC7YJleOcFNN7vsihHYCSwfox8YAc3slqDKX3YoEhT4cuFjJMPrfaunk2PvAJBTBxpYX0phdZeaAfT8j4Piux5P64LCizXZX6in1n9KVR+zlgRDGlhfSnm9/ZyaleScH6gu+26/Hl2AyDLdwZBWlhfWi3uyxDSmi7LpNOyMA2DS1ZvlzY2INLC+nGV6o6EeGRGI4e1beeyl1/BwGTN1JlBV1a25JouH+r6pDYdeOuKFfz4nnt2fxz1RKPE0p1U0uZU6rvlTT+BpEswCnVtv4kycAyDf3/00SE7qiadpo9mLEwTtfScoGosRxLY0YSNTw+cDahJpVjd1EhvJLL75jgpklSRRf0poArLknycjUbrR0ARy2U5q+UzdFXEiWezfPGBB/nko4/TRSVz2IxCrwq61HIifVgf6mspbUYMHGvobIX/94MfENoc5iT7CarpC6GXyYDuCSJdAn96eFgBbr7wbRxuP6+r6auUsE4s6RL4cRwdzSmVDg3dTRqYrv9b3uwBkJ3uR2vmdPVhDBoTu6AzQZ8hExoHRVpYPyFTr41XcdqmDsKOS2XWZltVjOUzGqTjHxAJrJ+sjR22eGx2E0rrPcNik9kgqypr0iXwk3M1iQw4+QURNdCfg6xcIhMUCWwxA2cJBgIbMgtemCgmhgTWj2VAJB9YA28crGVAWA66giJ9WB8G4Jpq6EWHrvZCKwIhe96HaypvrdnBDCV7LUCy6/2YRuH+qvRhAyOB9TO8dR0gZ2EDI4H1Yw+f9iVvlM3iwJPA+gmHIoWmKiJn3xFMQUKGFxahvtK3jWh4GqbyWtZ0ztZfrQgFXVe5ksCOkbohHdVfjsoiswGTwIqSIn1YUVIksKKkSGBFSZGxBGPwv9N+EV1dX92dMox7vvLKpe8Nup5yJgddRfxk9h12NWGzu7qCSNbGSib1+7a8Wz6ZAiI73scL8W+G3OpaM1tl0pjsIarStM2eor437y65UjYg0iXw8UzN9GRdqg87a6CzYCiXqU4n22tq5Y0eEAmsj3QsRC6j6K6MYcQ1jmFQk0hSHZFruoIigfWRCMcwIiGmdCd2b0tGQqRHG8UlDjj5aPORRtPY2zdkWzyTI23KwnJBkcD6UK5GFRhKmJGVEAMjXQIfdljRWVtJKJNj1ZR66vvTHNrRRU91POjSypYE1ke1TqNzmpsvPh0nfyHicRu3sXT5CuDEYIsrU9Il8BHrynH7mSfsDivAC3Om0xOPylFXQCSwPnZFY3RWjfz439BYH0A1AiSwvqLKYfbOnhHb61JyHjYoElgfyoFLX1rDtC7vPGw4Z/O25a9ThcytFRQ56BqF+sKuyys+dLH1ycdW8qmHXiCnNVHbYfPsJnqrI8WfQBwQMlprFOrraTfk2KquN8fS1S9x8wO3UZXpJ2uGsBzQmOTIrI/rew7Nqo/NdDD/T6EXaYwkpH5kkr1U4c7SGKs0oXdH9E/XBv03TQYS2FGom9K6sTdFZV8v6279XMH1kF0MbMI/hKp/VehBV9JmCdE76H6W7WLVWfqOvgJPI/aC9GFHk7SpTuV45P+1jrp4t4ELGFcMDSuANWRyGAPbcrGuO0CVlhUJ7ChUOscpmzZhW8VOuRa6veBj6va/KiGBHYXSCjtn828X/zO9Rg1J6slQuXteYwCNQmOvHdmpsodEVnu/3XTgq578JLCjiOPyx7mH8JaXNpB2Z9DPVHqZzU51KMsOPRGbMEnCbkT/er6L+S4XI60BB0M76FU5YhkHC5tIf5aq94f1z9cF/TdNBnJaaxTh/v7ERa+tq7p4zWtDP+B1lJw9kyyr6yv07V0AUf3Du4G7R3su2cnjR1rYUXT+cFb16Ss32mHXGXFbQzpFPB9WMbEksD66IiE6YxVDtmmgPVYTTEFCAuvHjYT4+psvZV1lM5vNJraEGrj9lDN5auGhQZdWtqR75aMnHmHmll46c3W791TzphTPnF4VbGFlTFpYH2FHc9IrG4Zsm9qZYEZbdyD1CAmsr8r+FBF75EHXnLZO+T47IBJYH3ZFDIyh2dRAU39/MAUJCayffiPE8pPm0z61FsdQJKqiPHvKAtobqoMurWzJQZeP2kyWrVPqaZ8y9JKYcEgu6QqKtLA+tsXD2AWGX9qGrDUbFAmsjyoFViI15IKYbqWIujJ5YVAksD4aOnZ9rj6boW57J9n+NOauBIu27yTS3ydnCQIigfXRsum9txp9CW2EDQ7p76PBydJnQUXaiQZdW7mSS2TG4Oqj7uttxKnsNS39tRcvkQ5sgCSwoqRIl0CUFAmsKCkSWFFSJLCipMhXs0Us+ZdVn9s4vfk7vbEoYdvm6A3bco//dKFMwR0QOUtQROOXe/SlL6yiNpXBNgweXTiLaG+i4+lfHN4cdG3lSFpYH8d9aFXi3ESSO08+glQkBFpzzKZ2djXWNQZdW7mSPqyPvngsev8x872wAijFS7OnEM05MlwrIBJYH9p2SQ5fMUYpNtXLeNigSGB9hNEoDWiNgfamzFJgjjo9nDjQJLA+onZKzetMoAyFaxigFChFT0UE1WpfFnR95UgC6yOWyZEyDLQa2qK6pgFa/yKgssqaBNZHd20dWwqsemi4LoCcDwyABNZHPJHUhE3QGhzX+y+gcq7XPRATTgLr45+ee8Eb+9qVhu6M95NzcMIyJDYoElgfU5NJRX9uz4e/qyGRHWhppYkNgATWx8a62pEbNWBLHzYoElgfMWfYioeG16gq6b8GpuzHEtxw2s+OuuOIU2/uiUQf23brnJtUqx07ZtuGN7X+7pdHLXaqWdjbzaqGeqgMe4HV2jvN5TiGaqVZt1g7VKu9EJgLPK5brH4A1WpbwBlAQrdYzwX4J04qZT1a6+1L//aHexce/+aBI37DdWyzykzfd/t/VC5u62R7pJnFyVXMueZ7dMcHTWw8eJ8p9RqwOP9bD7AU2AT8GTgkv/1h4BLdYiUP8J806ZVvl0C9c/rvFxz35sGnp1zDtOo7E5WzO1JkwhaN2V283jx9aFhh9zde+ccuHnRLDfAj4JvsCSvAucAVB+gvKStl2yXImNaxrjHy/Vrf3wdoMoTorjFZvKuNSC5LJjTmMdsLgEKL0R6/79WKAWXbwkYc+zmzwIIbOyu92bXnJrayrraRhmQfNz7w27156pXAkwW2P70vdYqhyjaw6Lvb3/76c3cP7o+arpPtravc9erMWnZGGohkLBIhizeufIn7bruFN6x8GbQmlk57/Vjvscth9/RbHcC/Al/AC+6APwL/PTF/2ORWvoEF7r73/Hf9159/OfuIHVvumJro/qj9zdpIJhabet2b3/WGtVOrPtPcl6Qql2Xxjm08P/MQKrNpfnXH9zm8fSs4joNSVbrFOhGvv3omMEu3WI/rFmsjcARwMnC4brEu0S1WOsi/dbIo67MExbRHPqqnZHtHbP/SRe/gpgvebusWK1TgYeIAKusWthhDM2SqzQEvzjikwFYxESSwPpJGnIfmHz5k24PzD+dPhx0NMpYgENIl8HHzcXe5jx87R+2KV3H+mhWsaprKPUed6M3ArZSjW6yyPS0YFAmsj4UfXZfLNNRam4YvwqG1BDYg0iXwEVIm83eliGWHThHf2J8JqCIhLYQPExvXCHHaxk621MRIh0ya+zLsqIiwU4YXBkJaWB+m7bK6voKwq5nTnWTxjgTxnMPWqijAJ4OurxxJC+sj4jiqsSvBXw9pJGeZWI7L3PYuGvvTbL+xRr65CoC0sD7CmbTujkfIWd41XLZpsLG5liO2dARcWfmSwPowXMXG5toh23KWSTiXC6YgIYH1s7oi3hfNjgxnv+HKAVdAJLA+tt6+oO6wzTuGbFu8ZQerY1XrAyqp7MlBVxEqnXtDXUfigZpMRvVFQvS4Kr3tv2fPC7quciXfdImSIl0CUVIksKKkSGBFSZHAipIiZwnG4NJLnt9Zk3Pqe0LW1vv+cNysoOspZ3KWoIirT3pQv+O5lYRcl4xl8uuTjuC7j58tVxsERLoEPt7y5uVd71z+GiFvxm0itsNlz6zgokueezng0sqWBNbHlHSu2hz2CRSxHWq0PiKgksqe9GF9GCZ0VcZ5ZfFcuqviTN3ZzeGrNpOIxYIurWxJYH3UZRLq3vOXYObn4OquqeS5Q6Yzr3MH3jwZYqJJl8DH69Nm7Q7rgArDYFXTVDnoCogE1ofh2iO2eQsjZkfeWUwICayPGb0ddFkWoZxNJJMjlLNZF48xJ7Ez6NLKlvRhfYR1lJpUP6H84t2mo5ndm6AiVWgCIzERJLA+Ilkbwxw6N3GN7aKROeCCIl0CH0a6cF811p+a4ErEAAmsjzVTm+iNhMkqRSZ/tiAZstheIx9MQZE978MOmeqBxkZ2WBYamJ7OMMd2qFbSwgZFAutjcyhEW2hPf3VrLErWsTlspy3nYQMio7V8zL98s94Zjw/ZZijoa46RU5Z3UtZSN+qrQl8OpsLyI33YUSyP/NsT1dmRq8xUZm1yVgjQ3t5z9bWq1X7nhBdYpiSwo3ht6hGnHrazF2vQJ5ChNYt29XDMhnZA7V57Fq3/OZgqy48EdhSZkMmSddtYnEgyxXZoth3mpbO84dV1zO7oyd9rd5h3BVRm2ZHAjuKCHc+/4ZjNaznrldUkHZs1FRHalSZhGmypyS/l6WjQaJT6XrDVlg8J7Cjm9t76l23NDdx+4uFsqalEK8Xm2ipuOecETlm/DrQDWmUJGYt1iyVXIEwQOa3lY3V9I23VQxdGToVDbKiu0fpLMXmzB0B2uo/ttZUUmqjQtGW3BUX2vA8nHtVHJPqHbJvdn2L2sG1i4kiXwEd3PMqF7e28Y+WTbIvGaMpmyFbNYmVDTdCllS0JrA8r1cfHn7iDBTs37N62tXoK1572nuCKKnPSJfBx6tbVQ8IKMKO3nQvXrwimICGB9dMRqy24vS3WMLGFiN0ksD7W1DazoW7mkG07KhpYUTctoIqE9GF9pKuquPOwN3D2tpeY0bOdHZVNPNO8mI1N9UGXVrakhfWRzqb5wDPPMGtrjm7mMrVN808vvExDol/GZAZEWlgfU5JJbbmuApepPd27t8/s7wuuqDInLayPV5ua6QsPvWo2Z5g8OW1GQBUJCayPs1ev+u5XL7iIjgpvPEFPNMrN557PSVs2vTHg0sqWXCJTxEVLX3ZentpsLEj0sbqygmO277D/dO8xMjFBQCSwY/CdY35V0RuO/duOiopbfvC3i2RirQBJYEVJkT6sKCkSWFFSJLCipEhgx0h9MztdfTNbG3Qd5U4OuopQN6VPx+ExTDUw/TZYpqW/EBo5y4Y44KSFLcbVj2EagAKlwDAgk8sFXVa5ksAWY5ojt4UtmQwuIBLYYgr1mKQXFRgJbDFOgXQWuPRbTAwJbDHDA6uRFjZAMh62mIgBluGdHQDvwMuWVWSCIoEtxswfX6lBx1mGHHMFRboEoqRIYMdi8JcrWstBV4CkS1CMBhzA0IMOuCSwQZHA+jBuzppeNhUMOc6SPmxQpEvg47SnXuy3nJFnBJR0CQIjgfUx1TJDlzy32vtFsbthjWdyqBtT3UHVVc4ksKO4q+nW1McffsJYPb0OTLw9ZQAm5CImoOP+zyAOBAlsAffO/L5daYWi8/pXs2J2057W1fCWOsqGLQhZIfXN7PVB11puJLCFRMLmvP5NmNEUKMP70kANO9BSgONeG0h9ZUwCO8x3T33QjOZy9FbEmd3TScFTWGp3h1ZOF0wwCewwn3vyfKcrFmeXbiBjmBiFrsgY2GYYGya0OCGBLWRaovvIdEjzo+PfgauNYd905X/sHCi1MKgay5Vc0+XjHZc86iw79WQD0/DOFGi8LxAcF3Az+ppYNNgKy4+0sD7m9nZoXA3WwPVcyguuoyFknR10feVIAutjdX39mYSG7SKlIGSgrwo9HUxV5U26BEWob2a1d9XsILaL/mJYzhAEQFrYYoaffwXZawGSXV9MoXa0UIjFhJDhhWPh6j0XI5oS1iBJYItxNNh66O8hCW1QpEtQTKF5CQptExNCAluMZPOgIoEtplCfVS7zDowEtpiBgdvgnTEwlQQ2QHLQNRYhY0/XwBsHG2Q1ZU1a2GJsN4vGa1UV3sgtW68IuqxyJYEtQl8TjWC7P8V2NI6rsfXH9bXRI4Ouq1zJWAJRUqSFFSVFAitKigRWlBQJrCgpch62iKWXrZwRz2Q3h1Aqh9am6578P/ce+2zQdZUrCWwRdenslpVTG9kRi9CQzqijt3U8g8xHEBg5reXjXW99vv2ZBXObN1btmUZran+a81auy/3q98eHAyytbEkf1k8k0jA4rABtFVHSsViB1ebERJAugQ9luyitOWFTGws6uthaU8mTh8xAS4cgMBJYH5ad473PruCMdVt3b1uyqY1dNRUBVlXepEvgww6HOG391iHbFnZ0EXEc2W8BkRbWR3VfGkNDLmRhh0OYtkMok2X+9h1Bl1a2JLA+FIbKxCL011bt3lad6OHC514PsKryJh9tPiLJflLVQ/urvVU1WFUJVqivTQuorLImgfXRVl1puMbIXeRGs9ikTg+gpLIngfWRikUx3cyQbaZjM7drA33UdgdTVXmTwPqoy2b546JpTEm0A1CT6uHtK+6j26omQt8RAZdXliSwPtbWV/Fo40x+NP8QDt2ynAtXPEyoD5pTnVSS+W7Q9ZUjOUswisp/78iYFZVUZLP8+v5fY6soDX0uEZ0mTRMV2PJ9VwAksKNQpmH21sT4p7Vr+colHyVnhajp7+e9jzzOER1ryMqHUyBkr4+iPpUzaxwHI1pLzgoB0FNRwZ1nnEqCGtqi01iu/vOrgRZZhiSwBagvJqoylWGakpkRt3VWV5GIxUlZcaJk/z2A8sqaBLawvmjOwcrZI26oTKWIZtLkXJMs5l8DqK2sSWAL0DdX6V6NkzU12ypCe7YDJ656jXC4m6pkih5q3xFcleVJAjuKbDiku+uqsLTCVuDkfx48+nDi9FNPJ+fqD8vlGhNMAjsKOxyqjzua+nQOlELnF0h2zRiv1c9DoSSsAZDAjiJ9fUXCtJ2Ct71UNweHrh9PcEkCCayvLkPRGx56+ZaZzbClthJFxf0BlVXWJLA+pvf08WRjNX0G4Lpk0CxvrGVubz8ZzETQ9ZUj+abLx2kbt7m6o8eY1Z/ave2YNthaW0UY87UASytbElgfldkctW5yyEJyloaGVD9H6mvbAiytbEmXwEdvOFJw1UNbzg8ERgLrI+ZqnTGHHnQ5SkEmNMojxIEmXQIfiViUdCxKbSpNJGeTM0164lF6UbIqR0AksD5md+xi++zpdFUMmq5Ia2p7+4IrqsxJl8BHVzxOc2c3yvUaVKU1Dd0JUqZMrRUUaWF9bKypyL3z+VXmtG076GqopaYnQUNPkjuXHC6HXQGRFtbHhtlT49+78ERSFZXM2taJbYW59fwlJKoraoOurVzJ/LBFvPmylV9f31jzpV2xCLXpLPM7uu78w68XvyfousqVBFaUFOkSiJIigRUlRQIrSooEVpQUOQ87BqrVVsAKoA44VLdYqSIPEQeItLBFqFb7IrR2gcXAVLROqlb7uaDrKlcS2GJc9/4hQwyVAq2PD66g8iaBLUYVGBArAiOBFSVFAitKigRWlBQJrCgpEthiZGzQQUUC62P65as2RZzCl2+pVvsDE1yOQALra6oZmpkxFLga9OAfAA4LuLyyJF/N+mirrlYoBcagU7Fag/fryNmOxQEnLayP/qg5ciKNgd8z9oUTX5GQwPqI2rnCN2jAyR07kbUIjwR2FOqmrHFU+xaO3d459AatwdEQsmT6lwDINV3DqaW1QEeXYVlfeuuV1GfDrKmv4h9Tagk7LmY2yXMzp4FpgFIdusVqDrrkciIHXSO1AVaNa1OV83bP/F0J5u/ypoPVTg/PzZg+0JdtUq323brFemdg1ZYZCexgaulcIAKglaInEmZLZQXLp9TjKsWxHV3M6nHAHHIgJivJTCAJ7FC7T1X1ROM8NLORNXV1u2/86+ypHLZTDz3NJSaUHHQNppdtARIAtal+1tdUj7jL2voRXdbvH/jCxAAJ7EjNQMIGpvb1jLjRHpgIzjtYfVa3WJ+ewNrKnpwlGIX6eiY6JdGfao/Fh94QNsBSYDu2vjoqp7YmmLSwo9DXRNJzurohrLyDLFNBxICQkb+uS76aDYIcdPmoSfaBZUBo2EGW1hC2ZGHkAEgL66PfCvuNh105gaWIPAmsj401NdonsCOPyMQBJ4H1oRO9T/jsob9MYCkiT84SFKFuyekRQwy1Rn9+eMdWTARpYUVJkcCKkiKBFSVFAitKigRWlBQJbDF+Z2LFhJPAFqOYM3ROAg3wy6DLKldyHnaM1C25W4FDUerNusWSnRYQCawoKdIlECVFAitKigRWlBQJrCgpcsVBEZ878cGXa8Oho+K2g7Jd+k2lv/LMefJGD4js+CKaQ9ZRyYZqOqY1sGNmI0ZFTH39qP9zgq6rXElgfXz4nEfX9DZU4xoGOcPAVYpETQWRSET2W0CkS+AjbFiz+kMhKrsSzNmxC9s0aZ9STyYeCbq0siWB9aEcR9fs7GbRhu27tzV2J1g/UyYsDIp8tPlwTaVmtw2dH1YBVf2ymHdQpIX1EcpktVaKbMgkHQtjuJqK/jTmKCvLiANPAuujLxqhuw7mtG3jlI3/oCqXYktVEzsS9XSrL6ha/U0ZiDHBJLA+zGyO+u4eLtq8nKjrrXfQmOllU1WCldRnTwGZW2uCSR/Wh2EY+pCubbvDOmBWYgeWTBIbCAmsD0M7KhEp3IhW0DfB1QiQwPqyzZDqUVVk1NDQZpQihHzZFQQJrI8c2q3td1gfms22SCN9ZpQ+orQxjSQh4yn1H3XFn0WMJznoGmbm53faqXjEMLI59/DmapWoDPPn+ccSMzMctnM1Z617knp7E+3MVBnSuxhYyFNMiLK8RGb6lTuOnt3T92LMtlVNMsOOWNRpRBl/XDRTua7C1Jq53Qnmbe/i9LZO7PCeLsGczk18+Nk7SRNmI4vYQXbb2fprM55UP14IfBlIAdedqj/WNvx1n1I//DaoCzXcf6r++Bcm7i+ePMoysMd8crO+9IXVzNnpzZiZMwx+dMEJrJjeyOE7e5nal2RLdQW1yTQXrm8f8fh//fu9NPe38RzHE6WHJA2rLfT8PQvRagc4+VT9secGHvOU+lFHDqtx4HcLu+1U/fFpB/hPnXTKrktgfHHnxrOS6d1hBQi5LkufeY3Fh83m8I49239z5CEAVKb7OWHLq8RyaVZMnY/GxCVKE110UoWBu2Do4YAyQf8YWALwtPrheweHFcDGnPqE+vFbTtMf+/0B/HMnnbILLK6e0ZhIjthcl84OCSvAlpo4GZXjyifvoirjPebkjS+TIY6LSRUJuqljlC9qZwz8j4azR3Z1FcB5gAR2L5TdWQJdHbtx5bTGEds3TKkfse3kTTupTG/dHVbwYhYig0KTIYYiyyi78aFBj7lJDZtARqFxUd/c17+jXJVfYK+t/KqZy+llJy4iFfI+YF6fWs89xy8kaZlD7ntcexdLtmwd8RwKTZIIO6lF0Y+Lvhz0Zm9BBA3oV4APDtz/ZH3FegvnJ0a+LTZwMXBuPUN/dPuIJxe+yvKgC2Dxv6zuioZDNZbtaMM0px+5tfONpmncPj2dNcCL3fMNNXz2yUc4Z8NT5IjhYhAmQ44Im5hJhATdVC44Tl+9BuBJ9ePpQOZU/bHOQq/5jPqBYWOc6GI8e4b+qAz52gdlG9jR/Mu7V6oVDZXve+bQqb/68O8eTZ2USoff+dyj7Pkw0tgY7MLCxHUX6htMv+cT40sC6+PjZz6SesvGddGTN68esj2rTLbpqHOCvqb8DloDVnZ92L3RFzKMykx6xHZLO1hkA6hISGB9KK311pqRZw+6Q5VEZOXOQEhgfWRDIZ2wYjw37RDSpoUG2ipq6c9VkKYi6PLKkvTBfFSl0oarFOvqp7KubgqG1riGweHrt2Mlk5uCrq8cSQvroyaTHfIFlZsfKrC9rprT9NWHBlRWWZMW1odytdZaU9OdJpx10AqSFWHaG2uCLq1sSQvro68iquJJh0jWQQGGhsq+LGktQ2CDIoH10RWPE+vPjNju2nLuOigSWB/VGdvNRUb2muKyElJgJLA+Yrncx9qn1Q2JZzoaorNOTmkFRb6aLeILFz3t1qVzqqqnH9sySVRFYUv769eufseioGsrRxLYMfi3Nz7rRNFGzjB00+atr33xlbceEXRN5UoCK0qK9GFFSZHAipIigRUlRQIrSoqMJShik7pmtkJvcAkpk4x2Sf5gtv7PTwVdV7mSswRFrFXX636q8IZtaaKkaGZrU63+/s6gaytH0sL6eEVdk3NpJsPgZY4U3dS014JcfBgA6cP6iBIyMoSHbEsTxSUsw7UCIoH1YeBQaDbN4bO4iIkjgfWRIwQjwqlxZbcFRva8DwOTGjqJ400SF6eXqWzElsVjAiMHXT5MUhzCSgxcNHs6B1s4JMiyypq0sD4q6GVgAjc1qC9bz46gSip70sL6yBJBY6AI4723NZAjS4x4wLWVK2lhfdiE0UTZs5sUmhBJZPGYoEhgfWj0iB2kUChZoyswElgfGcyCp7D6pEMQGOnDFrBZfWpbDGNqHXHVRz1V7Nx9yJWikiqZuTAwEthh1qhPbJxNcloftSSoox9YZ87BNW3SRpQ6O8WuCktd9u4VbnMyxeytO90ju/oPtXJuz3u3vHPIqh4/Wfi/NUD88lVv3z01/E8PvUf11FcfkaqMbbj6b2fIgrV7SUZrDdOnLnc1YbWNQ4d8KZsyLb70pnNZObeZw9p38ZZnV1OTzLB+ei25WIjmzh7iafsZy9anJONWCHgYOC3/8HbgDDscOs3K2j+1bMewLZPe+urlVz513okT/keWMAnsIK+o/1QLeMXdyXSSVI+4/c1Xvo81UxsAqExm+M7tf6E+kWLjrFpyYZO63iTRlP3DTMS0gU8PebDWGwzNbMPVuzvFrqHYMbP5+qv/dsZXDuxfNnnIQdcgGi51sXZ/WTCc6e7Z3heP8OAxh2Bqza54FNfwdqVjqouBS4c/VmnmDg4rgOFqQpnse8fzb5jsJLBDPQOKKCn0oEEvGthWXcm07j6MQaFNhb1DANdUZCzv/w1XbwY2DH9irUiNHEYDjmWuGee/YVKTwA5ytP5Mm4OBAcRJobBxAQeT5t4U3//pH/nhbX9AuRrDdTlrxSaeWDSTmnSWhp4+DMe1TUdfAXwKyA15cqWuy0bDTwze1F9d4djh0Ecm7A+cBKQPO8wG9flcA/1WgilooJdK9LCLC65+1zlsrqllaiJFTTZLvKvXPmtd+89NR1/9vs3v3AHwk4X/2wR8DagHvn35qrc/C3DrMfdfoQ318Vw4tDpVGfvINQ+e1oMYMwlsAZvU1a6FqTQWPdSOuD0VyfHpd1z8/id+deQvJ7668iaBHcVq9e8/sKj8eB/1anD7qoF6tupp+hbpTgVAdvooFujvfKKfsDZwMfNLHClcwM2PjhVBkMD6MIFKUmhUPqyaOOkRfVoxcSSwPkI4pIgyMHxbocgQQUlgAyOB9aHIMfKqWYUrwwsDI4H1ESJVYOyrppLeQOoRElhfFoqprMn3XwE0jWwkJC1sYGR4oQ+bHAYGU2jDxM4P5jZw5H0eGNnzPlLUoIkRJouJSwgbC02/LIwcGAmsjygO1rBl5hUaS84SBEYC68Okr+BXBKZcIhMYCayPDPUX5IbtIgeDHrpkwEpAZCxBERvVp7J1EBp4b6dJu836v6VPEBAJrCgp0iUQJUUCK0qKBFaUFAmsKCny1ewY/GX6d2bV9netTSqj5+zurzUFXU85k7MERdw16zu5XFXYOnbHJmzD4NX6Gczs7jz1zLavPhV0beVIWlgfa6quC+vpdVZ/KMYn3/h+qrJpPvfUA3THKp5AulOBkMD6eKS2qbuzupkvXPyW3dseWLCYO+/6oazTFRBpJXxUJ5PW7SecCsCs7i6qMmlypsl9C5cEXFn5khbWR0Uuy4zebm773a85tm0bKcviP049my1VtUGXVrbkoMvHn2pvyM5xndDsRNeQ7c83zuDMjqukWxAAaWF9NPWn1Cy7f8T2hbtkIe+gSB/Whx2OFNyuZSKNwEhgfeTsnNYMXW1WAzE3E1BFQroEPhqyNi4mBjrfqipAY8kVB4GRFnYUU65oy/3iiLNCoMiYFn+fcyjPTp8JGKSscNDllS1pYQuYfvmWTG9VzNo0o5nvV57BWRvWcObGDfSHQvzimOOZ1d0xck54MSGkhS0gGwmFpzua5xbM4ej2bRzT7q1aVJHL8f6Xnmdl07SAKyxfEtgCDNMk7HiHWmdsXDfi9in9/WTV0i2opWsnurZyJ12CQWo/tePqtGV+o2nQVwJr6xtY2Dn0vGtvJEwIZmgAtXTIOS7voUb+x3kF9DHoZUPPg6n3mMDHgTcC64Bvo3+zaURB6l1R4LPAmcAr3v3uKuuTwPJNV97iT3VUrrOMRNYwOKO9k511NQCctGkt//GHu7Hy+2lzdRUh2lnQu6PIM1p4oc0tR98zdPE49Z7/wlu4Y8A2YDH6N0NnmVPvuhd466At/wCORd9VtpN7SZcgrwv9s5xSHLa5nXc/sWL39kW7tlCtdxIhQZQeFvWuY37vrjE8o4PX3ppLUEv3tNnqPTHg8mF3ng7805At6l1zGBpWgCOB88b6N01G0iXI01CvgLm7EnTUVu3efub6VzBwMUgPurcJw6YwKsI7gesxKLzfh58rC43yXGV9Tk1a2Lx61D9FtOaBo+cRT3nfZPUqaI9Vjrivrcay20y8jLrr0cv2rEanf9MP3Dnszj3APUO26LvWAH8fdr9NwF/G8OKTlgQ277Vbm3aZtvN6XGvuPGUx/VGLPkPxzRMvIGntaewSoQh/nn0k4MVx+I/HxGtU7W5wFxV4ucuBm4EXgWXA2ejfdBS439uAW4GXgF8C56LvKuuv2eSgq4ApV+7SO6ZWYXUmsTMO87t28P6Vy3GU4umG+dSZSW7680+dsOsyPZusQS8bOaRLHBAS2AJmfLpD72qu5ITXt/BMQx25/MLHluNy/X1PU1Wzg0+9+GEZDxsAOegqIK21U9/RZy7c2s1n7l3OA4fPJhMyOe/1LcxIJOiMyGitoEhgC+i8tdlq+vQOJ2EaxpT+Pt737Kr8LZp5bODvekag9ZUz6RL4WB66KjvFDoei9FFDF6BwiNIeqWVu+mbpEgRAWlgfTXYKoyJNU/+eb0Ndctihkae6xMSQ01o+dlgVRJ3UkG0Gmr6ovM+DIoH1sTNaqTrjI1vT1xumBFCNAAmsr5hyufWEc9EoNAYaRUesgtfrpwZdWtmSwPqoSGb1Fc/9He+bKwMw6Q9V8PHljwZcWfmSwProiEc4bFfbkG1zendR5ch52KBIYH1UOrZWI1bzhu5ILIBqBEhgfcXDoUP6rKGTaWhgdXVDMAUJCayfJV3Xbnu5Yrpui1fhKEVPOMrzzTP50yGLvxh0beVKvukagwcabvzt2hkz3r4rHNarZk6P/Pzec8v2EpWgSWBFSZEugSgpElhRUiSwoqRIYEVJkWFHRZz9zn9k0jXxcCZkUZtIUp1In/P73x37SNB1lStpYX0c996VHZnG6nCjCw0uNCiTrvqKvwVdVzmTwPpodJ3GVVPq+L9Fs3ho3jTuPWI2UcPkjZc8f0vQtZUrCayPnppKuuLR3b+7hsFzM5uw3NwVAZZV1iSwPnrjIxfl6IlFMHO27LeAyI73Ud+bHLFtSm8/PWEzgGoESGD92fDuh1cQynlDBxZs3slbH1nJ6uaGvZoJTowfCewo1DdzsflbO1k5p5FcxAJD0d5YzWFbdrFwR7dc4h0QCexobDe5ZkYdL83fc/1Wb0WE7799CXN3dMn564BIYAtQN+dmRR2Xl+dNH3Hb2un1ZMKGW+BhYgJIYAvpT17Z1JdBFRh5qRX85eTDoyNvERNBAluIZWSV1qTNwrunr6ZK+rABkcAWYpqfa6uJYVsmuNr7GRjorhRVGblqNigS2EIMpbIhb9c09fZxwvotRNO53aE9oW2dyql3uym19OdBllmO5BKZAtQ3UjaGaX7qoaf4zENPEHZceqIRrnnrhRzduZ4rH32AqmwKRQoDB5cwkNOgcQhjoDDIZAzUw2D/XMHf0cu2Bv13TQYS2ALUTRn3sLYO9cB/Dm1AOyrjHN23YshMBSY9KGxy1GGSxiAFKHJUYZLLf4SlbAUfRS/76cT9FZOTdAkKUUods7V9xOamviTOsCHELhFvNS6S2FTs3m6RQmPgLc4RtoDvopbGD2jdZUACW4iCFTNGzlDYE41gDlufS+XXjlG4gJn/TaPIoQkBLvndXAPMOZBllwMJ7ChWzJjCj84+CTf/+Z8KWfzfUQuG3WvPgnNeS+vkd6jCJYwiR37NWYDtwOqJqX7ykj5sAeobKRdlKAOY0ZNgXscu2qoq+fZv/sBDx8zkTate4cwN/8AkjcLFIYZDHIsEBhk0BjaVmGTzAU7vUPBe9LK/BvuXlT75Trwwl7RjvuepF+mcWkUiGuHzDz7ESe0bWfRwG3WswSSZv6OhNRroch2UyhFOG+ikReJ1hfl9hb0CWIleVtYLwo0XaWELUN9I2WRdM2oq0uE9qyB+894/8i9PPUvrOadx499+qjXW9JD+TZvPU4lxJn3YQrI5m2hoSFgBfnTGKXRUVJAMR7Slf2tIWCeeBLaAiBG6CTVyuEBbdRVbayp5cZpMGR8U6cMWkAsZq72ZYIeGNhUO886PfBCzt0+GFwZEWtgCXNO4a7TbuiviTGnflZ7IesQeEtgC9FUhG1dr0y4wDazWNCZT0sIGRAI7Cn11xKjqS+0ZVjhIKuPIhMYBkcD6qM1kQSlCtkNFKoNyXVCKZG2VXOcdEDno8lGVyvCBx17gyj8/TkU2x5baaj572Zuo7+0PurSyJS2sj/nbdnLdH/5GRTYHwMzuXr5z1/3Ee/tlvwVEdryPhszIkwFzOnvI1VfJfguI7HgfG+trRxxx7YpHSVVEvxxEPUIC62t1bcVD/3vc4t2/5wyD/7jwNB799VHfDrCssiaDX4pY9MmNL87oTx3T1JukvapSr22uj266pVFGXgVEAitKinQJREmRwIqSIoEVJUUCK0qKfDU7BqrVPgJ4Jf+rC4R1iyUjtgIgLWwRqtW+AvgH3mhub84McFSrLMwRBNnpxf1glO2pCa1CABLY/REOuoByJIEVJUUCK0qKBNaHarXfWvAG1+XclS9x8gdW5Ca4pLInYwl8qFa7B6gecYPWoCGeTXP86g3u3//nKLlkZoJIC+uvsuBW24WUTdKKoOPKuPHCp+R89gSRHb23XA29WXA0JHO0xSp5dprRhDedpjjApIX1N7S/ZLvQlfbCmr81nVVc8Oqj234179b3BlBf2ZHA+hs6V5FlQNT7UFqc6OUtO3ZwXFKzo3Yh/zh02i/vOvS/LgmiyHIigfU3cka4uMX0VD9zbciFojimxXOHLESbtdQnu+6d+BLLy6Trw6pWexMwa9CmnG6x9vVbqZEzwilFk8riDSnYY33TDB484ijzgn18ITE2k6aFVa32W1Sr7TA0rAAh1Wrv6+RtBfdPLjzyLFYiEuaB+YuY8dltH1JfTRU8zVV/ba+hbkyfqW5My+Ic+6jkz8OqVvtTwH+N4a53AsfhBfr3wCd1i9VZ4PkuB77DaKe0gKM2b6UhEyLmeCMMXeDFpiraKyIQMjFtB8cyQBkMHLc1t3WTqIqRioYh6zC1vYe25iptKfW13HXxr/n8facD3wWOBv4OfAJXbwZagfcDvcC39FWh/xr2uP8HfBCwSNnbjb5sg4Zwje0wJZtjzdRqr8a8iqzNnO4+XmuqRSsA9QyKS/RVoQ7fvTrBJkNg9/UPuFe3WG8f9lyzgY3FHjhjVx874lFmJNKEXJe2ighohw889yjfP/uN+Scb9iDXHbox5zBlezc7m6pxI1aj+8VwoTdPNbAJb8mkAStx9R+AlmF3v1RfFfpD/nFXMDDKzMmf2Rj02qYBTk2hBck1QyZy1jyuvxA6Y/Q9MfFKukugWu2T9uPhb1Gt9vA+/MfH8sAFnZtZtGMbG2rjrK6vJBEJceXf/8r7XnzUp9hhCQ6ZpCwTJ2phZpwbRnnUBQwNK8Ai4D0F7vuOQf//r7v/L+Mw/N3jfTAUeJ+PnHX8dPWtXGyU2gJR6gddm/bjsTvJL6A1yIaxPPDsdav53CN/4Y5jT2JTbT0Xv/4PTtu0jj8sPGTsr641saxDr6vRpnp1lHsVWkPBAdqBmcO2D166ccfu/zNGnugocO5jNP3AQTUHQ0m3sLrFaqNgU1H47sN+/5pusYZv+xlekH09N3M2Fbkslz/7GDf85fectmkd4PDTk87Jv5IeOq+s1oSyQ1dQNPpzpCvDWGk7a18bvbVgwS3WE8Cfh23+EXAdDFmScQfw/UG/f46BN2PEHNFwVuth3ZO80MgJnG/UV4UOqrlwS74PC6Ba7SwQKnK3Q4BL8Q667tMt1t9Hea4KvIOu44ElBe/jutz98x/x1hUvAZAIR3hy5mze/v5PY4cMQo6tM2ZoDWFrAYCRy7lViYxRmclimybxZIauiKV7qyuf01HrLPeL4VGvXlCtdhi4DDgKeAz4nW6xtPpW7mjg3UAC+Jm+KtQ27HELgW8DTTjuT62u9Mlhx720IeuEpuayG19orm6yI9ZUFERt11nU3t0bzeb6XpzRSDoSWg+06i94feKDyaQI7ADVat8LFBoS+HPdYn1wH57PYZRPoYueWcOJm9eDZfNf55+FcmDKzl2s/OEhY//AFXut1PuwQ+gW620AqtXuxTst5QBTdIu1ax+fctTwve/Bf9CQSHH/kkPpiXmLdB+xYcPkefcfpCZVYAfoFmvkGNZx1l5XQUMixaoZDQDM2dlNQzp754F+3XI3KQM7EbY3VNJdEeGvxx3CjK4EJ65aoe/+3TmXBV3XZCeB3Ue/OO9IdtRWApqF6zdefffvzrk56JrKwaQ66BpvfgdduN5+MxwH1yaqvxzNTGBpZaukz8NOgMIHXQNvcq1ZsLYNCevEkcD6G/X8aENPH+96+CV0LrNiIgsqd9Il8KFa7VOAJwve6Lhg67i+JiJTFk0gCWwRfqPBdIslXxJMMOkSiJIigRUlRQJb3GjTET0/oVUIQPqwY6Ja7eHj8bRuseTNHgDZ6WOQD2cc74qEiIQ1ONLCipIiLYUoKRJYUVIksKKkSGDHSLXaj6hW+21B11Hu5KCriAIXOMoprQDJji9u+NW4SrXadsF7igOuLK44UK32ImAF3sn/Tt1iNY3xcYUmsgB5owdm0u941WpvAF7D+1sV0Khaba1a7SljeHhZvKFLyaQPLDDa1JZjWZOgHPZPSSnnf5D9Gcsq42ADUs6BHQs5hXKQkcCKklLWgVWt9vqgaxB7p9yPgucWuX20LsH+dRXU0sOBf7CnL6yBKHrZQTUX68GorFvYMRjt4GrfD7rU0hh7zgkPfr4MaqnO//xsn59/kpPA7pv9aWGXj+E+H0QtnbsfrzFpSWD9HYjTV4vGeL81B+C1S96kDqxqtfc3cO64FLJvJvW/zb6a7AddtxW7Q/4Cw08BDwO3AOfihWUL0DDaw/ajJhsYy8qMCrV0b7oeOWArcCsQA6bjrX0wDa+1/gl6WffelRoQtXQ68DHgNCAN/Am4Hb0sNamHFxa42tWPw/D1OP3V6harZ++LWjqW9RgOhNeA49HL9nVVyImhlk4DXgCGj/V4FL3sbPnY2WNvwgrwiX18naA+1RYDbwvotffGRxgZVoCzUEvPkMDuu2n7+LggxyHUB/jaY1Xnd5sEdt/9YB8fF1QfLAXcG9Br7407KbyPOoAHJ3tgx/rNUQ9wJV4Hf4BvsHSLtXIfazqQVytoYDXwEt7KiKvwDryeAt6EXrbtAL72+NDLnsFbf+w1vH+PFPA34CL0suRkP+gKUTy0Ed1iFbyParU7Kfwxuu/XdamlYz0Q1Hj93UXAMcCT6GUbBj1PFKhCLzuoVts+0Cb1aS3dYuVUq3+DNlpY8w5Ef1OP8Xk1epkLvJr/GXbrsjRDPxHKwmTvEuyv8R9LAGPtSrxvP15j0pLA+jsQ/aUjx3AfG73s1wfgtUueBHai6WUab0Xu4bYCm4Fq9LIgvlgoCZO6DzsGxcYKHJgjUr3sCuAK1NLZQA162SsH5HUmobIOrG6x9vbbrd0PHZ8Clm0al+cpI9Il8CdXxx5kJLD+ghxeKAqQwPo7EKe1xH4oh8A6o2y/e0KrEONi0gdWt1gW0Dds80O6xXpXEPWI/VMWZwl0i1W1jw+dQeEFkifvAIyD3KRvYfeHbrHSFA7nyRNdi/BM6tFa40W12j1AFV546/fp0hgxLiSwoqRIl0CUFAmsKCkSWFFSJLCipJTFediDnfpmLnbcq+u7Xpg/KwIKtAZX/1p/Nf7PQdd2sJEW9iBw9Oub+l6YPzuCi3fiTAOGuizy1f7LAi7toCOntQJWcX3ykCRqXcHxNAqtvxyVRmWQQLsEqtU+E3gk/6sG3qlbrGUBljThTly5+Z2PLMqvzGQo78frEoDryqiwYQJ796pWuwV4FK9pUfla7lGtdlnNixq1nbMwADMfVgCl9oRXDBHkx80to2yfN6FVBGz1lNonajM5L6SuBsf1WlilvBCLIeQsQcCmJrOH7qw3IJmDdH7SD0NBZVj+dQqQDn3AXNSanKP3hBW8ljaZRcmFDSMEEljVau8K4nUPRk2dPStjqQKzJdmaimxuyKYbL3zK+P2C1jdccckfjp2Y6g4+QX3o+M0Bimq15+oWa8ME1RIo19XnpQs1pIbCGXTKsfX0OzPfu/CN4Z1vOAGA27+epr4vQWOy57OvfG/+f05QuYE7WLsEZbNCYaK6ImPHwzB8MsSKEKmwtxTCjWf//pfXX/zm8M7KCu9gTCmyYYu2ulo210/9nmq193V+hZJzsAYW1Wofk/9vWrXaOv/jqlb7uKBrG0+HJ/o7s2HLO8iqjUBVGBpiELXAVKibs7/KGuH3JqLRoQ9UgFL0h0OcvGZdVyDFB2DCA6ta7bGetjo3v6hGZPDDgedVq33U+Fc2OtVqdwx609iq1T56vJ57a0V8GyjvXyJkekEdfD425/7zgvZN3qmuYS58/UW+c9//sLhzc+X0Kzc/Ol41HcyCaGHH+sVANaNf///i+JRSnGq1s0DjoE0m8JJqtcflqtu63p1vjTo2MHAeNv8zEFATDuvYxoWvDJql09U07+hki1nDw9MP48pHfqe+9dDdZ8av7doxHjUdzCZ0LIFqtZcC94zx7sUm/v2mbrG+OMbXrQLOBg4DnsebCHg53vTtZwKz8NaCmg1chteqn4k3+3VslKfN6RZrLOttjeq/jv3DtD4rve2GS99EKlTg+FfBoTu7ifZl6DUMuusqSFXHsfrSZAYdLzckE6z7/r9zwiduYM20WSuB7RjqEKAWTRhvXbAMij/hzSiewVuXrBtvhvLz8K4Ofh1vHgcbuC1/EaZXSqu9GDgHaMN70z4E9AIn4b2h6/HWCrsHOBH4HLATuAmYCjyrW6z9ni5/ogP7LLBkHJ/yEt1i/bHIa74DuIORi7m15f87dV9fXLdY+3yi9Noz/nbH6S+tucyo6eeNn/xEwY98pTVVnX30WhZzUxmO6UvjKrhveuOI+/7y3h/wwzMv5PFDDt/dv91PLnCxbrH+rFrt+4E3Frg9AdSM8fm2AJfqFuvF/SlqorsE4xlWgN/m1zEoSLXacbzVEAu1hFPZj7Dmn3+f9t8Pjvnj0Ueu67gsVJukog+mdSUK3k8bil7LosZ2ODGRIqI1hvaCPFzGCvH07AX5wsblCwcDb/++iZFhHbh9rGEFmAn8cDyKKmVRYK7P7Yexdzt1b522r49r6upjZm8Xr8+YxUcefr7w6MJ8MGenMrtvjmjN3OTQpQ3m9Ozk5bknMTORG+9Zv6qA8RxEvt/zOZR6YLOA3xyrqxk5TdF4enYfH/dCT1WUzdVNPL/gUF6bNSU/Oos9Y9dMr4WNO86IDJ7Q1cdRPX1gKWKG5qh+2Fo3neb+zHjPSdPP+K7t9fz+PsFEB3asB1xj9UHdYmVGu1G3WH14S2wWmhCuG9iv85d+r+3nEy+9+ekX5k95KJmopDsW4pEj53o3DAyyNMh/rCumuw5r45EhfVwF9FVFoDJEJKSIut5tiXD+QGx8jks08AHdYv0WeGKU25N78XydeItQ75eJPug6EXhmjHcvdpbgdt1ifWSMr9uIt87qIrx3+U7gMbyj5UvxuhV3A0cD78U7pXYs3vKco319vd9nCb5zzP8d/Wpj3Ut/PP0I2qIR6lM5YrZDVzREMmIBijNWruUN617kkfmnYFkWG+sq2F4ZpSfmdd2P3d7N/M5+eiMWTzbGSNRUdQK9GKoBiKK90baAi2IFXjcqizcWuQ2vf38a3pt6Bd7BVAbvLMzuGW5Uq30WcAHeQnU2cB+wC+/MwTS81cOTwM+BM4Av5m//Mt6q6H/XLVahecr2yoRfIqNa7bG+4G14C+UWsu8Lu+0l1Wo7FP4kOkK3WCPXz9pLZ/zrupYXZzbdckh3hoaUN9hFA6vrK9heHQXX1Vc8/ozaNG0uhoa2qghrGypxlWJuV5KZuxI8P7OBHRETnbN32tfXNO1vTQezIPqwR4zxfkcy+tyuHxinWorKr4MwuGXQwPnjEVYA01FOxFW7wwrex8oh3UnvoEvB3O52DLwxBFP7spy+oZOz1u9k8fad/GXeFHoNh1gisXyyhxUCGK2lW6xXi61OmHeNbrEeLLB85rd1i/XLA1NdYbrFih+o525I9p8XyY1cHTTkakKOS9ZQjyXD+oz6voTaVZmfNVQplOtyaFcvhobG/p7c+m/PPfFA1XgwOWjHtOsW68H8fxuCruVAmrG968muhXMuGd5h7wubZEMGGMb5mRD/3fLgLz70rQs/RG80StS2mdXdw6rGGrJhg2ontyCo+ifawXpaa27QBUyUqYnuqnTI4vXGSrL5QS+JsMlrTVWgQV8Vyt30l3d9+P7DDsu89+l7OWHTKpr6Erw8pZa/zJvCzM6O21/6jwUbA/4zJkxQLWwfUDnajbrFKpt/gG019Y2YBu2REO1Ta7BcjW0Z3vhYd8/x6ecf/1D0qVnXNsSS22/ePKVqzZ8XHfkt3WJpqA2u+AAENpGG39mC/fmOvtSc/LFNVzwzo/kHI24wlTdr0ZciZbMvxuKg7cOWi5pMduRIFsh/YyWz8gx3sPZhy0Z9f+qQgjfIFFIFBRnYfx1l+4sTWUTQttRV/bHg93lajdeoq0klsMDqFus2vNE7A02JBj6rW6xJdc1WMXW9ieP2jHgZIEEdjcxeGLCFV7Yf3hGOrOiODbuwQQGG1voamb1wMNkZAVv17SmvztjZDZba07AaQAgwjRuCq+zgJC3sQUB9KxcJJ1L9hC0zG9o9RPAZfXVEFrAbRgIrSop0CURJkcCKkiKBFSVFAitKiowlOAipVruSnPMqMB3LWI9SJ+oWqzvoug4GB+QswXUXL78IV/8fCgOlNHDj9fcvuW7cX2gSUjdlP0bO+RFRa9iXX+qrusX6WmCFHSTGPbDXXfTMUyh18pDvwQcWmYBZ19+/ZMu4vuAko25IaS+sI7+eLadhl6MZ1z7sdRcvj4wIKwze+Zuvu3i59JtHoVrtTURGn5tYtdrvnMByDkrjHZ73j2GE0Svj/JqTgvpW7vOk7VlkXb9RWudMYEkHpfEO7LljuM/icX7NycF2vkV6tKvad3txAio5qI33WYJjx/n5ykfW9f5ru3732t3A5GezaWdoo/MicLx3rdfkNN4tbPM4P18ZyWdstOU6vYPj1wBUq/1ZoIOR/37H5rdPWuMd2Lbid8G3CSlbEVNjKEY96PL6tbWq1f4o8B8+z9TgN2duqRvvwI6lfzrqa66svmrm5vgn5qGWlt8XGpapqAoVuyxmCmObFDirWu1I8buVnnEL7HUXL+/CmyVvr6XVOz6g1VJ9WGLN5pmptjUu5PrUu9L/O+u2AzZF0EEnv/5WkRW8L2bs/2bjMvfXwWZcAnvdxctPYOwzOgw5IMippbebqP8ZuKppYIrUCuzIKVv/vnk86isJxb7A8W5/+14816H7WdFBab8De93Fy3vRevmYH+A4g6bpW/oZlPnhUIFurQIqydTfNn/ZpP92R7XaZxa/017sBqXAcVGt9mP7UdZBab8Ce93Fy22gaq92pmFErrt4+WkADnw3pEc/92goxUfWLJ20p2gGeWTcL+k2DdD6dNVq/2h8nzhYBQ9urrt4uYm3wkoEODy/uSn//x/Gm/5y7/fwnjEFj7FnVv9ROQfxdfm3zV8Wwpu9+kJg3UfWLH15X55HtdpHoLU6gHMQfCx/ZqF+vEZ85VfPaQYux8tDEugBvqZbrD8Pul8IOAb4av7+y/FWEjoa2ApcAfwNsHWLNaazR0przXUXL68G/gdvWvUDy3HA1Ry/8uX2C7c9NKVCF1h6feCuQI+K99fr5DT0ssJrAw1z2/xlTcBvgdPx3lTb8aZCX4B3Yv3qj6xZ+nr+vhZwLd4EyTV406kfqAM9F29i5BiD3qi7YhGu+sAbJnrSDBtv9w4+k5DGewMO1OawjwfRe61Q/33Q+BPgHN1irYM9xd3BRIR1oBDTYHb/5ikdMf8Jo02gK1JXYStzrOsigPeOPSv/cAOYAbwBOATvoOXB2+YvG/iHugH4Sv62eg5cWGHgWHLYp8oz86YHMcOLxdCwgvdmHVzbxK0QPnCGZPDPHrOA51SrrQCM6y5eXge8aUKLMwzaKqZSne0teveZ6e2Y2lmEWlpV7L63zV92KHu6MKOZgbdUJUzg1POjqUvv00I05aYWbzlQDLz1QYuOuhg3SoHW1GR7qXDGsqiIAu9UWK7IHcH7WBuLvmH/DczB3E8/yPQBGNffv6QPmLgjyXx/JWxnsXzOEICX0hRhFPwVvaxoGD+yZuk24P4id3sK76AP4Jai9R5gNYm0zFRY3OsDi6AM9Fk+m/8Z/QhovORb2FdrF/3RUaN3kzSwNj6TGlKvAxftxSu8FW8F6Ta8daLuwVsr6jfA1cAbBk6VfWTN0v8HvBn4NfA48CTe6n/7Y+DTYODHzW/rBF7I15TJbycVNsGd0OEVGtiIt3K3zv/0Ay/na7PxcrCViRr3ofXIH48D3MmgUYB7dYnMdRcvV8DX80/0RbS29vqAwTu19dfr719y4dbox/WMzI7CdwOeqj2eU7uundSfmarVPg3vzXJgePv7Z7rF+vB4P3X+9FYdXlfMBrKFhjaqVjuK9yYFb7zJccAG3WLt9d+9X9d0XXfx8gwQHnR+dSz09fcvMQBei3w23Wx3RerdvoIndfuNKBXOHZM6sACq1c5xIK5g9v5dztMt1sPj/twB2a9vuq6/f0kE130K2KePtXnZbZ/Q7ujHUnF3rMdQJW/8R1Z5H63fmExhhXEYS3D9AyedOmfN5sp9OZcY1nffXkfmXwtFXQO9RP+2v/WVgrF+y7NXlEJfFb5m3J83YOMyWusja5b2o9TWfXmsqZfdZsLROYzdRwBZTDKYT9boO8ZyjVj5cNy9CeCkXDpqPPtNs/E61nv/nHrZK6FBYxP2a4nsyUyTwTvzUOyKAg3MO/AFTbxxG8B9/f1LXGAsk2TIScd9pfUc4JSid2uxDN1iTdyXQRNovC+RGUtHdtIf9R8wlvGybrGexxv5NNoK0/8+gRVNuPEO7MQNmChHSoUBdIvVoVusEPA79nxiucC5usX6blDlTYTxPvfXDcwscp9J+VE1DgaGHvoZcg5Qt1hvO2DVHKTGu4UdyzXxk/LodRycV/wuk/NAam+Md2DfMYb7XDDOrzkp6BbrKbxB9H7unIhaDmbjP93mxcs34Q263cP7ijALnHn9/Uv2ZjB22Rl1lXOt0Z8Plf0B67hPfXn9/UtmA0ewZ0DEz1Cq8vr7l0QkrGNyZMGtSn16gus4KMk6XQep/FywS4Df78uopsnq/wOKhE7u8O1eIQAAAABJRU5ErkJggg==" id="image6070718352" transform="scale(1 -1) translate(0 -578.16)" x="388.8" y="-43.2" width="123.84" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature127_fold0 -->
    <g transform="translate(135.813531 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-37" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 524.267031 638.149 
L 531.896531 638.149 
L 531.896531 27.789 
L 524.267031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagef0b2f87f72" transform="scale(1 -1) translate(0 -609.84)" x="524.16" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(535.396531 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(535.396531 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(569.797469 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pfe18c3e8f4">
   <rect x="385.504531" y="27.789" width="130.6" height="610.36"/>
  </clipPath>
 </defs>
</svg>
