<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="778.965406pt" height="679.5765pt" viewBox="0 0 778.965406 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T11:00:24.435270</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 778.965406 679.5765 
L 778.965406 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
L 520.118406 27.789 
L 405.646406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 446.60034 638.149 
L 446.60034 27.789 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 405.646406 609.084238 
L 520.118406 609.084238 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 405.646406 580.019476 
L 520.118406 580.019476 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 405.646406 550.954714 
L 520.118406 550.954714 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 405.646406 521.889952 
L 520.118406 521.889952 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 405.646406 492.82519 
L 520.118406 492.82519 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 405.646406 463.760429 
L 520.118406 463.760429 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 405.646406 434.695667 
L 520.118406 434.695667 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 405.646406 405.630905 
L 520.118406 405.630905 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 405.646406 376.566143 
L 520.118406 376.566143 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 405.646406 347.501381 
L 520.118406 347.501381 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 405.646406 318.436619 
L 520.118406 318.436619 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 405.646406 289.371857 
L 520.118406 289.371857 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 405.646406 260.307095 
L 520.118406 260.307095 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 405.646406 231.242333 
L 520.118406 231.242333 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 405.646406 202.177571 
L 520.118406 202.177571 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 405.646406 173.11281 
L 520.118406 173.11281 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 405.646406 144.048048 
L 520.118406 144.048048 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 405.646406 114.983286 
L 520.118406 114.983286 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 405.646406 85.918524 
L 520.118406 85.918524 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 405.646406 56.853762 
L 520.118406 56.853762 
" clip-path="url(#p694b60daa6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mb05029ad97" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb05029ad97" x="446.60034" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(443.100965 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mb05029ad97" x="497.418023" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(493.918648 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(340.201 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_0 -->
      <g style="fill: #333333" transform="translate(69.906719 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-30" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- XRDPowderPattern_xrd_11 -->
      <g style="fill: #333333" transform="translate(209.274844 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-31" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-31" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- VoronoiFingerprint_mean_Voro_area_std_dev -->
      <g style="fill: #333333" transform="translate(91.706094 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-72" x="1633.492188"/>
       <use xlink:href="#DejaVuSans-65" x="1672.355469"/>
       <use xlink:href="#DejaVuSans-61" x="1733.878906"/>
       <use xlink:href="#DejaVuSans-5f" x="1795.158203"/>
       <use xlink:href="#DejaVuSans-73" x="1845.158203"/>
       <use xlink:href="#DejaVuSans-74" x="1897.257812"/>
       <use xlink:href="#DejaVuSans-64" x="1936.466797"/>
       <use xlink:href="#DejaVuSans-5f" x="1999.943359"/>
       <use xlink:href="#DejaVuSans-64" x="2049.943359"/>
       <use xlink:href="#DejaVuSans-65" x="2113.419922"/>
       <use xlink:href="#DejaVuSans-76" x="2174.943359"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(54.014219 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- StructuralHeterogeneity_min_relative_bond_length -->
      <g style="fill: #333333" transform="translate(51.359375 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-69" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-6e" x="1383.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.066406"/>
       <use xlink:href="#DejaVuSans-72" x="1497.066406"/>
       <use xlink:href="#DejaVuSans-65" x="1535.929688"/>
       <use xlink:href="#DejaVuSans-6c" x="1597.453125"/>
       <use xlink:href="#DejaVuSans-61" x="1625.236328"/>
       <use xlink:href="#DejaVuSans-74" x="1686.515625"/>
       <use xlink:href="#DejaVuSans-69" x="1725.724609"/>
       <use xlink:href="#DejaVuSans-76" x="1753.507812"/>
       <use xlink:href="#DejaVuSans-65" x="1812.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="1874.210938"/>
       <use xlink:href="#DejaVuSans-62" x="1924.210938"/>
       <use xlink:href="#DejaVuSans-6f" x="1987.6875"/>
       <use xlink:href="#DejaVuSans-6e" x="2048.869141"/>
       <use xlink:href="#DejaVuSans-64" x="2112.248047"/>
       <use xlink:href="#DejaVuSans-5f" x="2175.724609"/>
       <use xlink:href="#DejaVuSans-6c" x="2225.724609"/>
       <use xlink:href="#DejaVuSans-65" x="2253.507812"/>
       <use xlink:href="#DejaVuSans-6e" x="2315.03125"/>
       <use xlink:href="#DejaVuSans-67" x="2378.410156"/>
       <use xlink:href="#DejaVuSans-74" x="2441.886719"/>
       <use xlink:href="#DejaVuSans-68" x="2481.095703"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_minimum_NsValence -->
      <g style="fill: #333333" transform="translate(46.445781 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-73" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-56" x="2191.166016"/>
       <use xlink:href="#DejaVuSans-61" x="2251.824219"/>
       <use xlink:href="#DejaVuSans-6c" x="2313.103516"/>
       <use xlink:href="#DejaVuSans-65" x="2340.886719"/>
       <use xlink:href="#DejaVuSans-6e" x="2402.410156"/>
       <use xlink:href="#DejaVuSans-63" x="2465.789062"/>
       <use xlink:href="#DejaVuSans-65" x="2520.769531"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(69.906719 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(184.554531 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- VoronoiFingerprint_std_dev_Voro_area_mean -->
      <g style="fill: #333333" transform="translate(91.706094 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-65" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-61" x="2109.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="2170.744141"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=4_43e+00 -->
      <g style="fill: #333333" transform="translate(78.892969 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-34" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-34" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-33" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(192.825781 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=1_23e+00 -->
      <g style="fill: #333333" transform="translate(78.892969 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-31" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-32" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-33" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(97.738906 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(131.130625 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(69.906719 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(83.776094 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(262.452969 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(99.35375 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(140.9375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageafd2ae198e" transform="scale(1 -1) translate(0 -578.16)" x="408.24" y="-43.2" width="108.72" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature66_fold0 -->
    <g transform="translate(153.999406 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.272906 638.149 
L 534.902406 638.149 
L 534.902406 27.789 
L 527.272906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image3612411dcc" transform="scale(1 -1) translate(0 -609.84)" x="527.04" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(538.402406 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(538.402406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(572.803344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p694b60daa6">
   <rect x="405.646406" y="27.789" width="114.472" height="610.36"/>
  </clipPath>
 </defs>
</svg>
