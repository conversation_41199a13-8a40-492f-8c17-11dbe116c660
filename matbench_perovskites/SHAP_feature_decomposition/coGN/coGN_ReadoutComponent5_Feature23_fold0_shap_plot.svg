<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="776.4175pt" height="679.5765pt" viewBox="0 0 776.4175 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T05:34:57.302854</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 776.4175 679.5765 
L 776.4175 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 401.6225 638.149 
L 519.0465 638.149 
L 519.0465 27.789 
L 401.6225 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 450.690911 638.149 
L 450.690911 27.789 
" clip-path="url(#p9198798e11)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 401.6225 609.084238 
L 519.0465 609.084238 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 401.6225 580.019476 
L 519.0465 580.019476 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 401.6225 550.954714 
L 519.0465 550.954714 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 401.6225 521.889952 
L 519.0465 521.889952 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 401.6225 492.82519 
L 519.0465 492.82519 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 401.6225 463.760429 
L 519.0465 463.760429 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 401.6225 434.695667 
L 519.0465 434.695667 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 401.6225 405.630905 
L 519.0465 405.630905 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 401.6225 376.566143 
L 519.0465 376.566143 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 401.6225 347.501381 
L 519.0465 347.501381 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 401.6225 318.436619 
L 519.0465 318.436619 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 401.6225 289.371857 
L 519.0465 289.371857 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 401.6225 260.307095 
L 519.0465 260.307095 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 401.6225 231.242333 
L 519.0465 231.242333 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 401.6225 202.177571 
L 519.0465 202.177571 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 401.6225 173.11281 
L 519.0465 173.11281 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 401.6225 144.048048 
L 519.0465 144.048048 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 401.6225 114.983286 
L 519.0465 114.983286 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 401.6225 85.918524 
L 519.0465 85.918524 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 401.6225 56.853762 
L 519.0465 56.853762 
" clip-path="url(#p9198798e11)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m574cb7097b" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m574cb7097b" x="450.690911" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(447.191536 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m574cb7097b" x="505.502106" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(502.002731 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(337.653094 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_avg_dev_NpUnfilled -->
      <g style="fill: #333333" transform="translate(49.701875 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-55" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-6e" x="2219.291016"/>
       <use xlink:href="#DejaVuSans-66" x="2282.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2317.875"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2373.441406"/>
       <use xlink:href="#DejaVuSans-65" x="2401.224609"/>
       <use xlink:href="#DejaVuSans-64" x="2462.748047"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_trigonal_planar_CN_3 -->
      <g style="fill: #333333" transform="translate(54.684531 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-70" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6c" x="1924.878906"/>
       <use xlink:href="#DejaVuSans-61" x="1952.662109"/>
       <use xlink:href="#DejaVuSans-6e" x="2013.941406"/>
       <use xlink:href="#DejaVuSans-61" x="2077.320312"/>
       <use xlink:href="#DejaVuSans-72" x="2138.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="2179.712891"/>
       <use xlink:href="#DejaVuSans-43" x="2229.712891"/>
       <use xlink:href="#DejaVuSans-4e" x="2299.537109"/>
       <use xlink:href="#DejaVuSans-5f" x="2374.341797"/>
       <use xlink:href="#DejaVuSans-33" x="2424.341797"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(24.252344 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(93.715 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(213.453125 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(68.111094 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-69" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-6e" x="2035.347656"/>
       <use xlink:href="#DejaVuSans-69" x="2098.726562"/>
       <use xlink:href="#DejaVuSans-6d" x="2126.509766"/>
       <use xlink:href="#DejaVuSans-75" x="2223.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="2287.300781"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_pentagonal_pyramidal_CN_6 -->
      <g style="fill: #333333" transform="translate(7.2 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-70" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1489.673828"/>
       <use xlink:href="#DejaVuSans-6e" x="1551.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1614.576172"/>
       <use xlink:href="#DejaVuSans-61" x="1653.785156"/>
       <use xlink:href="#DejaVuSans-67" x="1715.064453"/>
       <use xlink:href="#DejaVuSans-6f" x="1778.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="1839.722656"/>
       <use xlink:href="#DejaVuSans-61" x="1903.101562"/>
       <use xlink:href="#DejaVuSans-6c" x="1964.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1992.164062"/>
       <use xlink:href="#DejaVuSans-70" x="2042.164062"/>
       <use xlink:href="#DejaVuSans-79" x="2105.640625"/>
       <use xlink:href="#DejaVuSans-72" x="2164.820312"/>
       <use xlink:href="#DejaVuSans-61" x="2205.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="2267.212891"/>
       <use xlink:href="#DejaVuSans-69" x="2364.625"/>
       <use xlink:href="#DejaVuSans-64" x="2392.408203"/>
       <use xlink:href="#DejaVuSans-61" x="2455.884766"/>
       <use xlink:href="#DejaVuSans-6c" x="2517.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="2544.947266"/>
       <use xlink:href="#DejaVuSans-43" x="2594.947266"/>
       <use xlink:href="#DejaVuSans-4e" x="2664.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="2739.576172"/>
       <use xlink:href="#DejaVuSans-36" x="2789.576172"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(38.775781 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(180.530625 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(10.460156 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(174.028594 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_183 -->
      <g style="fill: #333333" transform="translate(180.530625 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-33" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(49.990313 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(65.882812 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(158.879531 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(188.801875 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(79.752187 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(258.429062 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(95.329844 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(136.913594 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 401.6225 638.149 
L 519.0465 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image0ad7d42e88" transform="scale(1 -1) translate(0 -578.16)" x="404.64" y="-43.2" width="111.6" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature23_fold0 -->
    <g transform="translate(151.4515 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-32" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-33" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 526.3855 638.149 
L 534.015 638.149 
L 534.015 27.789 
L 526.3855 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image84103c3bec" transform="scale(1 -1) translate(0 -609.84)" x="526.32" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(537.515 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(537.515 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(571.915937 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p9198798e11">
   <rect x="401.6225" y="27.789" width="117.424" height="610.36"/>
  </clipPath>
 </defs>
</svg>
