<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="782.875437pt" height="679.5765pt" viewBox="0 0 782.875437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T06:34:27.001708</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 782.875437 679.5765 
L 782.875437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 459.077288 638.149 
L 459.077288 27.789 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#pcc26dd6c98)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m2e8913bee9" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2e8913bee9" x="459.077288" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(455.577913 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m2e8913bee9" x="503.958952" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(500.459577 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_avg_dev_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(14.52875 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6e" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-64" x="2218.998047"/>
       <use xlink:href="#DejaVuSans-65" x="2282.474609"/>
       <use xlink:href="#DejaVuSans-6c" x="2343.998047"/>
       <use xlink:href="#DejaVuSans-65" x="2371.78125"/>
       <use xlink:href="#DejaVuSans-65" x="2433.304688"/>
       <use xlink:href="#DejaVuSans-76" x="2494.828125"/>
       <use xlink:href="#DejaVuSans-4e" x="2554.007812"/>
       <use xlink:href="#DejaVuSans-75" x="2628.8125"/>
       <use xlink:href="#DejaVuSans-6d" x="2692.191406"/>
       <use xlink:href="#DejaVuSans-62" x="2789.603516"/>
       <use xlink:href="#DejaVuSans-65" x="2853.080078"/>
       <use xlink:href="#DejaVuSans-72" x="2914.603516"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(114.380937 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=2_89e+00 -->
      <g style="fill: #333333" transform="translate(53.238281 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-32" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-38" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-39" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2b" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2530.761719"/>
       <use xlink:href="#DejaVuSans-30" x="2594.384766"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_99 -->
      <g style="fill: #333333" transform="translate(209.467812 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CrystalNNFingerprint_std_dev_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(90.028281 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6f" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-63" x="1592.75"/>
       <use xlink:href="#DejaVuSans-74" x="1647.730469"/>
       <use xlink:href="#DejaVuSans-61" x="1686.939453"/>
       <use xlink:href="#DejaVuSans-68" x="1748.21875"/>
       <use xlink:href="#DejaVuSans-65" x="1811.597656"/>
       <use xlink:href="#DejaVuSans-64" x="1873.121094"/>
       <use xlink:href="#DejaVuSans-72" x="1936.597656"/>
       <use xlink:href="#DejaVuSans-61" x="1977.710938"/>
       <use xlink:href="#DejaVuSans-6c" x="2038.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2066.773438"/>
       <use xlink:href="#DejaVuSans-43" x="2116.773438"/>
       <use xlink:href="#DejaVuSans-4e" x="2186.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2261.402344"/>
       <use xlink:href="#DejaVuSans-36" x="2311.402344"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(31.126094 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(147.7625 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mode_Column -->
      <g style="fill: #333333" transform="translate(105.469844 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-6c" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-75" x="2032.083984"/>
       <use xlink:href="#DejaVuSans-6d" x="2095.462891"/>
       <use xlink:href="#DejaVuSans-6e" x="2192.875"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MaximumPackingEfficiency_max_packing_efficiency -->
      <g style="fill: #333333" transform="translate(60.491875 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-61" x="86.279297"/>
       <use xlink:href="#DejaVuSans-78" x="147.558594"/>
       <use xlink:href="#DejaVuSans-69" x="206.738281"/>
       <use xlink:href="#DejaVuSans-6d" x="234.521484"/>
       <use xlink:href="#DejaVuSans-75" x="331.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="395.3125"/>
       <use xlink:href="#DejaVuSans-50" x="492.724609"/>
       <use xlink:href="#DejaVuSans-61" x="548.527344"/>
       <use xlink:href="#DejaVuSans-63" x="609.806641"/>
       <use xlink:href="#DejaVuSans-6b" x="664.787109"/>
       <use xlink:href="#DejaVuSans-69" x="722.697266"/>
       <use xlink:href="#DejaVuSans-6e" x="750.480469"/>
       <use xlink:href="#DejaVuSans-67" x="813.859375"/>
       <use xlink:href="#DejaVuSans-45" x="877.335938"/>
       <use xlink:href="#DejaVuSans-66" x="940.519531"/>
       <use xlink:href="#DejaVuSans-66" x="975.724609"/>
       <use xlink:href="#DejaVuSans-69" x="1010.929688"/>
       <use xlink:href="#DejaVuSans-63" x="1038.712891"/>
       <use xlink:href="#DejaVuSans-69" x="1093.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1121.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="1183"/>
       <use xlink:href="#DejaVuSans-63" x="1246.378906"/>
       <use xlink:href="#DejaVuSans-79" x="1301.359375"/>
       <use xlink:href="#DejaVuSans-5f" x="1360.539062"/>
       <use xlink:href="#DejaVuSans-6d" x="1410.539062"/>
       <use xlink:href="#DejaVuSans-61" x="1507.951172"/>
       <use xlink:href="#DejaVuSans-78" x="1569.230469"/>
       <use xlink:href="#DejaVuSans-5f" x="1628.410156"/>
       <use xlink:href="#DejaVuSans-70" x="1678.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1741.886719"/>
       <use xlink:href="#DejaVuSans-63" x="1803.166016"/>
       <use xlink:href="#DejaVuSans-6b" x="1858.146484"/>
       <use xlink:href="#DejaVuSans-69" x="1916.056641"/>
       <use xlink:href="#DejaVuSans-6e" x="1943.839844"/>
       <use xlink:href="#DejaVuSans-67" x="2007.21875"/>
       <use xlink:href="#DejaVuSans-5f" x="2070.695312"/>
       <use xlink:href="#DejaVuSans-65" x="2120.695312"/>
       <use xlink:href="#DejaVuSans-66" x="2182.21875"/>
       <use xlink:href="#DejaVuSans-66" x="2217.423828"/>
       <use xlink:href="#DejaVuSans-69" x="2252.628906"/>
       <use xlink:href="#DejaVuSans-63" x="2280.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2335.392578"/>
       <use xlink:href="#DejaVuSans-65" x="2363.175781"/>
       <use xlink:href="#DejaVuSans-6e" x="2424.699219"/>
       <use xlink:href="#DejaVuSans-63" x="2488.078125"/>
       <use xlink:href="#DejaVuSans-79" x="2543.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CrystalNNFingerprint_mean_trigonal_planar_CN_3 -->
      <g style="fill: #333333" transform="translate(75.350469 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-70" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6c" x="1924.878906"/>
       <use xlink:href="#DejaVuSans-61" x="1952.662109"/>
       <use xlink:href="#DejaVuSans-6e" x="2013.941406"/>
       <use xlink:href="#DejaVuSans-61" x="2077.320312"/>
       <use xlink:href="#DejaVuSans-72" x="2138.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="2179.712891"/>
       <use xlink:href="#DejaVuSans-43" x="2229.712891"/>
       <use xlink:href="#DejaVuSans-4e" x="2299.537109"/>
       <use xlink:href="#DejaVuSans-5f" x="2374.341797"/>
       <use xlink:href="#DejaVuSans-33" x="2424.341797"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(147.772656 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(147.772656 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(201.196562 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(209.467812 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(100.418125 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(279.095 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(157.579531 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(115.995781 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image72967537cb" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature2_fold0 -->
    <g transform="translate(170.125437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-32" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image07a48a772b" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pcc26dd6c98">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
