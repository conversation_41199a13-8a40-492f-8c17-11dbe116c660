<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="797.236594pt" height="679.5765pt" viewBox="0 0 797.236594 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:51:04.835100</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 797.236594 679.5765 
L 797.236594 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
L 526.293594 27.789 
L 436.013594 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 471.269567 638.149 
L 471.269567 27.789 
" clip-path="url(#pea3e563438)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 436.013594 609.084238 
L 526.293594 609.084238 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 436.013594 580.019476 
L 526.293594 580.019476 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 436.013594 550.954714 
L 526.293594 550.954714 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 436.013594 521.889952 
L 526.293594 521.889952 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 436.013594 492.82519 
L 526.293594 492.82519 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 436.013594 463.760429 
L 526.293594 463.760429 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 436.013594 434.695667 
L 526.293594 434.695667 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 436.013594 405.630905 
L 526.293594 405.630905 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 436.013594 376.566143 
L 526.293594 376.566143 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 436.013594 347.501381 
L 526.293594 347.501381 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 436.013594 318.436619 
L 526.293594 318.436619 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 436.013594 289.371857 
L 526.293594 289.371857 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 436.013594 260.307095 
L 526.293594 260.307095 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 436.013594 231.242333 
L 526.293594 231.242333 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 436.013594 202.177571 
L 526.293594 202.177571 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 436.013594 173.11281 
L 526.293594 173.11281 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 436.013594 144.048048 
L 526.293594 144.048048 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 436.013594 114.983286 
L 526.293594 114.983286 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 436.013594 85.918524 
L 526.293594 85.918524 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 436.013594 56.853762 
L 526.293594 56.853762 
" clip-path="url(#pea3e563438)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m30120e60aa" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m30120e60aa" x="437.548608" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(429.440405 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m30120e60aa" x="471.269567" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(467.770192 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m30120e60aa" x="504.990525" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(501.49115 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(358.472188 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- GeneralizedRDF_mean_Gaussian_center=2_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(40.039219 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-32" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- StructuralHeterogeneity_min_relative_bond_length -->
      <g style="fill: #333333" transform="translate(81.726562 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-69" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-6e" x="1383.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.066406"/>
       <use xlink:href="#DejaVuSans-72" x="1497.066406"/>
       <use xlink:href="#DejaVuSans-65" x="1535.929688"/>
       <use xlink:href="#DejaVuSans-6c" x="1597.453125"/>
       <use xlink:href="#DejaVuSans-61" x="1625.236328"/>
       <use xlink:href="#DejaVuSans-74" x="1686.515625"/>
       <use xlink:href="#DejaVuSans-69" x="1725.724609"/>
       <use xlink:href="#DejaVuSans-76" x="1753.507812"/>
       <use xlink:href="#DejaVuSans-65" x="1812.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="1874.210938"/>
       <use xlink:href="#DejaVuSans-62" x="1924.210938"/>
       <use xlink:href="#DejaVuSans-6f" x="1987.6875"/>
       <use xlink:href="#DejaVuSans-6e" x="2048.869141"/>
       <use xlink:href="#DejaVuSans-64" x="2112.248047"/>
       <use xlink:href="#DejaVuSans-5f" x="2175.724609"/>
       <use xlink:href="#DejaVuSans-6c" x="2225.724609"/>
       <use xlink:href="#DejaVuSans-65" x="2253.507812"/>
       <use xlink:href="#DejaVuSans-6e" x="2315.03125"/>
       <use xlink:href="#DejaVuSans-67" x="2378.410156"/>
       <use xlink:href="#DejaVuSans-74" x="2441.886719"/>
       <use xlink:href="#DejaVuSans-68" x="2481.095703"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_125 -->
      <g style="fill: #333333" transform="translate(214.921719 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(193.270625 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_range_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(36.632812 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-53" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-70" x="1943.949219"/>
       <use xlink:href="#DejaVuSans-61" x="2007.425781"/>
       <use xlink:href="#DejaVuSans-63" x="2068.705078"/>
       <use xlink:href="#DejaVuSans-65" x="2123.685547"/>
       <use xlink:href="#DejaVuSans-47" x="2185.208984"/>
       <use xlink:href="#DejaVuSans-72" x="2262.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="2301.5625"/>
       <use xlink:href="#DejaVuSans-75" x="2362.744141"/>
       <use xlink:href="#DejaVuSans-70" x="2426.123047"/>
       <use xlink:href="#DejaVuSans-4e" x="2489.599609"/>
       <use xlink:href="#DejaVuSans-75" x="2564.404297"/>
       <use xlink:href="#DejaVuSans-6d" x="2627.783203"/>
       <use xlink:href="#DejaVuSans-62" x="2725.195312"/>
       <use xlink:href="#DejaVuSans-65" x="2788.671875"/>
       <use xlink:href="#DejaVuSans-72" x="2850.195312"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(161.497812 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(100.273906 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(161.497812 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(223.192969 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(84.381406 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(75.978125 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(111.009062 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- AtomicPackingEfficiency_mean_abs_simul__packing_efficiency -->
      <g style="fill: #333333" transform="translate(7.2 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-6d" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-65" x="1362.451172"/>
       <use xlink:href="#DejaVuSans-61" x="1423.974609"/>
       <use xlink:href="#DejaVuSans-6e" x="1485.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.632812"/>
       <use xlink:href="#DejaVuSans-61" x="1598.632812"/>
       <use xlink:href="#DejaVuSans-62" x="1659.912109"/>
       <use xlink:href="#DejaVuSans-73" x="1723.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1775.488281"/>
       <use xlink:href="#DejaVuSans-73" x="1825.488281"/>
       <use xlink:href="#DejaVuSans-69" x="1877.587891"/>
       <use xlink:href="#DejaVuSans-6d" x="1905.371094"/>
       <use xlink:href="#DejaVuSans-75" x="2002.783203"/>
       <use xlink:href="#DejaVuSans-6c" x="2066.162109"/>
       <use xlink:href="#DejaVuSans-5f" x="2093.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="2143.945312"/>
       <use xlink:href="#DejaVuSans-70" x="2193.945312"/>
       <use xlink:href="#DejaVuSans-61" x="2257.421875"/>
       <use xlink:href="#DejaVuSans-63" x="2318.701172"/>
       <use xlink:href="#DejaVuSans-6b" x="2373.681641"/>
       <use xlink:href="#DejaVuSans-69" x="2431.591797"/>
       <use xlink:href="#DejaVuSans-6e" x="2459.375"/>
       <use xlink:href="#DejaVuSans-67" x="2522.753906"/>
       <use xlink:href="#DejaVuSans-5f" x="2586.230469"/>
       <use xlink:href="#DejaVuSans-65" x="2636.230469"/>
       <use xlink:href="#DejaVuSans-66" x="2697.753906"/>
       <use xlink:href="#DejaVuSans-66" x="2732.958984"/>
       <use xlink:href="#DejaVuSans-69" x="2768.164062"/>
       <use xlink:href="#DejaVuSans-63" x="2795.947266"/>
       <use xlink:href="#DejaVuSans-69" x="2850.927734"/>
       <use xlink:href="#DejaVuSans-65" x="2878.710938"/>
       <use xlink:href="#DejaVuSans-6e" x="2940.234375"/>
       <use xlink:href="#DejaVuSans-63" x="3003.613281"/>
       <use xlink:href="#DejaVuSans-79" x="3058.59375"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(290.06375 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(57.709062 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(292.044219 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(20.074062 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(20.925156 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(11.794687 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(292.820156 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image61c027396f" transform="scale(1 -1) translate(0 -578.16)" x="437.76" y="-43.2" width="87.12" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature43_fold0 -->
    <g transform="translate(172.270594 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-33" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.936094 638.149 
L 539.565594 638.149 
L 539.565594 27.789 
L 531.936094 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image354752cd97" transform="scale(1 -1) translate(0 -609.84)" x="532.08" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(543.065594 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(543.065594 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(577.466531 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pea3e563438">
   <rect x="436.013594" y="27.789" width="90.28" height="610.36"/>
  </clipPath>
 </defs>
</svg>
