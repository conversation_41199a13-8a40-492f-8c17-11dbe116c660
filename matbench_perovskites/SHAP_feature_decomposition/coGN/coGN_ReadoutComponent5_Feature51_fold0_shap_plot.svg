<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="778.965406pt" height="679.5765pt" viewBox="0 0 778.965406 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T09:45:10.168380</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 778.965406 679.5765 
L 778.965406 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
L 520.118406 27.789 
L 405.646406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 456.808063 638.149 
L 456.808063 27.789 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 405.646406 609.084238 
L 520.118406 609.084238 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 405.646406 580.019476 
L 520.118406 580.019476 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 405.646406 550.954714 
L 520.118406 550.954714 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 405.646406 521.889952 
L 520.118406 521.889952 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 405.646406 492.82519 
L 520.118406 492.82519 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 405.646406 463.760429 
L 520.118406 463.760429 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 405.646406 434.695667 
L 520.118406 434.695667 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 405.646406 405.630905 
L 520.118406 405.630905 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 405.646406 376.566143 
L 520.118406 376.566143 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 405.646406 347.501381 
L 520.118406 347.501381 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 405.646406 318.436619 
L 520.118406 318.436619 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 405.646406 289.371857 
L 520.118406 289.371857 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 405.646406 260.307095 
L 520.118406 260.307095 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 405.646406 231.242333 
L 520.118406 231.242333 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 405.646406 202.177571 
L 520.118406 202.177571 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 405.646406 173.11281 
L 520.118406 173.11281 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 405.646406 144.048048 
L 520.118406 144.048048 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 405.646406 114.983286 
L 520.118406 114.983286 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 405.646406 85.918524 
L 520.118406 85.918524 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 405.646406 56.853762 
L 520.118406 56.853762 
" clip-path="url(#p31bf92034c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m793dfd6c79" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m793dfd6c79" x="456.808063" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(453.308688 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m793dfd6c79" x="519.993579" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(516.494204 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(340.201 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(131.130625 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MaximumPackingEfficiency_max_packing_efficiency -->
      <g style="fill: #333333" transform="translate(43.849844 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-61" x="86.279297"/>
       <use xlink:href="#DejaVuSans-78" x="147.558594"/>
       <use xlink:href="#DejaVuSans-69" x="206.738281"/>
       <use xlink:href="#DejaVuSans-6d" x="234.521484"/>
       <use xlink:href="#DejaVuSans-75" x="331.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="395.3125"/>
       <use xlink:href="#DejaVuSans-50" x="492.724609"/>
       <use xlink:href="#DejaVuSans-61" x="548.527344"/>
       <use xlink:href="#DejaVuSans-63" x="609.806641"/>
       <use xlink:href="#DejaVuSans-6b" x="664.787109"/>
       <use xlink:href="#DejaVuSans-69" x="722.697266"/>
       <use xlink:href="#DejaVuSans-6e" x="750.480469"/>
       <use xlink:href="#DejaVuSans-67" x="813.859375"/>
       <use xlink:href="#DejaVuSans-45" x="877.335938"/>
       <use xlink:href="#DejaVuSans-66" x="940.519531"/>
       <use xlink:href="#DejaVuSans-66" x="975.724609"/>
       <use xlink:href="#DejaVuSans-69" x="1010.929688"/>
       <use xlink:href="#DejaVuSans-63" x="1038.712891"/>
       <use xlink:href="#DejaVuSans-69" x="1093.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1121.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="1183"/>
       <use xlink:href="#DejaVuSans-63" x="1246.378906"/>
       <use xlink:href="#DejaVuSans-79" x="1301.359375"/>
       <use xlink:href="#DejaVuSans-5f" x="1360.539062"/>
       <use xlink:href="#DejaVuSans-6d" x="1410.539062"/>
       <use xlink:href="#DejaVuSans-61" x="1507.951172"/>
       <use xlink:href="#DejaVuSans-78" x="1569.230469"/>
       <use xlink:href="#DejaVuSans-5f" x="1628.410156"/>
       <use xlink:href="#DejaVuSans-70" x="1678.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1741.886719"/>
       <use xlink:href="#DejaVuSans-63" x="1803.166016"/>
       <use xlink:href="#DejaVuSans-6b" x="1858.146484"/>
       <use xlink:href="#DejaVuSans-69" x="1916.056641"/>
       <use xlink:href="#DejaVuSans-6e" x="1943.839844"/>
       <use xlink:href="#DejaVuSans-67" x="2007.21875"/>
       <use xlink:href="#DejaVuSans-5f" x="2070.695312"/>
       <use xlink:href="#DejaVuSans-65" x="2120.695312"/>
       <use xlink:href="#DejaVuSans-66" x="2182.21875"/>
       <use xlink:href="#DejaVuSans-66" x="2217.423828"/>
       <use xlink:href="#DejaVuSans-69" x="2252.628906"/>
       <use xlink:href="#DejaVuSans-63" x="2280.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2335.392578"/>
       <use xlink:href="#DejaVuSans-65" x="2363.175781"/>
       <use xlink:href="#DejaVuSans-6e" x="2424.699219"/>
       <use xlink:href="#DejaVuSans-63" x="2488.078125"/>
       <use xlink:href="#DejaVuSans-79" x="2543.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- OPSiteFingerprint_std_dev_q2_CN_10 -->
      <g style="fill: #333333" transform="translate(140.151406 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-71" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-32" x="1425.953125"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-43" x="1539.576172"/>
       <use xlink:href="#DejaVuSans-4e" x="1609.400391"/>
       <use xlink:href="#DejaVuSans-5f" x="1684.205078"/>
       <use xlink:href="#DejaVuSans-31" x="1734.205078"/>
       <use xlink:href="#DejaVuSans-30" x="1797.828125"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_40 -->
      <g style="fill: #333333" transform="translate(192.825781 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(97.738906 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(106.192969 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_square_pyramidal_CN_5 -->
      <g style="fill: #333333" transform="translate(40.520625 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-73" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-71" x="1478.296875"/>
       <use xlink:href="#DejaVuSans-75" x="1541.773438"/>
       <use xlink:href="#DejaVuSans-61" x="1605.152344"/>
       <use xlink:href="#DejaVuSans-72" x="1666.431641"/>
       <use xlink:href="#DejaVuSans-65" x="1705.294922"/>
       <use xlink:href="#DejaVuSans-5f" x="1766.818359"/>
       <use xlink:href="#DejaVuSans-70" x="1816.818359"/>
       <use xlink:href="#DejaVuSans-79" x="1880.294922"/>
       <use xlink:href="#DejaVuSans-72" x="1939.474609"/>
       <use xlink:href="#DejaVuSans-61" x="1980.587891"/>
       <use xlink:href="#DejaVuSans-6d" x="2041.867188"/>
       <use xlink:href="#DejaVuSans-69" x="2139.279297"/>
       <use xlink:href="#DejaVuSans-64" x="2167.0625"/>
       <use xlink:href="#DejaVuSans-61" x="2230.539062"/>
       <use xlink:href="#DejaVuSans-6c" x="2291.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="2319.601562"/>
       <use xlink:href="#DejaVuSans-43" x="2369.601562"/>
       <use xlink:href="#DejaVuSans-4e" x="2439.425781"/>
       <use xlink:href="#DejaVuSans-5f" x="2514.230469"/>
       <use xlink:href="#DejaVuSans-35" x="2564.230469"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(79.471875 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_172 -->
      <g style="fill: #333333" transform="translate(184.554531 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(63.104062 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(192.825781 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(69.906719 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(119.89375 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(184.554531 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(192.825781 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(83.776094 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(262.452969 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(140.9375 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(99.35375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagebc4a6fdd6a" transform="scale(1 -1) translate(0 -578.16)" x="408.24" y="-43.2" width="108.72" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature51_fold0 -->
    <g transform="translate(153.999406 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.272906 638.149 
L 534.902406 638.149 
L 534.902406 27.789 
L 527.272906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagef91af5ae70" transform="scale(1 -1) translate(0 -609.84)" x="527.04" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(538.402406 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(538.402406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(572.803344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p31bf92034c">
   <rect x="405.646406" y="27.789" width="114.472" height="610.36"/>
  </clipPath>
 </defs>
</svg>
