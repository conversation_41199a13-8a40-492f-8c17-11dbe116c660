<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="760.779531pt" height="679.5765pt" viewBox="0 0 760.779531 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T06:53:42.905527</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 760.779531 679.5765 
L 760.779531 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 385.504531 638.149 
L 516.104531 638.149 
L 516.104531 27.789 
L 385.504531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 477.006561 638.149 
L 477.006561 27.789 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 385.504531 609.084238 
L 516.104531 609.084238 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 385.504531 580.019476 
L 516.104531 580.019476 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 385.504531 550.954714 
L 516.104531 550.954714 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 385.504531 521.889952 
L 516.104531 521.889952 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 385.504531 492.82519 
L 516.104531 492.82519 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 385.504531 463.760429 
L 516.104531 463.760429 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 385.504531 434.695667 
L 516.104531 434.695667 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 385.504531 405.630905 
L 516.104531 405.630905 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 385.504531 376.566143 
L 516.104531 376.566143 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 385.504531 347.501381 
L 516.104531 347.501381 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 385.504531 318.436619 
L 516.104531 318.436619 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 385.504531 289.371857 
L 516.104531 289.371857 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 385.504531 260.307095 
L 516.104531 260.307095 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 385.504531 231.242333 
L 516.104531 231.242333 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 385.504531 202.177571 
L 516.104531 202.177571 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 385.504531 173.11281 
L 516.104531 173.11281 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 385.504531 144.048048 
L 516.104531 144.048048 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 385.504531 114.983286 
L 516.104531 114.983286 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 385.504531 85.918524 
L 516.104531 85.918524 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 385.504531 56.853762 
L 516.104531 56.853762 
" clip-path="url(#p85b91448f0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m2c033b519f" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2c033b519f" x="424.205908" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(416.097705 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m2c033b519f" x="477.006561" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(473.507186 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(328.123125 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(13.364844 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- XRDPowderPattern_xrd_47 -->
      <g style="fill: #333333" transform="translate(189.132969 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-34" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-37" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(49.764844 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_39 -->
      <g style="fill: #333333" transform="translate(172.683906 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=1_60e+01 -->
      <g style="fill: #333333" transform="translate(16.454375 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-31" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-36" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2b" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2530.761719"/>
       <use xlink:href="#DejaVuSans-31" x="2594.384766"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_76 -->
      <g style="fill: #333333" transform="translate(172.683906 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(110.98875 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(8.134375 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- VoronoiFingerprint_mean_Symmetry_index_6 -->
      <g style="fill: #333333" transform="translate(68.714375 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-53" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-79" x="1363.804688"/>
       <use xlink:href="#DejaVuSans-6d" x="1422.984375"/>
       <use xlink:href="#DejaVuSans-6d" x="1520.396484"/>
       <use xlink:href="#DejaVuSans-65" x="1617.808594"/>
       <use xlink:href="#DejaVuSans-74" x="1679.332031"/>
       <use xlink:href="#DejaVuSans-72" x="1718.541016"/>
       <use xlink:href="#DejaVuSans-79" x="1759.654297"/>
       <use xlink:href="#DejaVuSans-5f" x="1818.833984"/>
       <use xlink:href="#DejaVuSans-69" x="1868.833984"/>
       <use xlink:href="#DejaVuSans-6e" x="1896.617188"/>
       <use xlink:href="#DejaVuSans-64" x="1959.996094"/>
       <use xlink:href="#DejaVuSans-65" x="2023.472656"/>
       <use xlink:href="#DejaVuSans-78" x="2083.246094"/>
       <use xlink:href="#DejaVuSans-5f" x="2142.425781"/>
       <use xlink:href="#DejaVuSans-36" x="2192.425781"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_mean_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(29.265469 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-76" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2073.441406"/>
       <use xlink:href="#DejaVuSans-6c" x="2134.623047"/>
       <use xlink:href="#DejaVuSans-75" x="2162.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="2225.785156"/>
       <use xlink:href="#DejaVuSans-65" x="2323.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="2384.720703"/>
       <use xlink:href="#DejaVuSans-70" x="2434.720703"/>
       <use xlink:href="#DejaVuSans-61" x="2498.197266"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(172.683906 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- VoronoiFingerprint_mean_Voro_index_6 -->
      <g style="fill: #333333" transform="translate(107.275625 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-6e" x="1599.996094"/>
       <use xlink:href="#DejaVuSans-64" x="1663.375"/>
       <use xlink:href="#DejaVuSans-65" x="1726.851562"/>
       <use xlink:href="#DejaVuSans-78" x="1786.625"/>
       <use xlink:href="#DejaVuSans-5f" x="1845.804688"/>
       <use xlink:href="#DejaVuSans-36" x="1895.804688"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_72 -->
      <g style="fill: #333333" transform="translate(172.683906 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(49.764844 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(42.962188 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(172.683906 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(240.0625 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(239.554688 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(53.238281 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 385.504531 638.149 
L 516.104531 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image7271e5ed70" transform="scale(1 -1) translate(0 -578.16)" x="388.8" y="-43.2" width="123.84" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature5_fold0 -->
    <g transform="translate(148.029531 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 524.267031 638.149 
L 531.896531 638.149 
L 531.896531 27.789 
L 524.267031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image65d7acf452" transform="scale(1 -1) translate(0 -609.84)" x="524.16" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(535.396531 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(535.396531 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(569.797469 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p85b91448f0">
   <rect x="385.504531" y="27.789" width="130.6" height="610.36"/>
  </clipPath>
 </defs>
</svg>
