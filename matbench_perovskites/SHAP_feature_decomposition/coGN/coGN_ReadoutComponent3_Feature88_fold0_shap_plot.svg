<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="797.236594pt" height="679.5765pt" viewBox="0 0 797.236594 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T18:02:14.605601</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 797.236594 679.5765 
L 797.236594 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
L 526.293594 27.789 
L 436.013594 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 466.74697 638.149 
L 466.74697 27.789 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 436.013594 609.084238 
L 526.293594 609.084238 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 436.013594 580.019476 
L 526.293594 580.019476 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 436.013594 550.954714 
L 526.293594 550.954714 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 436.013594 521.889952 
L 526.293594 521.889952 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 436.013594 492.82519 
L 526.293594 492.82519 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 436.013594 463.760429 
L 526.293594 463.760429 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 436.013594 434.695667 
L 526.293594 434.695667 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 436.013594 405.630905 
L 526.293594 405.630905 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 436.013594 376.566143 
L 526.293594 376.566143 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 436.013594 347.501381 
L 526.293594 347.501381 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 436.013594 318.436619 
L 526.293594 318.436619 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 436.013594 289.371857 
L 526.293594 289.371857 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 436.013594 260.307095 
L 526.293594 260.307095 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 436.013594 231.242333 
L 526.293594 231.242333 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 436.013594 202.177571 
L 526.293594 202.177571 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 436.013594 173.11281 
L 526.293594 173.11281 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 436.013594 144.048048 
L 526.293594 144.048048 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 436.013594 114.983286 
L 526.293594 114.983286 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 436.013594 85.918524 
L 526.293594 85.918524 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 436.013594 56.853762 
L 526.293594 56.853762 
" clip-path="url(#pd58a25faaf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m2fe3ea4b68" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2fe3ea4b68" x="466.74697" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(463.247595 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m2fe3ea4b68" x="508.01283" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(504.513455 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(358.472188 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_64 -->
      <g style="fill: #333333" transform="translate(223.192969 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- Stoichiometry_2-norm -->
      <g style="fill: #333333" transform="translate(268.414688 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-32" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- AtomicPackingEfficiency_mean_abs_simul__packing_efficiency -->
      <g style="fill: #333333" transform="translate(7.2 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-6d" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-65" x="1362.451172"/>
       <use xlink:href="#DejaVuSans-61" x="1423.974609"/>
       <use xlink:href="#DejaVuSans-6e" x="1485.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.632812"/>
       <use xlink:href="#DejaVuSans-61" x="1598.632812"/>
       <use xlink:href="#DejaVuSans-62" x="1659.912109"/>
       <use xlink:href="#DejaVuSans-73" x="1723.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1775.488281"/>
       <use xlink:href="#DejaVuSans-73" x="1825.488281"/>
       <use xlink:href="#DejaVuSans-69" x="1877.587891"/>
       <use xlink:href="#DejaVuSans-6d" x="1905.371094"/>
       <use xlink:href="#DejaVuSans-75" x="2002.783203"/>
       <use xlink:href="#DejaVuSans-6c" x="2066.162109"/>
       <use xlink:href="#DejaVuSans-5f" x="2093.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="2143.945312"/>
       <use xlink:href="#DejaVuSans-70" x="2193.945312"/>
       <use xlink:href="#DejaVuSans-61" x="2257.421875"/>
       <use xlink:href="#DejaVuSans-63" x="2318.701172"/>
       <use xlink:href="#DejaVuSans-6b" x="2373.681641"/>
       <use xlink:href="#DejaVuSans-69" x="2431.591797"/>
       <use xlink:href="#DejaVuSans-6e" x="2459.375"/>
       <use xlink:href="#DejaVuSans-67" x="2522.753906"/>
       <use xlink:href="#DejaVuSans-5f" x="2586.230469"/>
       <use xlink:href="#DejaVuSans-65" x="2636.230469"/>
       <use xlink:href="#DejaVuSans-66" x="2697.753906"/>
       <use xlink:href="#DejaVuSans-66" x="2732.958984"/>
       <use xlink:href="#DejaVuSans-69" x="2768.164062"/>
       <use xlink:href="#DejaVuSans-63" x="2795.947266"/>
       <use xlink:href="#DejaVuSans-69" x="2850.927734"/>
       <use xlink:href="#DejaVuSans-65" x="2878.710938"/>
       <use xlink:href="#DejaVuSans-6e" x="2940.234375"/>
       <use xlink:href="#DejaVuSans-63" x="3003.613281"/>
       <use xlink:href="#DejaVuSans-79" x="3058.59375"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_avg_dev_MeltingT -->
      <g style="fill: #333333" transform="translate(95.520781 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-74" x="2183.402344"/>
       <use xlink:href="#DejaVuSans-69" x="2222.611328"/>
       <use xlink:href="#DejaVuSans-6e" x="2250.394531"/>
       <use xlink:href="#DejaVuSans-67" x="2313.773438"/>
       <use xlink:href="#DejaVuSans-54" x="2377.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(109.839062 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_174 -->
      <g style="fill: #333333" transform="translate(214.921719 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-34" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- Miedema_Miedema_deltaH_ss_min -->
      <g style="fill: #333333" transform="translate(186.335937 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-69" x="86.279297"/>
       <use xlink:href="#DejaVuSans-65" x="114.0625"/>
       <use xlink:href="#DejaVuSans-64" x="175.585938"/>
       <use xlink:href="#DejaVuSans-65" x="239.0625"/>
       <use xlink:href="#DejaVuSans-6d" x="300.585938"/>
       <use xlink:href="#DejaVuSans-61" x="397.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="459.277344"/>
       <use xlink:href="#DejaVuSans-4d" x="509.277344"/>
       <use xlink:href="#DejaVuSans-69" x="595.556641"/>
       <use xlink:href="#DejaVuSans-65" x="623.339844"/>
       <use xlink:href="#DejaVuSans-64" x="684.863281"/>
       <use xlink:href="#DejaVuSans-65" x="748.339844"/>
       <use xlink:href="#DejaVuSans-6d" x="809.863281"/>
       <use xlink:href="#DejaVuSans-61" x="907.275391"/>
       <use xlink:href="#DejaVuSans-5f" x="968.554688"/>
       <use xlink:href="#DejaVuSans-64" x="1018.554688"/>
       <use xlink:href="#DejaVuSans-65" x="1082.03125"/>
       <use xlink:href="#DejaVuSans-6c" x="1143.554688"/>
       <use xlink:href="#DejaVuSans-74" x="1171.337891"/>
       <use xlink:href="#DejaVuSans-61" x="1210.546875"/>
       <use xlink:href="#DejaVuSans-48" x="1271.826172"/>
       <use xlink:href="#DejaVuSans-5f" x="1347.021484"/>
       <use xlink:href="#DejaVuSans-73" x="1397.021484"/>
       <use xlink:href="#DejaVuSans-73" x="1449.121094"/>
       <use xlink:href="#DejaVuSans-5f" x="1501.220703"/>
       <use xlink:href="#DejaVuSans-6d" x="1551.220703"/>
       <use xlink:href="#DejaVuSans-69" x="1648.632812"/>
       <use xlink:href="#DejaVuSans-6e" x="1676.416016"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(20.925156 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(208.81375 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_87 -->
      <g style="fill: #333333" transform="translate(223.192969 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(145.696719 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(111.009062 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(113.013906 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(161.497812 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(100.273906 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(220.722969 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(161.497812 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(11.794687 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- OxidationStates_minimum_oxidation_state -->
      <g style="fill: #333333" transform="translate(134.575625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-6d" x="848.779297"/>
       <use xlink:href="#DejaVuSans-69" x="946.191406"/>
       <use xlink:href="#DejaVuSans-6e" x="973.974609"/>
       <use xlink:href="#DejaVuSans-69" x="1037.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1065.136719"/>
       <use xlink:href="#DejaVuSans-75" x="1162.548828"/>
       <use xlink:href="#DejaVuSans-6d" x="1225.927734"/>
       <use xlink:href="#DejaVuSans-5f" x="1323.339844"/>
       <use xlink:href="#DejaVuSans-6f" x="1373.339844"/>
       <use xlink:href="#DejaVuSans-78" x="1431.396484"/>
       <use xlink:href="#DejaVuSans-69" x="1490.576172"/>
       <use xlink:href="#DejaVuSans-64" x="1518.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
       <use xlink:href="#DejaVuSans-69" x="1682.324219"/>
       <use xlink:href="#DejaVuSans-6f" x="1710.107422"/>
       <use xlink:href="#DejaVuSans-6e" x="1771.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1834.667969"/>
       <use xlink:href="#DejaVuSans-73" x="1884.667969"/>
       <use xlink:href="#DejaVuSans-74" x="1936.767578"/>
       <use xlink:href="#DejaVuSans-61" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-74" x="2037.255859"/>
       <use xlink:href="#DejaVuSans-65" x="2076.464844"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(292.820156 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image1a7bcb78be" transform="scale(1 -1) translate(0 -578.16)" x="437.76" y="-43.2" width="87.12" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature88_fold0 -->
    <g transform="translate(172.270594 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-38" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-38" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.936094 638.149 
L 539.565594 638.149 
L 539.565594 27.789 
L 531.936094 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image5b59f3cd50" transform="scale(1 -1) translate(0 -609.84)" x="532.08" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(543.065594 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(543.065594 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.466531 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pd58a25faaf">
   <rect x="436.013594" y="27.789" width="90.28" height="610.36"/>
  </clipPath>
 </defs>
</svg>
