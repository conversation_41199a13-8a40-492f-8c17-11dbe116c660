<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:13:33.635458</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 464.991753 638.149 
L 464.991753 27.789 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#pb286a6ad12)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mc0d2979707" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mc0d2979707" x="428.598407" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(415.24286 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mc0d2979707" x="464.991753" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(456.245034 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mc0d2979707" x="501.385098" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(492.638379 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_range_Column -->
      <g style="fill: #333333" transform="translate(108.348125 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-6c" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-75" x="2039.261719"/>
       <use xlink:href="#DejaVuSans-6d" x="2102.640625"/>
       <use xlink:href="#DejaVuSans-6e" x="2200.052734"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(99.924531 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(147.689375 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(107.539688 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6f" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-63" x="1487.378906"/>
       <use xlink:href="#DejaVuSans-74" x="1542.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.568359"/>
       <use xlink:href="#DejaVuSans-68" x="1642.847656"/>
       <use xlink:href="#DejaVuSans-65" x="1706.226562"/>
       <use xlink:href="#DejaVuSans-64" x="1767.75"/>
       <use xlink:href="#DejaVuSans-72" x="1831.226562"/>
       <use xlink:href="#DejaVuSans-61" x="1872.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1933.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1961.402344"/>
       <use xlink:href="#DejaVuSans-43" x="2011.402344"/>
       <use xlink:href="#DejaVuSans-4e" x="2081.226562"/>
       <use xlink:href="#DejaVuSans-5f" x="2156.03125"/>
       <use xlink:href="#DejaVuSans-36" x="2206.03125"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(61.607031 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_maximum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(41.2275 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-76" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.705078"/>
       <use xlink:href="#DejaVuSans-6c" x="2354.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2382.669922"/>
       <use xlink:href="#DejaVuSans-6d" x="2446.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2543.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2604.984375"/>
       <use xlink:href="#DejaVuSans-70" x="2654.984375"/>
       <use xlink:href="#DejaVuSans-61" x="2718.460938"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(151.583281 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(83.556719 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(90.359375 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(63.252344 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(47.794531 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(282.129687 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(106.288437 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(198.505156 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(210.808437 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(90.359375 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_avg_dev_CovalentRadius -->
      <g style="fill: #333333" transform="translate(40.352031 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-43" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6f" x="2077.640625"/>
       <use xlink:href="#DejaVuSans-76" x="2138.822266"/>
       <use xlink:href="#DejaVuSans-61" x="2198.001953"/>
       <use xlink:href="#DejaVuSans-6c" x="2259.28125"/>
       <use xlink:href="#DejaVuSans-65" x="2287.064453"/>
       <use xlink:href="#DejaVuSans-6e" x="2348.587891"/>
       <use xlink:href="#DejaVuSans-74" x="2411.966797"/>
       <use xlink:href="#DejaVuSans-52" x="2451.175781"/>
       <use xlink:href="#DejaVuSans-61" x="2518.408203"/>
       <use xlink:href="#DejaVuSans-64" x="2579.6875"/>
       <use xlink:href="#DejaVuSans-69" x="2643.164062"/>
       <use xlink:href="#DejaVuSans-75" x="2670.947266"/>
       <use xlink:href="#DejaVuSans-73" x="2734.326172"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(66.063594 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099062 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagee2a2fbebcf" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature67_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-37" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image165afd71f2" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pb286a6ad12">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
