<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="772.457156pt" height="679.5765pt" viewBox="0 0 772.457156 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T21:14:29.658260</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 772.457156 679.5765 
L 772.457156 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 384.570156 638.149 
L 515.962156 638.149 
L 515.962156 27.789 
L 384.570156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 437.61938 638.149 
L 437.61938 27.789 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 384.570156 609.084238 
L 515.962156 609.084238 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 384.570156 580.019476 
L 515.962156 580.019476 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 384.570156 550.954714 
L 515.962156 550.954714 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 384.570156 521.889952 
L 515.962156 521.889952 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 384.570156 492.82519 
L 515.962156 492.82519 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 384.570156 463.760429 
L 515.962156 463.760429 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 384.570156 434.695667 
L 515.962156 434.695667 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 384.570156 405.630905 
L 515.962156 405.630905 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 384.570156 376.566143 
L 515.962156 376.566143 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 384.570156 347.501381 
L 515.962156 347.501381 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 384.570156 318.436619 
L 515.962156 318.436619 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 384.570156 289.371857 
L 515.962156 289.371857 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 384.570156 260.307095 
L 515.962156 260.307095 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 384.570156 231.242333 
L 515.962156 231.242333 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 384.570156 202.177571 
L 515.962156 202.177571 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 384.570156 173.11281 
L 515.962156 173.11281 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 384.570156 144.048048 
L 515.962156 144.048048 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 384.570156 114.983286 
L 515.962156 114.983286 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 384.570156 85.918524 
L 515.962156 85.918524 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 384.570156 56.853762 
L 515.962156 56.853762 
" clip-path="url(#p22bbd553b3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mc3da82b8f9" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mc3da82b8f9" x="437.61938" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(434.120005 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mc3da82b8f9" x="507.266432" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(503.767057 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(327.58475 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CrystalNNFingerprint_mean_wt_CN_8 -->
      <g style="fill: #333333" transform="translate(119.86125 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-38" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(110.054375 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(110.054375 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- XRDPowderPattern_xrd_33 -->
      <g style="fill: #333333" transform="translate(188.198594 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_79 -->
      <g style="fill: #333333" transform="translate(171.749531 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_101 -->
      <g style="fill: #333333" transform="translate(163.478281 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_129 -->
      <g style="fill: #333333" transform="translate(163.478281 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-39" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_31 -->
      <g style="fill: #333333" transform="translate(171.749531 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_mean_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(28.331094 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-76" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2073.441406"/>
       <use xlink:href="#DejaVuSans-6c" x="2134.623047"/>
       <use xlink:href="#DejaVuSans-75" x="2162.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="2225.785156"/>
       <use xlink:href="#DejaVuSans-65" x="2323.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="2384.720703"/>
       <use xlink:href="#DejaVuSans-70" x="2434.720703"/>
       <use xlink:href="#DejaVuSans-61" x="2498.197266"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_1 -->
      <g style="fill: #333333" transform="translate(180.020781 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(163.478281 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(76.662656 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(171.749531 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- VoronoiFingerprint_std_dev_Voro_area_maximum -->
      <g style="fill: #333333" transform="translate(41.997344 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-61" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-78" x="2109.220703"/>
       <use xlink:href="#DejaVuSans-69" x="2168.400391"/>
       <use xlink:href="#DejaVuSans-6d" x="2196.183594"/>
       <use xlink:href="#DejaVuSans-75" x="2293.595703"/>
       <use xlink:href="#DejaVuSans-6d" x="2356.974609"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(48.830469 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(62.699844 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(241.376719 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(119.86125 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(78.2775 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 384.570156 638.149 
L 515.962156 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image3a19ea2ba2" transform="scale(1 -1) translate(0 -578.16)" x="388.08" y="-43.2" width="124.56" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature110_fold0 -->
    <g transform="translate(135.275156 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-30" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 524.174156 638.149 
L 531.803656 638.149 
L 531.803656 27.789 
L 524.174156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imaged3ec2ff818" transform="scale(1 -1) translate(0 -609.84)" x="524.16" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(535.303656 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(535.303656 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(569.704594 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p22bbd553b3">
   <rect x="384.570156" y="27.789" width="131.392" height="610.36"/>
  </clipPath>
 </defs>
</svg>
