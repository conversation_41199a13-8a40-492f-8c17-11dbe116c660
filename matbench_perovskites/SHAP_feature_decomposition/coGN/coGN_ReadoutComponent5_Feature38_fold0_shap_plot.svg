<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="746.726906pt" height="679.5765pt" viewBox="0 0 746.726906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T08:05:48.936943</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 746.726906 679.5765 
L 746.726906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 351.663906 638.149 
L 509.623906 638.149 
L 509.623906 27.789 
L 351.663906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 420.777911 638.149 
L 420.777911 27.789 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 351.663906 609.084238 
L 509.623906 609.084238 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 351.663906 580.019476 
L 509.623906 580.019476 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 351.663906 550.954714 
L 509.623906 550.954714 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 351.663906 521.889952 
L 509.623906 521.889952 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 351.663906 492.82519 
L 509.623906 492.82519 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 351.663906 463.760429 
L 509.623906 463.760429 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 351.663906 434.695667 
L 509.623906 434.695667 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 351.663906 405.630905 
L 509.623906 405.630905 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 351.663906 376.566143 
L 509.623906 376.566143 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 351.663906 347.501381 
L 509.623906 347.501381 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 351.663906 318.436619 
L 509.623906 318.436619 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 351.663906 289.371857 
L 509.623906 289.371857 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 351.663906 260.307095 
L 509.623906 260.307095 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 351.663906 231.242333 
L 509.623906 231.242333 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 351.663906 202.177571 
L 509.623906 202.177571 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 351.663906 173.11281 
L 509.623906 173.11281 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 351.663906 144.048048 
L 509.623906 144.048048 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 351.663906 114.983286 
L 509.623906 114.983286 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 351.663906 85.918524 
L 509.623906 85.918524 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 351.663906 56.853762 
L 509.623906 56.853762 
" clip-path="url(#p40eb34f695)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mb4db223d39" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb4db223d39" x="380.630484" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(372.522281 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mb4db223d39" x="420.777911" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(417.278536 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mb4db223d39" x="460.925339" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(457.425964 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_25">
      <g>
       <use xlink:href="#mb4db223d39" x="501.072766" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(497.573391 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_5">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(307.9625 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_6">
      <!-- BondOrientationParameter_std_dev_BOOP_Q_l=1 -->
      <g style="fill: #333333" transform="translate(7.2 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-73" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-74" x="1443.546875"/>
       <use xlink:href="#DejaVuSans-64" x="1482.755859"/>
       <use xlink:href="#DejaVuSans-5f" x="1546.232422"/>
       <use xlink:href="#DejaVuSans-64" x="1596.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1659.708984"/>
       <use xlink:href="#DejaVuSans-76" x="1721.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="1780.412109"/>
       <use xlink:href="#DejaVuSans-42" x="1830.412109"/>
       <use xlink:href="#DejaVuSans-4f" x="1897.265625"/>
       <use xlink:href="#DejaVuSans-4f" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-50" x="2054.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="2114.990234"/>
       <use xlink:href="#DejaVuSans-51" x="2164.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2243.701172"/>
       <use xlink:href="#DejaVuSans-6c" x="2293.701172"/>
       <use xlink:href="#DejaVuSans-3d" x="2321.484375"/>
       <use xlink:href="#DejaVuSans-31" x="2405.273438"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_avg_dev_MeltingT -->
      <g style="fill: #333333" transform="translate(11.171094 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-74" x="2183.402344"/>
       <use xlink:href="#DejaVuSans-69" x="2222.611328"/>
       <use xlink:href="#DejaVuSans-6e" x="2250.394531"/>
       <use xlink:href="#DejaVuSans-67" x="2313.773438"/>
       <use xlink:href="#DejaVuSans-54" x="2377.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_8">
      <!-- DensityFeatures_density -->
      <g style="fill: #333333" transform="translate(168.690469 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-64" x="860.072266"/>
       <use xlink:href="#DejaVuSans-65" x="923.548828"/>
       <use xlink:href="#DejaVuSans-6e" x="985.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1048.451172"/>
       <use xlink:href="#DejaVuSans-69" x="1100.550781"/>
       <use xlink:href="#DejaVuSans-74" x="1128.333984"/>
       <use xlink:href="#DejaVuSans-79" x="1167.542969"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_9">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(73.254219 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_wt_CN_5 -->
      <g style="fill: #333333" transform="translate(86.955 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-35" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_11">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(124.07 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_12">
      <!-- XRDPowderPattern_xrd_39 -->
      <g style="fill: #333333" transform="translate(155.292344 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-39" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_13">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_4 -->
      <g style="fill: #333333" transform="translate(73.254219 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-34" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(27.729844 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(15.924219 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(43.756406 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(77.148125 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_18">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(163.494531 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(15.924219 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(130.572031 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_21">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(138.843281 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_22">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(29.793594 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_23">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(208.470469 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_24">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(86.955 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_25">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(45.37125 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 351.663906 638.149 
L 509.623906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image31a899dbec" transform="scale(1 -1) translate(0 -578.16)" x="356.4" y="-43.2" width="148.32" height="578.16"/>
   <g id="text_26">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature38_fold0 -->
    <g transform="translate(121.760906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-38" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 519.496406 638.149 
L 527.125906 638.149 
L 527.125906 27.789 
L 519.496406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image467f4af3af" transform="scale(1 -1) translate(0 -609.84)" x="519.84" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- Low -->
      <g transform="translate(530.625906 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_27"/>
     <g id="text_28">
      <!-- High -->
      <g transform="translate(530.625906 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_29">
     <!-- Feature value -->
     <g transform="translate(565.026844 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p40eb34f695">
   <rect x="351.663906" y="27.789" width="157.96" height="610.36"/>
  </clipPath>
 </defs>
</svg>
