<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.657906pt" height="679.5765pt" viewBox="0 0 794.657906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:46:52.009125</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.657906 679.5765 
L 794.657906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
L 525.730906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 468.70684 638.149 
L 468.70684 27.789 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.730906 609.084238 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.730906 580.019476 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.730906 550.954714 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.730906 521.889952 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.730906 492.82519 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.730906 463.760429 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.730906 434.695667 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.730906 405.630905 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.730906 376.566143 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.730906 347.501381 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.730906 318.436619 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.730906 289.371857 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.730906 260.307095 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.730906 231.242333 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.730906 202.177571 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.730906 173.11281 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.730906 144.048048 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.730906 114.983286 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.730906 85.918524 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.730906 56.853762 
" clip-path="url(#p47c668cc4b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mc053271347" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mc053271347" x="468.70684" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(465.207465 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mc053271347" x="508.747586" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(505.248211 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8935 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_avg_dev_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(57.686719 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-76" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-6f" x="2207.962891"/>
       <use xlink:href="#DejaVuSans-6c" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-75" x="2296.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2457.71875"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.242188"/>
       <use xlink:href="#DejaVuSans-70" x="2569.242188"/>
       <use xlink:href="#DejaVuSans-61" x="2632.71875"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(95.679219 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(156.903125 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(88.876563 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_134 -->
      <g style="fill: #333333" transform="translate(210.327031 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-34" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(16.330469 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_63 -->
      <g style="fill: #333333" transform="translate(218.598281 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(123.511406 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(125.12625 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(29.226875 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(156.903125 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(216.128281 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(80.4225 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(89.780469 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_mode_GSbandgap -->
      <g style="fill: #333333" transform="translate(88.874531 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(287.449531 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(71.383438 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(285.976875 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image962e45f609" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature41_fold0 -->
    <g transform="translate(169.691906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.625406 638.149 
L 539.254906 638.149 
L 539.254906 27.789 
L 531.625406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image8e27c0fa98" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.754906 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.754906 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.155844 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p47c668cc4b">
   <rect x="431.418906" y="27.789" width="94.312" height="610.36"/>
  </clipPath>
 </defs>
</svg>
