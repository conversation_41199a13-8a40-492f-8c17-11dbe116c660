<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.621906pt" height="679.5765pt" viewBox="0 0 794.621906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T06:44:55.411459</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.621906 679.5765 
L 794.621906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
L 525.658906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 472.426512 638.149 
L 472.426512 27.789 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.658906 609.084238 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.658906 580.019476 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.658906 550.954714 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.658906 521.889952 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.658906 492.82519 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.658906 463.760429 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.658906 434.695667 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.658906 405.630905 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.658906 376.566143 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.658906 347.501381 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.658906 318.436619 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.658906 289.371857 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.658906 260.307095 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.658906 231.242333 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.658906 202.177571 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.658906 173.11281 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.658906 144.048048 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.658906 114.983286 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.658906 85.918524 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.658906 56.853762 
" clip-path="url(#pd767ebc78d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m1399c23567" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m1399c23567" x="472.426512" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(468.927137 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m1399c23567" x="525.146537" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(521.647162 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8575 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(216.128281 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_11 -->
      <g style="fill: #333333" transform="translate(218.598281 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(95.559375 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(210.327031 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- OxidationStates_range_oxidation_state -->
      <g style="fill: #333333" transform="translate(153.868437 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-72" x="848.779297"/>
       <use xlink:href="#DejaVuSans-61" x="889.892578"/>
       <use xlink:href="#DejaVuSans-6e" x="951.171875"/>
       <use xlink:href="#DejaVuSans-67" x="1014.550781"/>
       <use xlink:href="#DejaVuSans-65" x="1078.027344"/>
       <use xlink:href="#DejaVuSans-5f" x="1139.550781"/>
       <use xlink:href="#DejaVuSans-6f" x="1189.550781"/>
       <use xlink:href="#DejaVuSans-78" x="1247.607422"/>
       <use xlink:href="#DejaVuSans-69" x="1306.787109"/>
       <use xlink:href="#DejaVuSans-64" x="1334.570312"/>
       <use xlink:href="#DejaVuSans-61" x="1398.046875"/>
       <use xlink:href="#DejaVuSans-74" x="1459.326172"/>
       <use xlink:href="#DejaVuSans-69" x="1498.535156"/>
       <use xlink:href="#DejaVuSans-6f" x="1526.318359"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.5"/>
       <use xlink:href="#DejaVuSans-5f" x="1650.878906"/>
       <use xlink:href="#DejaVuSans-73" x="1700.878906"/>
       <use xlink:href="#DejaVuSans-74" x="1752.978516"/>
       <use xlink:href="#DejaVuSans-61" x="1792.1875"/>
       <use xlink:href="#DejaVuSans-74" x="1853.466797"/>
       <use xlink:href="#DejaVuSans-65" x="1892.675781"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(110.225 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_maximum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(46.547344 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-76" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.705078"/>
       <use xlink:href="#DejaVuSans-6c" x="2354.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2382.669922"/>
       <use xlink:href="#DejaVuSans-6d" x="2446.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2543.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2604.984375"/>
       <use xlink:href="#DejaVuSans-70" x="2654.984375"/>
       <use xlink:href="#DejaVuSans-61" x="2718.460938"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- XRDPowderPattern_xrd_22 -->
      <g style="fill: #333333" transform="translate(235.047344 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(95.679219 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- AtomicPackingEfficiency_dist_from_5_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-35" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_avg_dev_CovalentRadius -->
      <g style="fill: #333333" transform="translate(45.671875 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-43" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6f" x="2077.640625"/>
       <use xlink:href="#DejaVuSans-76" x="2138.822266"/>
       <use xlink:href="#DejaVuSans-61" x="2198.001953"/>
       <use xlink:href="#DejaVuSans-6c" x="2259.28125"/>
       <use xlink:href="#DejaVuSans-65" x="2287.064453"/>
       <use xlink:href="#DejaVuSans-6e" x="2348.587891"/>
       <use xlink:href="#DejaVuSans-74" x="2411.966797"/>
       <use xlink:href="#DejaVuSans-52" x="2451.175781"/>
       <use xlink:href="#DejaVuSans-61" x="2518.408203"/>
       <use xlink:href="#DejaVuSans-64" x="2579.6875"/>
       <use xlink:href="#DejaVuSans-69" x="2643.164062"/>
       <use xlink:href="#DejaVuSans-75" x="2670.947266"/>
       <use xlink:href="#DejaVuSans-73" x="2734.326172"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(95.679219 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(210.327031 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- VoronoiFingerprint_std_dev_Voro_vol_sum -->
      <g style="fill: #333333" transform="translate(136.395625 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-76" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-6f" x="1736.763672"/>
       <use xlink:href="#DejaVuSans-6c" x="1797.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1825.728516"/>
       <use xlink:href="#DejaVuSans-73" x="1875.728516"/>
       <use xlink:href="#DejaVuSans-75" x="1927.828125"/>
       <use xlink:href="#DejaVuSans-6d" x="1991.207031"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(218.598281 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(109.548594 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(288.225469 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(166.71 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(125.12625 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageed731a03f5" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature30_fold0 -->
    <g transform="translate(169.655906 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.548906 638.149 
L 539.178406 638.149 
L 539.178406 27.789 
L 531.548906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image147abf4a9c" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.678406 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.678406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.079344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pd767ebc78d">
   <rect x="431.418906" y="27.789" width="94.24" height="610.36"/>
  </clipPath>
 </defs>
</svg>
