<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="803.149594pt" height="679.5765pt" viewBox="0 0 803.149594 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T16:02:52.218574</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 803.149594 679.5765 
L 803.149594 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 445.958594 638.149 
L 528.174594 638.149 
L 528.174594 27.789 
L 445.958594 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 476.494708 638.149 
L 476.494708 27.789 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 445.958594 609.084238 
L 528.174594 609.084238 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 445.958594 580.019476 
L 528.174594 580.019476 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 445.958594 550.954714 
L 528.174594 550.954714 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 445.958594 521.889952 
L 528.174594 521.889952 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 445.958594 492.82519 
L 528.174594 492.82519 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 445.958594 463.760429 
L 528.174594 463.760429 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 445.958594 434.695667 
L 528.174594 434.695667 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 445.958594 405.630905 
L 528.174594 405.630905 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 445.958594 376.566143 
L 528.174594 376.566143 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 445.958594 347.501381 
L 528.174594 347.501381 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 445.958594 318.436619 
L 528.174594 318.436619 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 445.958594 289.371857 
L 528.174594 289.371857 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 445.958594 260.307095 
L 528.174594 260.307095 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 445.958594 231.242333 
L 528.174594 231.242333 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 445.958594 202.177571 
L 528.174594 202.177571 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 445.958594 173.11281 
L 528.174594 173.11281 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 445.958594 144.048048 
L 528.174594 144.048048 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 445.958594 114.983286 
L 528.174594 114.983286 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 445.958594 85.918524 
L 528.174594 85.918524 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 445.958594 56.853762 
L 528.174594 56.853762 
" clip-path="url(#p0a739ae169)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mb4bcf78025" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb4bcf78025" x="447.726129" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(434.370583 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mb4bcf78025" x="476.494708" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(467.747989 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mb4bcf78025" x="505.263286" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(496.516567 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(364.385187 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=4_43e+00 -->
      <g style="fill: #333333" transform="translate(119.205156 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-34" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-34" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-33" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_99 -->
      <g style="fill: #333333" transform="translate(233.137969 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- StructuralHeterogeneity_minimum_neighbor_distance_variation -->
      <g style="fill: #333333" transform="translate(7.2 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-69" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-6e" x="1383.6875"/>
       <use xlink:href="#DejaVuSans-69" x="1447.066406"/>
       <use xlink:href="#DejaVuSans-6d" x="1474.849609"/>
       <use xlink:href="#DejaVuSans-75" x="1572.261719"/>
       <use xlink:href="#DejaVuSans-6d" x="1635.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="1733.052734"/>
       <use xlink:href="#DejaVuSans-6e" x="1783.052734"/>
       <use xlink:href="#DejaVuSans-65" x="1846.431641"/>
       <use xlink:href="#DejaVuSans-69" x="1907.955078"/>
       <use xlink:href="#DejaVuSans-67" x="1935.738281"/>
       <use xlink:href="#DejaVuSans-68" x="1999.214844"/>
       <use xlink:href="#DejaVuSans-62" x="2062.59375"/>
       <use xlink:href="#DejaVuSans-6f" x="2126.070312"/>
       <use xlink:href="#DejaVuSans-72" x="2187.251953"/>
       <use xlink:href="#DejaVuSans-5f" x="2228.365234"/>
       <use xlink:href="#DejaVuSans-64" x="2278.365234"/>
       <use xlink:href="#DejaVuSans-69" x="2341.841797"/>
       <use xlink:href="#DejaVuSans-73" x="2369.625"/>
       <use xlink:href="#DejaVuSans-74" x="2421.724609"/>
       <use xlink:href="#DejaVuSans-61" x="2460.933594"/>
       <use xlink:href="#DejaVuSans-6e" x="2522.212891"/>
       <use xlink:href="#DejaVuSans-63" x="2585.591797"/>
       <use xlink:href="#DejaVuSans-65" x="2640.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="2702.095703"/>
       <use xlink:href="#DejaVuSans-76" x="2752.095703"/>
       <use xlink:href="#DejaVuSans-61" x="2811.275391"/>
       <use xlink:href="#DejaVuSans-72" x="2872.554688"/>
       <use xlink:href="#DejaVuSans-69" x="2913.667969"/>
       <use xlink:href="#DejaVuSans-61" x="2941.451172"/>
       <use xlink:href="#DejaVuSans-74" x="3002.730469"/>
       <use xlink:href="#DejaVuSans-69" x="3041.939453"/>
       <use xlink:href="#DejaVuSans-6f" x="3069.722656"/>
       <use xlink:href="#DejaVuSans-6e" x="3130.904297"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_NdValence -->
      <g style="fill: #333333" transform="translate(110.099062 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-64" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(171.442812 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_101 -->
      <g style="fill: #333333" transform="translate(224.866719 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- AtomicPackingEfficiency_dist_from_5_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(21.739687 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-35" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_range_CovalentRadius -->
      <g style="fill: #333333" transform="translate(76.770312 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-76" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2070.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2131.9375"/>
       <use xlink:href="#DejaVuSans-65" x="2159.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="2221.244141"/>
       <use xlink:href="#DejaVuSans-74" x="2284.623047"/>
       <use xlink:href="#DejaVuSans-52" x="2323.832031"/>
       <use xlink:href="#DejaVuSans-61" x="2391.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2452.34375"/>
       <use xlink:href="#DejaVuSans-69" x="2515.820312"/>
       <use xlink:href="#DejaVuSans-75" x="2543.603516"/>
       <use xlink:href="#DejaVuSans-73" x="2606.982422"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_161 -->
      <g style="fill: #333333" transform="translate(224.866719 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(85.923125 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(119.784062 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(67.654062 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(27.059531 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_179 -->
      <g style="fill: #333333" transform="translate(224.866719 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-39" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(81.466562 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- Stoichiometry_2-norm -->
      <g style="fill: #333333" transform="translate(278.359687 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-32" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(233.137969 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(94.962187 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(21.739687 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(110.218906 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 445.958594 638.149 
L 528.174594 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image240958a042" transform="scale(1 -1) translate(0 -578.16)" x="447.12" y="-43.2" width="79.92" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature49_fold0 -->
    <g transform="translate(178.183594 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-39" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 533.313094 638.149 
L 540.942594 638.149 
L 540.942594 27.789 
L 533.313094 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image900dfdeb36" transform="scale(1 -1) translate(0 -609.84)" x="533.52" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(544.442594 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(544.442594 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(578.843531 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p0a739ae169">
   <rect x="445.958594" y="27.789" width="82.216" height="610.36"/>
  </clipPath>
 </defs>
</svg>
