<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.621906pt" height="679.5765pt" viewBox="0 0 794.621906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:30:08.408208</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.621906 679.5765 
L 794.621906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
L 525.658906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 482.710997 638.149 
L 482.710997 27.789 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.658906 609.084238 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.658906 580.019476 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.658906 550.954714 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.658906 521.889952 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.658906 492.82519 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.658906 463.760429 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.658906 434.695667 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.658906 405.630905 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.658906 376.566143 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.658906 347.501381 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.658906 318.436619 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.658906 289.371857 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.658906 260.307095 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.658906 231.242333 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.658906 202.177571 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.658906 173.11281 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.658906 144.048048 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.658906 114.983286 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.658906 85.918524 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.658906 56.853762 
" clip-path="url(#p645ad310c9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m4543374ae6" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m4543374ae6" x="452.385771" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.25 -->
      <g style="fill: #333333" transform="translate(435.530849 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-32" x="179.199219"/>
       <use xlink:href="#DejaVuSans-35" x="242.822266"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m4543374ae6" x="482.710997" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(470.464903 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m4543374ae6" x="513.036223" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(500.790129 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8575 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(156.903125 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(68.358906 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(79.786719 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElectronegativityDiff_range_EN_difference -->
      <g style="fill: #333333" transform="translate(134.1125 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-72" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1115.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="1176.314453"/>
       <use xlink:href="#DejaVuSans-67" x="1239.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1303.169922"/>
       <use xlink:href="#DejaVuSans-5f" x="1364.693359"/>
       <use xlink:href="#DejaVuSans-45" x="1414.693359"/>
       <use xlink:href="#DejaVuSans-4e" x="1477.876953"/>
       <use xlink:href="#DejaVuSans-5f" x="1552.681641"/>
       <use xlink:href="#DejaVuSans-64" x="1602.681641"/>
       <use xlink:href="#DejaVuSans-69" x="1666.158203"/>
       <use xlink:href="#DejaVuSans-66" x="1693.941406"/>
       <use xlink:href="#DejaVuSans-66" x="1729.146484"/>
       <use xlink:href="#DejaVuSans-65" x="1764.351562"/>
       <use xlink:href="#DejaVuSans-72" x="1825.875"/>
       <use xlink:href="#DejaVuSans-65" x="1864.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="1926.261719"/>
       <use xlink:href="#DejaVuSans-63" x="1989.640625"/>
       <use xlink:href="#DejaVuSans-65" x="2044.621094"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(106.414375 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- AtomicPackingEfficiency_dist_from_3_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-33" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=5_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(21.74375 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-35" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(95.679219 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(285.469063 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- Stoichiometry_2-norm -->
      <g style="fill: #333333" transform="translate(263.82 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-32" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(166.71 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(145.66625 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(156.903125 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(15.479375 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- Miedema_Miedema_deltaH_ss_min -->
      <g style="fill: #333333" transform="translate(181.74125 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-69" x="86.279297"/>
       <use xlink:href="#DejaVuSans-65" x="114.0625"/>
       <use xlink:href="#DejaVuSans-64" x="175.585938"/>
       <use xlink:href="#DejaVuSans-65" x="239.0625"/>
       <use xlink:href="#DejaVuSans-6d" x="300.585938"/>
       <use xlink:href="#DejaVuSans-61" x="397.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="459.277344"/>
       <use xlink:href="#DejaVuSans-4d" x="509.277344"/>
       <use xlink:href="#DejaVuSans-69" x="595.556641"/>
       <use xlink:href="#DejaVuSans-65" x="623.339844"/>
       <use xlink:href="#DejaVuSans-64" x="684.863281"/>
       <use xlink:href="#DejaVuSans-65" x="748.339844"/>
       <use xlink:href="#DejaVuSans-6d" x="809.863281"/>
       <use xlink:href="#DejaVuSans-61" x="907.275391"/>
       <use xlink:href="#DejaVuSans-5f" x="968.554688"/>
       <use xlink:href="#DejaVuSans-64" x="1018.554688"/>
       <use xlink:href="#DejaVuSans-65" x="1082.03125"/>
       <use xlink:href="#DejaVuSans-6c" x="1143.554688"/>
       <use xlink:href="#DejaVuSans-74" x="1171.337891"/>
       <use xlink:href="#DejaVuSans-61" x="1210.546875"/>
       <use xlink:href="#DejaVuSans-48" x="1271.826172"/>
       <use xlink:href="#DejaVuSans-5f" x="1347.021484"/>
       <use xlink:href="#DejaVuSans-73" x="1397.021484"/>
       <use xlink:href="#DejaVuSans-73" x="1449.121094"/>
       <use xlink:href="#DejaVuSans-5f" x="1501.220703"/>
       <use xlink:href="#DejaVuSans-6d" x="1551.220703"/>
       <use xlink:href="#DejaVuSans-69" x="1648.632812"/>
       <use xlink:href="#DejaVuSans-6e" x="1676.416016"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(125.12625 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(80.4225 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(288.225469 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image6d2952e3f4" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature74_fold0 -->
    <g transform="translate(169.655906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-37" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-34" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.548906 638.149 
L 539.178406 638.149 
L 539.178406 27.789 
L 531.548906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image8df1f69bf0" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(542.678406 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(542.678406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(577.079344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p645ad310c9">
   <rect x="431.418906" y="27.789" width="94.24" height="610.36"/>
  </clipPath>
 </defs>
</svg>
