<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="772.857406pt" height="679.5765pt" viewBox="0 0 772.857406 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T11:13:45.358229</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 772.857406 679.5765 
L 772.857406 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
L 520.118406 27.789 
L 405.646406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 457.81991 638.149 
L 457.81991 27.789 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 405.646406 609.084238 
L 520.118406 609.084238 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 405.646406 580.019476 
L 520.118406 580.019476 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 405.646406 550.954714 
L 520.118406 550.954714 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 405.646406 521.889952 
L 520.118406 521.889952 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 405.646406 492.82519 
L 520.118406 492.82519 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 405.646406 463.760429 
L 520.118406 463.760429 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 405.646406 434.695667 
L 520.118406 434.695667 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 405.646406 405.630905 
L 520.118406 405.630905 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 405.646406 376.566143 
L 520.118406 376.566143 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 405.646406 347.501381 
L 520.118406 347.501381 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 405.646406 318.436619 
L 520.118406 318.436619 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 405.646406 289.371857 
L 520.118406 289.371857 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 405.646406 260.307095 
L 520.118406 260.307095 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 405.646406 231.242333 
L 520.118406 231.242333 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 405.646406 202.177571 
L 520.118406 202.177571 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 405.646406 173.11281 
L 520.118406 173.11281 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 405.646406 144.048048 
L 520.118406 144.048048 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 405.646406 114.983286 
L 520.118406 114.983286 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 405.646406 85.918524 
L 520.118406 85.918524 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 405.646406 56.853762 
L 520.118406 56.853762 
" clip-path="url(#p82e70c09da)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mbcfe96b1e0" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mbcfe96b1e0" x="412.419701" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(404.311498 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mbcfe96b1e0" x="457.81991" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(454.320535 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mbcfe96b1e0" x="503.220119" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(499.720744 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(340.201 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(192.825781 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_63 -->
      <g style="fill: #333333" transform="translate(192.825781 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(115.329531 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(131.120469 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(97.738906 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElectronegativityDiff_range_EN_difference -->
      <g style="fill: #333333" transform="translate(108.34 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-72" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1115.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="1176.314453"/>
       <use xlink:href="#DejaVuSans-67" x="1239.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1303.169922"/>
       <use xlink:href="#DejaVuSans-5f" x="1364.693359"/>
       <use xlink:href="#DejaVuSans-45" x="1414.693359"/>
       <use xlink:href="#DejaVuSans-4e" x="1477.876953"/>
       <use xlink:href="#DejaVuSans-5f" x="1552.681641"/>
       <use xlink:href="#DejaVuSans-64" x="1602.681641"/>
       <use xlink:href="#DejaVuSans-69" x="1666.158203"/>
       <use xlink:href="#DejaVuSans-66" x="1693.941406"/>
       <use xlink:href="#DejaVuSans-66" x="1729.146484"/>
       <use xlink:href="#DejaVuSans-65" x="1764.351562"/>
       <use xlink:href="#DejaVuSans-72" x="1825.875"/>
       <use xlink:href="#DejaVuSans-65" x="1864.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="1926.261719"/>
       <use xlink:href="#DejaVuSans-63" x="1989.640625"/>
       <use xlink:href="#DejaVuSans-65" x="2044.621094"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(45.610937 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(192.825781 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_mode_Column -->
      <g style="fill: #333333" transform="translate(88.827812 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-6c" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-75" x="2032.083984"/>
       <use xlink:href="#DejaVuSans-6d" x="2095.462891"/>
       <use xlink:href="#DejaVuSans-6e" x="2192.875"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(192.825781 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(42.799687 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(184.554531 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- CrystalNNFingerprint_mean_water-like_CN_2 -->
      <g style="fill: #333333" transform="translate(93.4875 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-61" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-74" x="1569.263672"/>
       <use xlink:href="#DejaVuSans-65" x="1608.472656"/>
       <use xlink:href="#DejaVuSans-72" x="1669.996094"/>
       <use xlink:href="#DejaVuSans-2d" x="1704.734375"/>
       <use xlink:href="#DejaVuSans-6c" x="1740.818359"/>
       <use xlink:href="#DejaVuSans-69" x="1768.601562"/>
       <use xlink:href="#DejaVuSans-6b" x="1796.384766"/>
       <use xlink:href="#DejaVuSans-65" x="1850.669922"/>
       <use xlink:href="#DejaVuSans-5f" x="1912.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1962.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="2032.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="2106.822266"/>
       <use xlink:href="#DejaVuSans-32" x="2156.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(79.471875 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(69.906719 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(83.776094 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(262.452969 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(140.9375 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(99.35375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image7f8896f81c" transform="scale(1 -1) translate(0 -578.16)" x="408.24" y="-43.2" width="108.72" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature6_fold0 -->
    <g transform="translate(160.107406 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.272906 638.149 
L 534.902406 638.149 
L 534.902406 27.789 
L 527.272906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image500afd3192" transform="scale(1 -1) translate(0 -609.84)" x="527.04" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(538.402406 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(538.402406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(572.803344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p82e70c09da">
   <rect x="405.646406" y="27.789" width="114.472" height="610.36"/>
  </clipPath>
 </defs>
</svg>
