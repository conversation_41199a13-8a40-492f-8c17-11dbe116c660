<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="766.387187pt" height="679.5765pt" viewBox="0 0 766.387187 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T10:50:20.998349</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 766.387187 679.5765 
L 766.387187 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 384.572187 638.149 
L 516.036187 638.149 
L 516.036187 27.789 
L 384.572187 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 442.46663 638.149 
L 442.46663 27.789 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 384.572187 609.084238 
L 516.036187 609.084238 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 384.572187 580.019476 
L 516.036187 580.019476 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 384.572187 550.954714 
L 516.036187 550.954714 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 384.572187 521.889952 
L 516.036187 521.889952 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 384.572187 492.82519 
L 516.036187 492.82519 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 384.572187 463.760429 
L 516.036187 463.760429 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 384.572187 434.695667 
L 516.036187 434.695667 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 384.572187 405.630905 
L 516.036187 405.630905 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 384.572187 376.566143 
L 516.036187 376.566143 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 384.572187 347.501381 
L 516.036187 347.501381 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 384.572187 318.436619 
L 516.036187 318.436619 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 384.572187 289.371857 
L 516.036187 289.371857 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 384.572187 260.307095 
L 516.036187 260.307095 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 384.572187 231.242333 
L 516.036187 231.242333 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 384.572187 202.177571 
L 516.036187 202.177571 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 384.572187 173.11281 
L 516.036187 173.11281 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 384.572187 144.048048 
L 516.036187 144.048048 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 384.572187 114.983286 
L 516.036187 114.983286 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 384.572187 85.918524 
L 516.036187 85.918524 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 384.572187 56.853762 
L 516.036187 56.853762 
" clip-path="url(#pf7c31d4e37)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="md0364e7da6" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#md0364e7da6" x="394.531627" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(386.423424 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#md0364e7da6" x="442.46663" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(438.967255 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#md0364e7da6" x="490.401632" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(486.902257 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(327.622781 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- VoronoiFingerprint_std_dev_Voro_area_maximum -->
      <g style="fill: #333333" transform="translate(41.999375 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-61" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-78" x="2109.220703"/>
       <use xlink:href="#DejaVuSans-69" x="2168.400391"/>
       <use xlink:href="#DejaVuSans-6d" x="2196.183594"/>
       <use xlink:href="#DejaVuSans-75" x="2293.595703"/>
       <use xlink:href="#DejaVuSans-6d" x="2356.974609"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(240.602812 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(51.060781 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-69" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-6e" x="2035.347656"/>
       <use xlink:href="#DejaVuSans-69" x="2098.726562"/>
       <use xlink:href="#DejaVuSans-6d" x="2126.509766"/>
       <use xlink:href="#DejaVuSans-75" x="2223.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="2287.300781"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(64.022187 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-38" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-30" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-30" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2d" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2157.714844"/>
       <use xlink:href="#DejaVuSans-31" x="2221.337891"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_71 -->
      <g style="fill: #333333" transform="translate(171.751562 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_mode_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(42.029844 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(63.378281 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(156.978281 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_wt_CN_18 -->
      <g style="fill: #333333" transform="translate(111.592031 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-31" x="1791.822266"/>
       <use xlink:href="#DejaVuSans-38" x="1855.445312"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_63 -->
      <g style="fill: #333333" transform="translate(171.751562 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(48.8325 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- GaussianSymmFunc_std_dev_G2_20_0 -->
      <g style="fill: #333333" transform="translate(110.04625 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-32" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- CrystalNNFingerprint_mean_water-like_CN_2 -->
      <g style="fill: #333333" transform="translate(72.413281 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-61" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-74" x="1569.263672"/>
       <use xlink:href="#DejaVuSans-65" x="1608.472656"/>
       <use xlink:href="#DejaVuSans-72" x="1669.996094"/>
       <use xlink:href="#DejaVuSans-2d" x="1704.734375"/>
       <use xlink:href="#DejaVuSans-6c" x="1740.818359"/>
       <use xlink:href="#DejaVuSans-69" x="1768.601562"/>
       <use xlink:href="#DejaVuSans-6b" x="1796.384766"/>
       <use xlink:href="#DejaVuSans-65" x="1850.669922"/>
       <use xlink:href="#DejaVuSans-5f" x="1912.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1962.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="2032.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="2106.822266"/>
       <use xlink:href="#DejaVuSans-32" x="2156.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(48.8325 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(48.8325 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(62.701875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(241.37875 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(119.863281 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(78.279531 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 384.572187 638.149 
L 516.036187 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagef736fddf15" transform="scale(1 -1) translate(0 -578.16)" x="388.08" y="-43.2" width="124.56" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature63_fold0 -->
    <g transform="translate(141.421188 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-33" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 524.252687 638.149 
L 531.882187 638.149 
L 531.882187 27.789 
L 524.252687 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image50fdf9d309" transform="scale(1 -1) translate(0 -609.84)" x="524.16" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(535.382187 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(535.382187 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(569.783125 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pf7c31d4e37">
   <rect x="384.572187" y="27.789" width="131.464" height="610.36"/>
  </clipPath>
 </defs>
</svg>
