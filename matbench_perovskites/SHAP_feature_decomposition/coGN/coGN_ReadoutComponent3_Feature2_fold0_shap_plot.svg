<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="785.174062pt" height="679.5765pt" viewBox="0 0 785.174062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:20:06.832144</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 785.174062 679.5765 
L 785.174062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 450.539591 638.149 
L 450.539591 27.789 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#pa8d022d358)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="maf4c6adff4" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#maf4c6adff4" x="450.539591" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(447.040216 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#maf4c6adff4" x="512.549483" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(509.050108 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(93.832812 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_q2_CN_12 -->
      <g style="fill: #333333" transform="translate(152.322656 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-71" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-32" x="1489.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="1553.296875"/>
       <use xlink:href="#DejaVuSans-43" x="1603.296875"/>
       <use xlink:href="#DejaVuSans-4e" x="1673.121094"/>
       <use xlink:href="#DejaVuSans-5f" x="1747.925781"/>
       <use xlink:href="#DejaVuSans-31" x="1797.925781"/>
       <use xlink:href="#DejaVuSans-32" x="1861.548828"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_NpUnfilled -->
      <g style="fill: #333333" transform="translate(91.671562 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-55" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2084.769531"/>
       <use xlink:href="#DejaVuSans-66" x="2148.148438"/>
       <use xlink:href="#DejaVuSans-69" x="2183.353516"/>
       <use xlink:href="#DejaVuSans-6c" x="2211.136719"/>
       <use xlink:href="#DejaVuSans-6c" x="2238.919922"/>
       <use xlink:href="#DejaVuSans-65" x="2266.703125"/>
       <use xlink:href="#DejaVuSans-64" x="2328.226562"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(147.689375 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_maximum_CovalentRadius -->
      <g style="fill: #333333" transform="translate(29.212656 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-43" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-6f" x="2163.382812"/>
       <use xlink:href="#DejaVuSans-76" x="2224.564453"/>
       <use xlink:href="#DejaVuSans-61" x="2283.744141"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.023438"/>
       <use xlink:href="#DejaVuSans-65" x="2372.806641"/>
       <use xlink:href="#DejaVuSans-6e" x="2434.330078"/>
       <use xlink:href="#DejaVuSans-74" x="2497.708984"/>
       <use xlink:href="#DejaVuSans-52" x="2536.917969"/>
       <use xlink:href="#DejaVuSans-61" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-64" x="2665.429688"/>
       <use xlink:href="#DejaVuSans-69" x="2728.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2756.689453"/>
       <use xlink:href="#DejaVuSans-73" x="2820.068359"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(151.583281 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_mode_NUnfilled -->
      <g style="fill: #333333" transform="translate(99.9225 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(161.390156 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(151.583281 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(237.929688 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mode_MeltingT -->
      <g style="fill: #333333" transform="translate(103.097344 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(53.959375 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_mean_Column -->
      <g style="fill: #333333" transform="translate(109.2825 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-6c" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-75" x="2032.083984"/>
       <use xlink:href="#DejaVuSans-6d" x="2095.462891"/>
       <use xlink:href="#DejaVuSans-6e" x="2192.875"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(280.149219 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(183.356094 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(102.165 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(119.806406 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(11.010625 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image9076ef609e" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature2_fold0 -->
    <g transform="translate(172.424063 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-32" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagef2d7bc7b7f" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pa8d022d358">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
