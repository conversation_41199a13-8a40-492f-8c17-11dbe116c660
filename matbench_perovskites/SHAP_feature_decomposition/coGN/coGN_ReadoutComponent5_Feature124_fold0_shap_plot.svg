<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="792.054156pt" height="679.5765pt" viewBox="0 0 792.054156 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T02:53:28.545461</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 792.054156 679.5765 
L 792.054156 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
L 522.851156 27.789 
L 416.875156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 462.076675 638.149 
L 462.076675 27.789 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 416.875156 609.084238 
L 522.851156 609.084238 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 416.875156 580.019476 
L 522.851156 580.019476 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 416.875156 550.954714 
L 522.851156 550.954714 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 416.875156 521.889952 
L 522.851156 521.889952 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 416.875156 492.82519 
L 522.851156 492.82519 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 416.875156 463.760429 
L 522.851156 463.760429 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 416.875156 434.695667 
L 522.851156 434.695667 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 416.875156 405.630905 
L 522.851156 405.630905 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 416.875156 376.566143 
L 522.851156 376.566143 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 416.875156 347.501381 
L 522.851156 347.501381 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 416.875156 318.436619 
L 522.851156 318.436619 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 416.875156 289.371857 
L 522.851156 289.371857 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 416.875156 260.307095 
L 522.851156 260.307095 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 416.875156 231.242333 
L 522.851156 231.242333 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 416.875156 202.177571 
L 522.851156 202.177571 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 416.875156 173.11281 
L 522.851156 173.11281 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 416.875156 144.048048 
L 522.851156 144.048048 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 416.875156 114.983286 
L 522.851156 114.983286 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 416.875156 85.918524 
L 522.851156 85.918524 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 416.875156 56.853762 
L 522.851156 56.853762 
" clip-path="url(#pe3d3326336)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m1759db75be" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m1759db75be" x="427.721366" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(419.613163 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m1759db75be" x="462.076675" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(458.5773 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m1759db75be" x="496.431984" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(492.932609 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(347.18175 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_bent_150_degrees_CN_2 -->
      <g style="fill: #333333" transform="translate(48.16625 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-62" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1489.673828"/>
       <use xlink:href="#DejaVuSans-6e" x="1551.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1614.576172"/>
       <use xlink:href="#DejaVuSans-5f" x="1653.785156"/>
       <use xlink:href="#DejaVuSans-31" x="1703.785156"/>
       <use xlink:href="#DejaVuSans-35" x="1767.408203"/>
       <use xlink:href="#DejaVuSans-30" x="1831.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1894.654297"/>
       <use xlink:href="#DejaVuSans-64" x="1944.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2008.130859"/>
       <use xlink:href="#DejaVuSans-67" x="2069.654297"/>
       <use xlink:href="#DejaVuSans-72" x="2133.130859"/>
       <use xlink:href="#DejaVuSans-65" x="2171.994141"/>
       <use xlink:href="#DejaVuSans-65" x="2233.517578"/>
       <use xlink:href="#DejaVuSans-73" x="2295.041016"/>
       <use xlink:href="#DejaVuSans-5f" x="2347.140625"/>
       <use xlink:href="#DejaVuSans-43" x="2397.140625"/>
       <use xlink:href="#DejaVuSans-4e" x="2466.964844"/>
       <use xlink:href="#DejaVuSans-5f" x="2541.769531"/>
       <use xlink:href="#DejaVuSans-32" x="2591.769531"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_0 -->
      <g style="fill: #333333" transform="translate(81.135469 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-30" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(138.465469 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(189.28125 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- OPSiteFingerprint_mean_square_co-planar_CN_4 -->
      <g style="fill: #333333" transform="translate(77.678281 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="923.511719"/>
       <use xlink:href="#DejaVuSans-65" x="1020.923828"/>
       <use xlink:href="#DejaVuSans-61" x="1082.447266"/>
       <use xlink:href="#DejaVuSans-6e" x="1143.726562"/>
       <use xlink:href="#DejaVuSans-5f" x="1207.105469"/>
       <use xlink:href="#DejaVuSans-73" x="1257.105469"/>
       <use xlink:href="#DejaVuSans-71" x="1309.205078"/>
       <use xlink:href="#DejaVuSans-75" x="1372.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1436.060547"/>
       <use xlink:href="#DejaVuSans-72" x="1497.339844"/>
       <use xlink:href="#DejaVuSans-65" x="1536.203125"/>
       <use xlink:href="#DejaVuSans-5f" x="1597.726562"/>
       <use xlink:href="#DejaVuSans-63" x="1647.726562"/>
       <use xlink:href="#DejaVuSans-6f" x="1702.707031"/>
       <use xlink:href="#DejaVuSans-2d" x="1765.763672"/>
       <use xlink:href="#DejaVuSans-70" x="1801.847656"/>
       <use xlink:href="#DejaVuSans-6c" x="1865.324219"/>
       <use xlink:href="#DejaVuSans-61" x="1893.107422"/>
       <use xlink:href="#DejaVuSans-6e" x="1954.386719"/>
       <use xlink:href="#DejaVuSans-61" x="2017.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2079.044922"/>
       <use xlink:href="#DejaVuSans-5f" x="2120.158203"/>
       <use xlink:href="#DejaVuSans-43" x="2170.158203"/>
       <use xlink:href="#DejaVuSans-4e" x="2239.982422"/>
       <use xlink:href="#DejaVuSans-5f" x="2314.787109"/>
       <use xlink:href="#DejaVuSans-34" x="2364.787109"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(204.054531 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=3_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(7.2 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-33" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(65.242969 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(195.783281 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(204.054531 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- CrystalNNFingerprint_mean_hexagonal_planar_CN_6 -->
      <g style="fill: #333333" transform="translate(52.318125 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-68" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-78" x="1549.349609"/>
       <use xlink:href="#DejaVuSans-61" x="1608.529297"/>
       <use xlink:href="#DejaVuSans-67" x="1669.808594"/>
       <use xlink:href="#DejaVuSans-6f" x="1733.285156"/>
       <use xlink:href="#DejaVuSans-6e" x="1794.466797"/>
       <use xlink:href="#DejaVuSans-61" x="1857.845703"/>
       <use xlink:href="#DejaVuSans-6c" x="1919.125"/>
       <use xlink:href="#DejaVuSans-5f" x="1946.908203"/>
       <use xlink:href="#DejaVuSans-70" x="1996.908203"/>
       <use xlink:href="#DejaVuSans-6c" x="2060.384766"/>
       <use xlink:href="#DejaVuSans-61" x="2088.167969"/>
       <use xlink:href="#DejaVuSans-6e" x="2149.447266"/>
       <use xlink:href="#DejaVuSans-61" x="2212.826172"/>
       <use xlink:href="#DejaVuSans-72" x="2274.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="2315.21875"/>
       <use xlink:href="#DejaVuSans-43" x="2365.21875"/>
       <use xlink:href="#DejaVuSans-4e" x="2435.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="2509.847656"/>
       <use xlink:href="#DejaVuSans-36" x="2559.847656"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(195.783281 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(142.359375 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- VoronoiFingerprint_mean_Symmetry_index_5 -->
      <g style="fill: #333333" transform="translate(100.085 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-53" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-79" x="1363.804688"/>
       <use xlink:href="#DejaVuSans-6d" x="1422.984375"/>
       <use xlink:href="#DejaVuSans-6d" x="1520.396484"/>
       <use xlink:href="#DejaVuSans-65" x="1617.808594"/>
       <use xlink:href="#DejaVuSans-74" x="1679.332031"/>
       <use xlink:href="#DejaVuSans-72" x="1718.541016"/>
       <use xlink:href="#DejaVuSans-79" x="1759.654297"/>
       <use xlink:href="#DejaVuSans-5f" x="1818.833984"/>
       <use xlink:href="#DejaVuSans-69" x="1868.833984"/>
       <use xlink:href="#DejaVuSans-6e" x="1896.617188"/>
       <use xlink:href="#DejaVuSans-64" x="1959.996094"/>
       <use xlink:href="#DejaVuSans-65" x="2023.472656"/>
       <use xlink:href="#DejaVuSans-78" x="2083.246094"/>
       <use xlink:href="#DejaVuSans-5f" x="2142.425781"/>
       <use xlink:href="#DejaVuSans-35" x="2192.425781"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(81.135469 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(81.135469 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(95.004844 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(273.681719 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(152.16625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(110.5825 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image1af150acdd" transform="scale(1 -1) translate(0 -578.16)" x="419.04" y="-43.2" width="101.52" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature124_fold0 -->
    <g transform="translate(154.872156 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-34" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.474656 638.149 
L 537.104156 638.149 
L 537.104156 27.789 
L 529.474656 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imageff0378c5cd" transform="scale(1 -1) translate(0 -609.84)" x="529.2" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(540.604156 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(540.604156 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.005094 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pe3d3326336">
   <rect x="416.875156" y="27.789" width="105.976" height="610.36"/>
  </clipPath>
 </defs>
</svg>
