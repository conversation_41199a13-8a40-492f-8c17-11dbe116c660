<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:11:59.771991</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 459.132369 638.149 
L 459.132369 27.789 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#pab11fe2314)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mf8974ad42f" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mf8974ad42f" x="459.132369" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(455.632994 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mf8974ad42f" x="517.507706" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(514.008331 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(213.278437 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(140.346406 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- StructuralHeterogeneity_mean_neighbor_distance_variation -->
      <g style="fill: #333333" transform="translate(12.162344 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-65" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-61" x="1417.427734"/>
       <use xlink:href="#DejaVuSans-6e" x="1478.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="1542.085938"/>
       <use xlink:href="#DejaVuSans-6e" x="1592.085938"/>
       <use xlink:href="#DejaVuSans-65" x="1655.464844"/>
       <use xlink:href="#DejaVuSans-69" x="1716.988281"/>
       <use xlink:href="#DejaVuSans-67" x="1744.771484"/>
       <use xlink:href="#DejaVuSans-68" x="1808.248047"/>
       <use xlink:href="#DejaVuSans-62" x="1871.626953"/>
       <use xlink:href="#DejaVuSans-6f" x="1935.103516"/>
       <use xlink:href="#DejaVuSans-72" x="1996.285156"/>
       <use xlink:href="#DejaVuSans-5f" x="2037.398438"/>
       <use xlink:href="#DejaVuSans-64" x="2087.398438"/>
       <use xlink:href="#DejaVuSans-69" x="2150.875"/>
       <use xlink:href="#DejaVuSans-73" x="2178.658203"/>
       <use xlink:href="#DejaVuSans-74" x="2230.757812"/>
       <use xlink:href="#DejaVuSans-61" x="2269.966797"/>
       <use xlink:href="#DejaVuSans-6e" x="2331.246094"/>
       <use xlink:href="#DejaVuSans-63" x="2394.625"/>
       <use xlink:href="#DejaVuSans-65" x="2449.605469"/>
       <use xlink:href="#DejaVuSans-5f" x="2511.128906"/>
       <use xlink:href="#DejaVuSans-76" x="2561.128906"/>
       <use xlink:href="#DejaVuSans-61" x="2620.308594"/>
       <use xlink:href="#DejaVuSans-72" x="2681.587891"/>
       <use xlink:href="#DejaVuSans-69" x="2722.701172"/>
       <use xlink:href="#DejaVuSans-61" x="2750.484375"/>
       <use xlink:href="#DejaVuSans-74" x="2811.763672"/>
       <use xlink:href="#DejaVuSans-69" x="2850.972656"/>
       <use xlink:href="#DejaVuSans-6f" x="2878.755859"/>
       <use xlink:href="#DejaVuSans-6e" x="2939.9375"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_maximum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(41.2275 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-76" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.705078"/>
       <use xlink:href="#DejaVuSans-6c" x="2354.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2382.669922"/>
       <use xlink:href="#DejaVuSans-6d" x="2446.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2543.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2604.984375"/>
       <use xlink:href="#DejaVuSans-70" x="2654.984375"/>
       <use xlink:href="#DejaVuSans-61" x="2718.460938"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(161.390156 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MaximumPackingEfficiency_max_packing_efficiency -->
      <g style="fill: #333333" transform="translate(64.3025 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-61" x="86.279297"/>
       <use xlink:href="#DejaVuSans-78" x="147.558594"/>
       <use xlink:href="#DejaVuSans-69" x="206.738281"/>
       <use xlink:href="#DejaVuSans-6d" x="234.521484"/>
       <use xlink:href="#DejaVuSans-75" x="331.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="395.3125"/>
       <use xlink:href="#DejaVuSans-50" x="492.724609"/>
       <use xlink:href="#DejaVuSans-61" x="548.527344"/>
       <use xlink:href="#DejaVuSans-63" x="609.806641"/>
       <use xlink:href="#DejaVuSans-6b" x="664.787109"/>
       <use xlink:href="#DejaVuSans-69" x="722.697266"/>
       <use xlink:href="#DejaVuSans-6e" x="750.480469"/>
       <use xlink:href="#DejaVuSans-67" x="813.859375"/>
       <use xlink:href="#DejaVuSans-45" x="877.335938"/>
       <use xlink:href="#DejaVuSans-66" x="940.519531"/>
       <use xlink:href="#DejaVuSans-66" x="975.724609"/>
       <use xlink:href="#DejaVuSans-69" x="1010.929688"/>
       <use xlink:href="#DejaVuSans-63" x="1038.712891"/>
       <use xlink:href="#DejaVuSans-69" x="1093.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1121.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="1183"/>
       <use xlink:href="#DejaVuSans-63" x="1246.378906"/>
       <use xlink:href="#DejaVuSans-79" x="1301.359375"/>
       <use xlink:href="#DejaVuSans-5f" x="1360.539062"/>
       <use xlink:href="#DejaVuSans-6d" x="1410.539062"/>
       <use xlink:href="#DejaVuSans-61" x="1507.951172"/>
       <use xlink:href="#DejaVuSans-78" x="1569.230469"/>
       <use xlink:href="#DejaVuSans-5f" x="1628.410156"/>
       <use xlink:href="#DejaVuSans-70" x="1678.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1741.886719"/>
       <use xlink:href="#DejaVuSans-63" x="1803.166016"/>
       <use xlink:href="#DejaVuSans-6b" x="1858.146484"/>
       <use xlink:href="#DejaVuSans-69" x="1916.056641"/>
       <use xlink:href="#DejaVuSans-6e" x="1943.839844"/>
       <use xlink:href="#DejaVuSans-67" x="2007.21875"/>
       <use xlink:href="#DejaVuSans-5f" x="2070.695312"/>
       <use xlink:href="#DejaVuSans-65" x="2120.695312"/>
       <use xlink:href="#DejaVuSans-66" x="2182.21875"/>
       <use xlink:href="#DejaVuSans-66" x="2217.423828"/>
       <use xlink:href="#DejaVuSans-69" x="2252.628906"/>
       <use xlink:href="#DejaVuSans-63" x="2280.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2335.392578"/>
       <use xlink:href="#DejaVuSans-65" x="2363.175781"/>
       <use xlink:href="#DejaVuSans-6e" x="2424.699219"/>
       <use xlink:href="#DejaVuSans-63" x="2488.078125"/>
       <use xlink:href="#DejaVuSans-79" x="2543.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- XRDPowderPattern_xrd_39 -->
      <g style="fill: #333333" transform="translate(229.7275 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-39" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=5_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(16.423906 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-35" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(135.782188 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(151.583281 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(90.359375 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(66.063594 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(99.924531 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(75.102656 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- GeneralizedRDF_mean_Gaussian_center=5_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(30.124687 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-35" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(282.905625 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(210.808437 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_avg_dev_MeltingT -->
      <g style="fill: #333333" transform="translate(85.60625 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-74" x="2183.402344"/>
       <use xlink:href="#DejaVuSans-69" x="2222.611328"/>
       <use xlink:href="#DejaVuSans-6e" x="2250.394531"/>
       <use xlink:href="#DejaVuSans-67" x="2313.773438"/>
       <use xlink:href="#DejaVuSans-54" x="2377.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(102.165 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAIIAAAMjCAYAAABpq8grAACBjklEQVR4nOydd5gcV5W331upc5isCcrRSXLOBoNtojFYJJPjEswSFgQsywJLToIlLCYs8BGXaJGTccA5ybZsS7KV40iTp3OodL8/ujWa0YywLfVM17TqfZ6x3JX6VNWvzz03nSuklHiZxyMfNh5oPykrBJpuuS996b7Xrqu3TY2I8LIQrm//zn8258UnmwpFANLBIDu7o+Ybtr8yUGfTGg6t3gb8M6Jl7ZMthdzY56ZSiZ5+1aijSQ2LUm8D/hnxQnnytlKZHy361bl1MKeh8bRHsFWVtNDI6EGkgKhVRpcWlK2metvWaHjaI9iqYDgcQUZc1LDNaCiMrajkQtrCetvWaHhaCCVNRbZJ9s1v52B3C+3BUTKRAA7qN+ptW6Ph6aJhqDvBlq4utrQ2U9I1enrm8vL77wWlWdTbtkbDs0L4yYKfX7pv7hxuWjwfS1UBOBiLEjJX0tqfe4KzfZ4qni0auvbt+vaeZIwzD2zhsh0PEDZLAKyfO5diwK9B1hrPeoQuyss+9o9v05UbAsAUOr9fdAU/OO8SevpH/KKhxnhWCE3uKB1VEQAY0uLKHbezuC/D/S3z6mhZY+LZokFTi5O3UaItnydiT25o8jk+POkRSuLtSiEaoOWImLBMFICtzU0o7xuR0hBSc5Wg9fmEWQczGwpPeYSMeMn6jHi1NYxhteWKgMqhLrEyYdJ00xuPUSJAxHR4xkBGJB27LP4znREfzkXraftspz4eQaxWbERRRRoALhoWCQKE0CgS4QAuGhUhBMiRoESc0UiIR3oWkpkTwVIV/tEU56zRDE5nMjaaDGTF502wHLABARgqqGNx5WMo4oVyjbZNrLXfBrwFcIHr5Brt+/V4DF6iLt3QjlidViA+PvSXCAQTbXFRKNNESYmwq2kO93SdSkHXuO70k9mVjFcOUqDFsRnuSlY+F22wJUQ0EBO/AehFiI8B3z3CpJfLNdova3mPs406FQ1K/Mj632QRqKRZSIE2XDfMvOEM81JD3LCg57AIKgcyrOvguJXPtqx4ATH5G4Bu4F1TGPS647qdBqBOQnjiZoAyCVz0CWec0r+X3fHY5INtCY58speeqlky/4RnNTh1EYLE2XxkgeSOCwwrnyeHL0HbYl5mivcoAL16K6qoiMI58hskwP3AxwBn3A4b+PpTuoEGpC5C0OS6U1x43KXyemwMCrSSpw2LEC4KOtlJ592wbBm7uppBGfezlxIMUSkaynblsyKhZFcCR1eClC7wXYS4XK7RbgSeBvw/4HvARXKNdvtM3LeX8daYRbFa5AksGyLxiw7kKhedMhEcFP540kn869VXkw8YtKYLVNocBSAhovfIT0R762v87MZbQhjHoPiAtDSFsqoxoifYFepm25wWNMclE4vwqUtPB8cx5Reb/IGsNcCTLYsANgHub1mMJYNj25b2j+IKQW44h/zcpIqHz3HgWSFY6Oxta6WkBjjYmiRSLLN8bx+2rlKWvgZqjWeF4AA7u9vZMrdrbNvjCzt55vqN7E4kvVmezWI8K4SdauAPO+e0v+BlG27g6kdvIWCb3LT0XP40/xIs0663eQ2HZ4WQb2//9wv3bHjB69f/cWzb8x+/E60sWZ88uY6WNSaeFcJVB96wuafrw5O2n7t/E8NiTh0samw81Q19JM2ZKVp+pUrKVv0YocZ4WghC0TDF4f4GB4WSGmegrfkldTSrIfG0EB7pPhlVhiiLKAURxyXKgwtPIVp2T/hOolrj2RgB4I6FK4mXc5yzdxMAd887hdsWrcIpWtvrbFrD4dkmZoD/fNrtsticQFRtlAiah0f58B1P91uUaoynPYIqWaxnSjt0rTJwxbEkwaHMhnrb1Yh42iMc4jPn/P0riuWuTNrmZW/b+ALvGzwLmRVC8Jl+PF1r8Jk5fCH4AL4QfKr4QvABPF59PMTCt/UOz0E0h6SkoAiGXLew/ds9kXrb1Uh43iMsubb37IWS5qTjEnQlTbZLDyI85+0H31Jv2xoJzwtBSO4NuHJs3ooAQq4kropv1dOuRsPzQnA0bdLkJQGUq3mVfGqD54UwEtAnTEuCynjG7KS5jT7Hg+eFkAtqYlMigl1977aATc0xVOEroZZ4vtag2VL2RoKiXyhEbYecqmBrGu2u3zReSzzvEeJlE4oWNpDSVGwhwLQZVn2HUEs8LYT12me2v/+mfyhM0THm2m4dLGpcPCuEbeK9DwdVZ/G1j95ETyY1aX9z0c+fVUs8KwSF0Mp2ZxSbIB+/+RYC9rhJLVJSVFWWvvPgJfWzsLHwpBD2izdlhmlFc0xSxLl4+wG++Ye/0SksEkEJIZ2CrhFI535ab1sbBU8KIYoWW8n9WGjEGCJCmrN3DPC2mx8kHYlAxCBgKAihdNbb1kbBk9VHgY2OShujgMtPLjqLLz79BWQCYQzbJmw5SENhrmn7zYs1YkaHqt0ofqiBfFGQ0neiWiYZkAWZVluF7pQ4xbkfA0uYBDBQARVwuW3hUp75tn+nO1uiK1tkIBKgLxJElZJw2Wbp3gFZ1gwRdB1WDqUIua7sD4e2pcPBV/3hBwvXz9jNzXJmTAg3ih8K4G/AFSt4nBg5tovlBKTJMu5Hp4wkRMVJOQhKCFxAsKu5k29c8jr6Y838+uQeSvo4R2A5kLEAiFg2b9qyi6CUZAMBBsLBl/76x4t/PSM3OMuZyRjhcuCKMHnmMMAebT6alDRzEIMyoFMRgYugUBUBgGThyEFesuGv9MZDE0UAoKtjybXyusaGliaixRKKdIlb9jdn7vZmNzMphLkAQSqZ1S1ZmdOoU1mQQ1ZNEZhTpEqULBreh+5M0YgkJeMbnEpa5TqKEOiunCIpo89UzKQQbgBppUlgoxITGQAytAIgxvoYpy6qdjf30Jkr0XLkWpCme/gUKVmRymJpGq6U5DX1gWm4j4ZkxoRwuXzdfhAvl4jSo5xCu92P0MpkaKaXxUgcwJwy0aZEEM/bGLbF87Yc4Py9gywazhLNlWgbyROxbFpLZa7ae5B52TyjQYOcYRxMWPalM3V/s50Zn+BSDRoDMSN1elKMbhkUrUtVW5jn2jcpwG8tRI9GUKiUAYmLgUMMULhl6dnctvRMbAG/m99FS6FAfGh0oDsQapNl0w46Vr4UjZ4sFSX1/Z8vn7zyh89R8eRMp6J4nVQJcmRi5T1Nc/jhBVdRVgS/XNDNvOFh9/b/O8lvS6gBnmxQyhGUScpCEpyw/e6eHn68fB6tuRJFXaVo2SN1MrHh8GQTc4bwtxVKjM+dfSAa57+e8Wz2xiM82NnMaNAgrBkD9bOysfBk0QCQFf8iM0RwQg4fveKl/PqUleSNw9l2FcvhFY/tCvxk3Wl+f3QN8GTRAKCLgro7cJLzk9Mv4PpTTyWvTzQ1VrbwRVA7PFk0AATdn7rzS8OqoziyMz+x7UCRklP6ho5yps+x4Nmi4RCL3r7f6UBVRg2N4aCBQNJasrBNm63f7PQHLtYIzxYNhxhVNfbEIlAyCZYsSkIwGAqw3PLT8NYSzwtBURTcsgUICodWbinb9Ac0b7uyWYZnY4RDtOcKU3Y/NB/Z5+BzXHheCEVdHUuvNx7D9Yez1xLPCyEb0AcWlsso48TQYls4QvhFQw3xvBCGvzanM6frGIZKi+ug6yq6Irh6V2/wic/2ebJ4PlgEGGiOaksPDpuWqiqd+aJcNZru+fzfz7fqbVcj4fl2BJ+ZwfNFg8/M4AvBB/CF4FPFF4IPMEtqDQA/WfS/W/+y4qSl7dksF+/b+/MX737rK+ptUyPh+VqDWGsrL77tfuuMVK9y4b7tHIwn+du8U7l848b+1+x6m7/cW43wtEcQny9/C0V564UjO3nnnf8Y2/6srZv494tf3PGa+pnWcHhWCIs/OKTSEn+rkJI33ncXg5EmHu5ejqMonHZgK8sK/fU2saHwrBBGDM1ECKSU/O/5l7J+3qksG8oScFwe7lqOdPwBzLXEs0JIxSMKQoAQfOCqqwGIlUzefN/jNBdNMnpbnS1sLGZP9VFK5g+nGQpoWAIiZRvEan+oWo2YPUIQgh1tSVb1DtGczrJ8by/iC7+st1UNw+wRAlA0dO5c1IWjqqC5yPf7w9VqxawSAsAj3ZVp9Kp0EZ+3/KKhRnhWCMsGDrJq/55J2zW7Mg1u5eCmmTapofGsEN5w3x08fefWwxukRHVcDiYiLBzeycLUXlZu3XVl/SxsLDwpBLHWXnp/z3yGolEAIqUyCIGjKmTCQd73oispiwR5hR/X2dSGwavtCB9Zd/q5dGZShEtl8sHAhJ272lq5c+FSTunrN97ynN79pqY1xcrO077+90v9VDnHSF2FIL5ovwD4HUdmxKhyMNHE8x59lD+fdtqkfYotaVNF6K75y7p7hg8wb3T3+utWfpeL925hfnrw/yXkD944zeY3FHURgviiHQI2AoumlgAc0kZ/YvKqficd7CPsjPKW9f9g2aiLaRgE3DZesPV+esr9/GrpWW+4/8I/ffaLdz1/23TdQ6NRrxjhQ/xTEVSR8MDcRZM2Lx84yKte/S5uOOlyTMMAoBwN8ttTnkUZg6u3P0R3ZmBL7c1uXOolhMuO5+S/L1/BsqHMpO3ppgj9oh1NuixPDfttDE+Beglh6xMfMo7x7YdSkjeCjIYDkw4zyja6tMlrBgdDieM08cSiXkL4FOAeJbfmlCRzBZ65cTtvv/Fe5g+NsrE9QSw3zitIyZJt+9GNHOvblvB4JHp7za1uYOo2VE180Y4CQ8Dhn7ag+uuX4LoYjsP84TQ9Ixm+9uM/EXAqrYplVeUP5y4hHS1TcrvoGS7QMZpB09LMSx2EomldlP+4UYfbmrV4asyiWGt/BtgHvPm8nbvOfMa27cSykpfduxF5RGT5eFcTPzjrVPe1D96vGI7DwqH+A3Ptg5/QMX9oyP8r1eUGZjGealCSa7T/ABBr7W27Wlv+vr8pycFEgvkDKc7d1TvhWMOSYDvWC/e/058MWwM82cQs12g3DkQi9DY14SoKfz59+aRjciEdV9V/O/PWNSae8ggTUA5r9P8uWElTvsgr7n6EWNlk95x2DrbF+e3Tz3xlHS1sKDwVI4xHfNGSHLH8s+Y4fPYPdwPQmRrgF+ddoPz+e3O9eQOzDO96hHEELZMP3vJbXvrwfYRNhRsXXMLS/t38/nsv80VQI2aFR1j3gy9y9ab7x/a5KGxKLuUnpzxb+fwdl3rzBmYZngwWxzN/ZIAXjRMBgIJLcznF5+/8lt+MXCM8L4TnPjL1kDSJCvLnfmq1GuFdIVTLrJuXnYzLxEZCiUBKf72OWuJZIXSNDD8bKdnT1sSe0GJMIkg0bILsCS9GEX6pUEs8K4Tez3X+HUSprOlEZJaSmmB3dAk5tYWuQpq9ydZ6m9hQeFYIAPL9WgjEzfd2LSToWHTnhgk7Jo4QZFzNjw9qiOfbEeT7tcs+9Lflqq0o1jm9+0QmEGBfJC73hZd53vbZhGfbEXxmFk8XDT4zhy8EH8AXgk8VXwg+wCwSwvy37vrNwrfuePTz5/7v5OHLPseN52sNbe/pX2UU7A2j4SCqdAmZNk2F0fu2/PDk8+ptWyPheY8QS5c2lA290uChqJiGgVQC59bbrkbD80LQhEUmGiQbC5KLBclGAwhVIt6X8nudaoj3haA4WIbGoUEqrqrQl0xy1uAev9ephni+mbZs6Oiuy/J0noRl0xsO0hfQOBBquhl4Wr3taxQ8L4QFowPMNzV6AwEOaDpLUzmWuyaPBI2L6m1bI+F5IUgR5tZkArc6HW5XKMAlIxmet3MjML/e5jUMno4RFrx1p3igvbsiAscF1wXH5f54hNaspbz2pQ/4s5xqhKeFMHd0uCOjq6ArnJZO88JtuzltYJgS8GjnIhxHvKPeNjYKni4ahoKxx4gHef0dj/DMxw/nXLxlcQ83nLyYV69/cFUdzWsoPO0Rti7oTrZnClyydR+7ezp4+OTF7JjfyYV7DtJSKLJoMPWqetvYKHjWI4iPZWw0jeZcio0nLWSkuZIBZaglwXBTnKZCiSZrxG9LqBGeFIJYa2vohooqGAiGx0RwiGwsQhSX3mTEF0KN8FSnk/ic+Qps+8coaqX52HZJ5sq88MDwpGPTpREeT7awsOTKZL4sS4IPrlt3ytqZtrlRmBEhiLW2CrwLeAFSduGyFGVcfCKZmDBLAIoAywVb8vw9/bSWLBxgZzDAoK4xJ58jVCji6AoLihKHSiukZju4SBlG7jHDoVuBL/zgJ4s3T/tNznJmSghfBd6FlOBSCVEPTVBx5WERiEN/1X2mC44kaDucPpRhSAoG9cOlWU86zU9+eT2/Puc81i9bPLZddVwCpRJJCa6qpICTfvCTxX3TfZ+zmWmvNYi1tgb8C1B54eNf9KFtYwcfsa9qXUlTubc9yYA+MaTZn0jwUFcXz9v4yITtjqoQK5q0jqQBkoC/WOgTMFPVx6MHdeP3HDmNTRXj9h+ZTquCrSj0jIxOcdkJ5Y0fVD4B0y4EuUazge8C49LnjWP8KzqymBICdMHi4QynDGdpK5cn7G7N53n67t2kw+EJ2xXHJRc0GKrUNtLAz4//Thqbmao+/huwDyGuRJHdOHLBhGDxkEAO5VgcFz9EsiVec/9DvOyxW+jJ7eYjF7ya+7oWsXAkxbX33k/Qsrn+zPNY1DtAKhpGt21wXRmWHCjGwrcCn/vBTxYfmKH7nLV4q/r42dJ1mO5bEYqCKys1Byk578Bu7vnOh8eOKxEjTzMHQh388Iyz5Nae+SzdsefnX1p/hZ9c6xjxVIOS/FDwWuBa8QXLiGXS5awaBkNlfnpownFBsgTJ8vslS1l75xVVz7Js5g1uIDzZ1yA/oJtZBwchEEAmNAdTnajZjBFif6zLO+5sluNJIQDQktQVxyZq2hSNKJ+99LWkA5VFPAbDCf7zklexM+mn1qsVnioaxiPXaFK8r5wxo+G4BG5ZfBZ3LFhJe26UvlgL2xSFt9xxz0/hrHqb2hB41yMAc0qF5aSL9AV1ACxVpzfRzqCmE3JcrED8K/W1sHHwtBA2f+9DI0g4KGGHrtGnKuzUNfZqCgHX5d6zTnq03jY2Cp6qPk5F87V9sqSpqEJBBRzg/P4BgsWc+4ffneNPcqkRno0RDvHM3buwg2H629pRAa1Y5H+u/y23Lk0A59TbvIbB00UDwONz5pBqaydAVbWhEB9//nPpi0T9ruUa4nkhmKHJ6z7ubW/nl4sv8Aeu1hDPC8GaIrFmQVUoNgk/T0IN8bwQBnSF8jgtuMCeoMGOby4p1s2oBsTzQiipyiUPxMLsCOrsCWg8GA+hFUZ319uuRsPz1UcA8cGsaM2O3he0rZ5zBncvWveby3xvUGNmhRB8ph/PFw0+M4MvBB/AF4JPFV8IPoDH+xrubflSJ9I5ECxpOEKwsSfufufpl7/kju/M/029bWs0PF1ruLf5S7J7NE9vLM5HLr2Eu+b2gKqw0DFZWHL9xT9riGc9wo2t3wouT2UBjdUvW82BaHRs3yY1RFtutAxHrPrlc8x4NkZoyvafFZAl9rYqE0QAlXkwm1viep1Ma0g8K4S4mf5glCyDseiU+y3FH5NSSzwrBIvYlQqSlmJu8pxIAUHTrI9hDYpnY4SyMJASejKpqlxFpeuxmjshVPLjxFriWY9AwGFnvIvWYgF0DRQFNAVUBRQF27MSnp149nEqjuT2uavYWpzLonyanfFkJamGAHSVnoEU0FlfIxsIzwohQpozBh7hlNEtzCmdyc7u9kqsICWYDjnDH6BUSzwrhGZrVCwZ3EtvPMndi5cfDhiFAEPl8RZ/SeBa4smWRSlerNro9sHgHO5vXsXmjh7+ftpCbj/5cBLunv4MBdt0hr/W6VkxzyY8+RAHiJWygQ4eSpwBDqw4MMyKA8OELYu/rVoCUuJYDmemC2rbewfNwS+3+S2Mx4nnag0PBT9xeTbcrO0JzZu072X3Pk7Asrhkdz8LsgV2h4IszRX9FsYaMCNFwwHx9kfb6T9FAeGiUCQiC0QokSRPzM0i+8/gkc4ymihoCWJ2irtbLmBEmxgHlEI6gViK+xedDRI2RcMUdY0FmbRclM7jIEDgCsTOTCj4kFDEo7Gy+eWP/fXcwqFrfOS5608C3gYMIMT/fPLPZ6Wn/QHMAqZdCPvFO+0uDqoK7oTtLgo7OB0bnaU8iETHogmJQIoUByMdPBQ5ayzXlgSG2+PcvGoJGzuaCTguq/pHaCrZqK5LrFhEs50xFyelxFEVbE0rqlKe9rG/nrvjI89d/yHgM2OBp5RlhLjwk38+68FpfQizgGktGraITzd30auKI0RQ+WKXLrYzh10oWFgkAMHucAejapIluZ3okQKFSIBMIsTOZV30dreiSEl/JMjDHU387JSFjAYNHFXFVtQJNyOEQHNcVClDpqL8z0efu74J+MSE5mohAkj5tel8BrOFaRWCg3gGHD3JYYACQfJIVKDSibQn3kFBjyAQvGDXX1mZ38BwVxIzFMBwJacMpnjthm0orsRSFR5tryTsllPMiKrskAhYQWXdn6mC4+XHdZMNwrQKYYT23zmok1IrHiJLM3kSKDgIbAB018bRKy9VlS5z0yO87IGbuOb+G1g8sB+A5pLJ3HQOAEtRQEo0x550fSnEofaHG4DNQGYKM246rptsEKZVCBfLN9v7mbtTokwSQ4EYB1hKHwsoE0ZnFHBYkj1ARq/8ym0iRErQkR1l7ugAL9pwK12pQQBMTUVIyZLhDOFyGQVwRSU2gMq/tqpQVtVHdNd93yf+crYJXI2U+bHEnlJuRoh3TuczmC3MSK3hUfElvYcH8mEsDRyZIZIzUEIjdAwWid41EJjzrdPKd/1cw2rO0iy2xpaLRcoO2tLmhLycAA/3LOFbF17Oj09fSnO+hFK2eMVDW+2Y60hbVfeoQrk9ZNu/3dfSdDBWNoe++PvTd40//6PPXa9KWAWkPvmXs3dO+83PEjzXsmiJt4hBhBNVbVFQWklYhQn7H21dyCXvewemprJgcJSsVBn6QrOfa/k48VyDki6/I3Wy6ZiTZp/ePmGfLRS2xDox1arZtkte8Jc6mNlweM4jHKKgvFbuVZczGI/RXkphKRrbY1387fSV3HbyfHKGQOvP7NzxvQWLn/hqPk+E5zzCIUbUmJxr97NqdCdl1SCvhThrZBuliMqcbBksiS+C2uHJTicAxQ6hUEaRDkuzvWPb56aG2dvchutNRzZr8axHSIkQ1hE6tRSFTXN6cAFpO/UxrEHxrBBC5NhDNyYaLpDXA3z16c9nKBLl4c4EIyF/hFIt8WzRkJBpHC3CukWXkmqKoFkOe4PN3LaoDYQgVLTqbWJD4VmPUCLAPctOI9UcBSGwDY0Vo2l6UjkwHdpzfvacWuJZIRwk9pHBWMuEbQrwinsfRRkpcNroVN0GPseKZ4UwTxleHzZLk7YnchlevWMfUXtyJ5PPseNZIfx53nNvWXSwd8LKb8lilrAWYkE+zx0ticfqaF7D4dmWRYBHQv/l9kc7hR3UEEB/PMnG7i5+tWyhtft/5vgDVmuIp4Xww4W/FuftvbuQDncHFdehvdC7d778yvwnPtPnqeJpIfjMHJ6NEXxmFl8IPoAvBJ8qvhB8AA/3NRzJp879wfrtXUvPLCsqedf86e9/+7TX1NumRmJW1Bo++PTf2P9Ycqa6ZGSAgm6wpbWNBf17M3++/vxEvW1rFGZF0bCxa6n6/B0PkIvGMDSVazbfy4GmOfF629VIzIqiIWEV+O3JF2FUvdeueAvn924BFtTVrkbC8x7hf079xuZtbXNJOA4R1638Scmelrn1Nq2h8LwQUqHQsoCcaKgClPUAvzz1237xUCM8LwTHVsTkCXMgcLmtfe4DdTCpIfG8EHa1d8t4qYANDOgqo9XJLZ25FBkhfI9QIzwfLKpS4ErBHYkIbnXqexDJ8/N50AKTEy/4HBOe9wgLR4e4q7VpTAQAJQS39rTRn0j6mblrhOeFcOGerWTVye97xNDRbcvzHm224G0hiNVfjJVHlO78KJoridkOhlspDRJOiecf3Jmsr4GNg3d/UWK1C4izM3tZOdhPvC2EEAIJxAojNBVHaDXL9bayYfCmRxCr91BNvZQJRDjQ0oOoxggCKASTvHbDXcRzOfGJi371kjpa2jDU1iOI1SpynYNYrVARmQsEAKf6OQ6UgY7qPo3Ku3058FagtXrcmED3Jtqw1Ik5NV1FoaNU5JwDGxlSz/4lXhX0LKI2QhCr3wr8F9CGWN0L9FCjl7N45ACxcoFsIDy2TXNsThvYTdQscuZIv7gn/mk5N2u+vlt+/Ie1+M4TkeN/WWL1RcC3gDlUcuTN++fXFeP+npiwbfHJm35ErFRJoRO0TN5z+59oKWQBcBWVz1z5DBz0H+wVn/ZHOB8jtfAIVz35Q48t1dFlux7m9B99gt/PeTbRnCRohbkt9HTOL97D75efTX88iSssFGk8l4oofZ4itRDC/id/qORYxbBHWUrr6OFpbhk1yY+XvZB7FqxAd4ogtadoi894alGO/wh4vAbXOSoSyCjJSdsLIkq0VOS5mx5Fx+kDP7HWsXL8QpDr0sA5wFuAjwLPAb4B3AFkqdQOLDjUhSjH/T1ZNOJy8uzn/uYYZ+zdwhvufuRHDvr8efLDfhqVY8SbYxbF6kPVTUCwoX0xL3vRO3jLzZtY3jcKgGIU+OpVF3LWxg35z97/qqlXCfV50nhUCC/WQFoA+yMJXvCKD7Kho7KQx9zhDKam8rJt97GxZS43/3Sln2yzBnizIUZebwNrAH68/Fwebese27WvJU5/IsINC09FFboHVTw78aYQAOS6LyHXiX/MO9OZn05N2n3N+s0YuYy/LnCN8K4Qqrxiw6186NZ7aS4UxhqfTz04wDUPbOTUAztT9bavUfBu72OVjqLLX5Z3kYqExtZ+3NjTwdcuu4A+wwnV2byGwfMeYXs0KfYmBK4y0dTrVy6nJ13yh6rVCM8LYUtXq2wrTF6ILWyVKCp2YYpTfI4BzxcNi0YHCZaGCdgWZe1wd/TSkT4iCm+vo2kNheeFIPUAX770Cn78u+/wxyWryBpB5hQz/PTUc7nhJ+f8vt72NQqeF8JA87zFfZHE7pe97t2cdnAfw5EoBxLNLNy9D2irt3kNg+djhM/fePmez/zp5+BKHu2ax4FEM6Jo8ckbfltv0xoKz3sEgIUDJe777//g05dcRdA0+cBdf2ZT81K/VbGGeLOv4QiufNn9F7/+wftuP6dvOxLBbXOX8aGXvDLU+8n45By9PsfErBDCIZ7/xn16Kqg7d143x28/qDGzSgg+04fng0WfmcEXgg/gC8Gniuerj18754+BQLn8Dc0S333TY6vvqbc9jYqng8UvnPOnXCkSj9iKiuK6JApZzr9/n3KBfIt3jZ6leNYjfGPVb1r653RFzFCIRLlMSdM40JIkJVT7gsqMKp8a4tkYYTQQPKDoOu25PCVNw1IU2vMFdnd3etbm2YxnPUKklFHtchPbO9pwFAWEYEhKQgV/mb/pwLO/roXpYaUvEUcKQdg0UVwXKQTlYIDnvm6Xn4O5xnjWI+xvmyfixQLzRkZRpMQRggNNSbKhIDvCwXqb13B4VgiuImnOF8cGrKpS0jWaYqAcZYGu+Su81RjPFg1zCoNjIjiEKiWL9vSRkfi9jjXGm0IQ1wgcG6TEUhSGwmFyho5wXfZFAtw7ry0jPpJ/Wb3NbCQ8KYQt0cWXBUuCpnSKuxfMZWNXB+vn9bAvGqUtnUfYLkh+UW87GwlPCqE/nDi3OV/ktuVLJ8xn2N7VwReuvAi9mmtRfLy4pF42NhqeFMKOQPO1O1raGY7FJu2LSYlpaJU1ox33tXUwryHxpBBG4s3dRTNAMpebtC9laKCIyp8QH6mDeQ2J54RwT+Bjv3zGzq08becuzth9ANWpjkqTkrBl45hOJQ2ToYIiEB8rOOKj+Q/V1egGYNp7H7eJtZcUDb4TMsvLsmFNDIWbRSoaYkdLG48umEvcKnDq9j04QZUle/qJ2SZChvjzRWcyGo8iAUsRuEKgAg/FIzzUHIOwDmUHVFERBYArK55CFYc/H6qBSnFY9lI6KGIfQtwG5IBLqKT4+RbwbblGO+F6N6fVI2wTa6+SuLcFTXdFIaAreiEkOoeKnLR7hNP2DrC7tYnYsI3ZqnDxYxvZMX8RCafATeecxmi8kg1HAIYr0aqC1aQER4JefeHBcUWFJkCr9EsgBKgKKErlX5XKq5aAqqgIsQApXwtcC5wGrAK+CXx5Op+JV5nuouF9Ki4GBZTyxGbh+YOjnL67l1wywjUb/srjLSsohw1yQYO+1iYAVNshOZJDs2yErLzDvqBx+BevH9EbLf5JFh1R9QhP/Ft/h1hrR57CPTYE093EHIXK+ktTEbAsssEwyWIGq7ViSj5i0JTNEcyUOeOBXei2g60qPHDGQu48ZV7lhUqg5EwWQm3QAAPIT8fFvcp0e4QfSwQloljGRDGkw0EeWjiXaKbI3fNXMTe1F6RESIVnPvAwZzywE92uZMvTHJezH9yJMB1Kh9oVLLcSA4znieIdlydzx3+Va7TRJ3uDjcJ0C+Grpqr9pxDSSpoZ0nGFdDjAtq4W/nDOClQp2d8R4ta2swlE0yzcu4/tLT10Dwyi2xOFo7qS5EiWIaPqxISoxAq2WxGAlGBLsJzDn2U1n6NbjSsOpYCu7LMRYg+VZKFFoAD8CnjlND8TT+KpMYt3Ra+LbOnQhp+175HAI5zL+HS9roA3vvlZDCQilc2BaqOSQqXsKdkmQimgij2oHADxHVTlz/I/AvbRvs/nMJ7qhr4wd23+M2/eH4rco7uJwTJ2KohyKEicG2MkFqjUClRRLRYk8iNhP89iDfBcg9Ifv9sje3Zsz//povMoLBRYc1yyixT+cdZiWi0X1XJQFSqu3nJ21dveRsFTHuEQ5XDsI0v39X35xxc9DXtcp9OqTIF75yQolGwcXQVNO71+VjYWnvMIAGEh9y7sPThBBFAxdvFoHlMR4IL8WGhypm6fY8KTQrhp1dPXHYyFUY6sHgKdg6lKjcG2b555yxoXTwrhP266UCaUDB35idnzYmWTs/cN8IqHHj5DfiZ+WZ3Ma0g8GSMALB/YzUPZM4iYJjnDIGjbxEyLSMlEc0L+TKca40mPALCjuZ2evn7CtkN7oUjctGjvT1GKhlCl2lpv+xoNz3qEbU1zZVdpWCzYDggIFE2y8TC7ulsxbXtyKlaf48KzQshpSRmwd4q7TjmDeNHCUQWuqhLOZtjd3XV/ve1rNDwrhPZsSmbkPN5xy4/Z2L2Evlgb8WyZzclu/viDBf7aTTXGs0KImflr71h08rdLeghVSgJpycFwG4VAwDudIw2EpzqdjuQzZ93obljYI8yQjqmqdPePctLB/u73bnjOgXrb1mh4WggAXzvjd7/Ia7GXOorMLdk31HTNgZf7xcI04Hkh+MwMnm1H8JlZfCH4AL4QfKr4QvABPNyOMJ6MePOeIu3zXBQM8gQororKbz1Sb7saCc8LISPedH+GufNcKgt7WQRxST8cHT+y1ee48XTR8L6zbrrp1s4zzj4kggoCkwgD4u0/r5thDYhnhfC+s/5+14/OPumZEWtyuiQbDZXS1XUwq2HxrBAe65lzwVm9g+zqXDRpuqIyNpvVp1Z4Vggr9w/QFA3x19NPJ68Hqc5ZwkUgUXCI+EqoIZ4VwsGuVvKREKfu3U/QsnHQcFBxUbHRANezts9GvFtrGDeUPU4/AYqAoECEPC0oZH2PUEM8KwStUOb0x/fwgkdvQ0HDIYJEQUcQYwCBoT/xVXyeLJ4VwtzhFMv7hlmUHSBMCZVK77OFgYVCgWR9DWwwPFvOLhwYZbQ5iqMIDhotFEQAAB0TBYnAqrOFjYUnxyN8c97/vSff1v7fihA4AY1MLELLaIYLHn+EszOP4gAmKlH5Hb91sUZ40iMcjEW/kG6KMdKWpL+9mWIowP6uNm446zzSRFApYhNgVLzt3Hrb2ih4UghL00Vdt2xKwYnZ+NOJKFuSiyiSwEEnQOaelHidOyLe5m4Mvm9/ncxtCGY0WGz+cGblMzbufvYzHtn19BtWLrx08f7h8ElDKXnj8rnikXltYsXAKGEFzopHuGdhOyenj2helpKAWUInj8SgTKtI0o+kTKyc7i6LV0iTkNQpCRWJgyElYJC/TkW+F3m9OZP3O5uYkRhBfM5S4vnS7zKR4JXJTJFkusgXfnozLVaZWKHysn928Up+9IyVlHSFF9/xGD+6YhXX3rmZ0LhOxhX7d/Cm9T+qpEFCUCaGSRKNMkFSKEgkkCGBQ5gmeqvHgoNwNewzkNf73ddTMFMe4epMJHil6rg0Z8tc/tB29IBCrPqLP9gU48YzlzEcD/Kc+7azYdGcylmFLOdvP8BwIs7ckV6evffPY7IQSALkMEkSYnTcdoiTopcOkigIXASgIRXgF8BJM3TPs4qZEsJZAAHTQQAr9w2iO4dHpe9vTVLWK+GKkJLWdAGk5A23P0iiWAaglV2oR+RrFLjoFCcNTBAIDErYGBgTFnsRflr/ozBTweI9AGVDRQJ3L+0mHwyM7Zw/MEK8aCFcye2nzeP592wlXigTMg8nRLOYvKCXRGHiWIVD26FMEH3Sij/y8ZrcTQMyU0L4QyJf+oWrCIZjAW4/eT4ZFA62VVbta0/nOXfTLpb0ZSgGdL7ykvP5+Vd+ya7W5NgFMrRT4nBm3MrLjmMRxsaYsD1HgjhpDg1ikoANDuAv/3MUZrRBKfzxwqLnPbDtmVc8tOsZfz5r0fPVohlbns7zWFtC6K5k6cCIMMo2G05ZzKtvupuThgcw3Eqns45FjBRhUggcJCoKZfIkcYROkAwBWaRESCoIIXCRWFJBSp3CB1T4CvJ6f5bUUfBky+KnV/1Vntw7yAOnLWbvnFZsVeGiHZt5zX03o7ku4BIkjYkqLSwgRgH3oQ75nbPqbftsxZOdTnkFe/+cZm3Lgu6xbf9YsYq0HuEdd19P3E7joPF4bPFFp2c+cjfA5EV/fJ4KnmxZDKL81465cyZt39PRgW5DiQB5QhwSgc/x40khfPShZ30aOTm1f1sujUOQMk2Y/lC1muJJIQB09g6jW4e7mlXH4cqH7wOoNhD53dC1xJMxAsCKXSMsGFrPvvlNtJlpztu5le70CAAqFo6/KnBN8awQpCKwVIXmwRynD+6iM1sRgcBBw8TG9F1CDfGsEFLNQZzqYmvr25fyaPN8wnaJ5/bei0IZiZ9zs5Z4Vgh6ycSJVpqP5+YGOHl0LwHXQiKR2LgE/WCxhnhWCIpVWaKnpZzhov7N4zqWjOqUNz8xey3xbK3h1mWdX28eLLEw3T9l72JBOeFW5JtWPCuEb9196bt+cfGyr3XkhqfY65CKhK+bcaMaGE/2NYwnJ950u0L44vHpEGxs4vI6fwRzDfGsRzhEVH7vkjLmNgcbF4esLuVonFC97Wo0PO8RfGYGz3sEn5nBF4IP4AvBp4ovBB/Awy2L43nRKx946Wgw/P2wWd7619NPPVeu0fyxhzXG87WGNz3rJisoVW1/ooWAZZMo5Ln1zHltWz8/b6jetjUSnvYI313+/cvLS07TfnLxaZh6xdRYscy///LmAT4/zy/WaoinH+ZwJPy37V2tYyIAyIYCPHjSXL9VscZ4WgjRXEAZjk3uXBqNBrlB/84b62BSw+JpIXSMFjlv6+S0Bxdu2oewzbfVwaSGxbMxwk1N33pxPtLCVfdtJRsKcNOqhaiOy5X3b+MZj+yirKqj9baxkfBsreHn3d9z7luwRLloS29ldXgAKUlmyqixAZRU0H6a824/xV6N8KxHUEuG0pHJsjTVRyoYxhEqWslFsQWhUomcFvYHLdYQTwrhxtg33Hk5wcm5fhJOieZ8AajkYe6nCduOs6ep2a851BDPBYvXJ/53KF5QRBCHkGmTI0SBAC4CBUkAi6RpMT/lrxNeSzwnhHQy3tLspomQI0WcAiFyRBgljgsUNB0UlznlkXqb2lB4Sgjf6vq/4qq+XnYlWhkRyQn7HFRGiaEoDqYmmcs+dosP/LI+ljYenqo13Bn8hhyOxLGEyoLh1KTRyw4CBZcW0mjkiTNMlqi04X4D7SOd8nM31MXwBsATQnhQXKcMGJqtGprIBCvDEdszOSLjZrVVU+QBkCBLCBMFF4USDuAQrM6JVJA4DoiPuASvEfAvi+SH76vDbc0q6iKEvHj3S0F+uIy1dDcnhV00FFxsVWFfUxMAiuvSki8QKVfEUFm5RRAnR5jyuKsdsl9M2CLHbZG4OAT2AV9bLD+wdppvb1Yy40LIi3efC9wDRbGNU3DREUhCFFFx2RePMWokiZbKJIoldMcdW8JHxaWVyUXGxNc+mYo3AVnJwPbmxfID35uWm5vF1CNYfDkgBCXcajOGjoWKRMPk7MxGlqf205YrYDiVZJkKhxJslo/yuv95k4KA8VNmX1GTu2gw6iGEVOWfwy/vUCJNGx2EjW5P9lIKYBxHcoxxV/T7KKagHkL4LjDgEiZEtuq2D5mhkJdJsnpg0kmVF/lEjYmHooPJW6tj28rAl4/N7MZmxoUQkV89CJwlMD7Zzb4/Bklls8LArJqSo5WD4SYcMTH4K2oaBYL/dLVHFwsFE4XimCSqyTYt0L8KnLNYfsBPwDUFnqg+Avw5cN3wHMtqDkmTzS3dqK4kViqjSEk+YFTHK5YxMImSJ0wJBRsLAwcNgYNOVqrkdugoX2yW3/hOve9pNuEZIQD8LvkD2ZPO0ZtMYGkT+8PaUzkCtgO4mKjM5yASAZT+Y5787GfrYnAD4akm5vXzOr6uqwXi6TIh0wIpK+0JuTxh20IJlkiSRcVlV6wVHSvpi6A2eMojAPys6wdy4cECtqERMG3yQYOyrhGwTOaXBghgUVJ0Bo0A5xXf73dF1whPeQQAYatX7upKsH5eJwPJKEPxKJaiEiy7DNLKfuZguwaKL4Ga4jkhXDPwmj+FMrnBwXiYoq5X8ihlCihjjkuQIk5R+sm0aonnhADwouxb23VpghBojos6RfGVCfg5lGqJJ4UAsKz3QEFxHWxVmdCmcIiBkFGog1kNi2eFEDVCixeWN9NmDpKNatX12wAkMbWPpUMHttbVwAbDk4NXAa7a98q+33V+i4v6HqOVfdho5EgQIs9+o4Mdibn5S+ptZAPhWY8AUA4Ycoge8iTQsEkyjIOB44QJ2drn621fI+FZjwBQzkt7X3uLzsBpGBQAyWg4jtRzvHzwdX+st32NhKc9wslDhWaVYXq7QuxubWegJUK3uY1t6lz7ic/2eSp4WghnyXfm9onuv93V3oXhDrMnHOfHC57jvmL4tf5UtxrjuSbmo3Fn6BsXqrbcdL71r/7Mlmlg1gjBZ3rxdNHgM3P4QvABfCH4VJkVQoj828h5sXcPhettRyPj6WBRvGtov6qIbkdREEh0V8ryV1pmhXhnG54Vgvj3rIZpVyYyCMZGqQvbcd2vtfrZUmqMd5uYS+URFBV05XAOJcdFovgeYRrwrhAkYQyVSe998pLRPjXAu0IQoiKC8YNSFAVUXwnTgXfdrJQTRQCVz8K7Js9mvPtUFVERwxGotp+hfzrwrhAEgiNfuus+4TRYn2PDwzGCogjLIWC7uKqC4koU26Gg+VKYDrzrEaQkKCUBxyVk2gRsh7gr6TYdPn32j/fW27xGw7sNSv82IqOurGQ6kZJuxyVWtVVxXVbu2Cy/9ffLvCvkWYYnH6TynmENOZbcgqiUxKRESIniSlxF4bEFy8TLVz/w6roa2kB4MkaQ0IaAoqJgAgHXZclIivmZLIqU9EXCbGptJhcI/wj4Sb3tbQQ8WTS86Zm/Wfr9My7dOr7NoLlU5nV79hOr1iQymsv2ZIzBSBOmlFJIN//Ad3pi9bJ5tnNcQhBrbQ24pvrXAcwBQoBZ/TfBkYmPjvw+KSemSnRcKNpQsCsbDidLZEUmy8v2HQRgMBhgQ0fb4f4oedi9yep/XUQlGacAXcpP3PXtro8d8802OMdbNPwSuPopnTFVa+F4FAVGy4wXgXAr2ZB2hitZWYuqyqa2Fo7sgpwY8AiUcduKQnz04rcemHfHt7ve8JTsPUE45mBRrLVX8VRF8GSQEqovHinRbAfdcdCdSh/DsK7TG4vgju+MklPfiOBwjsWAEFiK8vqa29sgHI9HmFMzK8YjRKV52XFRJChHFCV/6O5gnju5OPtnzUyH9vndVUfneKqPtwHTtxqrBDFF/DJs6FiKMinN3lSfD/051T9NSn9OxFE4ZiHINVoReB6wiamyXB71RHn4b/y28f8fNUBMfdFItVdy8VAvJ/fvRHVsHAGlcUYcDhbBrV5HSNyAlIufyj2eSMxI9VGstQ8V1zHgIqAbKfdgu9/EdRM4UgHiqEJgSYFjS0wUBGimjVItCnqyKV65cyOaFmU0lKQ/KNjW0oULRItZykbEEQIpJbtUId4kYSOC9N3f6vRLhSfAk+0I4gPpJIoyeihj1qlDKc4fHGFOyRwr7/tCgnfc8VP+ZfX72+77Vqe/YPhx4smWRSxHF7pAVoWwsTVJSLrogykitkVHppfX3fM3/ti1vOiLoDZ40iMAiDUpaSgCU1NBSkKOS7xk8fLH7yckXeaM9vOe9W/0+6RrhCc7nSpIWsoWXfkS3YUybSULA/jZirN51mP3sL157gvrbWEj4VmPoL0/JbvLk/NhZAEX2lJfa/WLhBrizRgBcB0Xl8kuq6xA/iu+CGqNZ4sG6bpOQRET2hIsoKB7VruzGs8+VUUIhCPJUTHyUOtg0B/FPC14VgjCkdgAQmCO2675TUPTgmeLBueo6de9GdzOdjwrBCTIKXIw+zMbpgfvCkHlC/YRQpCA5buEacGzQpBfbvmPsgKWInAE2KJSa7Ck/Nd629aIeLZB6RDKvw0XNKEEXSSKK88wv9LycL1takQ8LwSfmcGzRYPPzOILwQfwheBTxReCD+DhJuZDiI/ly0hhACCdvPxkLFpnkxoST9caxEdzDo5QxkY5C0DBlZ+M+nkWa4y3iwY5TgRQnajgZ9OaDjz7UMV/FXdOuUNKxH8V/bzMNcazQkDIBVMuAC0EuK418wY1Nt4NFqUAVYDtTuxm0qtpeX1qineFIGQluWZAA6eqBLU6Vd52DCp9UD41wrs/rUPFghCgKZU/IUAILt+699/qa1zj4V0hlB1xKEdCJR0vIKCllCUfCHxywbUH5ZWv2ry63mY2Cp4sGsTHcrsO/fpRGIsRLtrzOLd97xNYisbvT76ED1z4wuv556kRfJ4kHvUIYgEhDXRRCQz1Smxw5/wV/Hnp6QQci5c+ejNX7fKHJtQKzwlBfKxg6DiHYwKo/KtWJ8R2zB079ul7NtfDxIbEc0VDoFxOl+OhSh4lSbVZuZpOB8klex4fO/bUgX083vYeuTs+h4diK/Z8aMOLFtTJ7FmP5zzC+Qe2B5GikvBIUvnXqQSMiwcOcNHerQAciLexu2kOc0eGeNbO+7licP38r5z808F62j6bObZOJ7G6CegCHgOSwKuBk4G/Aw8D/wYsBX4E3E8l+5oOdAJ3AMuozGfdsDPecs2/P/tlz71h2cr2tBFTdSEVS528Brhi2nTkCrxo62ZWjA7zWOdyFuw5SKJQwtEEyVKK03sfJOwUyRhxhvRW2RdtoyyEfcHg+ttPyu7bCjwKjABRYCewFblu/1N/AI3HUxeCWP2fwH8CASADxI/1y0cDIa678Nl87NkvwxHVmMCWUw5YnzuQxaim2As6DmensgRth9ZUmo6RFACnDWzk6fvu5I4557Oha2XlelLiCsHC7F6u2vqXqcy4HngVcl35WO+jEXhqRYNYfQ7wSSoigOMQAcBoOMpHn3MNjqJSSbApKkkRjxCnZjtjIgAoqSq9wQCuqjDQ0kS2mojz8ZZlWIrGpjknTQg0FSnZ3rSQ7U0LpzLjxcC1x3MfjcBTjRGeVssvv3fe0krizPETWYQAy60m3azQnipMOrekHjY9G6kIQXctRoNJLNWYeLAQKK7ksdZlRzOlpvc1G3mqQni0ll++eKiv8j/j0+0dSo5ou5UqoyowjcmVm1bzcFeDYVUSapzR9whNpTS6Y048WEpcRdCZ7TuaKTW9r9nIUxXC34Gfjft8XHOTVwwe4OpH7mEs77LjHhqFVK0xuOBIErbJwnQWISWG47A4V6CtKgTdtGgfHeay3bdw5sAjGK7FWQceGiesyvoOMTPHWX1TNkBtAr5yPPfRCBxrreEcYCHwD2Al8FmgB9gArAfeSiV+eBD4C/CS6ucgsANoopIj87EH5sy98n8ueFb85mUr2ZtoB0URSAlFZyxoDFs2b9y0g7ZiqaIXRSEfMBgIh0C4XLLzQS7c8zCpYIIDoQ7yaoym1CjFQFDmgyEWpHbZzzxw3xDQBwwDdtWOPwJ/R6474ZMueG7MovhksbLqb3mis5mXyfMvm7aPfR4IGnxn6UK+8o8biVklDKtAzjKcN+x4g+cayWYD3ntojuviTC6y9sYjYw2NEtCFyoXpLFmlmXRYQTdz7nsff4H37meW4L0HF9CW4rADs+oRFEAINNelLxomYdqUdQ1HVVmaLbCrszl43R/OOKHbAGqB54oGAPGJokRWq5Tq4aql4kreuGk3kWoeJVMRXPerk/1u6Brgub4GAEzLJKBMEAGAqwju6G4Z++xMsW6Dz7HhSSHIT8UDHOUlDwUDICXSddmjq9+dYdMaFu/FCIewXIk6eTz7UDhAnypyN3fPSQ58pfWEr/bVCk/GCADiE0UX9YiMmwqVpmchdPnhwOT8vD7HjCeLBuBwm6WmjDU1IyprPWE7wbra1oB4t2gQVDue3MOdUo5b3SHy9TOsMfGuR5ByFEdWRic57uEZT0KArvizoWuMd4UQ1jsOD2AVY5NbUAXyPww/PqgxnhWC/IBuV5aHH7dRgclrCvvUAs8KAQBV0cCVlfkNApASVfWLhWnAs9VHn5nF2x7BZ8bwheAD+ELwqeILwQeYBUL4ycIffP63Pd80/9B63c+e+GifY8XTtYYfL/y+u3LfoIg6eVI0M5LQuSL1Dn8gyjTgWSF8v/sb+ecd2BouE6FIhBijFIiyX4tlnmG9L1Fv+xoNz3Y6zXNL4X4WYFKZxZSlhQQDBF09VmfTGhLPxggh0x0TwSHStKG5/np/04FnhTAQqXh/94iIwMYfijAdeLJouDWx9qymRJFvXvkCtnV30JLO8cK7N7Fqz15UP73itOBJj3DPyXPv+8k5V7CtuwOA4USUH15xNqWIRZIDdbauMfGkEMqqwZa2+RO2OarKjrnNFGmqk1WNjSeFcN7GzRi2OWl7d76fEdFcB4saH08KYX4poywf2Ddh2+kHtnLpvvuJqSN1sqqx8WSwONgapjs9zNvu/x3396xgXqqfS3c+hECSsNN+y+I04DmP8LuWLz3SlR7m5qWns7TvIG9a/yeu2L4e3XUYppOs2syqN+/yJ7bUGM95hIChnZYeWcY5W/ZzX/hilhW2omhF7pp3Erd3n06wYHNB3y6laU3cHV3b4jkhz1Y81dewQXlvOh1cFo8VXSTQn4iSCRr8yzteQC5USeSmOi6dfVl0q8iuee2HsrNKHNfGxkGwD115KZZ8DMFJILYSUEpyjeadG/UgnhDCg+I640/ntxc7hlHO3D6AIkGjhIbFULCJO1bM58svuIDheGUpJ8VxcRUxMRsbVOY/HCo0FMblYqIyL0b9J+HF4WtJ4E65RrukdnfofTzhWn/9jAXZxfscZSDRzP6WBCBx0SgRI1nK87oNN3LT177IaQf2AOCqymQRABPWgHKpZEpyqIhg3MIfU/4dRgAXi7X2DdN0u56k7h7hQXGdOrAkbW+Mno4tdPa1JLnqwYdoG7EwKLKc+9CrzcqWonLVGz7AX1eccfQLmlN0SmmCKRcK++e4co12wgyd94JHkAhBR3aU9Uvn0p7OcfspiwFoY/+YCAB01+E/b1x39CvVNnFG/cvMGaTuQjhTXuveM+fkfMtoliYzxY7OFra1zUGniMbk1sWOXGrqC7myksf5aPueOtcfy0mzFU9UH6+6fX/shvObSq+65xajEFPpU+excd48duhJXrTjTxOOvWHRmYQLJqZe8dr2ofL9yBJBUMnrfGjfoUyuR6NyHUklqvi1XKO94njvazZR9xhhPLvE2+8NCOXcktbM2ue9nOFkjOc8dhsv2HgLIavEHfNX8v7LX4up6hR1hb0dkeqCoFJiuo+iK7egq7+S79fvrPe9zDY8JQSAh8MfdgsJXXznaa+gEJo4COXi7ffzw+XnMhoKkQ+psu+LzXUv2hoFzz3IESPwm9FYkPP3bJi075nbHuGX//dVpOM4vghqi+ce5jNSH31xT3qIgaCBsEo4gCtdLt1yLycN7MEoCXZ+c44nYptGwnNCAGgaTPH3xWcyd+8Qz7ntIU7btIvrl53NYDjO3mCnt8qyBsFzMQLAbYlPOY/OOV25YOvesW0HmmJsOjlC6IAi37XzRZ4U8GzGkw80WUpx3ra9E7Z1jWYZVDrY3xKpk1WNjSfL2rIRcFVzskift34LA0lPanfW48mn+vvTn96SDk1cl0kC0WKZUMkfzj4deFIIn7z9WZn1Kzp4vLttbB3QsqqxYVE3rj15TUif48eTRQPAjp42fn/ufAKmRedIlv6mKNFSmYsf3FJv0xoST3oEgEQhC0DZ0Nk9p5liwCBhZomW/OGK04FnhfC0ex/tj5SLY591x2b5gf00p4aG62hWw+LJdoRD/LLjO+6jK+aJYtDgpF192GaQt+5e7Q9nnwY8LQSA37d+59yiHvyl7lofX93/pv9Xb3saFc8LwWdm8GyM4DOz+ELwAXwh+FTxheADeLhl8UjuFt9RZKBgJ9ySsIRGVuCOROZoLxx+tR/t1oBZI4SQNmovLu8VZQK4GChYynaJxSy6By8zKx7i3eI7+jKxUzymnUJWiWIqKgYOC8x9J8xMpOlmVsQI2ztD5TsjFzOSiNOmjrC01EvQLLMtMJ8HjC/9W73tawQ87xG+umzd95rVqFh/UTemYSCkyzm7HueZmx7gEWs5ZVf9LPDf9bZztuNJIZz8rkFDd5xRBRGeu+okVqVzWEZlHIIUCvctOpnlB/eyZGQf+0TMLx5qgDeLBilLAiUMgoOtTVjByYNRHmufRxCTIJYvhBrgOSF0fjC1wBXKWMYCWwjyU8xOT4sANipWJOj3RtYAzxUNKZXvxMe/WiEYkipnP7yTcKFMOaKRjwUQZZcku5E5yQHx/o93yS9+rG5GNwCeE4K0neRoWCecKQMQKltcfeujhE2LrlKKyFBlqrypgcClib30M+8jgC+E48BTRUPHO/v/1qIo5wwqCgcMjZIQLOwdBF2gRCQR53C+BMOGQRaiUyTKaB2tbgw8I4R5/9p/+1LbfVZZVcGWDBgaA7qKFQtx1/nL0eXksYol4rhEUZB+nHCc1F0IYq0dCn4080tDERePKiqxsk3CdugulnjuUIpgQGckHmNHV8ekcwMUAY0NrSexX7n2jpm3vnGoywgl8aniHFG2vx123CubLVcJl200R6JLCUIQchwW54uM/5nrlsWrbryT7uEUUElr0k4vMXLc2X4qStFgXna3G8K+olN+8eYZv6lZzowIQXymHAH5KKqyANsVwnSJ2g4tJYvmokVH2cQF+oOVpJoL8kVizuSiIFIscda2XXT3DXDewGYMbAAsoZJRg8TsFGGGcAiTo5UyQTTKhEkBilQRuKhoFNHI2kVaTZPY7hLRrxcJh4BNwI3L5XtPuB7Naa81iM+Uu5D0YlTKfhyQIY2souMGNeKWjWWKCSnMAkdZt6l7YARLqKzI7hwTAYAuHZJ2jiItFGkhTxRZTaBkoTJKEyexXjjo5OgmSzMBMnqIIb1EyykG1rdcSpQrywT9Arhm2h6IR5mJGOHX6NWvsVwIqJXkl4ogH9LZ1J2kPxggrR/WZG6KBqQluw5y2e2bOf3hvcSLxUn7dUqolCkSGRNBZbtDmRiDdKJTJsQw4FImjgACZAAIUEZUMnK9fIv48nk1vP9ZwfQLQbJ0rLCfIg2uoypkwjq2ohC1LJCSveEgw4HDzcqLd/dx+Z2bxj6nmbzso0aJAFkcJotIxaVItPr/FkrV/zgYYyn8BKAcTs22+JjudRYz/UIQ/ARXVlPdAlPEJGo1D6IiwdRUNs9NMho5PBv6srs2jWlJwWaYJAoFKtNjJRo5dDK4aGjjioxD2CjEGan+fwgXBXAJkKVcFYiLOCQiEzjhgs1pF4L8j8C/YbsDuBJ0BZyJQgiXbWJFCyElGUNnOBbAURVaCocbj/pbK/mZw9WXbRNgM6dQwiLCPoIM42BQIk6IIko1W6sEyqg0s58mBikRI0s7ChYJ9lGguSoMIQuEAXEAeOVy+d6+6X4uXmPGqo/ic2YbrvspSvYbFE3RFUUhYjm0Z0qYukrBUOkeLbK/JcxQPMj5+4ZZOpIHoCmV4w03/Imi2TLpuvPYShvDZEmSEi1IKdEoyASjFAm6oBY1rAy492mUgwpKKkh2p0PwTpOYUia5FdiWJtECjC6X7z0hZ9nWdaZT8wdGX51Ohn7s6iqhksWyvhz5oMb2zjghy+aynYM0VRNjvOWWX6D2NgMTYwwVCxEo4boKHc7w6gXOp35Th1uZ9XhmylvzB0bcuXlbKMBw1KC3OYyQklMGsjz34W284NFbMQYj2AQmnGcr0KNuQXXcrnnOfx+sj/Wzn7o3MR/CDOqdB2IGGU2QKJgoRRvHlDySjHL9qqXctOJ84oGDTEyaLmlz06SDUXwRHB+e8QgA2ufM1kR/qi+diKqOMlGjL9u6l4WZAqfu2MbiHf0EyhDARqdMVOyRXe51nhH1bMRTD8/+d2PIUsTNR4oAYCBcycu8cfFSbrngdCKYaLiEyWJLbXKd0ecp4bmBKU2Sh3LIK+QRQeEZe/q58IHtxHIlUokwLgIFiUERibGyTuY2DJ4qGg7R+oGUHIkEkEKgSMnT9vTx8R/fjG4frtmpOCQoEKKfefIL/niE48RzHgEgWSyPuIpobipbvPreR1nUn5kgAgAHlRgjFFH8YqEGeCpGOMT2r3e0JIrmSs120pppyVCuMMVRkn1aC7lg2HsubRbiSSEA7Ppq26Nbvt6RtGOR01JRA/WIPoQYeQLCRbMsP8taDfCsEA7x8Vsu2hRMFzd3MEQzKaLkaWOEFtJE3SIRKc6ut42NgCeDxUmI1coBVjgKE2c82ZTokZ/3A8Ua4HmPAIBc5+YIWqI6dkAiEZTIEfxRnS1rGGaHR6iyWXz27jCl8xwENurrlsuP/LjeNjUKs0oIPtPH7CgafKYdXwg+gC8Enyq+EHwAj/Y1HOLlr92+yBFie9BxRUFQmFcwY1+5/pSpZ7/4HBeeFcJF1/Z2nlQyd6gIdFcS1JRwb0h3OHLQok9N8GzRECmW9ocdSchx0aQkajm0lW2ufvXWz9fbtkbEu0KQTBqnZLiSnK68ry4GNTieFMLVL9v0zaKhYSmC0aDO+KDg0Kwon9riyRhhd3vybTs6ktyyaA6mphItWzxr+0F6MkUC0s+OMh140iPkQwFyQQOzOis6F9D529IucrqKavqVhunAk0IIWjZSTPzh5w2NvmiImHVCzkibdjwphOgU6z/rjktHrsi+sOIHCdOAJ4XQXLZIFspjn4WUXLRnkLjlEHb8doTpwJPBYkuhTFPR5LnbDzIcCtCVLZIoW9hCIIN+yt3pwJNC+Ovyblb1p7mvuwXNlSRLJomyxea2OHOHM+IR8VlXIoYd4j1nymvLT3xFnyfCcwNTOt7XnzNCgchIOMjTdg/QnSkwEgoQsmw0y+Y9v/wbEo3KZFgFsDaeKd9zWp3NnvV4RgivfeUWbU88aO1rinEgHuZFj/cyJ1ca228pggX7Brjs3s0TzhOUCZJ1i6ibILr2TPlufxzjMVC3okGstTUg1lQox5xsfvs1lqPbmk4mGKAjV5ogAgDdlYzGw5OuoyKIk1XaKJxmof1wk/jcD8uEqSZrskG/EbgFuB8wz5TX3jX9dzf7qIsQxFr7LcAX4plCIhMN8orHR/nNST0MxkJETBvNUSipCkFnYuNR+2h2wmcNkxXci0ElTKj4NpUCMdJ0CEAHnlv9A+BBcR3Ay8+U1/5y+u5w9jHj1Uex1l4JfCtaMhOZWAhHUbhlYQeD8TAIQT6g0x8P8/clcyad25LKT/isU2InK9nHMmx0BNDEAGk6xhJqHYWf1/KeGoF6tCM8BxAB06qk3AN6k5FJB+1silJWD5vnIFm4f2DCMUXiFEgwyDy2czrAWN5El8nL/4xDPCium6y0E5h6CGEHUE2R+M8D1TvmtVKqikFNZQmXzKMeWyBBgSgpJmdxPwpDT/bAE4F6COH3wK3D8TAc6lKeShBCsLEjyY/OWMjdPS3MG8094YVTtLGfZU/GhrvOlNf60+nHMeNCkGs0C7gcIa5GEd/WC0XHMJ2KKMYJYuFIludvOcDTdg+wNxnh9xecTF/L5NS7465MH4uqWVVl9c+lqrZDH/LAu8+U1140bTc4S/FMO0LzB4dtKxJUcyGDFUMZnr39cPJTR8AtCzu4fPtBXv7HuwhMSJohUSkSIC1tglhoZYn+8jPle38/83cxe/FME/PI51u0Ff/W965eO/TVVQcnrtGkSlg2lAVFYSQZpXMoXd0jgaJcJdd4svNsNuGpB/j4f8/52qLRwuvCU4w5aCuUKq9dlSiUUSkC1sCZvghqgmeKhvG8+lVbZeu4MQmSw2PY94UDXP/jJX4PZI3x5K9pS2uMvKbiAhlDY1NrjIc7EpgCkP7o1enAk0KQrmQgEuCxtjgh2+HUoSzJksXNizpoLlq+EKYBzwSL4xmMhYjbLisH0mPb5qcLFHSVvOFJk2c9nvQIuaCOOkXsMj9dwFL9MYvTgSeFEC7bjIQDk7bndY140fQDxWnAk0JYeHDEeawlxlDo8LpOliLYMCdBb3Dy4l0+x48nC9zbvr9Ie9rb9smfr5zPwtE8QdthZ1OUgqHRnMn7RcM04EkhAGiOKx1FEdtbYhO2O46TPcopPseBJ4sGAFO6NzQXJg5Qbi6UiKt6W51Mamg82bJ4iMvfsCN/oCURHowGacsV6UzlfnHTdxeecMv1zgSeFoLPzOHZosFnZvGF4AP4QvCp4gvBB5hlQril5Uv/9teu61K/7/rBy+ptS6MxK2oNt7V//aTHuudvHmpqwRWCeDHHKTu3yMv73jmrhOxlZsWD3NI5b3MqnkRzXQzHwVE0Hp+/TPy+43/X19u2RsHzQrgj+d/vykciY7OiACxNJ1HI0VFOn1lH0xoKzwuhrKofFlOUXlGzxEAk4XdJ1wjPC8Gy7HL3yOCk7csG92G4foa1WuHZ3sdDlLSI7BocIuKU2NoxF8O2OGPfNuaP9rN1fpf3I91ZgueF4Eg1vr25jat2rOeSHY+Mbc8YEYZicb9oqBGeLxpClumes2cjN644i2EjQUkx2B9r5zdnXcyCgd2+R6gRnvcIeS2A5cS4aN+tbJh/FootyIcMnv/4X3g4ssL3CDXCc0L415c9pv5gfkcuqYoAqhBzX/Vs/vu63zFamo81GMBSNNRRl3S+nf75Hb5HqBGeEsJFb9obeGxFTykfD5F3Jc0li5KmQsTl0ehCNKfy3h1FZXtsIcFs2fcINcJTMcLmWDCnI2nKlMEBx5Gct7OXbS0dYyIYQxG0DhfqY2gD4ikhzLEdLafpjFaHsaeDBt8/7zT2trdU0mtKB909PDk2bFVS8H34yod8z3CceKZoePM1W2OxRJiCPtEkS1VYv3wuV2x7iINz2tnTPIdIqYiWtnGbomwKf0K+SDG4LXoLlBVyIc3Kq6FvzM2kOoOu853T5QdurtMtzSo0ALHWvhR4K0CgaP06kim9tBANXKkJQotTOTka0Et7k+Hwiv6MaM+bqK7Lo81ROarrIlKyaClbLCwUSdouCNjeFCFSdliYzhO0bQYCBvuiYUCguS4xy2ZLJMiQpqGpcFEqy0LpMK9QZr2Uk9ZqaCkV2TlvHrtbugDIB8KoUYcFGy0OlntIuilOYjupUJAHWs/WS3r4PY5ToDv7+MvvFV+jRAgVUHFwEKSCASxDR6ri95ojX3RV+lUnfNCpibX2FcBfAQUpiaaK14y2RXG1SqnxcLgZILJy/yjRsoOtqNiKCpYUjpBkNI2MpnEwaPCSA0MEpGT5SIGmUomIVclXlbBs5hVKPNTWjKso7IiGOKhroKs8p3+IRak8CEHYLrFiOMtjrfExAxem8lyxZRO7WudNMNxRVdSgIDlaAkLs4TQKjgZFBanA3aedSuqRJNGcic6h3P4KKtBcKjOiCly0qxyF64HV0/2gvY4C/Gv1X4JFC9vQxkQwdpDjEi0dTkLmAMNHuPCSqrIrHMQRAkcIwtbEpGUB16WpXEmP11rNgaQaKgvT+Qk9i1fs7uPiPQOs7E9x6e4BLtnTz5zhUTRnchI0w7YZvwxk2LSJlkxChTKRbJEDc1tQkJMWeBBANG9W4g6XF/0+8dMTPsZQgMMTDCUc19JZYsI/Rz9MHjpOcmTPogBaciViRQvdhZFQkN3xLk7t3THhuHC+RNvw5ElPRlVkRsnCMp5gnqR4UuaeEGjA96lkQ6Uc0ommSwhXIpXDz8dVFUqGStCq5EZWgRbLYWhcroKA47KgUEKVlV9gQdfGigaAsqIwGqjWBqpJNG3LZSgcoLU4MZHm1nCIA8EAmpScknM5dW8v+ZLBWdmtDDXHEUVB94ERVNflyPdYMCoZV82gTtNwDhcxIfUOVASYCxsICY7CnVeP+jGCItdovwJeBdwhFXFHLhF8f2I4f59RtJxA2WZFf4rOVN55qDtJURcYjkPYslB1iaFBwrZZmC/yzKERFAklVWF/zGBjS4KBYICMprErHOS+liQOoLsuc0yTOaaFWrL5VWcrfSEDFygrgtubEhwIVabE20LQ3XuQeLFEnBJOVqFpTx5LCNKhUPUW5Nh/U+EApYBOKaiTKGe5bP9NBClgIahIxkHFJmUEsDUNW2Gz6vLMGX/qHmRGxyy+5jU7lKIQc379o0UHrviX3kBTqWTvDQXnKOXyB62QvmJvOHxF+YghBlc9to2P/P0O9scT7E8mgWr2TE1BtyTJbB6kRCsXKSbDRMyCTKFZuitlOFPqFYb+gUS5cE6/0XS9IZVNzy6/3m+FmgJPDV6d9+5BN4Mywddf3jfAh/5wE4+1tCGPWCQ4Wc4TsAtXBO3C8y9Kb36vKr/vnZuZZXimQQkgXrZIJ8KIUsUtCClpciV/P3sV3Xv6Jh1f0hVe0HftjcCNM2xqw+GpJmbCRuyUbJFASCUYUpmvShQpGU3GKBuT0+7rVmmKi/gcC54SwsYvt+a3t0Vjc0dzbiJXYsSR/KWjmfb+FHsXdFKsBpGq7eIaGrGc6RcFNcJTRQPAwMdjOYiNNQD8rvm7I0ZBbVKEgiLBMC2Gm6PMc/eREsY/u5TPU8BTHmEq9LLFSGuc/BwFNWZS7hQscPdyMNRCi8j5jUE1wnMe4UgShaHsQOecppFEGwfjDppts62tE92y6VAnD3P3OTY8LwQRCoabsjnCVp6T+/eDrbA33MZIS4Sy4qfaqxWeFwIu1uLUXhaafWPlWE+2n4fKK0gnA36wWCM8HyOM6GGzw0lNMDSASY85SNS0/RihRnheCBFprQs4k9dw1LFwbO87tNmC54VwoLn7fRlt8rqQoyJOLqz4K8XXCM8L4VV7Xyp3Gi1rh9U4LmChsUd0sS+ZYOdJS2NPeAGfJ4WnOp2eiLuin9qbTrZ2S+msf17vO86rtz2NxKwSgs/04fmiwWdm8IXgA/hC8KniC8EH8GgTs1hrfw744LhND8k1mp9BbRrxXK1BrLWjwFSrtHxUrtE+OdP2nCh4sWgYOcr2T8yoFScYdS8axFo7D4SrHx28Kc6Gp65CEGttExg/KtUfYFAn6v3rmzw02acu1E0IYq39m3p9t89k6ukRXlTH7/Y5gnoXDT4ewReCD+ALwafKrBKCWGsn//kBqxciVn8FsTo6MxY1DnVpRxBr7Q8d46lTtzOI1XEgPW7LuxGrHeS6yfcnVvcCXVNcxQFCyHWTR8qeANSrQek9x3heJzA8YYtYrTJRBIdQEatzyHXR6jEPAady9JxJKmAiVq8Bng58DbnuhJluP+OdTmKtLQD3GE8/W67RHph4wdVHtk4eyZEplI6XMtCKXJer4TXrTj1ihONZf6d/wiex+pM8cetkrSfBBIAsYnVD5V6aUSGItfY1HN+L2XD4YqsN4D+P06Tj4a91/O6aM9Me4X3HeX7TuP+fc5zXOl4aqp9kpoXwjuM8f/e4/9/Podx69SFfx++uOTMqBLlGu49jDxShkg+yerF1LvCK47XpODi7jt9dc2Y8WJRrNJVjF8PEhMxy3S+Yuuo4nuNpF5BT/P0ZuU4g1z1+HNf1HPVqRygCk2e2PjHDk7bIdUnE6sm5eCtsRa5bDoBYfTKw8SjHjV0NuU5BrNaQ6yZnAW9g6tXE/N5jPG+yEADkOoXJ8cKBMRFUjtlcPe4rR55d/XdXdT8nmgigjqOYxVr7WL44Kddo/7woEKuVavzg8xSYVZ1OTygCwBfBsTGrhOAzffhC8AF8IfhUqacQflrH7/Y5groJQa7RXl2v7/aZTL2LhsmLMPjUhboKQa7ROoE/jdvUx/H1RfgcI16cFr8f6J5il5RrtHp7sIbFcw9WrtF6jrLr9TNpx4mG54RQZRET+w4+JddoP6qXMScCnisafOqDVz2CzwzjC8EH8IXgU8UXgg/ggWRaafHWD2aJfnqEdsVBbDhDfsDPp1gH6ldrEKvVIobtEKVIEgGMkGSYJi7gJtUfYDKz1M0jpElkLZLYxFCQSCBBiSB9PMQ55hke8FYnEnWLEQKYoRJJFCQuMCxi7NfbSRkxgpjqRvGFC+tl24lI3YRQJoqBhYnKQdqwZQDDkggzwN74PArCuLNetp2I1E0IRZpRcRgmWS0DBIemHLRns4xE/OWaZpK6CKFXvPePEoUSBmKSCQJNSixN4+bAN1P1sO9EpC5CcAk93ySEg8ZU81hNVSVsOiQcM3GT+u2jJen2qSF1EoIxNu9MP3I6I5AKBokXyjiOQZc73HRX6FvhSRfxqSkzLoT94v2fP5wRSyFMCQMTkEgkKjZd+QwCiQKklCQFyx2YaTtPNGa8rh4j/f40CXIEKWNgYBPEnKTIABZFDAqBAEbRPpYJsz5PgRltWSyLtysWprONVeQ57O0NLJqOyDsxFAjTH0qgmw6RQplsxFjzgtzrvnQs3yvW2i047k0IsQplbDK0A+hyjeYPyGCGhdAn3vULFeNlu1iIjY47zg+o2Gi4oLjsTzRRCh/OTKObDk7JQqqiIAJ6y8v7rilNdX2x1g5hWWlUVUdKhGnZMSk0U1Mp6SqIKWfEH8p78D9yjfbu2t7x7GHGhHC7+G6gi/2lJEUEAhfBAM2M0ESYDFFSpJRm+kOt5BL6pJcWyJm4ikBKgeK6KC7PuTr76r+N3cgncncTCpw/4TwpwXVBfdLrgUgqORTeJNdo9x/3Tc8iZkQIg+K9J4+S3NhGRsgjwpISJl3jUiNtCJ7OYy0rJl0jnLEQjsQMKmiOxBVQCKjuXYta8tdfemZsOB7FUZWj/eqPh0MPyAEOAp+nkt3tIJCTa7SGCGSnJVgcFv+RAL4O5VeCq8bJkCBFjvYJx7m4tDKMSwSQCExOKz3MFmcp7vhfsSuxNIEV1lBcFyEBRRAuu0pQ02ODyThSmUIAUtZCGIcuoAFzgf+ZsHOtXbmVSgofG/gG8InZFntMV/XxGyrWayyCaoQ8KhKBqPYxVn5iGeI4yAnNy5IgCoLOzBCqXemFVmwX4UjyCQMzqCA1BSeg4ugKdlBlWW9qsgiqXk7MXPyjUEn91wb8F/CWmfriWjFdQnixi4uGhYYNCFI0M0grEigTIEOUBKOAiSCHQh5BAReNkh0imjKJDZWIpC3KoYp3UFxQjni3TYUykZI5cWPVC0ilbl0pq+v1xcfKdD2pfoHErfoCgFFaKBBhP11kiVLAwAUUymO+V+DiIgmZkpZygU4zNaEBWkzRHC2AoOm5lEf9T3yIt5guIfyXxMBFpUSo+kUVV++gYWKgIMlMkVhNx0LHxEYhJk067CxGuZK+2VEmS8FSBabuqaGXOeCY2jvqybQ8wRb5mR9IlAs0zB8UiLhFgjQzQCUgdEmQIUGWPjonnWtiYKMiARuVZjvPqv5e2jNZQpaFcB1wJUiJpQoGE0FW9A5VttUHCfwM+DjwMWClXKM9XC9jjpUZa0fYI96/I4i7qEiCIDZxhskTQiVLM4NARSbbOI0hupBUvEMTBQSVpz2ih9kXawJFgOlI1SV5dfbVmdYPDu7OREPz0VQs/XBDlOK4uELAVDWKJ0YCm4AXyzXa1uN+AB5nRlsW0+KtbpFW4aLRzCC9dJIlSYRRAmTIk2CUdg7V2MpoNJEnQonN8TbKqoF0XaTKD68ZfvXrJ93Mp4tO2HWVQtAYt/EpieBvco32nOO6yVnKDHc6Kac6aJtsDEwMcsRRsZnDbmKkACgQZTNnYxJEChgiRkiWaMpk7Uy4KXRl/tVHjQzlh0Nq88cyzUY691NLVR7KxyIfBm7GlZdWaqhHtDoK4VLJGP/nE+FX/8+Y8eHs28RnpFp19lmitLOHTnZNOGaAbrZxGnlhIIXCArmPHUbPC59Xet3vj+e7xVr7VuBiKq2EV8k1WkOtuXA8zHg3tEGh6BAMqdWFXMJkJx0TIU2hKgIVh6LQOV4RAMg12tOP9xqNyozXuzS0lhB5utkFuJjV6uV4UjThCgUhJWElR5/R+v2ZtvNEoy4znfLibVLFpkALCg5RDqBR6Vl2UdjC6QzTgaUJNLfAJc67at6T5DORuswmShF3BZoSxgYUcvSgUgZcogyzhK3kiCJtAydoXF0PG0806tIkl6PlYoMcChYRhonRR4A8srq+p45NB32QdLRLi2/7bT1sPNGo2yTYgniNDCBQxjUauwiU6qjmraxgufyoXyTMEHVrpFdQJ4igsq3y+QBzMVE31cOuE5W6CSHH5KkKEskQcxhiDqfJD59aB7NOWOomhAj5N9pHrPntolEiRoK+r9XJrBOWuqbX6xPv/2iEwscDFLEJkCdCisDjS+WnT6qbUSconsizuFd8IllCasvkx4bqbcuJiieE4FN/PDW0x6d++ELwAXwh+FTxjBC+eMHN0a+f8ZfWettxolKXYPF5r9r6GhX5w5FIUCSyBfe83kElUSyhuC6OK3m0q+393//T2Wtn3LATmBkXwjUv3/Q0ibjVkFAOGizt7ae1cHhyc07XWN/eyrzhNArid1+95YIXzaiBJygzXjTkNf2WiFAIKxrNRZuWwsQZ7lHLphwwuHXZfJb3Db3wU2fc4LctzAAzLoSF6ZyiS4EUIMTU3kiRkoJhsGluO3OzuZa1q254+QybecIx40LYHo3iClAdB01KhpKxCX2QRU1lVzIOQLxUIB8JoNvWz2bazhONGY0RWt/RdxPR4DNfvusg4wca6KZF62ia4VCAR5uSPO3xPfSMZGgvZHh8fg83Lu/hpbc9etEb973srhkz9gRjRoeqlZoiz1g5nOHI0SamobNlbheKdHnFnY8QKVncdNICHpx/Fm2ZAqc/3o9hOnfgoepuozFjHqHp47lLs4Z2y+pN+2k3rQn7JBDL5knm8ximzYaOFn589uEOyHihzFd++HeEU/jI6/tf86kZMfgEY8Z+YdG8+dsV+0e5Nx7BPGIammbbtGWy6I6LVBVOGxplXurwfIdMOMA9S7qZN2B+8u/K95ySuNYfwlZjZkQIF7+t94Iz+lKJXbrB3nCIu+IRCoArJaYQdAyNTjJq1cGJtUZHCAQCTQrlNu2M/TNh94nEjMQILYXynemAQUFTOXN4lNV7DowpMGUYZHWNgONMOCcvDms0VLa47NHdY7FF1Da7NorP3RmjfL6Jlg5hn9sjP7Z9Ju6lUZl2IVz8xj3hLoQIyEpOg2cdGJjghpKmyV8727hqd+/YtrSu8WgyxqKBFJ3DWd7x5/V0pAvVVBuSPGEU5IVBbAIUm1Iktg2IL44mkO2L5QemnCSbFW/7mI32LgcjIFEMBXNYw/24JPztpPzCCT8oY9qDxbPevC8315URFbg3EuTtm7ZNKo++tWIRqu2wcjhFXte4t72FtkKR967fyKLHBgmYE5d3ilMgTBmQqNWczZXcTCoBCp9oZuQaE32JQEXDFAHKQkESoFhN6SUwCWCjYZCijC4lcSFRENgYZP8jIr/32Wl9MB5jWoVwzpv3rtjbnnistWyhui6PNsX4l4e2sjCdGzvGBb56+goGVRXCOgQ0YvkS77jrEdqLZcI5k649o0RyhxNmVSbAZJBVEYwnTZAEOXQkCg4t9KFhI5AInLE8TA4qReK4qChYY1niJSAoE6RIJQFgqaTgbga+IlFGFMR8BWcdlVDm6cDfgTxgIdd5LpnTk2VahBD/eGHFkt6hezd0t8bDmmDJSJ6g7dIXNiiZLs/Z3cvS0QxZQ+emZT2olmR/LMjlO/czJ1MgaNnEy4ermIrtcvKD+xESpCII22XaycJYqq7DhMkRJ42FjjGW+f0QEoE9dk6GNkCgUSZAEYnERK1OslHQKKJhY6Ojk0PBGcveAoz9/wxVYSzgbuAh4HvIdY/W8uI1F4L4nHXKBTsOPHLPnDYlEBRctnuIgHP4O7aHAmxqiiIVBVfAolKBK7f10jKSJnpE+8J4OveOECxV9iuuy7zsCBG7POklNDFMkBIgMZicslngAC554hRIIJBoWMQZRCeDgoOkstRQkVaCjBBmcMqMbnXEBC5DrrujVhesefVxXjr/6YfbmhWpCOZlShNEADC/VKYtU6zMatIUpC0Jl8x/KgIAd1weJFdROBBJkCUw4fUEKRIY9/KnfnWSHAlyNOOi4qBRJohW/cVD5RceZgSNAholr4kAwADW1PKCNa81hCynyw5WLqs4kx+gkJUHHS2anLprkAt37KczWwRDxQ7ok44HCBfKhAoTk2qamk6GCGUMwtWFPzrpHXeEqLr0wwJzEbhoFEgc8Q0SlclC1Cki8Gyx31TLi9VcCHsS4S91juR/vjcWZp+hs1yAOk4Pg3rlKy/ZvperH942tl3mIdsSw6nu39kUY8X+QeYPjtI8kmUgOvHlKbaLgGpuVxeJoEiAEOXK9YAyEcpINMzqr1+dIqoAULDR0Y4Qg4OBBAwKx/tYpoOf1/JiNS8aih8J/SKB+73OXB4h4MFklBFdJa8I9hk6e4zKr/683QcnnCcArWSRCRr89qSFfPOClfzP08+k5WAeygqhknk4x7IrieSr8QJutUbgsp9uigSx0SgRwEbHxqBEFJMgQYpIVNRJv3KJTXAsVzSASQSnmhjUJIasFhB1LiRsYD/wIeBbtbzwtDQoPfzf7W8G3rzq2gPf74+H33BHTzMB0yGcKxM1LaQ6dZztqiqqplGKBHCDKvvb4vQlomiui5CSSK6M6koU+1CkLgliVVP5ymqFUUOppgBXcFBwUHEwKKFio1PCIo6KNeYdAhQo0oKLQogUlaKiSJghXDTAxkZKlbGagiVApfJDciqbyAH7gHD1sDhwC3AT8FcqL/E0YCewDBgAskCMyhoREWA+lTzOK4A/UqmaDlKpmk7rWtnT3qDUumZUDkeDEK14gvN3HuClD21DL5pEM4ddrgQGetqwggb3zG3nzyfN433X38XTNu+bcL1guUzMNFEkaNV2gUrud9AwaafXVVAVlTIGOcrVF6xhoWMhgBJhbAxAEiCPWq1iqpiolJCoEqw/BHHfIuRPZ11e5WNh2puYg6779lC29M2irkBAJYTKpoXzEEBH/zAdA8OoQpBLRLCqiTJ3xSMgJRds6Z10vVwoQDYaJGDa4AgSVpGYVcCgjIHzjjly7XXjjy+LDwswfwS8ykYIHYsAOcKUsdBcB/19cfn1r+TFv57kYrRG5Ldvn+5n4kVmZDzCWW/ZJx+Jxzm1UOCsVG7CvoyAuak0ScvGAcolB2WkyL1Lu3jrHY8QMCd2RjmKQLcskqUCEgXNsYmQfWSl/PdV034jDcyMCGHu+4a/d9Zg6o26ECQsZ9L+vyWitBZLfOzXd5HMl8e2P7y4nQWD6cNxvpQVIWSK9pXum6eua/ocEzMyHmHfl1retCsccIv65JIoryjsj4Q4e+vBCSIA6BnIIBQIli3+75KTEa7EyJuY8dC5M2H3icSMjVDKa8bnH2pPYI2rMLjA/YkoASlJFs1J5wRtl6HWGF990bncedp8yq50bV07aXXqVQ/NlN0nCjM6irnnvcOyzbYxLRdFwr6gwYKSycmFEomRLC/58wOo4+x5fEU3W1Z08vXzlvP2v6xnruMaH7jjkn/eFu1zTMyoELrfPbg/aDvdMWBnwCBsO1w+Lnicu3+I0zbtJWDaHOhuYfvyLhDQtL8Pu+Qc+NSGK7pnzNgTjBkdzn4gFlmwcjRtnd83wgrHoRCLTkidv6+nlX09reRVlZhpkTRNVNsmYDrohj5/Jm090ZjReQLyU2G7rAopdY35JZOLH9s56ZhDzWcCQEqaB0fRFLHrw/dd6tnen0ZgxieM9OTLZIUgnYhxoKsdrTCx29hUFFwhGNQUenbtZyQURNXEyTNt54nGjAuhbTT99sFImKwQoEA2ZLAjaLA5FGR3MMDOYID1kRB6Jsv9Xe2yPxrU3nvPZVMuCu5TO+qSKOO1L3pk4/45racgBMJxaC+U0RybjfEoaV3j9IP9jiGVFT9fd6o/RH2GqGt6vavfuE8D3OTo6FxT0zdIgUxY1mXf/M1Kv51ghvHzLPoA/uxinyq+EHwAXwg+VXwh+AB1WtxrPO96xm3/aDftp2uuS0bTZOdI7unv3HTlCTlKqJ7UtdbwoYtuKbWbZkAgMFWF/liEZL7AYCDw4pyu7/j+DefNulXXZyt1E8K/P+3WT7i6+pG56fyE7Tcun8vNsSQFw2DV8CinZdLdP/zD2QfqYuQJRF1ihF/N+bn4zZnLPqJPIcIlg2lkT4IFQUnUtTkQCE4ewepTc2ZcCN9eeP2XehNh98oHtrNiez+RTHFs4grA7tYE+WCAne1N3HHmEh6bU9OZXT5HYcaFELCd97YUTZam0qRbI1iGRnIwh3BdCrrKvOE0H//d7bzi3s2ETBuigZk28YRkRmsNP53360jIchnqjOGqFQ2aQQUzqIPrktc1/rhqCTtbE3SlcjzvkR30x8OAn71/uplRj1DS1VgppI+JoD8WZkdrAkcIUBRaCmV641FcRWF/c5w/rVrMxVv3PcFVfWrBjHqER5Z0xZYfGMQRgh+ffwoPzu8AoClf4m23bqAzk+eZW/fyp5WLASgaOgPJ6EyaeMIyox7BsswtUlN5pLNlTAQAo5Eg15+1DICTDwwh3MPBY6g8eZi7T+2ZUSHMyxSFBA5GJy8HvKc5jnBc5g1nOH9Xpdlg1e4+4vkyV71i8ydn0s4TkRktGoZDBp29IyzpG520b1H/KO0H0wC8/sYNnPXYfi7cup/B9hjlg4MfBj4ybYaJ1XHgMuDvyHW57eKLyhL5/mmdhu41ZkwInz/tz5/NdCQJFUwuemwfd500lwcXdgLQlCvyphsfGpvj2JQtctGW/Qgkpq5yx7Kep5S4zBSvui5N+9sFLjolYgzvV+Sv50485sVZDRmFyohpF0GBOKp4La0EKYpXYVCUJmoqhNOJXFee8ssahBlrYn7Nlevl0zfvIVGwkEA2EWJHdwu5gM5Zj+0nXJo4gck0VArRAA8s7uKGZT3s/NKcJyUGKV5cPMiS4By2jSXilIACZSBkQb8GbZWXXxHBVBc+lDZPAhYGAgsbWQrB64EE8P3ZnFfxSKZFCGKtLYDnaraz9KX3P/bK8x7fc1Y+FFLn9acJVGdDW5rCcEccS1UxcmU6RiozniRQDBsUIwZWQGcoFODGZd1c8/BO5Z3rn310Y8XqlTY8KFFUDXfSyx1/4pH7HBSGmY9OiTiDk1LrjBfF4XNAgDx0LRfQKllRHCAFlIARKgL8DZXsKG3AGUAv8DPgINABrJ8gKrE6BJxJJbtKExBErnvwqPdeA2qfZ3GtPRfYIFy3eclAivf85V5UIXAVQetAdsKxGxZ28NXnn0e4ZPHJn91Cc75EqjlMKTyxNXHE0NAtezAmOeud6589sWFBrDaAh4EVx5P80kZBo25hwT7gSuS6RxCrLwN+CTQzMZ/nBuC5yHV902HAdNQafgg0S0XhNfdsJJYrV1oOq1Pb9rXE+fMZS9gwv4PvPfMM8kGDwWSEd7/pOXznWWdSDE9uUm4ybfa1JtqAz0/xff/CcYoAqKcIAOYC1yFWq8D3qYgAJt7S6cC01Z6mI1g8/dD/NOVKyGoroq2r/Onspfzw0qkTm5QNjU3z2rlq867J+1SFUKVIuWSKU6+AGUuDO52cB3QC8/7JMedP15dPh0d47ND/jEZC6CULpMRUFX5x4Sn/9MSDyShldaJJeU3ltmU9pCr5le6Z4rTboe5p72rBg0Af8M/GXjwwXV8+HUJ4PZAXrssPLzyVfDRAYjhHJhSgZDyBA5KSL11xDiNBg7Ki8Fgyzv+etIS9zQnaM/kM8O9TnHUdMOg+yVuZHEZ6gkHgX6sB41upZHs/km1MY1vKdNUaNOD1Acte9cL7H7vy6Zv3zLcCAfHJqy9hOBocOy5cKpPIlznYEp9w/hm9ozxj615CmRy/W9iDiBn8y72b9Xeuf/bRq2ti9RuK6N8LYomjvepDd2pjoGE+KUkcGXuMf1pWtfKp4VbWMz08mdukkk29CGSAO4B+YA5wCpXawo+Ax6kUB3cg1xXH3UsCuIDKy28DAtVjJiegqhEz1o7w36f/Wfa1JPjxBadwMBll3mCKT/7sH7z/tZdP6lha3j/KqsECp2/fRVZTuGduGzf/+KQn9VM2xUtsHXeqlJ6OjRotoaYimAEXFRdRXcvh6MiJ/28r8AXgVOClyHUN0xEyYy2L8UzxQYly5sd/fSuZgEFzrkTItOlO5SYJYf5QGtDJhYKEi0W2zWl50mo15K+1knjFXw3Kzx73ggtCrotoQBSCKfHWBTEGd2qH0jAwuZ1AAm6lncCS8CxNrrvVk4VKjZjRwatrLvqHDJQdustFNMfFMB32tcRZe+X5ZEKVauPckQyvu/sxts7v4YxtuxiIBvjdKQvNHV+eM21DlQritc+3MFaXSX6wnZ02IJDrJneINDAz2umULJvcPK+Lq/fsxxGCchiaTZOP/v5OtnS2ELAdFg+MsmVuF3MHhoiUy5QTIcqhwD+vbhwnYfmjPwF/ms7v8Doz6hE+d9ZfZUBR0KrfGSyYBEoWiu1QjBgUI0FywQCG7RCwK3GhBbx3/bMb2St7ghkdj2Aqygs0txJYRzJFkiN5QgWTgOkQy5RQXJdYsTQmAqikQPeZfmZUCF2Z4k2iuv5jJDuxV1d1JG0H0sRSxQnbG6ChaFYwo0JIpItWIF+pcYkpiiQFkEdYNBryh7PPBDMqhJf2XWNvmddJLFXANCY7fRcoRia++OxR1nnyqS0zPsFFt8z3PLJ0LoNNUXJhg6KhUTQ0+pvjDHfEcbSJAomXGqbNxtPUZRLsovcPahds22/t7Ghm3rhQYcn+g3Sk0hNa+rY3xeRX/36hn8dhmqnLA975xTZ7V1N085auiTOYtvd0cv/yxWQDlfUch4MGS0azzVNfxaeW1DU/wtPesM3RQlGlbdyajuFiiZXbdv5FU5RPvHP9s6fqdvaZBuqeXu9jF958byaZODcfDhEulUmm0p/+rzsu/c+6GnUCUnch+HgDPwjzAXwh+FTxheAD+ELwqTJj4xHEh/PKix7f8++acD8ctu1dYU0//5u/PiX3xGf6zATTXmsQ7x1VXrF5e0bGQpEbTpnPSDQEQKJQ5vKt+1/5658s/9m0GuDzpJh2Ibz2Oeud5z+wS0mmCqQiAb71/LP5xxkLAFjRO8zJG/f8MSTEfF03Lv9/f1o1MK3G+ByVaRXCm59z3+AlD+1vfWhxB0VD48JN+0gHDP7fZaeRjwTIxgJoUpAL61z5wDZKiB2/+sMZS6bNIJ+jMq1CuPrlW+SdS+cwmIxUNrgSciZ6roxqO8wpm5ybSjMa0OiLhXnB/Vu595Ql8278f/P8DFozzLTVGl71/A1rH+tpOSwCAEUgDAVLQllRyAKPRCOcN5imHA2yaV4bi3btu2O6bPI5OtPmEZ7z+p3ytgUdFKsJMxVXsmwoRTJXwhkp8NKN22gplskaOqnWJHu6Wrizo5nkSJZrtve2/9s9zxicFsN8pmRaqo9Na0YDV6eyRNIxitEAiit53fqtLBrNgpR07O1DdSqDWGOmRfTAIJlYiGeM5PhHzxweDuiPUJkK5jNDTEvR8OLHd+UXjKb4yC3387yHt7OibxjDdnAEaKY9JoJDCGDuYIpAQOW1Dz7GLSsWzfnyqX+e1rkMPhOpuUd4zmu2XRBJhNQl6SzFaJiyVHi8Ocnm1mbiJZPXPPg4bb1TpK/RVbqyBR7oaSdZKnH/os77gMhU3+FTe2ruEZrKpbtOGclRjIbZ1NPOTScvwFUqX5MJGqw7dTGjTTH6klHcqhpumd/F51eexM5ohIc7Woi4DppQQ7W2zefo1DRYfN6rt2VWjaRiRvWSt89t55YFk4t6AxfT0AiUTJp7UxwMVKbKB20HqSosSGcJRA0+tO525ZoDL/cHTMwANfMIF7xp567HeppjcpzTn5MrTnmsWU2YUQ4aHJx/eNxiSVO5bNtelgym6Owb5WeXnLK7VvY9Ea54nXDF6+q+xlW9qMmNX3vlA+cfPGn+gn1NMVoGc+RaK0X7ktEsp/WP8GhHZfypYdmYoSPmKWgKqAKcyg//sh376coW2JuI8ujSznm/mPNz4+V91zypMe0PiusUgZMMYNkny/dmjtw/IP7twiKRP6rYySAlYRLCJoyLoIk4EdI44nVYGJgEpEQgkLZGuqBBX5Hmq0PknwtyqU7p06r86f7jenAe4riLhrdf9aD6585W+2BXC7GSxYseO0BIdUkWSzhCMBCN8szbNzAYDxO0bN7+9hdMvIAr4UAl7d6lO3tp1iQPLepEdxwu3rSHgsT+2d/P0x8U14kz5bVys/jKqx3kh1ycpQqaruCiYqEgsNEBiYLERMUFDBwUXAxMBAKDHDEGkSiYRHFR0DHRcatJM1wkoFaPz9GMTRAFizAjOECRJiIMESQ3NiXPRUMgq1fQKZCQKi4RhlwF25HoioZUKvUlW1qoaLiiYqU8Wt5PKrM/VSrpG115xCRASSUry4PAwwLulSi3gzsqQEGuSz3Z93jMQlh78T9O/dvCrgceamsxhkNBmktlVE1w0eDknuVrr7+ZlkwlLdAnXvo0fnfuiuptSJbvHmLp1gNkowFOHk1x2sEhArbDxrltfPcZZ1LM2SSzRf71zgd5ycZtYwktDj21MHkkgiJhJJXkmQ4KChIHBYGLgY2KQ3BcupxK1lVJmAIh0kTIjO2TgI2OhkmROAWaxvZEGKJMqJqUUxAgjUEOCFEiio1ARSCwMTEokCBEBgudBCn0sWSeNlCe8J1Tp+gJUinBrerfYY48d6pr/JNp5BIYBv4Lue4bxxQjrL34H6HH25oeuKOrwxgOVQK9kWCAsiNxpUS3bTpH03SOptBtm41LuzGr2dI++qvb+MwP/04gVSKQKXPGzl7+tGox+aDB2fv6CdiVNEGn7hvkpfdsxo4YjISDXLD34NhNHf7XwcCkSHDszhwUJApOdR51EPP/t3e2sW1dZRz/nXv9du34Le9u0mR9SWnXvaW0aB/WjbVMBYlSbQiG2DShDQESfEEaSHycBOLDJiY2QBNMwMQ+lH6Y2EQnpq3bpFVatUxtWdu0zbaQpkmd2Ens2LHvte/L4cO1EztOV6mLCwz/JVvJPa/3OY/PfZ57zvk/BDEIVIRYFbACqNhEmG2gzxGAgo0AguRQKS2nOAgsgpSIUSJKjgFKRBHo+MgSIo9GhgDzREnSywXaSNPJdI0SAJX6a9tk1f/uNbPyl718bfX0sVouV6tzjeRO4DeIBx66XhthfzKk+QxPffGc3097doqhhUW8lePvA/MZRjd2Me9TGb40w2Q0zJHbhqBosis9x9GhQaQQ7EzNNzSycyqFskeyLZ2hP9c40wigQIiqzasAPixKeKmKy0atEYj7u6kqg43KDJtJMNFQt8oKb5UPHR1/pUyQ1Ta2TjsBFlExkViArBscDyaS1Yd5r03wKSstunmbSgj6yPUqwmLUWIOsXEq2LGTx1gTeUKUkrJfYUCjy2s2b+MPtOwDoKxQBhbzfB0Aq1BjDIR0JESiYzIU0LCGWCTaqcHAthFq4v2Zn+bpoOFhfy5bkDqhOCB/GVW/Wi4EOeCgh1hgQWaMYje1VYVNvm6t80uBWa1np7WqWp3VF/nrdx+PtpfLCUKbeMO8omficxs5WleatgQ0A3DOT5ocXxzk4PcPgkms7vL2pj+nwyovEsqpwYvNGSsBcW5C/3r59jW5cTTgrT08/qx2OxvxZulmkHQtPJbU+j8DBQRIhhZ9CQ3qAbF3taw9Xla+7CrUhn1z+CKjMZCtK4GuWGjjAr69rRnj8+BflU3e9PbCP5OleXd9aFgJFCHJakJmQRqJQ//5gOhKiK5PDVAR+22ZfcmUj0jf+NcWRTf1MtoV4au8u7ppOEbVsLscjfP6jaWS+REwvcseV2VW0+hXes1XTtEvJ7w6pgqx8O2saU7XI0kOWHsLM085sXZpOGx4MBOClTJg5dMJIIEAOjZXHWokYKhYeCggkEkGJED4KKBhI1Jq+1xp67mBXLR2wKwNfdber5m1t8IFrq8Ya9+tUGqh6Gz9FvjTyqd3HB79+tivl86ZGt/bSvWBwS2qe4dl03dyWCfgI54ucjUZ4Y+sgPx79sK6O07EIf0/0UPR6iJkW/VJyz5kxbp1M8fR9u3MXnh+M1uY/J54OgfOwgvNbBUe18VCh2cCHgQLYKNLEI0BKFUf4MBHoKPgIs4BOmDLBBlEpmPQygadCp2mgkaUTjQJhMpVbcqM4uFJVEBV30CSIgoqDbXvRF4GsivmkQXSTB3Orn6V2BzUjUcYFTrc7J4hukLtBauD4QVErqiHBEaAI123EAeZBCVWEOw3KMXCuCOTzn5a1fV1eMT9+9zsPXurvOjyysZuDoxP05wsr0V2FK7bz7VEcvQxlkz5VwSskHyQ6MFQVT6HEqWiUZMUDCVsWcWzuPX+JPx+7c/2ItMQDSoHooUXif4qSi0hUUSSKjYKJioNWmXFM/OQMFWfJg3HEIPpihGxHgNKrinzBkeJhv5AvfqYiuqzbWsNjB0+NhBVld6xQQLPqmWKvBDWeGd6BVjb5xdF3MDQfv7x/L3mXaBuvZfPIyEVe6eoiHXCt64NjE+y9fOWFn5z56nfWpYMtfCLWddHp0DfPP9en69/fmC8sP7kl8Ks7drhb1hRBR7ZAoqRzdlNvXdltqSybJ9L8Y0MPmmnx3XfPyWdO3ts6gHODsK6CfvnIjh98FAvJnMfLK58boKgqHNucIJ2IQpsXgh7mExHO9Xc1lM1o/mU7+qGTH1IIBhrytNA8rPtq2+uD/Wqw7Dgf9LQzF9QY647Rv6TTWSyRDAfYNrNAV77IS8NDdeW2pzLE8jpPvDnCzo/n+P2B4fH17lsLV8e6K4L8eUg+ui/7unVT4r6x7jj7L82yfcFdVMp4VYo49OSK7Ls4yfEtfdiK4JaZDF97b4y+qTReB57dfxu7cNYO9dJCU9C0Xcy3fu+yTPbE+dZF94jCSG+c9xIdy+l3Xkpy/9lxpBBMd3fSO5uhO52lXLZ49+aB8b8c3bWlKR1rYU00zRg7MJFkYNFdHyh6VN7vqefEOjGYIN2mYStuBDjbqzDbEebkTb3EivaOZvWrhbXRNEUoCvHY9vEZbCDv8+Aoja8D0kGNC90dWLYDHoXJNo1EofDms2/taZEr3mA0TRF+99qeP57e0ImSKxI3ymhmfRQer20TMkqc6OkkpuvkEWiOw86puS81q08tXB1N9dOHr6SSS5ZNJhDg0OgE7ZU1iIhR4tunxgiXLb5y5mMMwyRQNFBzhSdam1X/M2j6sfhHv/y+kVgq+af7O9k+laTo9xIqm6gSyo4j39jYF5+PR8L/fC7xmdn/97+IG0Kv97O7j4/F53JDS11RPAp4LQvHsmU23tbx5Ktf+L8KmfPfihvKs3g4cXjQ8qiHsl2Rv/3o5IHJG9ZwC9fEvwGA0Nqpd/jK+gAAAABJRU5ErkJggg==" id="image1914ee789e" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature26_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-32" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imageb5bdf721b8" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pab11fe2314">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
