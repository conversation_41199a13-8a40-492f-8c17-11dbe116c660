<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:48:50.489677</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 468.219739 638.149 
L 468.219739 27.789 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#pabdb660da6)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m42d58802bc" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m42d58802bc" x="432.536787" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(419.18124 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m42d58802bc" x="468.219739" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(459.47302 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m42d58802bc" x="503.902691" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(495.155972 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(43.983906 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_147 -->
      <g style="fill: #333333" transform="translate(201.196562 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(86.54875 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(79.746094 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(86.54875 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_2 -->
      <g style="fill: #333333" transform="translate(50.14875 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-32" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_167 -->
      <g style="fill: #333333" transform="translate(201.196562 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_174 -->
      <g style="fill: #333333" transform="translate(201.196562 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-34" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_mode_Number -->
      <g style="fill: #333333" transform="translate(103.030312 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-75" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6d" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-62" x="2108.890625"/>
       <use xlink:href="#DejaVuSans-65" x="2172.367188"/>
       <use xlink:href="#DejaVuSans-72" x="2233.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_110 -->
      <g style="fill: #333333" transform="translate(201.196562 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(209.467812 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(57.796406 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_63 -->
      <g style="fill: #333333" transform="translate(209.467812 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_mode_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(23.84 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(23.842031 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_mode_MeltingT -->
      <g style="fill: #333333" transform="translate(99.286719 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(147.772656 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(62.252969 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAIYAAAMjCAYAAABgQGhRAAB7b0lEQVR4nO2dd5xcVfn/3+eW6bOzvab3AiGEXhQUEBFswd7Fjn5tP0SUoiBVo+JXRez4tSIQsIFKEaSXBEhI79nsZnubPrec3x8z2yeVZGf2zn37WsncNs+987nPeU57jpBSUgr8tfrXlynSugbBHy7q+vinC21PsSNKQRi/nn6P9fLSuUra50XPGBz7yjb5iS1vVQptVzHjeGHcUfXb9S+95riFaa93aJtiWdiJPvmT+093xbEPHP9gRJk+ShQAtqqysK1LfPTtL9gFMqvo0QptwNGmMpZENS02VoTZGfYTMC2O7Y3yr2lTqEunRaHtK1Yc7zFCUYMO2+Dhpmq2lQVZW1nG36bUcNNDD6MmU1z89qf+X6FtLEYcLwwhBA/OmZb9t5SEMyYJXeeeYxfz4ZfX0OYJ31RgE4sSxxclfpkiqWuctHcPn1/1OJbm5YkZS1kzew5vXrcRzXb+MzgcHF8reU75kXzgpAa+9sKdeGwTgM5gBd86/39YXVNBt2HLDb+c6XjPeag4+oE8In6tKkLwhVX3D4kCoCbeyzlbnqatvAyPJtwANA+OFkZnxH5UKgIPqXH7quK99Hl16o10ASwrfhwtDEvKY9dOqWFz9Zxx++6fv4zugJcFXV0FsKz4cXTgFfX5w1/+yJv45HPTsdYKjtvzCgmPlx+dfiE3v/b1nLanixkd/YU2syhxtDBap1YoSZ/Os1Pq8BsX8Melb8VSFdpDAT69aiszetqJa16i4nOVYfmjnkLbW0w4WhgeAy7/+1PM6uwDIK2prJ43nbqeTkKJGFrSpiYRpdVX1j1DfIguaq9qkituAIiLj1eYyBdVzAdD8jefKOR9FAJnVVfF8rcC16SE5/h7579GBLrCtFeVjTqkOtnLm3f/F4mGiZc+qlCQgE05O4nTiIXARwaJioKFSgKNFAqxVg15HnLl+oLc3wTinOBTLD8DWGmhLttSWy+uOf9iTHX87UV1Pzr96HTjYQA/CWwUBII+ZmDhxYuJRAMENhomIWx0JMFG4AXEcs9E395E4xxhwAcARcHi98edzVnrd+NPWuMOqkjGABCASgyNNAKBThKJisRm7GORKEh0JD4shB943VG/mwLjJGEkBv9RmYxS0x+nfCBFZV8CcsWlP2Uwp6N16AQBSCQKJhppJAIbdR+Xl4Ak1xoWP0r3UDQ4SRg/BWIWGpeseojm6jCoNlPaoyza1sn87V3M3dVDnbl36AQJxISfCC2kCZH1DQCjh2moGKgYKCRRkHuBJyfsrgqE04LPhcCXDbT3bAk1hZrFMZRHDQbvsIY9zGZ03Njua0RLeUgTRiWJjyQGOh4sFEDBRCWFwJAasX8p8G7kyoGJvrWJxlnCGMMD5b+U4bSFlpY8fsoClm+5j9ndu0Yd0+2tpttupMKISgWlqUp+Z8ilmOLDEYmS1OWvMxNufIFxdDuGJCE3z5ghQKF5Si1rEovGCeOFKYs5f+sX8nakafI3Jdss6qQYYxwb6yotPZVix9QaAB6dezoPzzuTpOYlofvYUDWDPyw+s8BWFieO9hjz93Z0ttRPb6htGyAaDuIxDO5feC5/WXIBG6vC9HliXLRmM3BCoU0tOhwtDJ+w/uJLW58u7+9joNo/5B6jPg/PLZmO1/SxuTxaUBuLFUcXJef0X/YZW8KeWVWjbjScytA4EGNRazNrKyoLZl8x42hhACSCOmn/+Bbspq5+tioVnL1ro3OrZa8CxwsjFEuiZ8aP4Nqs+0HzUp9M5DnLxfHCsFSL9790F+FUtuZpCMFDddU0hwOc2d3HyxVN7ti+PDg6+AQIKp00xDv47NO/oC1Uw1P101m82+a4BWfQGWzA41EuKLSNxYjjhTG7rQ/Idpg1xDq5eGsnNoLt9aewvVxw359OfbSQ9hUrji9K2tRpbCifO2rb2spFbAtGOGHnnvH98i5ACXiMmE/55XPBEz+2rWwG1aluOvw1dHpqmdPalrpszVv8hbavWHF0J9ogKyt/mcz4Qr6kX8ebNpEZ035/x/v2NfDChRIoSgCW93zMH05lKgMDyRWYcrorigNTEh7D5dApCY/hcui4wnDJiysMl7y4wnDJi+PbMQDe8L713zd8/i+2hwJUJlMs2dOaue2B07wHPrN0KQlhNNdUfXFjTfnQ553lYY95wdOv/OyB044pnFXFjeOLkne89aX4purIqG0tkSCd4bLFBTJpUuB4j6Epik8Kwcz+OLMG4sQ1jVeqyqhIjB+j4TKM44VRPpDgtc0dHNsbG9q2sGeA6f3uWM/94fiipDPo4Zie0SIIWDad5ZF9nOECJSCMmmg6b1q+Mrco2S+OF8au2go8qTSWEPR7PXT5fQzoOoFMmv+IXzn+/g8Xxz+YkGWSNi16vR4Suo6hqsS9HrY0NbC1MXhjoe0rVhwvjEgiJbZVlWNoo+PsWMCPJYSbYH4fOF4YL1ZXAmbefT0Bj+Pv/3Bx/oMRgk6fl6g2emzOgCI4v+8lZae43k0ZnQdHC+Pd5z0zUClN9tRU8N/6CraG/XT4dLqFhd7bz8vlS9GVvXaruMrtNxmDo0dwzf/iXrl5Sg1IyTE7OpjZ28+ljz/JcS2tGEJhV6iaWns3WhyqSQUi8vvJQttcLDhLGGL5jJvOfNOv/jbl+LOVQFhsmtpAV8jPR57dwIJcElik5KRtW1jYsgeAPkVhmr2NbqYDYKMjSMsAqbYU4dOmymt27ePbHI1zihKx/HV/n790W6dS9bqdU6eKZT0p0prKxS9tGxYFgBC8OHMWGTUbc9TZHRiUEyCDjReJhk1QxKhsCNC/s1lcv6wwN1RYHCOMlKrd+JGLPql8ZvWzJL0+EArzOwdY2jp+dQFT1UjkVlbsp4YA3WTwDuXsyyJIE0Qn/ZsJuoWiwjHCEDBtQPezpmEKKV0nLgSLO/pRgLGFZSCVoiyRYFtFFXFCKBjYjK+cmOiomA0TcgNFhmOEodnWvWfu3cTszjaELdnp1cGWJDw6UohhcUiJals8MnsJvZkICY9OijL0PG0dPuJk8N09oTdSJDgn+BTLQxtrGla+982fOc9vw7qm6UT29uIp97F8UzPliRSmqtAXDJL2evDH07z+Xy8zxeyhgTZUIIWXND4kAi9xdDK7MoRnTpFXOeQhHTzOEcYgYrnntE9+s8nuSj03f8Cu/u25yziltYcT9vbCiH7WSE+M1/5nPbVWHw3sxsAvJf0ZiXwuhPhqktoXpsqrjALeSUFx3kAduTLzNOwAat72jldsbCnW1URY2BUlYOYmt0vJ3E2tKNKmjH7SWMYM+S3HryhwKDgmxsiHtK1LKjtjxDwaf140hWcbK9lcFuCkxzdS195Po91JH8noDPl9VxRjcF5RMobFH90p375rLY/NPZZIMsOpO/ZSEUviJU64dUC+u/2jjn45DhfnFSVj0AXM705Qmdo+tC3t9+BLxdhSW2Pv59SSxvFvS1cogCXGZz1I6x486ZSbsm8fON5jNPZFZcYzuvVKSJsdVXWk/eaFhbKr2HG8x4hYNpuqZhCMD6CaJrqRIaEqpFD430fPeLzQ9hUrjheGYlvM3NOGofpQTRthQSSe5pQNOwptWlHjeGFMbe+WqGNiDCHYU11RGIMmCY4Xhq1piHHdaJBSHR9evSocLwzdkpT1ja58KJZNUrpDPfeH44XRFlJ/EezL0LS7h2A0RUV3jHmb2kl4VGe37L1KHC+Mv/zlpE+tWtQkTakwb2sn5V0Jnlgyk+7K8JxC21bMOL5JfJC3LV/VekJLf0OPV7WenjN17jO/muVWS/ZDyQjD5dBwfFHicni4wnDJiysMl7y4wnDJS0k1/1niXTeCfgUgwBxQ5Z1uvqV9UDK1Eku88zcK+odAIsjONbERUpV/cL1mHkrGYwi0D47sMxGAgtsuvi9K6G0Zn6NNAFK8s7EAxhQ9JSMMOeQcRe4PJAppdDfOyEPJCANkbtLyoDAUbFQ00h8rsGFFSckIQ+SZtqxgYcP/FMSgIqckhCEu67tJkG+mgESSZwi5i/OFIb7SJ4ArTBRkrpKazeJnIQElT1DqUgLCwLITBHQyhAGLrChswEJg5smK4QKlIAxF6BgWkIIxxYnINna52siD84WhCoFh4yWTVwH5Yw8X5wsj2wLOP2ccO/jPEbvGp2FyyeJYYYibMheLmzKSsEcgBNsrqgEPEiUnCAHoWOgFtrQ4cVwnmvi2EcCyVyHEAoSARAaSJsFMmn//+Uectndn7kjJYI7xLs1jV5nJaxX55+sKZXex4ShhiG8bX8CWt2Y/5MLKvhSY2ThiWl8nKx67i9NbttEYHyDbh6iSrboKwEbBrEDe1VeQGygiHNO7Kr5tHI/NrQzWMwbDCcsGIQhg8LUX/8VF29fjM63cASbZR6AhMJGo2MgOBUo+w46TYoxzgPGVT58GZV4SkTCfefsnOOnS6+j3hsj+9iqM6ooXgOIGHThLGJuB7O88UhxefVSP+7q6Jn59whm5TyMd5qBAHFS2vgqcJIx/IPgPcMA66PaKmty/Bg+0yTZ32YD5qaNk36TCMcKQl+sWgnMQfJmREXWeVq0LtqwdPIuH5sxDYiKxJJjHKPKun0+MxcWNo2olIxHXJ1LoqhcLMGzQFMLpFJc/fj9XPvp3Bpu3TGx0eafbLD4GxwoDQNycCdGdGEAiZvV2sOE31+GxrFHH2NhSkXc6xnMeKRz9QOQVnhh2VvjbK2q5e+7x445ZUzd1os2aFDimHWOfCFuCKgA+eMFH+fusYzm1dQcfWv8ktlDYUNPA0gKbWIw42mMAoCoSIahKxrAVhX/MOoYyI055JkF5Os4ZWzcU2sKixPkew+Q0yrTnmr9/BdvKK5nZ30XQzACgIGlI9Ts3yHoVON5jyO9WPE/CTHktk8XdrUOigMF6iauLfDheGADylohfoCDQxjSA65j7Wsa5xHF+UTJEdl6JwMNgu7kEdOyXCmtXcVISHiOLHPp/OTjaE4gK/+cLaFTRUkLCsBnuGxEMjhTv83gqC2dT8VJCRYmd61aH3HRmJDbT0+a/C2lVsVIyHsNExCUmg5kxsnNMQJW/tfZ3XqlSMsLQ5V0hG82QyJyvUKVEWVpou4oVR3eiuRw+JeMxXA4NVxgueXGF4ZIXVxgueSmhdows4uvR+4BZCGWpvCHozmjeByVTKxFXxhcg2DCyM1VI60X7xrJlhbOqeCmhokSuH9vDrtuMH+vnApSQMJTBXjNVgJb9p6EoNHyl+8RC2lWslEyMoUmbTMgLnmwuNsWyseMmWn/mP0C4sNYVHyXjMTIBfUgUALaqENYkPV7NzdqXh5IRhifPlKKYz4OlCrdmkoeSEYZpjq99qYaF37Rcj5GHkhDGbYvv+45tSMhYw5PZLcn0zigDZQFvYa0rTkoi+DQD+mWRTIZ+RUDKAkWAJUmoCvXJzIEvUII43mN879h7e0zNx8LOnuwGCVhZr9Hp8xL3uHlS8uFoYXSID51RaZkVCMHCnv5x+01FoV91l/LOh6OF0cOcJxZ3baAsmmBa93hhICXlyYybAiEPjhXGavHl5mlsxtcVYNkru3nLUxvwZsbMLRKC+njKFUYeHCuMCmRTLw2Uy3YqaOXx+VWkPaNjbZ9tk/TonPzOdasLZGbR4qzeVbH8gl1M/1uSStVGp4pWWpjP4KyzXZVl3HzhaTw/rR6EoCGR4l079yKlRFgWesagPJ1GkRJbCDKqKhV4WcCJ1zx4akmNJndOdVUsPyaJ9/4eZiLRCNDHXuYwmIQr5dURfp2v/mcVTy6ZQ0t5mIyabdsSQiAUBXQd0ikEoEqJzzRFwudbqlrWNmBGoW6tEDjHY4jld29j8cX9NAAQoI8E5QD0hf2sWTQVW8mVnFLSWVVB0uMZTvUoJYFUGlMRVMYTQ5dNaxq2quJPp5Ur/nOmQx7WgXFSjCFGrquaoIzBSUW7plQNiyJ3ZDCZOuiFSiSQ0ZzjXA8GJwnj2ga2opLOfVQooxuQZDzjf1TFskeP25ESU1WJJJLDmwBL01Bse881D55aMt4CnCQMuXKNj8zbqtkhy2khSC8h+ljEUzT0dow7POHzDvsY20ZYFraZrc4OTmJMqSqKaW5UbXv2RN5KMeCcGGMMO8WX7ensFCnNi24Z3L3oInaVT8cWCttqyokHg1Ql07R7PcTTmd333714eqFtLiYcW3BKOroyeGp0M8MdJ7+XzRV1KJZKX8DHI3Oa2FETYUlLD5FYiv/+fq4rijE4pygZw0z5u9qXKo8lo3rp06qoHzCojaeY19nHJ59+hYpEmjV15ZhCONNlvkoc6zEAwr09lz5ad+Ztip1ds+SFumqeb6hGsyVTOvrpbazmucqyQptZlDg2xhjke8f/ow+fL/JsQy1/XjhreIeU2XEZEilvCDrWcx4ujn8gX37xwnKAZ5pqR+8QAiRobtK+vDheGADY8lfdvvwj+BR3hc28OL4oGURcGc97o5qRzBjfrnbHfY6hNDwGZO90ZBt4bkE9U3eH9uXD0bWSUagi23UytoPEsN15JXkoHY8h8nSZCdBx2zHyUTLCaOiJDi5bMoSQNlLhnQUyqagpGWF0+P3Sl8yMmO0uaehPYNxU/o9C21aMlIwwLOSitKJSMZCkti+OJ2PR6lV3FtquYqVkqquDKF/peRqYKXXPInljqKfQ9hQrJScMl4OjZIoSl0PDFYZLXlxhuOTFFYZLXkqmSVx8feAadO3awZWbsW1T3hB2O0r2Qel4DFW9lpSVzY1h2mBJTXx1oLnQZhUrJeExxNcH1oOSzdo32Gdi24CcUlDDipjS8BiSuajK6I40xR2isz9KQxgW+XtXXWHsk9IQBihDQedILMnZH17lJk7JQ2kIQ0VgjEjlKCWYFuiCx6qn3FtY44qTkgg+8WqQtiBt5kaH5wQS1MDwn1FY44qT0hAGgK4CdrY2ogjQVLAESNtdqTkPpSEMCdlxfCoIdcQ2wKu5MUYeSkQYMluEqCNCKltmvYc86PwpJUVpBJ+QLT7GflaVoSzBLqMpDY+hqWL8vAGyXmSsYFwAh47g2lR9ubJZ8ybr7IS+vbxKXPfGd7Ghegp5xaFIsO0T5TWBVRNuaBFT9MIQlw+oIP8MvA7wAx5sKarj/RhCocKEYCbN3K6dzO/dK7ZWNdASrmTKQDfv3PQ8tqLws6Vn859Zi0HXssWHaWU70gTg18CwwLQlioSwbweKcjPwB3mZFi/ozReQohaGuLy/EugElJFveyQZQxEaAkFPbrLyWzevoj4+wE+Pfx1N0R4e/dMtPNswiw9c9KnRF/UoYIyZfKYpWaFAdnpBuQ8U0QOcKi/Tthy1Gyxiij34vI8xogDo9wUpTxtDogD43OqHuOPYMwF44K7vMaevgxUnvXH8FceKYiyWhJQJUAlc82qMn8wUuzDm5d0qBNEx64zM7evAFoJFXS0c290CQMzjG3MeecOMoX2DWEPiWXioBjuFYhdG/lliUhI0Ric8eWLKPN6//mmiHh927rY+tO7J4QM8KgR0CHiyccVIIWQz62QR5FpJAXjkSNzEZKTIhSE+DnSM7R+vSgzQ4fNSlUwNbfvFsWfzudUPccnax/n3jEUAfP2Zv3P94/fQmOgdPUhHUbL9J5B9AkYuf7wgG5x6VYB/A986ijdX1BR18DmIuLx/EfA+YAC4CPCTSs+vSUb9AlWcuHc7Zcl+ugMR8cn1z4llrbtEUitHCIuXaxu47M3vorWiavyFM1au2JA2NpKA8iRB34+AJ+Rl2t6JvMdiY1II43B4cuqNTe2q8dyW8vqGq9/4TmEEAlkRDHoHdTCTigSvqsirfM58EIeJY4UxEnFNXGYzxY9ZcsSrgpTIb4Xc5s8xFHmMcYSQSNJ51qHJWKM71lyGKI2nIkT+O5WA6fai5aM0hIE9sgo6jEfNLjPgMo7S6F2F7IgtIYaDT03J/llWSa11drCUiDAGixJlTEwhwaavMDYVN6VRlAiy3mHk2AtVZKuvkr8WzK4ipjQ8hmVZoGvoI94DKUEoyFuCHyucYcVLaXgMG0jbo+eVZNyoc3+UiMfAxpaQtIYaO4F997S6lIjHgBuG/jWy1cJ0KyT7oiSEIW8pu44x3fTZYkX+X0EMmgSURlECIISGaRoMFiBS/kHeVPbhwhpVvJREJ5rLoVMSRYnLoeMKwyUvrjBc8uIKwyUvJVEr+dnCe88MJjL/DaYzmJp88h17PvSaQttU7Di+VvLjeX/YMqXHmIMNLZEQi/Z20lketN/Z9sE8AzRcBnG8xxjwRebcc2wVfzp+HoaqEkxn+OojzykvVt8UOr7ra7FC21esODrG+EfjT6cZKPzuhAUYatZBxL0ebj3rBL6z9IyNBTavqHG0MISRmbapvgI5JsdnT8BPpWE1FsisSYGjhdGhhB/oqYpwXEsHb127hca+KACqlEztaS2wdcWNo2OMukQ6eMGLWzhr0y4ALCH41rmn8NjiWUxPptxO9/3gaGEYXq8YFAVkPcU1Dz7D/xomp+7cWTjDJgGOLkoyedZtV4DPP/oCL9RN5VfH/OjYibdqcuBoj2EhkNh4MZGAgQYIFGB2ewzvwKYXcfgzOFwc+1C2iZv1+eE4jVhAtqpqoNFDBImCZinoRsBt5NoHji1KPvDpN9vTo/0MigJAxyRIEsjmfS1LuwOC94VjhfGRfz0zYOEbt11jcIifQKCRFB+cM7GWTQ4cKwypl/v79OC47Uau9NTJ4CVFiqotneIy2Sf+5+SJtrGYcUQnWoe42gOoUc0z9aXqyAnVA23/LxWYekKPv4Lju3ZSmcym64xpPrqUchTFZkqqEx8DlNFCnEpSVAM2gpjUULAQ0iaYBrELaANWAn8gu16SCWRy/xUANfJ6Rw05n9TC6BBXXwZ8GxA2w0n5/Mpu/t50PhnVQ7QiSFkqiS0EUX8AgEgixkUbnsTGg0Rl9AQTicBAI4VBmEOYfPIX4KM18vreI3V/hWTSFiUd4urFwHfI/XIjMzX67SRWbvkJ1bTpDwSHRNHU28Xxe7ZiEUDmqq+jEUg8ZB/NIb00bwX+9zBvp+iYtMIAPjLyw8ift40ZROLZjH7eeBqRy9vZ1NfFa7ZvoD4aPeDFDQKoGIdq01sP9YRiZTILY/O+dgxQRUNfjETIh60q6CkDG5jV1X7QF1cwsPAcqk17DvWEYmUyC+O3QNfgh5EtEgIF3bKpjiUZqC4jXhHC8uik8mXVyYtEYXhu0kFiA1cfygnFzGQPPv1ky/V3GRCyhKJo0sZAo59ybBTaqyPsqq/gL8vmodhpfvW7P4+4gkQhg42KSiKb/5UMJj4sgmRjjHHvjiSrmDRZYQ4AzwDfq5HXv3K073mimNTC2B/t4suynzq8GGgY7ArW8LfjFjOzp5NztmwhaCVRMRC5/hSFjG0T/G+NvOF1hba9GHCsMP5V+V1rfm9S6VYiaGMW491VV0Eo08upvWuIUyHBuLpG3nrDPi5Vkji3E6229ofHDzz7hdV1s5jT2kVnOMCa6XXM2dtNd1mYgYBFbMBDrfmdyRxnHTUc6zG+cta/1N5gxPzsP5/nryfO5/qLz8JUVYQtueQ/qzi1bSfTO3dz3p7L3ZFceXDs2/Kdx8634pqPmNfDt99yJmZulLhUBL96/QlEYlF2VlQ58604AjhWGADV7V38a9l8Er7R7RFSCForPRzT3LyjQKYVPY4WxtK2Th6fOWWoFXQQxbaZ2d9Gdb+2uECmFT2OFkbGqzJ/VxvfuvMRyhJZcfgyBpff9wR9ehVz5VWpA1yiZHFsrQQgnEjakVRaee2GXfz7W//HtvpKpnb1o1k2jy2tKbR5RY2jPcbWqvKbW6oi9Ib8+EyLxXs6KUtlePSYOeyubnADz/3gaI9RnTL++kJ1xdf/8MYzOX7zTspiSbY31bJ+RiPHbFzrCmM/OFoYr9nc+vzuadNobajhyePmD21XgE21DRcUzrLix9FFyXHy83bSMiVjGvEiAzH+7+8n/btAZk0KHC0MgKdmNamh3l4CyRS6YVI2EEXr7b2k0HYVO45tEh+LWGEqy7btFat+MtVRg3aPFiUjDJdDw/FFicvh4QrDJS+uMFzy4uh2jJFsE98KCeyvatjfmyavdcSkoKNJSQSfL6nfNvvUClWXFqZQqDY7rcX2lSXzUhwOji9Knhffvi4jgmqT0UetGaXR6CehRNT14pufKLRtxYzjhaEI5apyKzFqW8RKYgnf7QUyaVLgeGEEZSbP7FQwxyb/dBmF44WhCEtYY6RhIXh6zjThvWrgwJNYSxTHC6NbDaLmkrMBGIrCHeeeyaOnn8pr2wdC6hX9LxbUwCLF8cKIyDg+0qBaKIqFJjK89cXnkYpCuWmxIJFeWmgbixHHV9m8lomh+PBbw31nUdVDq0dQZUJlxtzP2aWL44Vh6F6CxujsfAs7utgWDvCy309jLFkgy4obxwvDGlNYtpRHuPuE46lP2XQLg11Bb2EMK3Ic3fK5Xf2qkVICmkdqeC2Ie3Q+/753MeD3Dx3T69XYXB1S5ZVeN+nnCBzrMbZoV9hbQguFoWRnoVUaPTy5aPooUQCUp018fbFt4J1ZCDuLFcfWSnYGZg2JAqBHr2R+Z3PeYzXTapoouyYLjhTGC9oV7SnFP267t8+HJzM64VqXXycZ9Oviiu7EuBNKGMfEGOt81/w3ZMnXpPCT1jy0eitJi9Epo0NWmoGQjwePmcMjC2bQ79FpD3uRQFna5ITOfsKmRZ9PY13QT8IW+LDltJSx4qXb6i8vzJ0VBkcIY3X5jf+c0h8/P0Y41/htoysJ1gVnk1K9ICV9FSFePH42L5YFeTkUJKUqmAEPVpkPFAGWzUXb2okYFqaAXl3j2aCfflUFRbAskfr2qtvqv1rgW50wHFGURFLpN2TwDieAJUHITnFq9BVOjK7ntIG1hIJxdvh9PFNeRlJTkUKgJg3UaG5es6qwuTybe1yT4LclMwaLHVvS6tO/NPF3VjgcIQxFylG9ZBoGChIBhK0kXmlw9vqXWNi8lUAmM/rc1HDLZ0YZvoyQEs8Ib2oiSmptE0cIo9fjbdUZ/sGtEbVwSTZpoyrhiscf4dHf/4qaeHx4v5p7BFKysDs2dI6hKuzVc9cRUJ8xHj/a91FMOEIYumbP6fVqpiYSJDWVbr18qDdVojAykev0gX4uWbMKANW28ekK2JKKvgTlZja+GNBUNvq9tHh0NEUwxzCa1/6o7uwJv7EC4ojgcyQt4jrRpWas6VZCKIA9bnUBeK5hGredeCbHdfZy9+JZ7KoIQ3aC6xfjdZEfycu0km8FdVzLZ5O8RjaB8ljt/8oTO7eSxI93TLJ4Qw9x3u69mELQ3FCBmUgbqe/WHHLicCfjOGEMsidcQ0e4gqb2LqbG+wmRwEZhU00TO8trMRTByiVzieoeEJmBQttbbDhWGGftfHna47OW7k5bPtqEByEBFKxelTXHVvL70xYT93rAssHjW1hoe4sNx8UYY/lD052y0opybHszPznlNL7zutPJKNmaZ6VpklEUojeF3YHBY3BErWR/KFgs69iBD4NXGquGRAHQo6qUpTP7Obt0cbwwZvZ2oOa84v3z5o/eKQR9nvHLfLuUgDCqkrGhf9fFYuP2j2ztdBnG8cKIazpGbrXmrz32GGJMTKUbmXQh7Cp2HB98PhT5uWyZUcfr1r6ER5q80NDET888gwFd48WpDXLg5ojjX47DwfHCeMx7m3zizOOJ+71E4nGifj8geO1jL9hviH+qpDrGDgXnvy2WlKc9v4HKgRi94TCBVIaTX9iEP+3OJ9kfjm3gGiSty04tSu2pj29EKDaWrWKigm4+WmjbihnHFyUAD3t/IUVmsJdVgsfi9elPuNWR/eD8ogQ4J/1xIQPp1UbAsvGnHnZFcWBKwmO4HDol4TFcDh1XGC55cYXhkhdXGC55cXw7xkgWXNr67oZE6o9e2xKmEGwOBG/f/ZOGzxTarmKkZGolb7lkt9Jrm9aqcJikqhI0TU7rH+DZ2so5A9+t3FZo+4qNkvEYW1RiW0IRrFw3e1zTeKI8wux4bAtUukXqGErmgWgSr6UI/FISsW0UKUmpKpVpy23sykPJeIy4AjMNk9pc0WkJ2OL3UGG4nWn5KBmPMSc2MCQKAFXCjIyJYo8f1eVSQsKQ/vEjwX2mzaa62kKYU/SUjDASyvjBnQlF0OMdn3nHpYRijC3hAD4bGjImAsgI2OH3YFulUV0/VEpGGLWJlNwQCYsBBZb0x5maMWhMJLFsG6gotHlFR8kUJbWpDAv6Y4QNm63BABuCASoyBk0ZkxM/3Vzys9vHUhLCEN9MaLZA9Ho8WLllSno8Oq+Eggjg+N64WPzZ9pJ4FgeLI4uSdeK7WlyNtaVFZaXXtsUvqwL2Ne97O4zxC50enYwQhCwLaRrHAi8XxOAixHFvyX/FjQ/3ej1GWPqq5ppdYprdw/mde5TewOic4ZYQaEKyy+9FmBYDivx0gUwuSiZ1J9o9Vb+ZbStiSyBlCM2yZUhNiqBI40lapIWXpO3FVqC9qYKH58zg3oVzEAgGAh7iPh2EANtGj6aYmkxy5t4u6+65U29I+H2PAk/I6wLGgWxwKpNWGL9uuvPvIcO8MOn3YKkKmm7TWVWFP5bhhG3rWJDcQlRW0K1WY3stgnonfzn2dO5feCIxXeeVihBJTc2Kw7TQ4mm+sHknAdPiN3OnsbsivBs4T14X2Fzoey0Ek7Ioubf6jzqacmEs6MVSFQyPRlttLZaiMLu5mRMTL9Ehp9LGNAwrgJUI80LNyWxrXMSUWIpWr4ckCpg2WBI0FcuWrK2pAiF437ZmkEwDvlPoey0Uk1IYwBTVtrNvO2DkUhmols2U1F4MPPRTOeqEFxbMA+CxukpaAoOppAXYEo9hcX5XL73ebBou3R7yoicc7RspViarMJqtES3cqpld1srSVGKeQC7162gMXUMCzQHfuH1+y2JxNEbYzPa0ZpShx/LskTZ8sjAphfH2rveaGPJuby6lsyeVIZJbKfOFmYtIqxoh+kedM3d3SzZTsGGNvRxz+qOAYG5PP0jJH+ZMAcFm4CtH+VaKlkkbfAL8X+OfakPp1F5VCsWXTsmAboqucBhfm800swWJhwxeQvRjRvr4/Wlv5unG2TxaX8ngerxlhsHHN+5kS2WIsv545p550z+X8uhrgOfldYGSbRGd1MLIx7Pi+jv79cZ3YQqEhDo6mM5OvvamD/L0jDkApBVBv64RMkxO7+xibXWEZtO6rv32qd8osPlFg+OEAfC05yceacR2Gh5PvVcxxL9nTrVXnXiSsicUxB6xcndDMsVzDRG6UUAVZ8jrQk8V0OyiwpHCyMfrP7pDhlEY0DUsIQibFgHbZrPPw5awn2RtSJeXae44vxyTMvg8HKRt4wGqDZO6jEHAzoYPfT6NukRKuqIYTckIw9A99HpH9xmmVYVYwMuOH9WXzHM4WBzZu5qPpKKwamoVSzoGqEmkGfDqrK0tY0B1NZGPkhFGRlXJCMFzU0a3iHqibjbHfJTM62JJi/L+ZDapvJTZP8NiRne00KYVJSXjMWxT2o2xtBI0bLr8OqqEpv4kHqNk27D2S8kII6DY6KaFLiWRtEHItKhJG2wOeg98cglSMsIY0FR0oVJpWFRbFhkhaA54MVU3B2w+SkYYJiJVbsvAgK4x4MnetpAS0zTdsiQPJRN87rytMaiZBl4p8djZPwPoqAi5cxTzUDLCANgb8r41riHLM2kkFj1+9drEDeHuQttVjJRMX4nLoVFSHsPl4HGF4ZIXVxgueXGF4ZKXkmnHGEIs/zpwPSCBeciVbirHPJRWrUQszwBj19P8G3LlWwphTjFTakVJvkVW3zzhVkwCSk0YLgeJKwyXvJSOMMTyxkKbMJkoCWGIFea7P/GWj+wpoTD7VeN4YYgV5tnAn353yjli/KxVl33heGEAjwCkPF56A6H8R4jlpfAcDolSeCBDcxI7Q2X7OsYtZcZQCsIYYktVQ6FNmDQ4Whhihbl66IOUnLlrUwGtmVw4tq9ErDBHFQ+qbVOejBfKnEmH4zyGWGEKscK0GdMHZKkqKS1fizgAnz/qhk0yHCUMcX0sipQ2UgrE+DxczZGqfZ16K2K5zP25Y0CZTEWJWP4a4NfADCAF/Bj4mvjOnz0LWnbFNjZMVfF6ySeIQbZV1DCnpx3N3u+MgUrE8jRyZUnPRJoc3e5ieT2wExj7Y10R/uav3hULBJftTxAAim1hKyrnbXqZW/96B4s6Wg70rZ9HrvzhYds8yZksRclbGC8KgA9UJWPHH0gUALai8j9P3M+/f3HDwYgC4PuHaKOjmCzC6N3H9h7jIKcY+owM3/rXnYfynSWdYWeyCOOvwNYx2yRwU2tFzXWKfeBekKp4lEgqeSjfOf9QDnYak0MYcmUaOAn4Ntk1RR4ATkGu/Ke8TPumN52+QzMyjK2ijqSlvIq19VMP9huvQK7c9WrNnsxMjuDzIBErTBXLMlCUvNXVBe17+O2ffsSJe7ZjCYE6/t4lMLPURQEOE8YgYoW5GxjtHqQEIfAaGX5290/50OrHR+5VkSvdWe8jcKQwAMQK02BMO81dd6zg9dvXUTm+aVxBrnTmgzhMJkeMcRjIyzQdUEbGHR7bzCcKXFGMx7HCAJCXaaN+8H8sPCH/wAux/MANISWGo4UBjKqpXP3gXXlWMnHJh/OFoSgWQCCTYkq0r8DGTB6cLwzwASxp3r7v8XtujDGOydO7epjkkseLO2ffFRMQLLQ9kwXHVlfzIpbnv1m50g09xlAKRYnLYeAKwyUvrjDcOSV5KTVh/CDPNneycx5KK/gcRCz/GNCBXPm3QptSrJSmMFwOSKkVJS4HiSsMl7y4wnDJS0kJ41cL/uq7t+FPNYW2YzJQEsHn9a970jN7696UJ2UKCfRVBvHHE+95/553H9J8glKiJDzGnE3NKS1tiJRfJe1XKe+JkvZ4/lRou4qZkhCGN2MJWxGE+jP4YwYZr0ZlX5y/lP92H7mXXBxflNx4zlPa4he2G6EBY2j0lqErRCMaX/rA+VKxZdf5Hb31P/vjPHeU+Agc7zHqtu39hj9ujhrSpxs2pqpjC0WkVbXmwfoK66Pv2+IupzgCxw/U8cQS1ao13iv2lAVpjfhBgs+weKzGNiiBF+VgcfyD0DS7MlbmGbVNAi/PqgW/Bj6VlEelPRxwB+uMwPEeI+nR9IHKMJIopu7B1BRMXTIllUKzbExVAY8KmZKe3D4Ox3uM2rbohZai8PLJs3np5Fm8smwGWxZMxfbolMXTYElQoD6WQlwd/Xqh7S0WHC8Mj6Lriq1h6LnEbEJg6RpxTae+L8Xxzb0s3tNHQzwNmfQbC2tt8eB4YUhVIR7yZT8MzoAXAl0IgqaNAvgsSdKjo5hybsEMLTIcL4yETwhvyhiXtE0AYSND1KthKAIBNBnUFcTIIsTRwmgXX3ooWu6jqq13XFIVCaxuqmBzbZg1jRH2hn0Id/jnEI6olVhi+XtS6L9VUFQDw1YIq+AlgM5x7bt4YvYS/IkkyWBg6JznGyrp8+fyvQlBa8RHyNGvyaFR/E3iYnk18D3gveSEnM9iicDAi4rERidJGSZeot4yBnwButRyEjJIXyRIeV+cdfUVXPGhc0Zdw5/KsLi9T8aCPkxFtM0YiP9yfnfsVEsRC0A8KQUfv/3OBbGjfs9FQHELQyyvAzYD+1xPYhAJmESQuayPKfxYeEY1he+mnijD/Waf/cT5vDyzftR1wsk00VywKqTkdTs7md2XzakhbBmViqj82R/nOb7Ro9id58c5CFFANphUyAx9HisKgEr6R31u6B3x8isCdIXp/Umwsv1pUghW15cPHSIVEVZs+7OHcgOTlWIXxiEtMCIOkJpzZHBpCcHz8xoHFQWqQEhJwLTBHj4uqY/rW5t9KDZNVopdGPccysFyxHq7Ksa4/e16BQCWKnh80RS6AwGwyKZ6NW0qBlLEvBqow49lZt+I1EzZYvd/D8WmyUpxC0Ou/A/wZeCAYyUkIFEYDE0VDDKo2AhsBFHhY1NNA7sby9hTH8YrbEKp4aIHU+JPGuzRFHyWjWrbzOsaSJ2+u0siJcKWpmrLr9x+54KxiWgdSXEHn/kQF6sgTwTeAVxiQYUEIdER2EgEKapIEUaiYqNjI3i2Zi5J7+he1j+ftJC/nDCcAFjPZMhcG3J7WZmM7RjyHgt4Nvf3lXyjazLiE7pNrNmLp263t5I+NYyZJ+d47cDoYmJme78Ed7QfTEZhHARB+XMjCPUAf511j1y2o5mE10uPNjqhzrqmwZkEEr9hMqsvOtGmFi3FHWMcAbR0SgqgoW+AcDIFUqLYNrZHpd7MgC7Ao4Am2FBZ5vj2iYPFkR5jLAN+L2XJNLO6e7CEIObV+c7Fr+exuU3Z9gsgrWt0Bn0PF9jUosHxwtBTplw3vUHMbO+mLJEmGvRx+fvewKrpo1s8bSTSo3UUyMyiw/HCiAvxd1Nob9kwtR5bVekJ+lg1bXzvumKBX8pLC2BiUeJ4Yfhgd0dFGAUbRUrSPk/eBfVsy6b7xoi7MGsOxwefNdGYXwiJkmuvqYmnmN49MPogW+KNpwtgXfHieGH0BIM+YY9uxPvoM+s4cWcbmDakTby9CRRp31UgE4sSxwuju67yR2nv6BWaw2mD967aTG17FH9/Ct0w1ia+XfmuAplYlEy+JvHD4NZTHpJJvxdPJtuxZmoqs3bsJa3q1R/YebG7MnMeHO8xACo6+qkYiJPw+0h7PMza3Y5E4Ipi3zi+VgIgbePBUE/svNlbWlGkpKOyjJimudHmfiiJogTgz1P/eGFvMPQ3qQiC8eTPPrjrnZ8utE3FTMkIw+XQKIkYw+XQcYXhkhdXGC55cYXhkhfHV1cvP/vJWZG++FbVMoU/Y9BcHfnaiifPvrnQdhU7jvYY733j89dsrgxv6y0PCK9HJVEeJOP33PTdOfc9fuCzSxtHe4xwxrp2YU8XVZlsW5bHsilL9xMNe88ssGlFj2OF8YE3PP/Ewp4BHj1uJhsbKpnT3sOFa7fjN200N6PnAXFsA9ebL3rRfOnYaaqhqlzyyIvMbeshranYmkpzeRjFsqpvePYct69kHzjWY/hDPjXt9fCe/65hblsPAF7TAtPiuJ2tKOn0VqCisFYWL44NPm1V0NQbZcmu9nH7gmmD9VPrD2oWfaniSGEs+WxrqCxp8I5XdtJbXU7KN3pqot80qIomCmTd5MCRRUmkL3Z7QypNfzgE4RCdDdX4B+LU7+0iFE9SmYijapWOfCmOFI58OBldf5/hGe0lEmVB1h4zB0XX6fWG0PdxrksWRwpjSTw5bn6AyP09t2Qu4XQay4L7q26fNfHWTQ4cJ4xPXbT6XENVxyXUMIXAFoKMRyfp9ZL0KBzT07KtRVwl+8SnbyqIsUWM44TRGvTd4zMtWkNBzNzEIkMRdAT8IARlsQRN8Xbet2E1PgQ6giQVV3SIj/Yf4NIlheOCz11Bf3hxew8tZSFingiaLUmpgrVVZUzti3HLIw8TIj0icZtARacMWZYSH5YmphWSv3fcczlUirbl8/jPdkwDtBd/XLt9cNvFl+z2lCVTp+np9DvbA95zp3YPqIF0elp7wKfFNU2UWRbtFRGxsD9GcyhIv8/D2uoI7X4PVbEE5YkU52zZwjceemRURj8JpFCIE0YgqaCVCP3YILMJnAwpEShkLAWZVmC7QKYVzF8IWIlc2TXhD+goU3TCOP6zHX7gD8Dbcpv+A1w8K5E8TZFypa0oXn8qjaIo2WTxpsnUrm68hsl/p9VTZUFlKoMNPFdXwQu1lXzz74/yjtUbUKVkV2UZx/TsQRsRhcQIECcwyo5qOgiQQCeNh2wnnCCNQhzQsAkBFoIYCvIXyJWfOOoPZwIpxhjjiwyLAuB1SHmdgDttRfEqUqKo6tDEZEPTaKmqRJGSx2Y3YQlB3KvjsS0CmQxv2LCNd69aj5p7Aab3DJAgMCq7cIrRVVuABCEkOhlCGLn9Ei/ZXF8mgjSgIbOC+jhi+QlH/lEUjmIUxmvGbhBwrhQiBKDY4z1cWtcxFYW0ptLj1bAVBd20OLGtm5N2to473kLFSwIfMTTSaHlSPyojPIo5QjhyKCyzcv/VBkX27oO+w0lAMQrjlTzbXkRKA8DOk1NPM00022Zab5TBRTQzqkp52qApnRl3vIc0XlLoZPATp4xeRmaMVLAJM5w1eGTi2OEks4PJ3gYFwn8P8v4mBcUYfa8ALgIW5j7vlkJcqUj5nA3ft4UAy4JcFj4hJY29fUghWNbcTkcwgGJZrK4pp72xhtqmOhbs3ktDbzb1gcCmgtG97WXEKONl+qlDAh7EiBjExkMyd24agYFEy+UstxEkEfACcuXfj/aDmUiKLvgEOP6zHRpwLqAD/37xx7VpgHd8dNf0smTqUtXIfCKuqGU1yZQSSGdIaCqGporKeJLnG2qIenWebqgdul5lIsW9P7sLLQGV9DKdljHfKNGJk/UVFgIDgxA2KhKBhyQSQ6qYKNhINFtg25BZp8I3kCv/OlHPZqIoSmG8Gt6z/GW5ct5MjDF5Pa994DEuWLcVJSOZz1bUEcWDjYKNisIABqpME15cJ7+7YaJtLyaKsSh5VQwoasZUlHHVjA1TG5gXjfPStHpmdTRy3va11CT6sFEwAI0uWyNQWy5/6o7qwoEe4/SP776hw6t/fVt5GUjJjGicUMbgp3/4C3WxODZw16lL8ZqSC154njR+prO+t1L+srLQthcTjhMGwMff+qLcq2rMMg1qc4nkvZkM5z3/MmWJJGlN5ednncR7Hn7hs6fIL99WYHOLkmKsrr5qRNqQZ7Z3DYkCIO3x8MApxyPJjv1csqsFVxT7xpHCqO8b+E0i6B+3PeX1sKWpnj6/j7jlDtXZH44URrIs/DkjTy5PT8qkubqKbVU1IBV3dsl+cKQwVvz7lHh47wA17cNDLML9CZa8tIuGrh7WzZqG10gV0MLix3HV1SEsyTHrm0n52rBUhWA8jaUIPGaatCKJ+nyux9gPjhXG3uqQlCB8qeEOsoCd5sYLz0TagmVrt36zcNYVP44sSgDa6sr3PHnyXFoqQ6R1lR0NZXz60xdy34mL8FgW7+r6yA2FtrGYcazH6I+EF22cqUYfPHYqjyyYNrRdMy1O3bwbWFow2yYDjvUYD/58aqxbYcvCzihv3LCTqb1RFu3t5rp7HyWWobfQ9hU7jmz5HMk579vwhfc+tfHWSDKJRLCzIth7+ca3uM3fB8DxwnA5PBxblLi8OlxhuOTFFYZLXlxhuOTFse0Y+0QsjwBbgRQwA7nSOsAZJUlpeQyx/BGgD6gGpgAmYvl1BbWpSCmt6qpYnv9m5co8s1VKm9LyGC4HjSsMl7y4wnDJS0kI4576/7vmvrofP77PaEosv2gi7ZkMODr4fMb/E//X3/baxJrGOvp9XjI3fph9RJkWcmXpVd33g6Mfxh2nLYj9Z+5MAOqjvfsSBQxPXXfJ4WhhtJUFFZ+wEbqCrmdTKrn10oPDscL46fR7a9QTZjK3px9f2uB/nviXK4pDwJExxvcW3Pf3csO6UArBzqoyZnQPcFzvWk7seXl/p81Hrtw8UTYWO46slURM+8Kp0W5Ob9nMwrZOAJoDUzjAK/DABJg2aXBEUbJZ3HCshrUqmxbaWjq7oo5FvXsBMJTsLfZ4K3ilbD7HDGzaV5EyA7G8Abkye6JYfjywBbkylv9wZzNpi5JbTr7/qrJY6v1nbFjb2+uvOC1iZUBI1lRXc1pHK2VGil3VtawLN2ArWcfYmNjLBXsfPtSvGnxAb0Ku/OeRvIdiZlIIQ7s5fe97n1z7ttev28nvzziWXQ2VLGnrpSKeRGbS+FMpzlu/G49hs662AunJ8MCxxxC0BMLM4NN0amJJTt69jve+/Fc89vgsfQdJEggiVxb/Q3uVFL0wxArzp+95cs0nz39pOw8snc3Lc6fy/tVbhoIjGzhz1Xrq+qJct/ws7jx+IWSysw81y+aLL22kPD0shLmdW3jXmvtejUlXIlfe+GouMBko/uBTyg9e8NIWLFXhn0vncsbOtlFGK8CehmrWTK3lzlOPGRIFwLy+gVGiANhSM5eoJ/hqLLrm1Zw8WZgMwactc+GiIiUec/yAK1NV2Vlfxdi1KGwB/1gwlfV1FZSlDF6/rYW5nf2vtj2jJCZDF7/HEOIHK09eiG6avP3ZDbzUWDXukGltXSzbuTfbrKkM/+ybGip5ekY9/X4vzRUhfrdsLqF0O6FM/NVYdNmrOXmyUPTCkJdpV/7thPnfvevkBfL0Lc3M3rmXR2bV0+P3kFJgt19DNS1mdvbx1b89geoRoGbFIT2ju0AsRaEn8KryYvQgV5ZEeqaiDz73xbcX3z/PVsTbFrTtNNu9dd/FD+FUGn+Pxe5ZQdoqK/jBG04m6R2d2fHWv/yaLzxxSG1Zkmxe6JnIlXuO4C0UNZNWGCO5v/6Xip0xt2i2DIUSct59Z87vu+Q/L3PtO8/hzycvGjqubqCPdd/9MlWJvG1WFuBHrsxGq2K5glxZEvFEPhwhjLG84x2rZNrr5ea7H+A7F57H1oowFYkUK/71U+b37vOlfwa58rSJtLOYKfoY43A4blv7JzfVVXL1xW+mKZ1mWVecE1p66QtU7+80dxTXCBzpMQb5xAXPy9Na9vLksdniJJBJ8Kkn/sAxbXk6Ud0pBKNwpMcY5Pg9zczc1cEJG7fjS2fwGhlaI3WFNmtSMBkauA6b6p5eSxcp9fQ1Gzh79Xpm8gJ+3DXdDwZHe4x3tXxMi6pe+fKUBjY0VuLdtyjMfe0oVRwtDIALuj+hmP7ktK6y+Of2c7ML972rNHF08DkOd+7qQeN4j+FyeLjCcMmLKwyXvJSaMH6VZ9upE27FJKC0gk8AsVwA9wHtyJWfLLA1RUvpCcPloCi1osTlIHGF4ZIXVxgueXGF4ZIXR/eujmWz5wp7qrFXKJj0aBX8tWrZnz/Vdsm7C21XMVIytZJXtCutRdZGRRkx571L1FFt/8TtJ8lDyRQlU6wuRaLQTSPtzCBJkHLZwy/qf+nmxMhDyRQlNipbOJEBwlgoeJjFTNbTFQrMLLRtxUjJeIw+6ummHD9JIkRJobONRYTjyUKbVpSUjMeIEqRK9NChVoIt8GsJZEahLJko2bkj+6NkhGELSbusBTMbayYyZai+GB1lkZLxmodCyTyUrkCYsckcRcpDMB5zPUYeSkYYce9456hhEY0E3eSveSgZYeyt8KBiIJBomCjYVKttLO1rdYWRh5IRxokdO5jDZiLEKCdBFVFsK8C22vH5NlxKRBit4jpRFrfpogkdG4Vsjh4TH03d8Fvfj0syZeP+KAlhNMpr5OqamUjUcWmWZnTHscojryoplxMpieqquC6lPNvbB4CXFH5S2CjECWCh0lrhK6yBRUjBhCFuNpqw5S4UVCTZvDUKcujfo+uWEoFAMLyEgIIEIZAyd27ucEsOp2xVASHAp+IxJcmAZGqib+iifhLcN2MJH9n8LJefgX3Ji499YUHihz88qjc+SShIUSJuNs5Eyj2oqCi5nFnZuoHIFf9jGhwQqIKhYxUBiKxQVGVUQjaU3NmDxykCBHzm/Reh+TKjLqsAZ3eu5YGaE9hUNkt8+a3v/d+4+OCjR+/OJw+FijF+m/0BR/ygQrDPLPDafnrGxw4bECJ7V6oYvr4leWbOFPzG+IzADbEBpmg7+dh/1vHA3KX8bsnJZ/WJr1Yeys04kQIJQzaMEsWRRhlzbTsrnjuPO46U0NmhN7DZM4VuNYxOgoV9rXiM7DFxrxeg6egZNzkoUIwhHkTKi0aJYzB2yOc1DnUskZSjvZEqwJTcdtJpnPRiL8js+9CpVaBkEmRsyUBIQ7NMpnV3SWD9IX6j4yhU8PlObGIoUh0qQqTctwAsOZS7cxRCjC9KpMzm39PksOdQFRr6e3ndK3uGRDHINn0WlrT5v9fP55r/3Mebtm9/V0D+ouTXey/o0D5xc6YBm+1kBZoGOoDvA7cAHoZ9SBtQy/CidiZgIPCOWOgsW1chV7MhV49Rsv+877t3oMS9KGNKz5SmsHGOn5jZf9VNL3/khqN4u5OKkhjzOfuzreojt91qvuI5ZnRQCjw2p5E37XmBs7svd8d+jqAkWj5vvucRDFWlLJNBT1solo2wJV7DYO3USrq85YU2segoCWG869MX25atoyAJ2AahdJqyVAq/YVKWSRJTxRsLbWOxURJFCcBucblsZu6obR6SdE6N86bdX3OLkTGUhMcA6FCraGIbg8uNaGSYzQb2+KpK4804REqiEw0gEfQhBlSW8SQZPARIEKOabXqk5Kum+SgZYdSlevEjaGcuGgYpLBQk02NdrsfIQ8kIw9ZMRAaCpHNbBAKTSCZaULuKlZKJMdZXj88h3u/x82zdDDfwzEPJCGNq3/gZZ37DZmqHm1s8HyUjjIzpGbctLnyEbcOdV5KHkhGGJ2XRRWSony6Jh83+Ory6+pFC2lWslEwD10vi2tZemhoUQMUijY5f9MnT7S+XzMtxKJSMMADWiWtWRUXNMiTEPMI6N/25kqmVHSolJQyXg8d1oy55cYXhkhdXGC55cYXhkpeSi8rFlbFjUcUqEBq2vV1eH5pTaJuKkZKqlYgrY2egKE9kPwzNVZDyuoDrOcdQWg9E4XHEyMHAAiRCXBn7WEHtKkJKSxiDihicwjg4z1VatxXSqmKktISRnVE/7DHE0ARpvYBWFSWlJQwxZuoiZD9rYye7upSYMPLcrpQgBeLq2P0Tb1Dx4vhayXdPvX9hIGb87uXptcf/7PhjhVSUbE6NQSw7O3BcAYQ9R14b2lYoW4sJx3qMMz7TJu5Y8Ds7aHSv/8eShcsihiI+/NKmrBAMKztR2rQHZxOABN2wtxTU6CLCkR7j3qk/fPfmhql/aqmZQlk0yvSOKKauYno0YprKVW86HdufizcNG5IWIAlkTOLfjrjxBg70GD9YdO/SJ4499U/PLTyOnfV1rJk7h5YpQZra2wkOJNlZW47t07Mew5bgUcCnohsWpm0jvh59baHvoRhwnDB0T2p1c1Ulhjbc2r96+jz8oRj+WIq/L5yZ9RKWBFNCxgYV/CkDy6uBZf+ngOYXDY4SxufOfTyeKKsWKc/4gb8d5eX4rCRtocDoHRKElcsIpyqgulVXcJAwfjfnN58IGlagLxDBa5rj9s9v3w1xFVsZf8taykQKMD0a5ElGX4pMqqfwt/DvBVCR0URtS1NkkeVXflTZ21MV90f0VFmdqEla9ARV6uMJWkIhTFUBKVnc3kx1a4oeJAv3dLNhyoj84VKipUziIW/2s2EK7Yvd0gYpJQgpUcEyvZoEtqGJn+DRO0FEgQEUWhHChxCbEYTkVd6eAjyaI86kqZX8Lfz78yT82lZFUyKgc1r3i0T9PrYq8wB46aTZmB4NGzBVFSkEaVVFsS1O2LCGho0ZQGHNjBq++pHXYSsiG3zmqq2KaSGkxDZt5EivIiVIiUI256ypquBVYUQMk9051Dn3EvABeZV33QQ9mqPCpPAYfwv/PizhbikoU2yb4zo3M91s4e7IhfiTBnumVWeLAbK/UbfPi6mq2AIGvB62nvFaao8dIOENYGVMFNPGTpvZdgwkupSI3PuhAqZtDxc5IptA1s5VYrAlqGNWshhsIMuyFPgzsPhoPpOjzWSJMU4HyoQAC0G11Y2Jij+ZTehqeEboW0J3wEd3wEev34elKCAEXeEyDF3DDvrwRlM5UYAiGRLFIOq+vKgQiDE5vEZ+7wgWievT0w73ZouBySKMHZB99qqUJEQADYtEIFv7qOwaGHWwxxqf8mLwp0wJQXzQGygCNAXLoyEPsjIibTk+heTIL8gSBboO6oJFyqQQxpuj798s4HZhA4pgbWQeCXzMM7dg6CqRvgQzN7WiZwxO2fk8n3nyTwg5YkqqlKi531KXMusRPCqEfNhhP1bIhxEJYHmzraHjJrPmzhWDgjDHHDE2/TVcLa/yTurZ0pMm+AT4W/j3Z0n4SDygvSVW5quYNdAiuiMh0sJD0E7gJ8n52x5BkZJ1dTN5fNYyAukkz049jtbK+qHrPB3y0l4ZAmVMrCAlSl8cO9uokd1mWaiKwFaVrFdRFIlPy3oIGwMYQBEx4AmEeBHBw/Iq78sT9EiOGpNKGPviyrOfLDvrled6G5VO5ZjOjeP2P6OfwX/nH09XVZi/Lp7KpppIrlzK4zBtE/nNYMk3ck2KouRA3PDoGQNv6PqSuiNcOS7rtI1C0gwztbOPmd09nNbchkfmqqnjXgoJ9iFnLnckjhDGIGtrT/b8ZcHZWLny3kJlg1jC7mn17FxUR19NiHmxOMvau7NFwUhxDP7XdnUBk6Qd42D5+tOvNb57QnTVR9517glnbXuO6s0a/phNx5TIqONes7uN5+uqsAZ7WCEnFAsU8buJt7z4cESMMZavnvu02V8WViv6BzjxuWa2Hd+ApY0ONO+f2cgTjXVg22BauS54FXlDuOTjC3BYUTLILQ+dpk3fs+eLHZEy/vD2k+kM+EftNxXBxilVEPFAxAuIbOeZ5WZdGsSRHmOQCz64PfT67Rv6o96g0lxRTU0iSZfXw6MzG9lRG8kKIWFmixAJeJRd8vrwjELbXQw4WhgjedMn9qgP1JabqAqkR7SMqgJ0AYYl5Y1ljvSgh0PJCANAXB2XWHJ0keHTAIm81m27GImjaiUHxLDGL6+Vscit/OoyAtd1ZqurrjDGUFrCUMZ2sOewSE2wJUVPaQlDyg+MW5NVU0CIhsIYVLyUlDDkTWV/QLApu2S4kq2R2PIv8sbQwIHPLi1KqlbicvCUlMdwOXhcYbjkxRWGS15cYbjkpbRaPnOIFaYAdgKd8jLtxAKbU5SUXK1ErDBfAo4bs/kd8jLtngKYU7SUYlEyVhQAd024FUVOKQojH27P6hhcYbjkxRWGS15cYbjkpaSEIVaY6n72jV/KuYQpKWEA+5tofMuEWTEJKDVh7NNjAB+cMCsmAaUmDJeDxBWGS15KTRj7vl9zfBaeUsYZnWhi+SzgDqCM4SZvCajIlQfXGSRArDDnycu0zUfFxknG5BaGWJ4AhiamSka1bWdz3hxsc7eqKsAmscK05GXa5H4uR4DJ0bsqlitkk51VjLX2QL/6yONbw+VMufqn+bPujceQl2njc0+XCJMlxugBKiArhJF/B2LksX7TOJTv1MUK84lDM9M5FL8wxHIvEDngcQfB75aeebDeYpAzjsT3TkaKXxhH0MaEGzocNMUvDLkyCaSPxKWaon2HekrzkfjeyUjxCyNLJWBBNpgc+XcgRh7rtcYvV7EfLHmZNqnTPr8aJketJB9iubDAUHL9H/uMHOTKoV1ihbn/6quUIIQEvPIy7ZAiVacxeQtduVKqg/aL5T8EPknOMQweASw4lCsixE/lZdpnjqidk5TJ6zEOgwN4DFtepu2v97WkmCwxxpGidN6CV4krDJe8lJowXA6SUhPG/jK8TvqlJI4kpSaMGfvZd/5EGTEZKKlaCYBYYea9YXmZ5s5GG0GpeQyXg8QVhkteXGG45MUVhkteSlEY8Tzb7phoI4qdkquVQHY0OLCebFd+qNR7UvNRksJwOTClWJS4HASuMFzy4grDJS+uMFzyMnmH9h0Gb3jf5oqwaXRZQii9Pi+9wSD9mipjHvVb3Ssqv1Fo+4qJkqqVvPb9m+0nGmpFbSpNTNeI6xoLYik0VbBLU3YNfL96RqFtLBZKxmNM/XxHg1VeJr6wcTsh22ZPyM8DMxrYVFeGnjZRpZxeaBuLiZIRxpT+2IIl/VE8isLqyggPzqgHVUGqCpmABzVzSHNOHE/JBJ+hjJEK2zb/aKjlxfII1X1J5rT38/rdnYQzJpam4rsqOr/QdhYLJSOMTWWhylWVFfR4hzMb9GkaCSk4va03u8E0XymQeUVHyQgjbRn3rC8LjdtumiZLd7fikxaVGcudV5KjZITRaNqe1NilNQG/EFTGExzX1o23hGpoB6JkhCEAr20TMoeDzLBpMjeZpC8UYklrJxlddcd95iiZWklU10kLwbndfXR6dISEasNAAFtqq0mbGeK6W5IMUjIeY8Dn5byWNtK2TW3GoMYwEFKiWDZ18QQRBGWGxXfP+I/rNXCYMNaJ7525Qaw4J9++hr5+yjMG/zetgd1+H3FVRTctvJaFz7KoS6Q4s6WLuxbOeHCi7S5GJm2TuCne/TaBPE5B+9YrnDTfR3q9FwuJjU6UMjqwgWfD8we2TVtQNuAP8t8ZU9hYUYau68yLJZk7EB133e2alCvvWuKoF+ZwmBTCiItLQmBcacFCG96koeg2ATL4SBHAxEMnVaTxInL5c6awg1p2o2OwuWImj8x4Hf1eL3ceO59+n5eaVJqzO3tGfY9qWWzzqczsjZLx+ujVddBULGBAUWRDKvXcz/6y9NSCPIQJpiiEsUvcqFqIS1SSn/cQq/ERr9FJCw8ZBKYw8BOjEhBU0kz2pwIThVYWI/FiodBPiAQ+5rOaGtqGrm8oGr9Z/F7+umABL9fXZDdKyXntXZQb2VpK2UCM8v6BoXN6An6MYIC0L5uHJa0qDAiFcCJBTBN75iQyb7vqwVNXTcwTmngK7jJ3iRurJXSq2D+rY+8x9XTVlZNQglhCJy10TAJEqWUXNTSjYSNy/1NQ0RD4SFBFB1NoIUwv1SNEAaDbJvN6t9EV8A9vFILHaipp8ej443HK+weGM6oIQWUyRV1XD7Wd3fjSGbymBZpKc20NYVtMiXn0F64/75nfT9iDmmAKLgzgqwIq/ETxDiXnE4CRKxZGMjoRvIpJJS3Us5NyOqilmdlsJ00wz9dIpvSPjikyqsqDM+p5fEo1glzyjDF5QL3pDP50Gn86Q8gwkIqgoyyMpWlYQrzv+vOecWTRUnBhSDgBwEPqsM73kRz1OUSUdmaTGUrFlc19sKV8Nqe0tDGjt39oe9Kr0RP283JjLdY+EsMKQLFtPKaJamezKGR0jZiqYCsKwLGHZXiRU3BhCHgQIDXuLdfypL8Zba5E5E2opWHRz/ASZxvL55HQA3hMi3es28Inn1/DuZt30hn0IAX0BXzcd+zsrDjGxFy2EFiqigR0KysMC/BZ9qBQHJlWuuDCAG6V8EqaIDHCyKGsnAKJnzRBMvgw8QNe5IjVqwRyXCYUG0GcMDHK6KWG7SxmU2T+qJzikXSGtoAPYgbEs3ONHpszlWsuOJ11TTVYWU+ApSgkg34QAkPTSGsammWhGAahVBpFyq9f9eCpG47+I5p4iqJWArBT3DhbYn5aJVGpk3yNjTUjQEIRKEqMamHgJUwPKjYSmwo6EUgy+JF48ZLGQKeNaQxQQQoNL3Fq2YPujXHv3DeT0AMAtAQD3Hb8QmpsSdiy6Qt62V0eIGCYfOrFTfgNC2wbjy2xBRiqRkZT6QoESCgwp6P7Gp+i/PCqB0/tK+xTO3oUjTAOxEZxfUjD8jSx5jdpKi7K4MNLHAVBB1NzAadG1gnagMEcVuPBQALby6Zw9Zkf5qXaKjaVRzglkaLMHvY3CRWksFm6txtfNC7X1dcSV6EhnmRKIi0Nw4iHpfzE9Q+fcWdhnsDEMmmEMZaE+LySQVfK5XfN7eJmTxw9lUIVKqBhI5GkEbImsPmTuz1NP7vyguXiyalTQUKlaXFSdPxKmwlpsiPktZpvrS+ZzsV9MWmFcahEvthpDwT8AkWhOm1wQl9s3DGrA14azeTFL/146soCmFhUlMybodlSkAsquz0aSUXBP6Io6dNV+jSVjh+4ooASEkZaUYaWTJNC8HxFiLnxJGHTotfnYVfQg0i6I8UHKYbq6oRQmUwjRhSbSU1lXTjANl2nQwiSuoZPKY1i9WAoGWHM6OsXr2/tJJzJtltolk15IoMAdNMmHE9jaaqrjBwlU5RsipT1fGRvZ2V1IsGTtdVYYvQ74c1YeHR38NYgJeMxbF379j+m1nHnwpn0Brzj9lcnknhS6Xz5uUqSkhFGVNc71lWXA5Dw6WTU4Vu3FEGlkaZlSnVVgcwrOkpGGPU9scxgj5sUgu4yH11hHz0hL5ZHsKmqHPlVPVNYK4uHkhHGBeub7xGZEeM5hMDQVcKmQUjaRBV36sBISkYYP3nhzNTiti6btAmWDbaNbll0+b00o1MbTz5QaBuLiZJpEh/konet7+kJ+CsU22JzJCxVYZtVBgte+UnD9kLbVkyUnDBcDo6SKUpcDg1XGC55cYXhkhdXGC55KZm+knz8c+Y1gS7VG0XTRDjeL0/saY00xe8YP4KnBCnZWslD065sSKpK6yntuwgaGaK6l13BCAGoO7bzfzsKbV+hKVmPsTZS03Lp+ifx2tnW0KCZoSoVZ3MwvBco+WbQkhSGuCIa+mjTLPEzjwdTUXn3xmdpjPWhSZsAqht3UaJFibi83/ZZpkjp2dSOim3zzk3P8bN//ppO3cfs2K9KfmBGSXoMhMiKQkoQAltRuHPhqSQ1Dx9+8T/MLrR9RUBpu80xE5n/NmcpbcHywthSZJSsMOZ17x23zWOZvG7PRnp87y/5ATulWZQAJ7TtwFRUpg1089Ytq2kNl6PaNrP7u/jfJWd98zL4n0LbWEhKThjiiugUgMemLWD9z68gYqSH9kmyKQ72BMoXFsi8oqEUi5LtAFHdR9kIUUB2NlLM4+OM1s2Hl8XFQZSiMDSE4MyWzXmTrihS8kLtzMUTblWRUYrCkABN0b68O5+un8XsntaS7y+ZUGGIFWafWGHaub81E/ndI82AbHrGO+efREzzjNr5zznH8ZmLPnFMQSwrIg7Y8ilWmOXAGUAvsEpepqVz2yNABRAF+uVlmilWmAKIyMu0vtwxgmyZPh3yem4JZIBVwAeAJJCUl2nZDGpieQDwI1d2v6q7HHk/V0TtkbYots33H/4dn1/9MDZwz9wTeO+bP4OlqUl5SyRwpL53srFPYYgV5lqgIG+ObmT4989v4DU7N6JISVLV8VvGSGVtQa6clzV0+ReAW4Dx08vGY4nL71DHNmwJ26b/B5/hW6e9mS5/mF8veS0A8paykm0azysMscL8MHDHhFuT40PPP8pv/nzbgQ67GbgHeP5gr2sIBc9XfjWuxROgqb+blsiIdq1sc/n18ubw1Qd7fSexrxjjmgm1Ygw7K2sO5rBLgY8dynW/9tqL84oCGC0KGDzua4dyfSexL2HsmlArxtAw0Hcwh/UCzYdy3dk9hzz+xjrwIc5kX8L4+IRaMYZQOjkq+es+wuMLgZ8AA/l3j+eTa/8LUuIx0qhW/t+8IZpbUTFbxPoO9tpOI68w5GXadmA2+35j8v1WR2xgxy9PeT1fuvADtAfLaA+Wsb2s0pbD108CJyNXrkOu7AVmAr8FWoAU2VyONmACCaCbbM1pq4q8HaA+EeWG/95FOJXg2sdXsvmnX2Hb7f+Plh99nnN2rhvKDixvDpfeYJUcEzJQR6wwvwt8ifFV1oflZdq5R92AkbYMVlelJP7dTxCwjFH7f7j09Xz1rHeQ9AVPlTeHn51I24qJCR/BJVaYAbJtFQV5GweFUZGM0f2/nx2n1J3hSi564yXGK38+3ZPv/FJhwntX5WXa+MyrBSBopGkOVzItOnqVox5/iCnJ+HUFMqtoKMW+EpCSPWVVXP2ai0dt7td9fOSCjxHJJKcXyLKioeTGYwDdSLsaoRJOJ7l37glsq6jl/O1rmdHfhdc0mNbXUfK9q6U5SvyrAxIhOK1lC083zc1ukzY/+ddvqIv1sbas9uWrV396aWGtLCylWZTkGBQFgBQKV772HUjg2alzby2YUUVCaQpjH16yOxBmXWUDf//LOXdMrEHFR0kKQ5G55PJjBDKvey9V8fGL9JYipSmM3HzVkR1qoXSS3/79dn51/NkFsanYKElhYNlRf2Z49cWaWB/33PtDPvOGj/C+dU8V0LDioSRrJQBbAx+Vx33qFkxVpSYRpaWsinAqwa7bvkRF5g8lO0BnkNL0GMADUxdE+35wKZ9d9RBBI8P/e+Z++n/wGfZ6/OOnqJUgJesxAB6s+7L9uo6dQiXbdbstELHnxn9d8rkxoMSFAXDLyT8PC9t67eUvfPofhbalmCh5Ybjkp2RjDJf94wrDJS+uMFzy4shu935xWdkAZbqN1jddfr1kR3q/GhwVfK4J3nJOpzf8kFQEjYl2yOiolhqdL68oK7Rtkw1HCeP+2p/JBf07mZHZRIYAApsYEbpofGW+vOrYQts3mXCMMP5d99OF5bHY+sWJV2hlPmn8SAQqBh6ixKhSl8r/sQ98JRdwUIxRFY+KKYkWBqgljQ+ZG/9toZOinIwnlZ7zmbadGUXMsYUgaMt/brqt7oICm120OKZWEojHVoXoI0w3csxtSRSClqHNyJhzZhgW89IGtab5xjmXtq0rkLlFz6T0GPdV/sETkD29tuoN+K0MPXrAPoseJUiMDD6yPR/DHaQS6NPK6fZoeG1JRmSX2Kww7UWFuodiZ1IKY05yXVojQFOqO5dpTyg62URrHlJU0Uo3TaPO8Zo2laaFQlYyNrJ0ZywfBJOuKHlJv/IBSw1Sm+pjc3Aqq8vnszPQhD1iwYBGtjOLl6lgbzZlj6rwveXnggA796cAKpLAF7rP29d3iRVmyfa0TiqP8XLgm2fUKT1v3KNU8Wz1Qnp9IQBaqaDf6+Hs3v+Q9QcCjQwxygF4ZOl82iuGmzIkIKVEBeb19t8HVUEAcVXsFFTxTzxqBI8qAMTNGciYs+U1gZJafnNSeYza5J7HtwUWUJEaQBUZvNZwns7WQA19agVJQpgITLyESFDGAHurxrdvSQEmAsXjCYgVpk9cHWvHrz+DVyvHowpssgrSFPBq28Q349dO3J0WnknTjrE+eP38UDq6scFqQUGiIrFQWBNZyIZwdn7IvP5WGmJ9KNJmgDIyZOclW0Lwy/NPZ/20hqHrKVJiS+hWBC83loMn5zwlYI14JqoAjwLRNPLaYMkM+Zs0wtilfs6cZreqAi9jaxx/rzuXPl+Y/ooQHtPklC1bUNKjS8nucIBvvfdNuZMkupQkFYWMkeSZedOy24Ucvqg5IiOHR0DcyArEsCVp+yp5c/hGAHFV3IPkS0iZAJ6XN4aeOWoPYQKZFML4d+UKc0lii1qf7iJfcr7Ha05ibcN87NziRHOb99LYNX5+yO/OOo4XZ81AU1QEkrRQeLqhjETQNz5zhy0hbma3e1UwLMhYWbGoSnZ9eEEGoXgQZOeoSLLikgxPTbBsiaQLaJS3lJlH7KEcZYo+xnjRd2VPV1m9aimDIzPHC7m5qh5bVfBYGU7Yu5qTe54ed0zSo/JibQ26ouaqrAKflCzuTkDCGL60JJuPR+SqLoad3Z8wwLSzgsiYoAgQigdkVkSWBNPKHmPL7H8NC2wpgBrAEF/tv0VcEZ0UNZ3irJWI5TrwJwnLywLTSGoB4upgLlYD0AGBBFbXLiat6rx9y99oig0P8O4iQQo/IIh7ND7xrjdihwLMNEa/tDVpA8W0scfqTQJ+HVQLknledFtm4w/EsIikAoPXlyOKIimz+4VyOcjLxdeiuf3SRCgANlJ2AHtAbCUrzRTwEHC3vGXiUz4VpzCyWQMbYNgj16faWVO+iE5vFdXpbhb1b+H5+qWsrj+eN2+9f5QoAOrYRiezAcEnl7+V5soINcb4H9gQAtu3n8fgUbNFyNjWMDmmyBg0VlOzXmXsT2kDihw+XggQQht2gmIKMAU4dcRZnwS+B/y/fRt4dCg+YYjlZzAoCmBKohXdyvBEzansDWRrFTtD02kJNFJl94JtUxvtpofpmPjwECNKmD5qAdDJ0OfLJt/r1lTihklwRFy1y+fhgAsnqnkqI6rCUDMqjPYQguFrylxRk/deBSDzlY4j+az4avRaeUv4oLMTHgmKMcaYNvJDu68an5UaEsUge/31bA3Mprw3zt8aL2RPoBGJSjeN9FFP9tYUDHy8f9UGAGwhWOP3ssWj068oRFUVny2zHmF/eJSs54Dsj6mquRhjjLcY/jB6uxi/+RDwAsHDOvNVUIzC+DM5xy0BQ/WgC2PcQYotwVDRLYszOjYwLdGJlwxWHif4lrVbePuazSi2Tb+msjng5Ymwn00+naBtIxJGNlDMlvvDs+ClzNYydDVbFOhatqgYK4oDInIeJM85B44eHpW3hCd8dlzxCUOutIDzgYwNNCTamBXdSSTTP+owr2mgWyYLevfgM42h56sxPo6QwBcffZ6EIrCU4TK+Xddo01VkuQ80ka1ZDP4N1i5UJb8QxlbzBz/nKzYUMVy0DHkPAfbYWESaIONAV+7vT8B79vWojibFF2MAyJUPk2094IXK682T+9aos6K7eLbyZHq9ZdSku6lKxKiMSzRpY+JBYKOTIUyUJL6hTjVbgCptVjXWkdbG1xT3+HJZG4XIVnYQw6/L4I9t5eIEZYQ47MGahhiOLwa3jRwnJkYeIyWSNQjxeRTxhLy5rGhHlBWnMEbQozdOWRsx9h7Tv4moUsby5r+jYtPFbCz8Q8dJFGxUdEwqRTudnjK6PVXoJlQlEwiR32d7kBiQ/fG9eZoYbAmxzPC/IftjKyL3WQ7/+JqS3TbYyJVb8DcrGvuP8pay9x2xB3OUKXphvLX9kjZAbPN/RWYUD6+ULea4gbXYjM/PaqLQ7ovQ7imnzExTm4yhmNnX/8SWPQRNi7imDhULqi2ZYVqsg/xlvRDZqqemgm3mfvRcM6cqLsDkUWy5FpiDCtgkEPJCJI1IHpU3TXxscKQoemEMsj3QeENVsvfKDeH5zI5vx2PFSBMZdcy6yHS6vWGmR7upSiawUTBRERjUsZXlzXt5rKGWbl0jbFksSaSxsFlXE85eYPANH4kkF2NIqPKXycu0sW3t847aTReQ4gs+98F53V+6qj7ZTFL38Pem89kTDiFFNtC0ETQHquj2lVGXjFKbjAECBYkHE4FNM8dy7K42lqQyvD6a4KREmqBpUt0XA9N6ANOC1JjA1bSznWdpA3R1Zx5ROJZJIwyA1kDDtQt6NpBU/LxQezzPN8wjqursDFaxJ1iFalvUJLO/nYJBgB48xAGdnlCAZ+bNIKqqpIVAsSVz2rtoDgRs+Y3Am+Q3AwJFaSNjZquuaTPbvuFXwaOsk98IzCzs3U8sk6J3dSQPRb4T99m+QFcoxLzeDirTCdr8ERYln6NTTKfT00BDei81bEXJBQ49ag1fuvjLJP2+bMlAdh20bR6N6lT6sgd+N+e7I79D3JyZQ8b6Fba9i6T1EXlLWckND510wrin5o9KRbLLslUfusygSIkpdE6L/hcPGV72ncyxqVVoZEad93zkZK5/+/vwSUlSEezxeogpgs0/riuZwTeHwqQqSgAu7nyvbanKRcI2CGeyfw3JbnqU+qx/0NPjRAEwb2Arj4f8rA762Rjw06Gp1GfGt6i6ZJl0wgA4r/+z/zgneqnIBBLTo2HzHRtq68OGDMoWFtOtV2Lmq2wp0GjbJBVBWkCdaaGbVtfEWz85mDTV1Xyc2vP13cBugNX6zV8sN8UPevRKWr1NTEuPXO9Pw7CClKUzVAuBACyIP/zr6Qe1XGMpMik9Rj6mmLG/KViUp5O8WLaUPrUa8ABeEiJCO7VXPfWLqcKH8P3n51PEf38+JVRom4uZSRd87osucaWaQTFjSpjtkVoGdC+1RjeViThxy2edZnxhUnvHicYxHqNa3mBB8vmwPcDS3u2c1rmRsmQaOyO59iPvqC20fZMNx3iMQTrEl45PEHzURAv8d86Cv/357Ne8958/b0of+EyXkThOGC5HBscUJS5HFlcYLnlxheGSF1cYLnmZdHX76m9EP1OezNwWNkz6PCpzO/u/8+9fzb680HY5jUlVK5n+td5ZJ7d0b6tLZNCkJKkpvFgXYdHe3mvvuGvxNw/3uqvFbb8D3gb8ZJm89CtHyt7JzKQSximXNpuntA+MmjeWUhTWVAd56ifTDrn7fLW4zQsyNWI6We5PUZbJSyfPgzkKTKoYY8ZActxkQo9t45EgVpjj8yMcEDs6ehKQIPtI0s2Hb6UzmDTCECvMoJbHuylAZcpg6Y69h9GFLvT82z1N+beXDpNGGFM6+x6qHjtYN0fQsAinDLe39AgyaWolC7sGThBAWlXYWR7Ea1lM60ugAAHDouRf8SPMpBFGQlOV9qCXexdNHZpqWBtLsXx9Mx7LRupHyvmVdMw5xKQRhlAU5aFZdZSnMlQkM+wpC9AR8vFKbYRle3uJeQ79VnRSGCOmOWYZL4xt4punRGh9SkMqKfyym4b3LJZf+/Nh3sqkYNJUV+dc3mXP7UuI+d3ZeSM28NDsegRw3rY2+jVBwKbux3ct6jjYa24XV8tsLo3hmkkFrUSpwsQbmcfzmQTKQJiE7ieNjSBOOQY6O5lvg7dxmby0/QjfalEwaTyGP2MyKArIRs2v2dXBrkg2p4jXhh1+fS1Qd7DXTOFlbEL6XupyVzd3SJKVfjyY+NjJdHqYOuJYWwHZtlrcRu4i3mXyUscMO580wpjVGxu3zW/aQ2Lx2RJVEQc9uDct3ndaipMZXzHLxi8NbKz0Iumiil7qSRNidJvHqPMEkM5zsUnLERXGFy9e93qyb+yfb71n8RGdvbW7PChmxNKjfprBzIsq0OPTSXj20SyRhzamXJdLiJGHDPW0EaOMHprIENjHcaMQq8Vtg+Xyycvkpc8ftDFFyKsSxhcvXvdb4H2Mf1P+8MWLx60RYwDVt96z+JCTjFVfG1uSrCmnMm1yTOfAqJRW2ewD8OiMWqZ09h10IpJeqmfte68GCNIED1YUY3lutbitY5m89KCLtWLjoILPL1687hLgNrKv2JF0l4OdE4O/dQbYADwN/DG3b9O6yuAXH5tV//WmaJK3bmodd5G4pvCLE+dwxo4OTmzvm3nrPYt37vMbxfKlwH2dVE5t5nglX14snRSLeIpumtjD/Fd3h6MZdHLvWCYvve9IXvhIs19hfPHidRoQI1+e5glCAlFdZXtFiOM6+veZ+O6pKZWc0tKTTaUJfwfeeus9i0d7ELF8KzAbsr/OS5xDPmEoZFjKf+mikd0szHvMESABNC2Tl/YdjYu/Wg709t9EAUUB2Z9ke8S/T1F0BTzcP6eepv4EO8pDg8dcRLYbfcSFlp9GThQAaYLs6wf3kEYiaGY+QfqpZzsR2jnCjV8B4OdH8oJHkgMJ4y0TYsUBsBUl70+YEfDQzDq21ERIeHVS2qjbOW3M4R8b+UHDYF8/dIB+JFDPDubzAo1sJ0InR8FznHrgQwrDgYTx7wmx4gA09SfG/YQS8Eh4z7pm3vnKbjoCPpoGkiMPeWnMKX8Y+UHPMyN+kCr2YiOoZ+fQtgo68XPEE+q8eKQveKQ4kDAuhzyJMycQCVSnTaK6MiQOm9HvbmM0yTEdfVSkh9qXniGbSHbEhVY+AhywVdRPP2H60bBHfYeKRQNHdPUrA/jEkbzgkeRgayU3AF9ldPbsI8HI7NuDEXsXsIrsG94DbNrr03/aXBk6d0tVmKBhcVpzF3Xx9LgLxVRB2JJn3HrP4qf2+Y1i+QeA2+N4/Zs4c1ytRCPNsTye9yaTBNjA6Yd3p8PYwLeBrxfzKLHD7iv54sXrvGSrljM4OLFI4I233rP4kIund39o68XPNVbcnfR6aA/7OWdbG8d0jM4ULIFNId3+52/mHdR6IFvEldEojaF8pgvSLOQ5fIwWn4lKJ9OwUOjY721LQPQBVcvkpUWb5HV/HHYD1633LE4DQ41EX7x4nULWqywBLr31nsW9uequPa7aeIjc+X9z7jnjM3vQlDTtYT/PNVUxrS9OWWa4lBNAmWEftDfzknoxiv0aGK8jmWsRtVAQyKFcXhoWDewAoJ/aXM1m9Jm5xUnOWyYvffjQ7rK4OGJN4rkf/6Yx245YfKLbllzS0S8GvCppTeWpKdU0xJIc09HP4NpH6iE4P4n3HLAz+YQBAhUDBZsMXix0Agz31WTwEqSHNAFGeo1l8rOOyec1aTrROv1emRExcdau/EM7Y7rKtuqyg+4Cny5vNLrFj/a5v41ZG6vZvUDDYgfHUEMLIfpJEqKF2aMWACarywlfOuJoMmmEEfVqtkeOr0VJYEd5ENWyWF9fftyhXXXfMcJUtr62k7ryCO2bbGyxi8Ujjpdkl1kU71gmL115aN85OZg0wpjdHRP56tb9Xp375zUyp7OP2JWBgx6ks38kyJWdNdAJKClx2+Iqtq9JU6b4iGIhfjxTfvtzR+a7ipNJIwwdFJvxDS9r6yJYisBrHE58O3qQzr5YJi9dR/5gxLFMmoElmm3L5kiA7RXZotwG1teU8VJDJQhBIt+SEgfEMbHiEWfSeIxwyjDQNe+fF00nkDGxhSClD4uhK+A7gt82ad6Xo8akeQJBW57vz5jUxFIkPNooUQCEkpnUkfu2SdkmdUSZNML41Z8WPLbX7+H8LXs5cU8XqjX84zUMJAhwOHOO5D560jIln8xt0hQlAHGPaluGpZzR3M0pLT20hP34DYu0qnDPHbN6Dv2KStXwxObBeMMC/JN2SN6RYtJ4DIAZA4kPmQIyikCzJdP7EwQME1vKwxoesExeGgNFBbEKbAtYtUz+j1gmL+0/4MkOZ9JMOBrk/e/Z+CNF4bOGpiEkJFTxwF/umP2mQtvlNCadMFwmhklVlLhMHK4wXPLiCsMlL64wXPJSsHaM13x6b01KEd8xFHGqJfnT2h/XfbNQtriMZ0JrJf8Rv1bvPGP+kzsbq07pqAyjCgVdSkwhSCmK1KRVvfq2hsNoqHI2O8W1ES/9UxrYuQG5ckLa6ydUGNct+Zd175lLFI+dLcNGuisL6NEVVMnD635Ud+6EGVWsiOXCxhOykJ1pAl6BToIQBkQbaS1H/umoCmRChPG8uF351+IZ0bBhBlJeL48tnkFvJDQ0VnOwQdoGBhSBLcTmdT+uO6KziScDq8VtopI9m/upm2OjEKaHRrYCQeJE0MgQYIABfJtr5I+P6vM56sJ4XtweemJW/bZTdnbU6nZW5BlV4QsffyMpX4CK1OgkNBbQ7lExdV3b9d2KkloheZf4WlxBBiCb2cfEiyBDIzspoxsbhT6qqGc3KvYPkSs/f7RsOaq1kj/V/LG1JRKKHrene0gUAB7L5t1PrKOjbPwYChXorA6R8opnjqZtxUar+NLmJrYFmtiChzR+YugkWMAL1LEbP3GCRGliJwYeJPzP0bTnqAjjm8f/6/57K35rz+jqb1ClRU94bGY8OG5nO2et3Y4cMyvVJlu89JQFTlz22fYlR8O+YmO1uK06QHyuhskuFtHCXKJUYRCgg2njjveSQgC94qPPHS2bjrgwPvn6p3re+NKOC6b0xYUKDIT83HHO0nHH3X7+Cdx9xiLaynxD0pCAKQTz2qIE0yYJXXl54Wc7HL8C4gIeb/cRx0AjTC+zeZEqWgCZd/ChADpoBNST0uId/zwaNh3Rdox7q35X+YGeeMVIta2eWQ8C/nbiXI7f3pY97tQF3HvqAgC21ZahmP2UJw3iukpGVwmlDBp6E9iGxGdZm4HyI2lnMbFZXPvOasJKhC4UoJpsxqAI3XhJIBF5hyzX0oFEkCB4viLeeaIu73rhSNp1RIPPx5XbpSJFtgTMYSgC3c5+3lUd4TOfehNd5aPn5sxsi9If9NATzuZoUS2bGR0xKhIZVClRbHnTUz9r/PoRM7SI2Cy+YcaoUY/hCTxjUjPsbwy7hY5EQ2ATx5Muk785koNej0xR8ry4XVx+0oNpr4SXZ9bx+Y+fz7svu5jttZEhUQBM7+rn99+/lxW//DfHbd87tD3m14ZEAWCpCi1VATxSElcU0qrytXM/vsdxQ7q3i2t+HqNO9ZLKW2iM3SIRWLnXTsFgL1PZyrFYaN5t4tq5R9K2Vy0MscK8/PbXLbEaepKeZ2Y38qVPvJFn5k9lR10FPmN8bbMikeK1G3fz85/8g+/98l/UDCTxWuPbalIeDVuAX0rSQrBX1w4521+xkyDwMT99VNDKbhbSxtT9JnOSgEmIFPXs4hg6mUaccnawDD+dm/vEJf86Ura9KmFoN6efu/jRNbe89/GN4rTtezllWyu/+f59BJPZsbTPz23c7/lnbmzmXU9uoDw5PqFuMG2iSfBKyfzufm74w0OhS855vm3h5zon/RzR1eK2im3i6l3T2SxsdNqYQ5RqWpnP3uEEAnkQCGKoxIkQH7W9i5n0UvOG7eLre/d5+iFwWMJ4Xtyuzfpcy65If/Kktz2zmbCZQSU7sXR2Zx+3/+QfAPzoTSfz7AHEMXtvFzbQ0JfIZhAANNOmqStGVW+Uj/7jWT75l6fpCQfpiYTqgql07E1vX2ef/d4tqTe+Y9Nvz3nftoPP+loErBa3iQht3dPZOC1FYFwqhTZmkiYbLoz0Hh1MZQ2v5RVeyzYWogxV7LMIFAaoRSLqm8VXrD3i/133auzMG3yKFabAkhqqqMaSf0bax2NLDSG82JL6nijtNWUowH+/9hv0PPMwLr78nTTXRACYsbePH/zqnzT0jU/7fM8Zx/LkMdm3JK0qDOgqmNkSd/bebr668omhYzvLAlz1/nORAhDZEjicTPGm1VvkCzMa9tT2x35T1x+//6ZVZz/9ah7KkWa1uO0ksH4GLFRJexUkNhph+ujLk/p8Ni8SoXvoc5wyNnHyqGN8JEiNSE5bQTtT2ZhLOpdrC6KWThpsH/3bbHx39dG0EcS/gOgyeemohGVjEdyYVhCswWYxKtkHLgFrMDdr7j9CgG1n26wVwK8RTGX447fvpj6aGHfh35y1hE1Tqlg7vY43rN7Ox//1PIoi8FjDua1emV7Pb847EUsdnjwkgR5VReZ++Ot+/zD1IwR13xmL8GgqZfEkStrk8aVz6PZ70aRkQUsnyx9+iUDawFQVNjZVy/tPmv/Xf9w9/237ewhHgtXitjcA1wPHAr6xr1u+yFnBwEZhUCgKBo1spZqWUa68jRm0Mmef3+0jzhzW4mH8izeYr2yAanayEAtPbo8FaBYMdVklgBeAK5fJS5/UEKzGZjG6GHoLs0jISFBzaaqkzF4LQAhq+2L88kd/pSaazai3ZkYdd5+2EK9h8v/+8gwffmwNAAmPhj9jZh+MlX1cTyyczhPHzKKjsmzcjQymgR7MuDL2AcdqyunLrTgQSKQ45eWt/OE12ewHz0xv4LXlW5nR3ovHslmyu0NM6Rl463GfKrv95Z82fHqfT/ZVslrctphs0tlRxdqBqlE2OmF6mcM6YoQJ0oWSJ/z0Mv7F00kxmxeReAkQzbV2jGdQYBG6aGAnLcxBopJrwhp8IwXZ/B5nAf9eLW5boGBzLCNc8wirR28b+b2W5P2PraWuP7v01JrptXz60xfyr2VzmNHRj98YTqQTyJgM+LMqTWgqvz91Mb89ZXFeUQx+7eDZFdEEujVcs9k2tWZIFACJgG9UCW0qgicXTx91vcpYivktXe/L+2VHjvcyQhQHN4c+S5RyQBAmirKP9sZyOgkxcpiKzRQ24yFNkGiuZ/rA31hGd04U+yUAXJxtIckXhNoyFwzmvnDM9zb2DOe8XHnaQqzcwpczO3rHXWpvZZhHG6t444vbeP8z63jXc+t54MQFPHr8vFHHSSAtxFAKv95wgK9/8DxO27gbASTrK8ZdOxYYnbjYkx5fw8lo6r6Teh4ZxvnwgxWHTmbE256/LiCQTOcVYlTSyRRMdAaoQiNNF42oSPqpZA4v4x9VWxlNigAKJvaBG7xjCoK7s4kUx7giVWRd/2BwKgQow7f6zMzh2oY5YjXUF2aPr4XsrI5w1rpdeM3s26/bktM27EKM6HE1gR4hMIRACkHAtJieSDE1lWb13Ck8tWAaemL0lFJh2+yIDC+eKKTkNet2jTpmW10Fm5uqbz7Qk3iV3EE2yUrWjoM+TdI4Isns2IJz5KcMPtqYSYJyMgTppolmFjCdDUxjI3N5CXVEStYu6kiNWNbLRGMvs/Cw35gTYDfwZ0Ve4XkvqrgVU8ax7KxALDtrlS7AsLN/tg1eAQEVvAr3nrmAl6fVYAMXPb8ZkRPQK1NqMEaUlCYKM5v7KB/zo/7rpAVIZVhQGuAnK5CIYXJMLE59JkNjOsOxsTgLYnE8iRRL1+8kHEtS19XHqS9s5rmZjQgpUWybC57fSFt5kJhXpzPslw8smxP7+fknfmH9j2tXHOhpvBqWyUvbgJOA75HNSJwaHHiUDeFlrhdZAhYaacroYg6vUJXTk40gNSJESeGnZTj1OQJBkiAWAguBjSBJmCTZItlHAk8u/aQEolSwgVPYyUK6qaONqeikSBECbBQyNtmEuAZZj/cScCNw6jJ56cB++0rEClMg5YeQcjYWe5H8ANBVLCwb8Ghc+o/neM2G3Xz4i28j49F5zyOv8MkHXhxyj4NNvUK3CI6IPb7zjrPZWxUZ9X0ZIahKpfHakog1utU0qQj22nDaxt0sbulk1cwGVs9qoD/gp7p3wDhxe9uVF7y89f9eZ3686NYoWy1u04BTdWLHmIjvL2W1TyBop4EEYTQM+qkigw8/URQyzOIVdIaLxVZm0TKmZiKwWcQzozIKQjYN5QZOAWxZw7Yd1fScr8q7tx6KzYfdiVb+tb4fmpb83IeefIVHl82moS/GtvoK6rti3HTHo6OO7Q15mRIbyN0M2AJ+8JYzaa6vGnVcEjgmlsAQoI8xq0dT2RIMIKSkpnsgM6Nn4OqFLV23X7H+/EnXVD4gPrGtjPgsC4VtLCI2ovPYzwDzeX5c7WQLS+lj9AgEFYNl/Gfoc7bOGWQHc6NeZO1c+Y3DzhnyqnpXf19359d3VwRvqE6m+dynLySjayAl37/93xy3PZsnLenRiIY1ZnWPnkD+3Nwp/Ol1JwwVyBlAlZLZ8WTeuHlzwIeB+MnzP2u89LANLiLaxJe3VtM2W8EkhT/3F6KCNvx5qqc7WUQnU0ZtK6OL+awGssXWBk7EJPDX4+QX3/pq7XtVfSXvb3/3jedu2lPdp6h93pSJZlogBKZf8I/TZnP1h8/isk+dw/Se8S/10u2tBFNpFJmNcS0hUIWgb8y6Zjaww+clYfGsU0QBUC+/NyeBz1Iw8ROlkg4aac4rCoA6dqKOKFoEFpW0YuRqGN3UySa2nXgkRAFHYKDOSfLT3SdBxSvnPvPwkpbu1wshOH3jbmxFoaU6zFkbdqHm8Uoey2berr28OH8GaUUMGdLn0UkpCgHLIq0Imj0eNCnjL9/eULRrexwufuL/a6F8SR3qUtj32Gc/CRbzFN00IlGoYi8aGRRMeqikhi4VedcRG1xzRAfqPCVut1RQDtYN/f2khTyybB6x3EI1QXt0n4vXtGjxeqi2rPLHb29wXjITsVzECcQ9ZPz6flf/0Mg695HNf1kGiDBAlTlFrjiinYlHdMznxmk1ZTcsPx2LAy8SZQPnvLSFr975MKdtaSYpBOkRLa1DEpHSdqQoAORK2cL8j4pxQ6JHojD8MymMTDeawYOBlwi9tx9p046oMC7ZdXFcteSfvvHu15LU8jSmimwPar9Pz/bDGSZ1fTE+9PAqmrr6iCkKPapKEvCYFm26BooYP0zaQcyTV9/ZzrSMwMu+utpGMpg120agYBKgf0NY/vKITyU4KhOOTrtk+1ub2nvvePOqreWKEOypipDRNR47ZhZrZtZw701/xGeOLk//dPJCHjphARrZ9gzTsqiE4JO3N+SPxhzEDnGVZwZb0tk1D+wRy3IJxi8anC1ObKCfyD8r5K8vOBo2HZV5JU//atZf7v7HCRWzu/rfHkpn+O+imfzpjCV0hAJMae+nLzR+3GpXZHhsgSUlHvh6KYgCYKa8PmNjPCax6WIqMQYb/gYXfWLEZwsJbOU4tnLKhUfLpqM6E+1M85P3vb3nA8JUZfvM9m5MVSGpe/jV65eOOq61IsRjC6ajku1Ek0KgqOotR9O2YkOVK8+2yVwfoIttHMd2ltDMXFKoZFutB/8kRnaBnW+cJD991CY2T9hs99e/b1N6b3nYs2VqFeWJFFN6opz9yk66wgH+edwsyvozubEfkqZY+rbHfzHlsxNiWLEhLj5/gLIHupgqTHQq6KCalqHowwYM9JVeeefFR9WMiRLGaz69V5+6uy2d8mhi1dxGkl4NxZZYIpuzEyEIxlJWU9I697GfNz46IUYVO2K5lsC32Ut6pkBioWKiPeiXf3zDUf/qiU7nuOTS9m8otn2VqQotILPN4KYiCMaSPY/+ZmbVga/gMhEUNM/nvMt6jlNNqwKTJzb8qKag67u6jOb/A+CQJ3jytXKyAAAAAElFTkSuQmCC" id="imagef80a19eb24" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature42_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image009564fd2b" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pabdb660da6">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
