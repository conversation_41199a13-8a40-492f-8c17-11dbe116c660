<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="778.965406pt" height="679.5765pt" viewBox="0 0 778.965406 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:49:24.286189</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 778.965406 679.5765 
L 778.965406 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
L 520.118406 27.789 
L 405.646406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 449.225919 638.149 
L 449.225919 27.789 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 405.646406 609.084238 
L 520.118406 609.084238 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 405.646406 580.019476 
L 520.118406 580.019476 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 405.646406 550.954714 
L 520.118406 550.954714 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 405.646406 521.889952 
L 520.118406 521.889952 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 405.646406 492.82519 
L 520.118406 492.82519 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 405.646406 463.760429 
L 520.118406 463.760429 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 405.646406 434.695667 
L 520.118406 434.695667 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 405.646406 405.630905 
L 520.118406 405.630905 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 405.646406 376.566143 
L 520.118406 376.566143 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 405.646406 347.501381 
L 520.118406 347.501381 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 405.646406 318.436619 
L 520.118406 318.436619 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 405.646406 289.371857 
L 520.118406 289.371857 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 405.646406 260.307095 
L 520.118406 260.307095 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 405.646406 231.242333 
L 520.118406 231.242333 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 405.646406 202.177571 
L 520.118406 202.177571 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 405.646406 173.11281 
L 520.118406 173.11281 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 405.646406 144.048048 
L 520.118406 144.048048 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 405.646406 114.983286 
L 520.118406 114.983286 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 405.646406 85.918524 
L 520.118406 85.918524 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 405.646406 56.853762 
L 520.118406 56.853762 
" clip-path="url(#p1010a174d0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mb7ae5321da" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb7ae5321da" x="449.225919" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(440.4792 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mb7ae5321da" x="508.843747" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.2 -->
      <g style="fill: #333333" transform="translate(500.097029 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(340.201 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(54.014219 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(69.906719 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(64.007969 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_range_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(14.445469 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-64" x="2091.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2155.130859"/>
       <use xlink:href="#DejaVuSans-6c" x="2216.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2244.4375"/>
       <use xlink:href="#DejaVuSans-65" x="2305.960938"/>
       <use xlink:href="#DejaVuSans-76" x="2367.484375"/>
       <use xlink:href="#DejaVuSans-4e" x="2426.664062"/>
       <use xlink:href="#DejaVuSans-75" x="2501.46875"/>
       <use xlink:href="#DejaVuSans-6d" x="2564.847656"/>
       <use xlink:href="#DejaVuSans-62" x="2662.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2725.736328"/>
       <use xlink:href="#DejaVuSans-72" x="2787.259766"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(79.471875 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mode_Number -->
      <g style="fill: #333333" transform="translate(86.388281 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-75" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6d" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-62" x="2108.890625"/>
       <use xlink:href="#DejaVuSans-65" x="2172.367188"/>
       <use xlink:href="#DejaVuSans-72" x="2233.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_102 -->
      <g style="fill: #333333" transform="translate(184.554531 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(84.4525 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(69.906719 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(81.712344 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_maximum_NUnfilled -->
      <g style="fill: #333333" transform="translate(50.839375 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-55" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.556641"/>
       <use xlink:href="#DejaVuSans-66" x="2304.935547"/>
       <use xlink:href="#DejaVuSans-69" x="2340.140625"/>
       <use xlink:href="#DejaVuSans-6c" x="2367.923828"/>
       <use xlink:href="#DejaVuSans-6c" x="2395.707031"/>
       <use xlink:href="#DejaVuSans-65" x="2423.490234"/>
       <use xlink:href="#DejaVuSans-64" x="2485.013672"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_116 -->
      <g style="fill: #333333" transform="translate(184.554531 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-36" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_9 -->
      <g style="fill: #333333" transform="translate(201.097031 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_71 -->
      <g style="fill: #333333" transform="translate(192.825781 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(54.65 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(190.355781 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(259.696562 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(131.130625 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(192.825781 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image5f474bbee5" transform="scale(1 -1) translate(0 -578.16)" x="408.24" y="-43.2" width="108.72" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature82_fold0 -->
    <g transform="translate(153.999406 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-38" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.272906 638.149 
L 534.902406 638.149 
L 534.902406 27.789 
L 527.272906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image9ff19e4464" transform="scale(1 -1) translate(0 -609.84)" x="527.04" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(538.402406 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(538.402406 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(572.803344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p1010a174d0">
   <rect x="405.646406" y="27.789" width="114.472" height="610.36"/>
  </clipPath>
 </defs>
</svg>
