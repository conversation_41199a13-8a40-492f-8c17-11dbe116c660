<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="750.871187pt" height="679.5765pt" viewBox="0 0 750.871187 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T10:36:00.582379</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 750.871187 679.5765 
L 750.871187 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 358.832187 638.149 
L 510.744187 638.149 
L 510.744187 27.789 
L 358.832187 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 423.532792 638.149 
L 423.532792 27.789 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 358.832187 609.084238 
L 510.744187 609.084238 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 358.832187 580.019476 
L 510.744187 580.019476 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 358.832187 550.954714 
L 510.744187 550.954714 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 358.832187 521.889952 
L 510.744187 521.889952 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 358.832187 492.82519 
L 510.744187 492.82519 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 358.832187 463.760429 
L 510.744187 463.760429 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 358.832187 434.695667 
L 510.744187 434.695667 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 358.832187 405.630905 
L 510.744187 405.630905 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 358.832187 376.566143 
L 510.744187 376.566143 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 358.832187 347.501381 
L 510.744187 347.501381 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 358.832187 318.436619 
L 510.744187 318.436619 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 358.832187 289.371857 
L 510.744187 289.371857 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 358.832187 260.307095 
L 510.744187 260.307095 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 358.832187 231.242333 
L 510.744187 231.242333 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 358.832187 202.177571 
L 510.744187 202.177571 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 358.832187 173.11281 
L 510.744187 173.11281 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 358.832187 144.048048 
L 510.744187 144.048048 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 358.832187 114.983286 
L 510.744187 114.983286 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 358.832187 85.918524 
L 510.744187 85.918524 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 358.832187 56.853762 
L 510.744187 56.853762 
" clip-path="url(#p3881f30ec4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mfd9c0a3723" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mfd9c0a3723" x="369.739353" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(361.63115 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mfd9c0a3723" x="423.532792" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(420.033417 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mfd9c0a3723" x="477.32623" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(473.826855 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(312.106781 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(116.089219 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_1_0_1_0 -->
      <g style="fill: #333333" transform="translate(16.95 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-5f" x="2335.466797"/>
       <use xlink:href="#DejaVuSans-30" x="2385.466797"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(59.37875 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(33.827656 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(23.0925 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(35.8325 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- XRDPowderPattern_xrd_32 -->
      <g style="fill: #333333" transform="translate(162.460625 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(23.0925 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(50.924687 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(214.862812 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(84.316406 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(23.0925 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- VoronoiFingerprint_mean_Voro_vol_std_dev -->
      <g style="fill: #333333" transform="translate(54.615469 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-76" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-6f" x="1631.392578"/>
       <use xlink:href="#DejaVuSans-6c" x="1692.574219"/>
       <use xlink:href="#DejaVuSans-5f" x="1720.357422"/>
       <use xlink:href="#DejaVuSans-73" x="1770.357422"/>
       <use xlink:href="#DejaVuSans-74" x="1822.457031"/>
       <use xlink:href="#DejaVuSans-64" x="1861.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1925.142578"/>
       <use xlink:href="#DejaVuSans-64" x="1975.142578"/>
       <use xlink:href="#DejaVuSans-65" x="2038.619141"/>
       <use xlink:href="#DejaVuSans-76" x="2100.142578"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(137.740312 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(146.011562 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(36.961875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(215.63875 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(94.123281 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(52.539531 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 358.832187 638.149 
L 510.744187 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAMYAAAMjCAYAAAD++m/xAABiZUlEQVR4nO3dd3wcV7n/8c+Zma1adcmWey9xmpM4PYQWSiAkwaGE3nu5wBUllAABboCIXi8QyqU3U3+EEgikJ3aKYydxd9zV62rrzJzfHyPLkrWSpZ211hM/79dLEM+2R7v67pkzc+YcpbVG+LfmDXtyUa2tjOveueGHiy8tdz3CHyXB8Gf2ezrN07p67YMVFSRDFlW5PNXZrL7jh4uMctcmiicfnk/LO7pzbfE4K5KDXNTdy7x0hq5IWJ3/ut3nl7s2UTwJhk92KKROH0wTMU2wLKqV4tTBNBlX3VHu2kTxJBg+VTsOrjn6bQwbBv2G7KIGmQTDp0rXHbNNA2FXghFkVrkLCDpTK7TWoNTwNst1qZVcBJoEw6eModgWjzE7nyfqarotkwwhrORguUsTPkgwfOrVqK5wiJyC6lye9nCInGFQm8+XuzThg/QxfArlbWan0xyoiLGhoYa8ZTJvMEW7kn2pIJMWwydLwQN11cN9jIOxCI6hmN/WLV86ASYfnk8HaipHdbwB2sIh4mFLjfMQEQASDJ/sAn/+Ya1J67GHcUVwSDB8itsuUWd0COakczhhU1qMAJNg+LBRfUH1RMIszOYx4xZUh6E2QtuMBP3RSLnLEz7I6NopcNWLTusl+nAVGXNzbDUHw3NpTyT49EsuZees2iN9Da3BdmB4qIhKofgqSn1cN1u5sv0CYtLkqNRkqbVWP5UP1zJgboydzRPRxQDUpHLsbaiCvAbXBVN5h6qU4Y0N8cISR6kPAQngXeX7JcRkya7U5F1skTMVcCA8b3ijoxR2XkPeBUdDzvV+ABxgdE/jNdNXrvBDgjF5KT30doX0kbPaDy2YiT7qrx97KChj310ZJxIQEozJ0uvWa4y0BpZnHh/enAmZBe9eO5gBQ3m7U0e6cV88zlWKEpE+xhRUMTijg+rtc3NPNIXdLPtCizhnt0FVKkN/PDp8v5DtMHswS09tHEBjqM3Ap3Sz9ety1S6mRo5K+bTiDXv0mb39bG6qp7UyTmXWJuZo4slB/eD3F0qLHFDSYvjUH4/zeCyGCcxJekdiNbAjLucxgkyC4ZNZqMXVGstFmuIAk6bepznJQcK2M2rbjMEUEdlFDTRpMXyanUyxorWDzY11JEMh5iQHWdHZw45Fc2UUYYBJMHxqTcQ5v6uHS/a3Dm87EItS69jSZASY7Er5tKmxhi1VieF/pw2Dv8xrIhWS75wgk0/PJ1ehf7RkvpqbTFNp2+xMxMnFIzQOyEnuIJNg+FSbypGxLDpiUbrBGxuVyVOVzpS5MuGHBMMnQ2sqNBweGBIa+u/KvDPRw8QJTvoYPmVDIY4eLRVG4xqGXMEXYBIMn7JWobdQkTVl8togk2D41NSfdNyjR51bBpURq7o8FYlSkEGEJdD4vi43ZZjKcFxsy2BRX7/z2HfmS/8twCQYJXLR6574pG0ab6/UeuU/b17QVe56hD8SDCEKkD6GEAVIMIQoQIIhRAESjBJSN2Tl/XySkM53CVx/xt9SO6srY7ZpEM/ZNOUy/TduuEzOYwSYBMOnG1b+5apsLPT70/o6qU0N0JaoZUtVDdl4fO4X77z4QLnrE8WRk1A+paLWupjq59OXPQUzFCLvOLzy3vsw++29MGYYlQgI2Sf26dzO7Wrvgrl89s7f8+l//4bL2vbxl3PPoy5vl7s04YO0GD7ZMVt9+w/fxBpaKOa5W9bzoee/iUdnN5S5MuGHtBg+re46OBwKgJDrcNXmu0hWxMtYlfBLguFTw2ByzLZ4Pks0L1fwBZkEw6fWWA0A7tAPwPbKBczr6pYLlQJMguFTl6pjgGpsKrCpIE2CpY/3k0LW4AsyCYZP81MdRIZXiFGYwFK1nfY66XwHmRyV8qFDfaCqocBaMDFyLO7uLkNFolSkxfAhh9Oj1dg9pqwVoiIrIwqCTILhgyaq9sfqcdTorb84+ykMxqLjPUwEgOxK+VBFF6F0nBsuewWX7XyQeCrL35efxeZ5i5lxUK5uDTIZRFikHvWGQ1FosomTNqJEXAdHGexIzGZXfDYdsQq65wzqd9z7+z0NTm4r3tHclcAe4LXodXvK+xuIiUgwitCq3v+iGjp+PchMwqSA0KjbN9QspdOsofnap7Oiaz9//8WNRz+FBlaj1z0yTSWLKZI+RhFswt/WGChcjKOXMgYWpVpxQgaz02n+seRU7pmz9Oi7KODH01GrKI4EowguZiJLAhMXCqwo5igDrRQdCW+81N6q+kJP03RcixS+SDCKECK7AcDFwiTP6HBo2mjkzlMX8kQiQcTO88wnHi30NL+bjlpFcSQYRbDIXarJa9D0U0caC7CxgQM0Ec1qOmfUMmNggF//5qs0pMcMNNyEXvfW6a9cTJZ0vos0qF67LE/1tn7q6MLbVXI58k3zs2eu4cwD/1n4yi33dQHZoZsSwAB6Xa4sRYtJk2D40Kve7v5s4TNVfTbPHy4+k7a6aha2dnHVXRvZXV/Lf219vgwkDCg5wedDhEHyYc0Pn34huZB3yPaJpnq+/fyLmbfjYJmrE35IH8OHfsL75uW6h0NxWDYa4YHlc8pUlSgFCYYPM/V3F8waHDuK1nQdzuiQ0bVBJsHwaVFHJxc9sWnUtudsvR87FilTRaIUpI/hUx6T162/hcu2b2BX3WxWte3mjqXn0R2XYASZBMOnv516GgeqZvCb05ext6KS2rzDylSeOYcOucd+tDhRSTB8ShkW3159Bq3VCQD6I9AdyvHxe9vLXJnwQ/oYPoXt9HAoDhuIhHm0sbZMFYlSkGD4FHIKv4UJR6boDDIJhk+uHXfX7G0dte20Q52ceah1nEeIIJA+hk95rfOv3rg1sqq9m921VczvG+Dc1g4yrp0qd22ieDJWqgR+sej/dE+iDm0olOvS2NfJi/a+TsZJBZjsSpWASzymbdutTXaiXcfdMmeBnMQIOGkxhChAWgwhCpBgCFGABEOIAiQYQhQg5zFK4CvLfpf9z5nLwlopLt68w25+/KrQsR8lTmRyVMqnD55/q/O1p5xjpA2F0hDVmjeu3+h+9T9Pk6WMA0xaDJ9+d/pyI214e6RaQVop/r1wruyiBpwEwyfXgBc8sZs3P7weA/jxaefwQH1ducsSPkkwfLpy13ZuuO13hFwHgEt2beLGp1wNLCxnWcInafJ9eu3G+4ZDARBx8ly97aEyViRKQYLhU9jNj9kWcWSiwaCTYPj0yNgp/tk4d+w2ESzSx/DpwVnLqchnuHDXZlyluGvpGWxskmAEnQTDp1N2PsEdK1azYd5paCAVCbNixz7grHKXJnyQYPjU1thA2NVkwyFQirDrkonI5RhBJ8HwaaA6gWMdOcntGgadTXIeI+ik8+1T3ho78sMOyfdN0EkwfGqPhcdsS5nytgadfLX5lDEgmsmSiXr9CuU65EMmObU2p0CFjiy0dPj/NXATet1Hyle1OBYZXevDNS/Y4Db/a4OqTGdonR3ltL6HmZlsR6NQQ//rrVysh36O/rdhon8jc9yegKTNL9KVL3/860t7+1Uik2XTyjk05fbRlGxHAQZ6aPXvw186asTPyH+7Y1atFCcGCUaRIunet83tHGDfjFoO1dSzuGf3MR5x9DRTCiB2fKoTfkkwimVG9tsYtNdWEc9ksFVR5y5kP/YEJcEo0q9/c/aCg7WVmNrhuRsfIpevH/VXPvovXo/ZMtTnWHWcyxRFkmD48K/5je9b3noIy3XJUE0bK+ljFntiS9GgXQzt4mrQQz/u4R8XSKB/u6Xcv4MoTI5K+fRY5CZdlcuM2tYXjnJq9v0yd22ASYvhk5Mbe+Y7n5dJQoJOTvD51GVWYTkuFXitxgBxeo3EMR4lTnQSDJ/2zaojvN+lh6rhbXvnNJSxIlEKsivl0+bFM9i6qAnHULgKds9t4O7TF5a7LOGTtBg+bVg4i5CGO85biW2a1Hf38YC0GIEnLYZPEcfhr6uWMpDLk8pkuX3hbIwxZ7lF0EiL4dPSzm5ylskFB7cTcfLY4VOJ2hqYU+7ShA8SDJ9WHzrAR277HYlcFoBXPngPf125Bji1vIUJX2RXyqen7XxkOBQAIdflmdseLl9BoiQkGD5F7eyYbWFn7CRsIlgkGD492jR/zLaD1TIZQtBJMHzaULNU761pQA+NoO2JxfnVKZfIALSAk863T9c9/GzjKytSbvcZDUopiPSlWX1gT0W56xL+yOhaIQqQXSkhCpBgCFGABEOIAiQYQhQgR6VK5B+xr2SjSllGLvPai+0P/Ljc9Qh/5KiUT/+o+FZFLhpJOvkwKIWlbUyS7nP63yHrfAeY7Er51F8ZHSAdIjaYp6I/h5kGrePGgHrjzHLXJoonu1I+1STzqj8c49D8WpQCI+8y80APu6nafgYjrncVgSLB8MlBoSJw6tYDAORDJntn1TNjb2W8zKUJHyQYPqUq4tR2JOk3omgg5LjM6OxDK9lNDTL58HzYbX7SIO+SNsJopUAp8srEzGgcPShHNQJMguHDhur6TH947IpKeWUwoCrkwu8Ak2D4sMDNWDV2asx2SztUS3sRaBIMH/qjCdWge7FUbsRWzY7F1chJjGCTzrcPTlaxJNlGxN5Hl6ohS4haPYCbWkiSkCwhFmASDB/m9nboOFll4jBLdw5vP+/gLp4gIn2MAJNdKR86K6qVYmzDoJRNiLwEI8AkGD50xqrIGmOn/B+0YuSxy1CRKBUJhg+VOsW/Fq0mj0mWCGniZIjwaPV8+rDkuFSASR/DB1M7VOXSKBQhbDQ2OaLMzAxwCjsM1Npiw3F40T4HOAh8Bfgyet3xDZtaawLvBK4E9gGfR6977Li+5glKhp0X6Z7wV6uNaKp3ZbIdc8R7qIFBEszk0VJP7XwDet3HS/uUR1Frvwi8d8SWXmAVet2h4/q6JyDZlSpSnd31s5BjjAoFeKt3mzjHY77zd5X+KUdQaw3gzUdtrQFedlxf9wQlwSieckKq8CLFHJcpOqfjKFeh1zgpj65JMIrUYTW+grzL9oq5aMBFkTVM2kN19FSMHT9VAt84Hk86TK9zge8etbUf+Plxfd0TlHS+i3RJ7p09dya+QmtFIz01YbY2LSEbitAw0MVpB7aRQxEe055MiQZsoB34GnBTKeo+hmZgP17nez/wWfS6g9Pwuicc6Xz78GD0c26q0lDrl549anvDYAenb9rgrtYtMmQqoGRXyodK3a+y8bG7TZ3xBrqoOyn3zZ8sJBg+5Cjcl4jn0jRa6WmuRpSSBMOH/liV7nVrSSQzRzZqTdOBPgZUSPZRA0w63z6Y+bya1dvPrANpuusS5EIWtb2DRDJ5skrLrlSASTB8qMwPUpXvYR8LaehKDm+3TUWTLDcWaLIr5YOJSwMdhMiN2v6fM+agsWVXKsAkGD50WhEnhM2pbGIW+2mgnSVso7Wmij5cCUaAyXkMHxz1BrOTOruBDhTe2e8DLKRH1ZLR3e4F+hNyHiOgpI/hg6lvdvaHvkyPPYMIGdJU4GBhanD8nfUWZSa7Uj7traknT4Qk1TjD3zMumogkI8AkGD5tm1VPfyQMQy2EBvZV1YLMEhJosivlk7ZtfaiyRnXFHSzXIWtabJ7fwBUP93y+3LWJ4kkwfFpxqLv63pXz+xMZh5rBDDubajhl617O0u/5aLlrE8WTo1Il8pPaH2Szsai1qLXtZ89w3/Oqctcj/JFgCFGAdL6FKECCIUQBEgwhCpBgCFGAHK71SX0idTqm8cjwBte19cfjYye0FYEiLYZfpvHI8ISaGlCGpT4xMHaZJREo0mL4oD42aBEyIWSApbxg5F1QoVi5axP+SIvhh+PUETa8YIA3Z1/YOEnn7ntykRbDD6ViGApsF3IuaA0RE0z5vgk6CYYfIdPBcWEgB87QCIJUHuIWjDO1jggG+WrzQ+sMWQcMBdURqI16ocg4qOsGZIcqwKTF8MMwwNVeKNRQDqwwkPdaDhFY0mL4kbfDhIwjoTgsao7dJgJFguFHXj8w7qXdMmo50CQY/iTIOYyZKSclK7YGnQTDnyhKQW8G0nnI2NCXhZwDdu6echcniiedb7/CpnfGOznU2bYMiFlg55vKWpfwRVoMPzQGBhAzoSrCTGzm57NU5LP89cf/M6/c5YniSYtRLLX2/Hc8/VrVX1vDA/OWctET/czvSxK1HQw3Q61hGf9zyff10zZt0s9/zQdqer46q7/cJYvJk2AUQ639X1upN3/9tl8A8M1zL+O/r3otmUiYeCbPyx/YyorORfxizVNwIiF128/+p6+W687p+ersB8tcuZgk2ZWaKrU2DLz58PreW+tn8Y4XvZFMxBsCkoqG+OH5q9hTM5uv/uH7fOOSy4lpm+tu/+v6MlYtpkiCMXWNcGQA7ecvucI7Az6CbRl0VijOPriHtsoa4rksZ3Tul/c6QOTDmrqDcOS03v5E7diTeVrTmDpIfyTG6Qf34Jgm/5i/MokIDAnGVOl1GrgyZVrkDJP6waR3cdJIeZcz2nbzqWes5eZffpNwJscXL3nOwnKUK4ojE64VS61VVe/9uh1ztdFeVQem8n4cDa5m85ebSRmu26Hif3te9xeeV+5yxdRIMHxQH0naaG2ijBEXJ2nI2qx55O6+9bc8t6ac9YniyeFaP/KuxlCgh8ZLKYbHTW04/aLestYmfJFg+ONgGt57eHiYuQG4Ltjun8tXlvBLOt/+5FBq7LUXhgEh413lKUmUggTDn9vHvUW6boEmwfAjbLwekwIXJWlwZKWxIJNg+OG4oF1vLilTeZ1vBdiOXNoacNL59kNhYFneNRkjJYfmmRKBJS2GH6bKD89COFLIRH++SnoZASbB8EPryoLbLXlbg04+QT8MJZNHPUlJMPywQgfHzBACY2cNEYEjwfBBXx/VOK72jk5p78d2IZO5o9y1CX8kGD7p62MGedvBdsFxCacH/6o/U3NpuesS/sjoWiEKkBZDiAIkGEIUIMEQogAJhhAFyFipEviX+oFKWAfzvTUNZl1fpxtznKZTnes7yl2XKJ4EowQOLIq7hrMcw3XojNUag/FQ+zL1WiOsfyiH/AJKdqV8+lv8S3utjEtlboBENk1NtpfK1CB/rTtfFskIMGkxfOqtq59dmUuTUfGh6QljVKZT9FbUyAUZASYthk/RXMYLxQhpI05jtrNMFYlSkGD4FNfpgttjTnaaKxGlJMHwKWXFqM73jNqWyCc5FJ9RpopEKUgfwyfDsTFTYarMDNmQSdh2qUg5ZENIHyPAJBg+hXMOsawDuBy5nE9ROZgpX1HCN9mV8iljVoAaO/FBzJVZ/4NMguFTKJPmwKy6UdscU5GxQmWqSJSCBMOn3kSFirp5OufEcGYNEKrtptJJkrUKz5MggkH6GD6tP2O5cd6hvVy99Y/EbK9fkQxVkE6fV+bKhB/SYviw+M37kgca6lnd8fBwKAAS+UEazL1lrEz4JS3GFKkWu5regReQtD9eG49Ed2vN9ppq/jnvMlZ1HuRpe7aicAmbGXrU+0K1+osyxU4AyTXfk6Ra7N+h9dU4enj6zbB2cQAnd+Q9vPKxx/j+739LdyjCo9ZcLk7vcBRc0KA/t6FMpYsiyK7UJKgWexZaX40L5LU3YbMBuYg5KhQAf1y1ij8tPp3avE0oZvBQxWJTwd3lqVwUS4IxOd9DDS08eZihxl0D4+H6eeSVSV02RU6HMSDUpa5LTE+pohQkGJNzN1ozapDH4VAUGPixvK2bpBmjPxrFcB00YOCmpqFOUSISjEnQzdZnAI01IgUuGFpDxBoVjiguTX0pLDdLTzjODKcd0HfV6s/JugABIsGYLKVCuO5+bwUlh1A2T0PbAFHXhXgIYhbELV5196PM5AA55TD30N784nzPp+r05y8pd/liauSolA+z33UoPwNlvf6BP7GzdhYX7NjDC7bfS0IPst04jWXODTLCNqDkPIYPg5prL925/TfvvueWMbdZhoyuDTLZlfKh7+uzfvvCB+7S7bH6Uds1cKiqtjxFiZKQYPhkZLR758wL6Ix6I2wzZpg7Zl1IR7z+GI8UJzLZlfIpkbbZkmjgN0uuIpZPkTUjuIbJ3O6D5S5N+CAthk9tsTr6q6vRQDoUxzVMHMPANaLlLk34IMHwqS6fIhcJ01dfQ6oixmBlBX0NNSRy0vkOMgmGX/lBFrYdwglZpCsryFTEqEv2s6RjhxwHDzDpY/ikVIyXPPonHu1ewd6aWTQOdnPmgcc5oBrLXZrwQYLhl3IxgTMObeWMQ1uHN4csGQESZLIr5VNU5Sg0zNbScn1SkEkwfOq0qnXeG0A1vE2j6TNkMoQgk2D4lAnVVHaaNWSxABcb6DRr2JKYM3aciAgMGURYAr+v+8Hg0r598Qa3i6SqZGvVAuf5vW+S/luASTBK6GdNv46/vPXFckHSk4AEQ4gCpI8hRAESDCEKkGAIUYAEo0R+vfgH7//toh+8rNx1iNKQzrdPP1vyw//asmjpl/fPmInhapYc2Mui1gO1125/TW+5axPFk2PtPm1fuOTLe5tmAeAasHXBIuLZbDfSGgeafHg+9SSqxmxrr2mQ2UECToLhU2Oyd8y2Bd1t01+IKCkJhk+Xb76PkG0P/7syneIZWx4oY0WiFKTz7VO/eqfuStTx4IJlhB2bc3dvxcxmaNRflt2pAJPOt0894Voak30891Fv+QsN7KicjVy/F2wSDJ9Cjs398xeyZdZyLNvmzP2PUz0gFykFnQTDp/+sOI29DfO8xWSAO5ZdwMq9W1hZ5rqEPxIMn9qqmrxQDPXVHNOkq3pGmasSfkkwfLij9vOvVivOJjqQIprKgNZk4xG0Kwf7gk4+QR9a6+t/VNmXJDaYRmmNAqKprPfvD/XZx3wCccKSYPjQVttAOJsds11rl4hSZhlKEiUiwfBhZ9MCGjLdhJ0slnvkSJSlcoRcjfpURs5lBJT0MYr09hc/HpvX3c3TW+8grDO4ymBL9VLubzibhYN7SVZEIGu76tNpMIwX6A9H/lzumsXkSYtRpHQ6O/CKB/4fEZ1BAaZ2ObV3G7sWwkXv+YB3p4gJaQe0/pP6bF6O4AaIBKMIb37p1nOXdfaaM7KtY257x93/JB8OQcxEORrCBriA1tJiBIgEowgu1IZth7yKjLmtJpVnTk8vKIU2DTDV4UkKq6e5TOGDBKMIFY5z64HqCjbOPAN3xFvoYuIQp34weeTOGRtCgOI101+pKJYEowhf+c0qN1OVuHhL0wI6rMVkqSVLHRlmojB4379vA9sFXIha4OjH9YfCfyl33WLyZNi5Dxurb9KVuSRNmZ5R21tj1axs/iD5dO7/9E110lIEkLQYPtQmU6Sc2NgbMiHyrkJCEVwSDB/6Q2Faw3X0cuS67wEq2BeaAdqVpjjA5ASfD49U1bfPz/fP2GotosLOoNBkVIRsZR5iIVm2NcCkj+HTzur36s3R09F2BKU1hpWlMtzN0/a/X4aDBJi0GD49NOcMrtxyC22RJhSauE7y5wXPKXdZwicJhk/x/jQ/PPNa6px+bEx6VBWntu4ud1nCJwmGT7NzvWyuOpVOqwmAaC5DrF2u+Q46OSrl0/r5p2NboeF/Z8JRNs1cXsaKRClIMHzqio0dAtUVHzttpwgWCYZPnYn4mG0diYoyVCJKSfoYPh2srMTJpGkYTKGBtsoEKbfcVQm/JBg+zW3r5qEVCwg5DhqwTZOnbNxa7rKET7Ir5dOq3dvbVj5xCK3BcFzO3L6Xmf0dPyh3XcIfOfNdAl8556/n1/X33oWh3O6q2jn/tf7ZHeWuSfgjwRCiANmVEqIACYYQBUgwhChAgiFEAXIeowRe97Tb8+e1dVsK+Pe8malf/P1COfUdcHJUyqf3XPwPt2/uXDUQDmNoTVUmC93t9vf+9fTQsR8tTlTSYvi0bfECtb6hns5oGICmdJanu3l5XwNO+hg+7aiqHA4FQGsswqbG+jJWJEpBguHTvrg3fU7EzmG43ujB3ZXSxQg6afJ9akwnedljd7K9cRYR2yaUy3PL0nPLXZbwSYLh03P3bOTzl141/O+QY/PsHY8BsjsVZLIr5dOfl5016t9502J/tUxsHnQSDB8uuXb9h7sqKsds31strUXQSTB8SPQNXqn02Mv1BmQSwsCTYPiwL5FYftbudkKOy5yeASzHwdBgO6BuzF1Y7vpE8aTz7UNtzq649rFtvPM/DxG2XfqjYf5w7gq6YmHurpx/F/LFE1jywflw7ZadVmP3AGHb252qyuRYe98WNs+dASBz1waYBMOHZDyujKOGmlXk8sxKpkCB+myurjyVCb8kGD601zeoo4dg5k2Dg3WV3oKUru5Un80fUC12bTnqE8WTYBTp1Zf9x67Wig0LZw5vc4FfXrSKgXgEDAWgMJiN7Xarz+c/WK5axdTJsPMiqOsGzGfsb7XPGBhkS30ts/tS9CnN3UuaOFQ34ryGq8EyvIUqldKYytTNlrzhASBHpYqwqD85LxWOkDWzVDnw6Jx67ptTYG/JUKC11w3XKKAK6JvmckURZFeqCLurEnsrbJs9cW/e2vZ4eOIHuIBC62ZLQhEQEowi6Bsr3bmH9jhp00ADVVm78B0d7e1OKUDxrumsUfgjfQwfnvHKndqKRqhwXf6zoJ6e2IiWw9VgaxfL2IqlztbNVqZ8lYqpkj6GD71mVi/LaFXluLxwywEOVsbIWAYPzayhLxKCkFGvPxjqLXedYupkV8qHFa1dusJ2UIAJzBtIs6xnkOXdSdAaCUVwSTB82NzYYGdNEw2kTYOsd+6CSC4PGtlHDTDZlfKh0yC7PRoNV2rImSYAFXmb6u5WmDfj9DKXJ3yQFsOH5X29O0xlDIcCYDBkMSNvoz8cfrSMpQmfJBg+dNUk/hApcFRvT01jGaoRpSTB8GHzj07/ZE9k7ISDW+tlzGDQSTB82h8xyJhH3saesIXODZaxIlEKEgyf3nfXH7nmkVtIGmkcp4dP/PNmLtmzrdxlCZ/kqJRPr3rsHub3d/GhO/84vM3VBvDM8hUlfJMWwyfbGPvdYpsyS0jQSTB8+uPKS0f9OxmK8s/FMkVn0MmulE8/W3Ge65hR48xDj5EORVk/dzV3Njblyl2X8EdG15bAsrfsyyxLpiKOUmyrjPbv/tZ8maMz4CQYQhQgfQwhCpBgCFGABEOIAiQYQhQgh2tLRP1Ptg1TNeJqjaFq9AfDA+WuSRRPWowSUDfmXJSaASgMZaDpVzdmryl3XaJ40mL4pG7MxzBQGArU0ATnWoPNr5EvnsCSD84v2901KhTg/bchywAEmQTDv8IjBpXkIsgkGP4V3h2VEQWBJsHwS40zTY40GIEmwfBLa6fgdtmVCjQJhn9yZO9JSILhm8oX3CxdjECTYPiltewzPQlJMPwyVKLgdolLoEkw/DK0dLSfhCQYpXD0OQuN9DECToJRLLXW+kf9h1/6ztvvtS7Z0Y6Vd7yAHA6JnOALNDnUeAxaveQdoL+occIAGSNE2LUxgQOzF9Be08SD1Qns/qGDUzEL4t7bqj6ff6f+QOjr5apdFE9ajAlodc1McL+ucMKHexFxN4+FpjccY8PCc1nfUEtqxDIApG3IO95SxoqvqZvssbM+ixOeBGNC5icVLnB44dUj2qL1aMNkdyI+9mF57zFDu1PPPc5FiuNAgjEhvW+8noLpKmytqcsWmFvt8Ozn3oPbj09t4niSYExA4dwEZh68v3GXIwebliT3c+b2XTylrQtjZEfbUt6PBpRCv9+6b7rrFv5JMCai1+WAuS7WelD9GnQegx4rTlrFeN7Of3P6jh3EoupIpzseAq283SilZHbngJKZCH1SH+11SFSM/YJRoD8YkjN/ASUthl/xiFtwu3zhBJoEoyQkBE82coLPL214x3FHthDG0Qd3RdBIMPxySWGoqjEDCZ3Ce1giGGRXyi+FHjuIUIMtu1dBJsHwa+h8xShq6FyGCCwJhn9OwesxDAlGkEkw/FLST3sykmD45bhOwXMWch4j0CQYvumxpzF0gW0iUCQYfhnmvQA4Q1fvuRocJBgBJ8HwSX8s8jxg6CTf0EYD0FpOZASYBKMU8m56VAvhaq2vi5jj3l+c8GR0bQmpG7O3g/qWvi7883LXIvyRYAhRgOxKCVGABEOIAiQYQhQgwSgR1WKvUS12XbnrEKUhnW+fVIt9B3DJiE1aN1vyhRNw8gH6d8lR/1aqxS68/JgIDAnG8SHva8DJByhEARIMIQqQYAhRgARDiAIkGEIUIMEQogAJhhAFSDB8UC32OeWuQRwfEgx//lPuAsTxIcHwp6LcBYjjQ4JxPGgNau1Z5S5DFE9m0SuRxmQfr7//NupTA2yeMQfgQdTao++2A71u2fRXJ6ZKglECDck+/nzzjZy3fxcAE8ybsxS1druE48Qn12P4oFq8uf6V66INg0t3PsZvfvwFGgcHJn6gXiczPp/gpI9RAtrw3sbbl6ziA89/5bEfoNZKME5wEowS+/eSU491F41eJ830CU6CUWLLOg+xp7p+orvMma5aRPGkj+HD4T7G0Zr6ezj0qbdo4B7gKcCpwGZpKYJDjkodB61VtaDXjWyNN5WtGFEU2ZXyR1qAJykJhj+fLncB4viQYPjziXIXII4PCYYPutmSxWGepCQYQhQgwRCiAAmGEAVIMIQoQIIhRAESDCEKkGAcH3JGPOAkGP4VOpfxwLRXIUpKguGTbrZMoA+vldBAs262zi1vVcIvGXYuRAHSYghRgARDiAIkGEIUIMEQogC5tLUE1s3+3lO1Hb7NNi0VcmzCTt89V3S966Jy1yWKJy1GCTjE/t3VWKP2z5vBoVkNDEbqLvxrw9dfUO66RPGkxfDp5w3fze5bvpTB6gQA/UBfdSUrt+X/gHzxBJZ8cD7lKyqtwarRqwFkK6IcamwoU0WiFCQYPvVVJUCNnXEzFwmXoRpRKhIMn+p62lDuUcOltKY6OSjz0waYBMOnmvQgVd0D3mIxQ0zbYU5XWxmrEn5J59uneCqPa4aob+8jPpghVRGlu7GKtgbpYwSZBMOndKSCxtZuEoMZAGq7k1T1DpKriJS5MuGHBMOnZCgyHIrDKgfS5J0yFSRKQoLh02AiSuUg9NYlyMQjhLN5qruSOAWOVIngkGD4NCfZyvY5pzBYHQcgVRllsDJK/YGOMlcm/JBg+LS1aSHpqtiobflIiMHqqjJVJEpBDtf6lItGVKETfErmQwg0ubR1BNVih4GV5J2lJHNrybvLjDCrXIcorqsImZBxlDIVOmICqHMPHlRX72jHGvk+ak0ok+G8jXfa5+Ye2xFGfw697ofl+a1EMSQYQ1SLfTnwUwaytaQdcIfeFwOoi4E11LhqDY4GU6E0XLblACvauljQ04/puoSyNlXdAzhRg/bZdcw71MnLtv4MQCtYil63qzy/oZgK6WMAqsWOAj8h79SSGxEK71Ywj9pVGgpJTTpL1NEQiTB3ZzvRrA0o8mGFkzdY0rqfTYtWsOnAWZyRfEgBdyCLUwaC9DE8q4A6cg4cvd5k2Bg9SHDEzYbjMi+dYdHuNipSNqYDpqOJpF0M2yWatAE4UDPv8ENmHc9fQpSOBMOzC0gTMse2DkcHZcTNlbaD0poZ7X1j7hLKa3TIBKBxoP3wTQOlLFocPxIMQDdbvcD7CZsuIWPUHz+26/2MlHdAa9oromjtoo2xR6UsZbNj7lzmHWrj7L77Dzc0Lz5ev4MoLel8j6Ba7AXAs0jlriRtX6K0rjIVhm0YEBlqTVK29/9hS6FdXn//Fs7c28miXUdO6GmgItJPPJvS5yTv0SHcjQqehl7XX7ZfTkyJBMOn76/4qes6MZWJxqkcyICGfEhzevcu2u0m4wUDr5A3OIBkV8qnpv42Hc3niacHcSwHJ+RQlUljhnJIKIJLDtf61GnMhEqL6p4sLhB2HLobqojvzJe7NOGDBMMnV7sk43HyMYvOeC3V2QEi2SyHok2sKndxomgSDJ+MUIRsKEqWKJbtMGjGGYzH0U3y1gaZ9DF8cl2n4HBBU65UCjT5WvNpRqqXXE8lhu0ST2XJRkOk4hHm9sn1GEEmwfDJVjC7tYus6c0jFcna1PUM0Fkl12MEmexK+RRyIWuERm2zDYtYKlumikQpSDB8GozEC85EaBTueoiAkGD4FM2lMfOjx1IpV5O1Ci3mKoJCguFTMmLoyoEcoayDcjVW3qWyP0dMp8pdmvBBOt8+OU0VYd2bdipHDChPVZpksnyzfFUJv2QQYQn8duY3t1W45jKVs3DDNil09pqOt0bLXZcongRDiAKkjyFEARIMIQqQYAhRgARDiAIkGCVw6cu2fvY5ax9NP+Vl275f7lpEachRKZ+uvmqju6uhVrXFYzSksyzp6OKPfzhT1gAIOGkxfHjWVRu/es/cJvVYooI0sLUixh3zZ3PF8x7cWe7ahD9y5tuHqmz+HRENl6YzKKXQWrPXslCGWlTu2oQ/Egwf+uMRltn28OhapRQLbJueRLzMlQm/ZFfKh7hWY4ecK0WV9NsCT4LhQ0Umo46OgAYaemWK2qCTYPhwsKGG7rA1fEWSBpKWSVtlRTnLEiUgwfBhSVcPTi7N1Xffz9X3PsDZW7fzSE2MFZ3d5S5N+CSdbx+a2jr56a23E8/baBQKmNveyWM1deUuTfgkLYYPNemsiucdNAbecpSK5z2+k/uXz5cTfAEnwSjSl5f9cqULuIzOgEIxe6Cf1W/bLa1xgEkwiuT2962/bdlCCjUNYVtDekDOfgeYBKNIB+fOid+2ajG3L5k/arujFHctncPmxhlNZSpNlIA090WqzaZVLmTxicufwtvufJDV+1vZ2VDHw0vmc/7+Q7zj9t+EMi07nBC5O0zUs9C/lXUBAkRG1xbpv87/i16Z6uWFjz6GoTW3Ll2Ka2jWbruXGH0oNKDRHJnc2TtupTSoavSv5SzgCUxajCKoj6Vqftbeyox0kmtf+lIea2xg/mCSD935V+L0jrwnCgM4PPmaxhtDovuQ3dgTmgSjCEsPtP5oQV8PV7zm1fTEYxAL0WHU07Wp0NtZqHuuFeoldehfyZnAE5QEowgZy0yunz2PnlgMwkOruVqKHY1T6m/LAhonMGnOi7C/qfFNXeGhoeWGFwqU4rsXPIPNM+eOuKem8N+/4aJ/1TcNpYoiSTCKoG+Ip+Z393D6oXZvT2lo6HlfrIJz3vMZXvryd5IyQ2icoS44QwMNDcB1QckshSc4CUaRDiQS+kc/+yNzukd/8eesEL9afSEbqhfoNOQ0nKn0OqX0OoX+jUKvM9G/kkO3JzgJRpG219bqWM7h6k1bC97+weddk4vrdRFDr3tkmksTJSDBKFL1QHe/q8C2wgVvv3fJqkenuSRRQhKMIi3O6dVuyGFP0ywsZ/QiMav2tqI/ETunTKWJEpBgFOm/H7tmz/9btRJtWZy7r4cZAxkqM3kWdQ3y6n9vLHd5wic5j+FDlxXWytWqKmtz1kGvE660Zl+iSsbZBJy0GD6EbcVpu/eP2nbKEwdQyPp7QScthg8DFXGW729lXlsXXdUJ6vqTVCXT3DNvVrlLEz5Ji+FDTTrN785cgZm3WXqgnVg6y+/OXEaVkyt3acInaTF8aI9HdW88qj777AtpSKboiUeJ5h1q8na5SxM+STB86KqMc7C6EoC2qgQAOcsibUlDHHTyCfpgjTMXSDwvu1JBJ8HwwXT0K+cNJEdtq8rmqOkauLdMJYkSkUtbfXrzZfc4ndUJ44maamYPJFne2q6/ePvT5Asn4OQD9Ok7t15oasxzzt17cHNTb/J5EoonB2kxhChAvt2EKECCIUQBEgwhCpBgCFGABMOnv1d97uYB823aUa/Vjnqt7g6901Wfy8syAAEnR6V86g69U9fYo2fbPBip03MzX5IvnQCTD8+HP89s+XWlnSJHlCQ1pKlAA425fmkxAk4GEfoQ6ep/9iDVpKke3pYhjan6iJSxLuGfBMOH3nBjJJ0e3TjkiNEXjlBTnpJEiciulA/V6bxbaNJm+bYJPgmGDxX0ZU3y9Mai/PTc1fz03NX0xsJUuAM860Xr31fu+kTx5MvNh55Qwmiv6uHpb3sLnQnvQqXPP+dS/vrdFmK55OeAL5a3QlEsCYYPX7/86Yn6SGo4FAAdiUq+8pRnsai9W45MBZjsShVJfXzAfbRhhrGrbuaY23bVzWTlvt1lqEqUigSjSNFkTtWjuWTXrjG3Xbx7F6f29E5/UaJkJBhFWPyybe1EQmSUyY76Rk49dADLtgnZNq9Zfy+vfOA+suHxrggXQSB9jCKka6oaQhq2LpvBVuXtSlUNpvn317/Iiu5OHDPPPbOXlLlK4Ye0GEXImibJ6ujwSkoA/RUxfr7kaWxnETEnxweueaVCra1ErX0t6iXSegSMtBjHoK4bWIXmxTXJ9Mcakynjpl/fzqtXzlNfecnFY+57x6nzuXTzE6xIa9724G1Kw9CgKfsHqLWH76ZHLm58+D9N+Bl63auOXdC1lwDnAvehf3H3+Pd7ycXAecB69K/unMSvKkaQFmMC6rqBN6LZXJXNfaIOzO/+362qLpVTVjiMmTuy6OTs3n5e+NCjJKtDvPgjL+XLZz2PpX0dBRcyBpQx9GMe+TGAV6LWTrxgpbr2q8AdeOdH7kJd+4XC93vJF4E7h+53B+olX57yL3+Sk2Hn41DXDYSAQ7i6fm46x8vv3MwVD20HFJ95wQXcvXQ2+eoIV21+nJvW/QXL9d7H/7vgLL7y9Is4eOObi33p89Hr7h9b0LWLgR2MHoPiAovQv9h75H4vWQDsYvSXngaWoH8lx5AnSVqM8VUD9QAhrVl5sJPDf5P7KmKEHM3svd18+g9/Hw4FwKvvfYhZ/QOFnm+yxu6jeRYxdmCWASwscL+jP1c1tF1MkgRjHPrGyk7gAbQmZRr8/KLTONwreMr2/cQdh4SdoyI/dgHWi7f5+mK+eZzt9wJH72p1A+uP2nbf0PaReoe2i0mSYEzsFZjGhj6l2Tyvga1NdYDmKXvaSIYtBhMJOiriox7gAr3hKH2R2KRf5Mg64HwPva6/8J1+MQhcAxxO3U7gGvQv0qPv96s08CK83SmG/v9F6F8NTrogIX2MyVDXDdTj6obFbV3fvv7/bu387Ksuu+aJhTPUij0dvGDbVl70yCZmDA6SCoX41kUXsqTnIINmH80P3Xr4KUa+yVuBXA50rxnaiTL3z7AzDwI/Rq879lJM6loDqAO60L8Y/8PzDhHXe/f7lXzIUyTBKELtOztcK2yqV961mRdvX88HrriKqOPQlqjktNZDfOf//S/Pu+aN7h2/PM8sd62iOHIeowhau9qylZrb1c/cnj7++NOvc9/s5VRnU5zTsRUDmxndHc6xn0mcqKSPUYSUYazNRC02zptBhgRh1+E5++/hwo6NWOToo4GGXFre2wCTD68Iua81/qG/Osxfzl3GIGH6aKSDhXQxh3YWkbKiJFxZuTXIZFeqSG7OTvfXRGNz1V4yuoIsIUJAnD6U4/BEVY2MjwowCUaR9Cer4uvVJ1MVZGNVjD5i6mpFd3WNHNUIMNmV8iGNmXcLvIUOJm/9z21dZShJlIgEw4dB4qqDxjHbu6nhaQcH5pahJFEiEgwf0rF4OA8cYiY5QmQJs5e5VDDATP3psWNFRGBIMHyoTvc7i9mBi8sWlrOLBdTSQdSU0RdBJ51vH2YwqLJmhMXOThazc3h7t6qloox1Cf+kxfAhRdXrtBPG4cjIj6wKk7fDZaxKlIKMlfKpQ71Hx8lj4I0AyRGhD23P118Nlbk04YO0GD5t5gyjg6iTxSSNSRKnU0IRfNJiCFGAtBhCFCDBEKIACYYQBUgwhChATvD59JnL7jE2VlfmWysrjNpsTtdkcs/90e9O/3u56xL+SIvh0z/mNDnhkGWcNphmhu2ovTWVf3vT1Rsry12X8EdaDB+uuvKh22clKthdXUXWNLFcl0UDSQ7FIr2ATIQQYNJi+JCpiF+0tzJB2jRJhU2ypsn2qkqwQnL1XsBJi+FD1grp9kSUJ2YksFyXvDKY3ZuiJpMtd2nCJwmGD46y1UDc4rUPbGP2QJpk2OIfS2eTVzIRQtDJrpQPrqH0VVv2MHvAu+Y7kbO56rG9GIbsSQWdtBg+zOtJqqacPWqbAcztT5WnIFEy0mL44BoGthrbOsgUhMEnwfAhHbZorxx9rV4yHCYTkVHnQSfB8KHSdsmaFv2hEG3xCBlDkbYslh7qVPdb35D3NsCkj+FDTrtkLMVvLlhBOhwikrd5waNPYGbCxJycjXzxBJZ8cD44IcvY3VDF1Y8f4BUP72Zl5wC/O30xFhoTLYemAkxajCK96YoHauMVMfXUPR0YjoPpujSkstiGojsawjYNHlYfGVitPyPjpgJIglEsTVdt1qY2OUgilUYBedPk7IjFNXffRWtlhAW9scRB9V4XMvZs2t4J/AS9To7lBoBc812Ejzztjhs6EvGPzcx5wRgpHbK4+vYHiakeanVmeHuIfqK0YcHjwKnodfLGn8Ckj1EE5Tgfqx9MkUilx9wWy+WZzX5qRoQCIE8Vg9QBnAK8f1oKFUWTYBQh7LqYyuC+xjqUq4mkskRSWXA16XAIrcYuyA0QYXg629dNY7miCBKMIuQsiwfra7m3rpbKjj6qepJU9SSpbe9lX10trdW1jF6oFcDFIHf4H49Ob8ViqiQYRUgZRn53Is4Ltj1BxD4yAMRyXJrauolnsxi4OMPthkucdrJeWDTwrjKULaZAglGEL952SbjC1TQNjD3ApPI2j89oQgMHzCps+qlgLzaDupLcJqAave7QtBctpkQO1xbp9AOtPdsaa2qX9vSP2r5hwUxWdO5jG/NpdPboRv0tA0CmeQ4WaTGK9INbL6zrr63mwbkzcAFHKe5aMoeN82ayq2o2KTPKIv0FeX8DSloMX/LuLWeuMP65ehl9kTAJ20G7mj+vXsqVmx4ud3HCBwmGDxnD0Od09zJ/MI2rFIbWpCyTB6ri5HVejjwFmATDhwptMDuTpScewzUMlNbEcznm5WzO0h85rdz1ieLJPrAP8XyegWgE1/DeRq0Ug+EwdemcDPcIOAmGD8mQNRyKYUqRlckQAk+C4UPUcSk0CDOctwvcWwSJBMOHpGXqgXB41OCPjGnSlZA1W4NOOt8+OKahsqZJ3jAIuS6OYWAbBuRzx36wOKFJMHwwQGutcQ2D7FBfwwUiKbkWKehkV8oH19bu8r37cbSLrSAPzD/URuFB5yJIpMXw4dZzl0br77HdCx/dhmNZhPM2W2fW0x4K/a3ctQl/5NJWn57x8m2PzHT16bXpDKmQRXsknPvLT5dFyl2X8EeCIUQB0scQogAJhhAFSDCEKECCIUQBcri2RF68duNjpmLB/cvn1ey6sS5/7EeIE5kclfLpipc8OmNZZ09bdd4lEw6BY7OlNpH+/e/Oipe7NlE8aTF8mt0/2NrR2MDeWBTL9a7gm9nZFSt3XcIf6WP45ITDKqE1i/oGmDeQZElvPz3V1cz97876ctcmiifB8CmTiJMYcf2FpTUzMlme/dju2jKWJXySYPgUcVyU7aBsF+1qcFwijk1lKntOuWsTxZNg+FTdnyQXDpGLhDBtF9N2qUhleO7t93+w3LWJ4knn26fqVJa+aJTVG3Yye38PCuhsSLCQ3Fnlrk0UT4Lhk4Fm0Y425uzvGd7W0JkkhRytDTIJhk/xdIaGjoEx21NEy1CNKBXpY/hlKGxTs2VpExtPnUdfpRcIC5d71JdlupCAkmD41F1Vwd3nLuPQvGryFYrHTpnFYCxEijCaiHmH+rpz7GcRJxrZlfIpHQqzas8BzBFDa3IxE502SBOjin758gkg+dAKWPye1vcsfG+HM++/DiWPdd9EKj0qFAC9jXGa1D5iJMkgo0OCSFqMo8z67057IBY3B0MWputWzGnu1is7ex42FOofP1iy+vD9Xv2qXWpbpTX3eaOWGrO5+OBGFvfuR2kXiy1s4IJy/BrCJxlde5TaD/Tq3ujo9Y8MQ+OaBk2DWVbt3Mez93dgOS7dNZWAYnZbJyjFU/c9wCndu0c8UtNBJTtYlo9gPONc/fY7j369O9V36kzcOzRqqUJ3AS+9UL/1juP7W4pjkV2pERJvemLz0aEgbuEmIhAL0VpbQbaygng2R1djHfl4jHw8SseMOlwUi3v3H/WMiloGSDAQstF3bFDfSBz9mgZ6n421ysEM21izXIzb71Pfqj6Ov6aYBAnGCFnLmF+bzh7ZEDbAGvEWmYpH5jWSjkWxQ0f2QlMVcfYtmEXWCBV4VgONSZ4IwHdG3nKX+t+nOxijzgS6GGjU/5bi9xHFk2CM4IYsY3VXN5brehvMsW+Pa5noQhMNKsXDdctHTfDsoshQiTO0NKVi9Fk/BRXjzFooPfYyk2CMEM5l2+9bMpsXPLGPF23Zzar27jH3md8zQCydxXBGn55I9CfZG5/F35ou4LGqReyPNjFILXkihBgk5K3y/Y6Rj7lIv+XPJs6oy2AVLgo96n5i+slRqRHiTTOWdIct93erlxW83XIcdF+KtvoaEv2D9CZi9MdjzG/tYvbBDgA6o7V0Rms5o+sJqjIdpIgyQKUbhdet0e8osL63PsvCvs1FNRjolIJ3nK/fdnRnRUwzOSp1lMrr+91kZUyhvF0cZTvEewZs07Rosu3Y1s832ACvvWbTz/aZatuZ3QMfPxiJcvljO4efI2ZnufTAo6RUlLQb5lT9IZnlOWAkGAU0vL99jx2LzQvn89rCSBy8sSY93n0/9LwNOtbaw2A8xOLuPmb29hLKO0Rcmxm9nWhXsVpfJ8EIGNmVKqDzphkLJnvfWDKNW1NB78wGHpw3m7r+fq69/U5w8riuTSXu8SxVHCfS+fZpwApzaGbD8L+7q6r491krqE8PUk+WJHFpkgNIguHTYMXY6y721jdRSQcpYnq1/oC8xwEkH5pPiVx2zLZ5vYeIqCQr9Ufl/Q0o+eB8mtnTx5zu1uF/V6f7eclDfyFpyqWtQSadb5+SsQgzu/qp70uSDkc4a/+j1A/0siu0lMZyFyeKJsHwKRWySIdioBSmDY80ncajjctZtP6xjlXlLk4UTXalfFLA4ZOBh2WtCPdcvOaVZSlIlIQEw6dwfux8B1nToLY/Nb8M5YgSkWD4lMjb2Ee1GBWZLGHXfaI8FYlSkGD41F0R01Yujzn0Y2WyJCvivGfT824td22ieBIMn9JO/s3ttdX0V1aQrIjS2lBLn3ZkPqmAk0GEJXDWG/YYz969N+sahjEAr//2rRf/qNw1CX8kGEIUILtSQhQgwRCiAAmGEAVIMIQoQMZK+bRFfVFF6XVcwspFYZHVWWKRFfq6/LEfLU5UEgyf4nQ7vTSqI/NDVaoaOrJIaxxo8uH5lKVCgcLExsJrJPqpk8kPAk5aDJ9sTOroJUIOgDwWvVSWuSrhlwTDpwRpIhzpToSwqeSYy2qIE5zsSvmkC8w9q5DRBEEnwfCp0OqsGW9mcxFgEgyf+lWUDEfW1Mhh0WHI8hZBJ8HwaYHeSwqXKB3EaCdPjjrdUe6yhE/S+faphh5msJcMlbhYzGcXOS3LWwSdtBg+aWwOsYI2ltHBIvZzGhrFoHqbzJ4TYNJiTNGAek9FhvxAgn7SxDXUkqNi+HYXi15mM0j6nqWwtIylCh+kxZiCXvW+qjBtyTB5laRJQcjIDoXCBeyhtzNHDEW2toylCp8kGFOQI9nTzxwsYsTJE8EihEsnCfbSyH4a2EsDDpp3v+QNNXPedfCZIx9/yn/tf8kp79r3wnLVLyZPLm2dgm71ejdEpcoSx8FC4TCIyR4Wjbqfgc1Tb3o9leks19y/hQNVFXpvVaWakc4xO5WBkMGsx3feekoy+yMMlQV2vnnL1Q9OtZ4vXPCvNyv0lWi++777nvmHUv2eQoIxJYfUf7sGMQWKASropYoUYdwCDe+/1szna1ecj51zMQbzPOtQB2f09gNgK8Wu6goufWibd2dDAfz4zVuufvVka/nChf/an4+G5wCgNeF07oH33ffMNb5/SQHIrtQUmcrGIkuYHmrQGFBg+IcGTjnQyQd/dxfJvGZOOjscCgBLa+b3p9g5o8YbUOJqgFd9Z+XvnzGZKlou/Ndrh0MBoBT5aOicL5/7j1N9/XpimARjCky85ezTI4aBhHFGRUMDqagFhsGCzn7m9yWpyY29ZinqurTVjRmFe/pk6lBaX3n0Nm0YaMXayTxeHJsEYwpMUjgYmIycT00BGhfvyJQG0okQHP639mZEP1rKNFh8sOvo9uaOydShlfomR+0CK8dFufo7k/5lxIQkGFPQR5Wu5BBRsoSGhprnMVEoDBj+qUh612YkK+PUWdAajbC+oZb80By3KdNkXzxMU3/Ke2KvA/6RyXbAm+95xq2hTO5O5XoLXyrHJZTJ/fQ9G57dVsrf92Qmne8p2KOuc1PUqUb2U0knncxjLwvIHjWa1rU09569iDtXzueUHft1W1Xc3TijwZyTzzNvME1r1OLix594S10m/1cMVQ0cePOWq7unWs8XLvjnYqX11UrrH7/3/mfJAK0SkmBMwRPqvV2ahroUcSxyzOVx9nEKrUetnTSbNv44pzbbvP+tY8eki0CQYEzRQfVBrbDIE8UkTx6LHGE6qQGgnl4qyNBPtn+VvlHGnweU9DGmaLb+nLJJdoXo1zBg5wgRJ0W10U/CSBIlwwAxwuSmvGskThzSYvi0U31a77Zmk1fekSilXeY57SzVW0MR/T1ZDiCgpMXwqdeoHA4FgFYGnUY1Eopgk2HnPmWHQlHl9mPi0KuqRwVFBJMEw6eQY3OOu5Fa7Q35SBNhi1pS5qqEX7Ir5dNs2oZDARAjy1K9t4wViVKQYPgUJz1mW5RMGSoRpSTB8ClTYF6pnMwrFXgSDJ/SxBkYMVdthghJEmWsSJSCdL59StDLIebSjo2Bi41JE/vLXZbwSYLhU5qIO4s9Rp4oCpcIGXqp0HXlLkz4Ime+S2C7+rhtggkaB8tdpq83y12T8EeCIUQB0vkWogAJhhAFSDCEKECCIUQBcri2BFSLfQPw0aF/DupmS1anDDg5KuWTarEPArOO2qx1syWtcYDJh+eDarFjjA0FgFIt9oumux5ROhIMf94+wW0/n7YqRMlJMPxZNcFt8t4GmHx4/syf4DbpvAWYBMOfiS7VU9NWhSg5OVxbJNVip4CJlmd1p6sWUXpyuLYIqsV+EzDhzOKVHV1uOl5puIZiQXcv7VUVX0t+surd01Si8EmCUQTVYjscazc054A6sjdV3T9IX33l93Sz9abjXJ4oAeljFGfi/oPW3ipJWdv7cVySsSi13X1vnKb6hE/SYhRBtdguE4XD1TCYh7DphSTngql43iObqCbGtvpasgpm9HfpFW1PrPrmv67aMn3Vi8l4Une+VYut8Bah36WbLadEz/k2jtViOC5ETEzXJZHL0BeNs7C9l1BVE2mlmGtrdiWi3L1oseqJVzx+zOcT0+5JuSulWmxbtdga78jQNsBWLbarWuxwCZ7+G8e6g6k1r3nwPxz43Nvp/dQbuPfbHyNupnEM7+1WwJJkhpjjsqeulo9f8NOPTvyMYro9qXalVIs9F3gCbx3JQnwN7hsaG5U61v3O2vME67/xQcwR7+36uUt41cs/zMqO5HDzMKdjO/+7+jzO3LMzFYrEotFkf/rfK1Zdpj8Rv7fQ817+pj3WfmWYz9i9/8xts+s33fKj5WNnexMlEfhgqBb7N8A1U3jIP4A3Af8EluC1KmroZwtwqW62xizbNbRbNqlzE+/5z5/50p//b8z2ho9/j8FwBas6Bpjfl6axbyffX3MBjmlyamsn1254nJmDrdy+YCY/ueDS4XPnSmtObe2mIx6lvTqOBuJ5h5xpYJsGaJg5mPlh6w2Vrzuq5muBTwGzgd8D79TNVs/k3qbC1E35q4CbgXogg+ZrKLUsnrevVK5rLOoYJJJzXAXrtVJvfaCh8iCGuhfHXRQKKfIRC8PRWFmbXMQiot1s1rKu0x8MfclPXaUW6F0p1WI/i6mFAuBZwCN4oQDvPTj8Jb4SuG2cx016IZgdjU1jtnXGK+mLxsmETB6cVc3BRITvnXcRjmWBUjw6q5GfnXsqA5Em3vTA+lEDSrRSbJ5VT1tNBVopUIpU2PJCoRQYirbK2GvrbhgcPhSsWuzVwE/x+lhx4OXAtyf7OxSiWuxFwG9Rqh6vjiiK9xvavToVMo3FbYNE8i4oZWilzgf+otC34upFlqXIR0OgFK5lkKsIE07nyZpmRGn9RfW5/PP91FZqgQ4G8OEiH1c1wW3jLSJfM9kn/8vKs/nPgiPjC12l+PBzX4ZtWtQP9jOnr5ueqIM2Rr/9W2bW4SjFwcq5VGaOucfmGdHiK83IcyQvZOzn+8Khlq9YV6LU6N1UpXANg0TGJuyMaVBnVeSd0wGcSIG9W9MAV2N43wJT/YI7roJ+VGob8LQSP2d2nO2aSR49clE853Uf5tk7N7K0q5W/rziDx2fM5bu//javfeDfWK7LVy5+Lu+56vWjHpfI5jC0Jp5P4aqp//0aWh8a8c9CSxu362bLz77z2OccCmbeLPwdayuVB0KGo3GOuosaeqzy3tkTainmoLcYH6e4Uax/m+C2b46z/bOTfnYN2XCIP52yhi9dcgVPVDfypvv/yRvX/wtraG3uN93/L5Z0Hhr1sOc+toewk+O0ts0MRo8ahqX18B/SKEMBMlxXD0RCzSNu+Qmw/ah7f3LSv0Nhv0PrHaPrIqc0ZMMmXYkxB/1+mQmZXwBQWWdU62bYLnkUGGAr1cv473tZPBk63414LUfNJB9ygW627lMt9hvxOuHJoe1x4Bu62frJBK/1GHDKsV7AyjrYI3aTIvkc+z73dhpTA6Put7N2Bivf+yWqsxle9eAGzjq4n1i2m7dc9cqBnrq6BBpluS6JbF6ft7ddpQx4dE4jCqgdzHAwESMdCbk12fzGCsd98f5PVe08qt4a4DV4ne8/6mbrrsm8QRNRLXYCrT8AXIbmceDjKDU37DgfrktlVs7pzWRiGXtr2rJ+C/x6wzdnOuoT6Rej+JKJbjLAVK7WrkJHlU6mwtbPXMP8pP5gqNVvbaUU+GAcTbXYy4Gt49yc1s1W3OfzT3zWG1jcMcCumtEznt/3zY9y3v7RX+B/Wn4WV77qAyzv7P7n1i/PusxPXaK0gr4rNYZutrbhHUo8uid4v99QDDnmIdsz23qY3z84/O9ZyTTfufAVtFccWfa7M5bgI896KYbWVDhaTvCdYJ50LcbxplrsKBRYRmmENfu7OP9gN73hEK6Cumyex+uraDVgdfsOXOXw/5afxUA0zor2XnfLV2fKJNAnmCddi3G86WYrwzFajQdn1bKnKk5VLk9NNs++qjh3zaujJx7BtSvZ1LiMWf1p/dTtT/xTQnFikhajCMe8HkNr5nUlaa+OYZsGIdslPpilOxHJ6uuiY9cmEyccaTGKs37CW5WiLWKRz2lU2sbM2AxEQxCyVkxTfcInCUZxnnWsO+RiIfcZj21yZvf0uJadvy0fC4d0s7VnOooT/smuVJFUi70K2Mz4h25lms4Akw+uSLrZegx46QR3KcmFUaI8JBj+XDLBbXJVXoBJMPyZaF4pCUaASTD8uW+C28Zc7CSCQ4Lhz/cnuG3BtFUhSk6C4cPQtQ1vK3DTQd1sjXddhwgAOVxbIqrFXgo06WbrznLXIvyTYAhRgOxKCVGABEOIAiQYQhQgwSgR1WK/TrXYfy93HaI0pPNdAkPz5I6U0s1WRVmKESUR9HmlfFMtdhXQy5EhHA6wQDdbByb5+EKDBUtxbbkoI9mVgj5Gj2sygf1DU89MSLXYixnnPfQ5458os5M6GKrF7prg5h0T3HbYU0tVizixnNTBYOJJ2uom8fjHJrhNWowAO9mD4dcDE9wmRzUCTIIxvmP+Yetmy56OQsT0O2mPSg11jif6YjhxdoXU2iiQALrQ66QlmgYnc4vx/47z8/sPllr7RtRajTfzYQfgotbKteTT4GQOxnP9PoFqsasnuLkU3+zfLbDNQK19tATPLSZw0u5Klcjrjn2XIqm1109w6yrU2kqghyNfbp9Ar7vhuNVzkjmZW4xS9CEWleA5xnOsSd368U5GHl5Y85OotXLVYImczMEohT8ex+cuZlesFOuYCyQYvuhm65/H8emfKOpRam1tacs4OZ20o2sLjIidSKGFKbvxBguON3u5UfRCkGqtgf+ZDG2O7Gox9HwTLTmQAr4FVOKtd/499LqBCe7/5KDWxvDWV7wMeBx4F3rdoZMyGKrFLsUf3rH4CcbEywxMj4eA89DrntwnMdXabcCyEVtSwPxyv/nl8o1peI2ZRT1Kra2g/KEAOAu4vNxFHFdq7cWMDgV4ewHvPhE+gHK4eBpeo9gLlRLHvsu0mcxAyiAb7/drOlmD8aFpeI1dRT1Kr2vjxBiAOAD8udxFHGf/BDIFtn/zpAyGbrb+Uu4ajuH5ZXrdg0AncCfwXPS6ia5XCT69LgU8HTiAt65iH/AW9LqNJ2XnGyZ1VGrMwi9DK7Y+E/jL4Y71BM8T8jX6Vq29dei1pmoZet1kLrISE5AhIVMwtGLrZAcf+j3qVVxrLqEoiZNyVyogJlxLfBwnZ/N/HJzMwTjR/4gmWmIAYPCof+fR607mz7OkZFfKB9Vi1x/Hp183wW0uet2JdFj3Sedk/oYpdJhuqmaX4DkK867U6xznVhkPdZydzMEoxR/XRLOETDQuaXL0ukbgHLyOvAb2o9cp9Lp+388tJnTSHq6F4VkEx/tycHWzdcw/7gkO1xY/VkqU3cncYggxrpM9GBN9o/u9wu/EmWVETNnJHgx3gttyPp9bdqMC7GQPRmSC24oZjiGeJE7qYAx1jgsdWfqxbrbumuzTTPDcIqBO+hN8utk61edT3Ah8+KhtE+2iiQA4qQ/XlopqsZcA2/A63B262Sru6j1xwpBgCFHASd3HEGI8EgwhCpBgCFGABEOIAsp+uFa12M8HHN1s/bXctQTNM99yUD1eEUodmlntzYboamKZ3PrUJyvPK3NpgVe2o1Kqxe4AGo7a/DHdbH26HPUEUeXHBtxkZXT0mCytCadyW7OfTKwsU1lPCmXZlVIt9rcZGwqAT013LUH1zLccDCcrIiqRGTvzv2uoFWUo6UmlXH2MN493g2qxH5zOQoJqW00sfuq+dgbDR838rxROyP81Uie7cgVjoiHZZ6kW+4JpqySg9kcjiads21/wnYzkZUSKXyfqUanJDuA7aZkmK6K5PFqNTUY09+SeoHw6lP2o1DhO1MCeMAxDPbJpUa3XYhx1/KRxMAVUDf/7ZS+667I1+7f8ra2mSv3ijItpHLQ/8cA35st6fROQP8CAstAL7lu8wNuTMtTwSnyWdrls577h+330qb/d0Zjv+sd1V73CuOmZL1Tzkt3q1Pbtn7zkTY9uKVftQSDBCKimgUwoGYuyuK0Hsg7kXFTe5QWP7KIid2R20AXptiVfu+R55C1v5+DuRSvpqKriQG29HLmagAQjoHYnInfganZWV3m7Uhq0CwOhEI/PPLLsw3+WnDLmsRvmLsU1QN1ky+c/jhO1jyEmMOP9HV+jocbE0aDU0K6U1wm/bclclOF1Oj5x6S/Pra+pHPP4xe29XPTEZla07ck3/32W/aszLr1o7xcaH5jWX+IEN+3fGKrFLnqZL9ViX6Fa7H7VYu9RLfbxXGP7hJY3zXcMr+49IhQAjmVimxbXvPzeZefu7r6/ftDgtP2tw7cn0lk++JfbWdrawztf/Ebjm099TvjMzp0blvzXE8d7TcJAmdYhIarFrsVb7XQy7tPN1gWqxTbxZv4OFbjPoG62Tog5XFWLPRt4GvAL3Wwd1xMJDR/u1V21CdC64GIDC3uTrGzt5Cu/+hlOZT+RzjpuW7GM/liEp23ZTdYyeeoH30jOOrLDcMqhvYSz+m8Pf2vpc49n7UEx3S3G+6Zw3/OH/tjyFA4FQIVqsa/1X1bxVIt97tBshAeAnwKOarGPazBsY6iFUKrgCb65A2liRojeaoemgTYeXF7Jws4ern7ocWrSGX67ZuWoUAA83jSPfbU1zz6edQfJtLQYqsW2gOcAv8JbFXOy/gS8YBL3e71utn5wjBoUcDbecrUJoF83W1uHZiy/EK9V2gosBB7RzVb/iMfOAhbjXdf9fbzlqVJ4f5aFxnzBJKf4nKrZ/925u7O2YmE+POIPe0SrUZPJ8bLH99ITjTKjZwvfO/8SUmFv8O0b776DL/3h5yy97jO0VY+eutd0HE7fs1c/vGTR7cAKDi+S6QUvi1IO3rqCj+FNZv0IsBM4De/L65d4X2Db8dY+X423dFfV0Pa/Dv33EmAj8FFgBvBV3Ww9fLgO1WIvxFsx9hCwXjdbjmqxm/A+I423x9ELhPEmnTCGatiim617p/p+jue4B0O12GfinckudhXTyWoHZutma8zOhWqx5wC3AKcfddMjwCrGHoQYBN6km62fqxb7k3izgBRzoKJRN1vjzVg+JS98/b74/TOrenpiVjgdPWp81IiP8KJ9HZzR1kdHRYzbFlbTHR+9pxlybc450M/DM6vJjBhT9d+3/4m3/+tuvr/muXzmqqd5u2kj/zQUo/oyEzj8x3q0w59LoS+LR4GnAJ8H3jhi+0HgN8A7x3nOoz0GrNHNVjGL7owyHUelvs3xDwV43z6fBd5f4LYbGRsKgDPGea4K4Duqxd4LXO+jpv8Dnufj8cN2V8f+92AsHCZ61EemNSP3p+6e30hrIkZF1h4TiqXdSVa39hF1XGaksjxeX8lg2OR5Wx7i87f8FKU1r73zAf52+jI2LJ6Ddwx4yqWO9wc8Uet5KnAz8MKjts8G3j2F114FXIe/zwyYnj7G+dPwGoeNt9ppMYMSE8BVPmqBwmEsSsYyLit4w9Hf4hp21VawaWYVFZkjS4DM6U9z4YEeYo6LAqpzNmtae7lkfw+zB10MrVFAmBSn7W8r/NzH15oSPU9JZpCcjmA8PA2vcdh4gw+LOUafwtsv9qPX5+OHxfPOPQVvKPSNrhQYBqt3HcJyvD2YhX2pMXczhnaj5/UeQgMuijwxNs0bmhZrei9ie6REz1OSAajTEYy3UZrVi44lDbxjnNs+TOEF6XdQ+E8rB7xbN1v/Ar40zn0m41tFPm6MBf2Z1yxOZlwjax/5gz38/447+o/Y1Zy9p50XPfQwL9x0PyjImYU/6opskhl9h/jWxa+mTS3i5kvW8MCiOWP7GP4NnZ8vaA/wBryDMyP1AD+ewmvsAUoyOHK6jkrFgFfhda6qp/DQg0xuOa8f6Gbr9ceowQIuAbJADO+o1AbVYs8FnjW0fSMwH3hAN1vtIx67BFiGdxTm98BywMbraMYY//qSki4eo26yVaI/1bmgp7dufsrhicoKHg+HwXbBUKiKEDoWBtflWTsO8ZKH/05HqIoPv/hFAIQdlxVdSc5s7QNg48wqDlZGuXLrIepSA6Sz7fzhzNMPHKibUYfX/1RDv5mNUi7QhvfH14D3pbJt6L3IAr8Y+v/H8T7j84EuvJWrTLw/+lq8I14PAl/E6xf+D/DvEeumn4bXEd8P3KqbrbRqsRfjHdU0gX14R7tMvKDF8HZZH9PN1h9L9l5P8wm+5wN/nsJDLsJrGifa2b1ZN1tvnOD240q12AZeSI6ucbVutjYej9e88PXb3N6aWrVTm+SPui1eGWZGOk8i08ezt2/i0aY5/O200ccYXvTwY6SileStEAt7BzE1vOjhP3PzKWv0z37/VBk/xfSPlfr3FO67Tzdb96gWO4y3a1MoHE45QwEwdJbbUC32M/COzV+hm62xO/QltL+qktaqOHbf2Ou9U4aiMZOmuqeXvy07lQONdWPu0x23+PFPvsH9S5fRH01w5oHHiGaT9EWf8s/jWXeQTPssIarF/iHwmmPdTzdbY4KgWuy/AJcBA8B1utn6TskLDIDQx5I6jqZ/MD92r70+zrKBFM/aeN9FtU7/3S1PfwHZo64LX9F2kA/9cx0L+3pY2NPO7QtO42urn6rX/+A0aS2GlGX6nAkWdBxWKBjCU/GxgR4nbNW4GZt8asTOVNSCujir2nt59KYG9Y2zf3LtzZes+flD85YMH3o1XJcbbvkFX3rK8zEy6Gg2r6v7B96z6QfLvlamX+eEJN8QAZSqiM5zbZsF2TyzIgqqo1AXh9oY8ZzN4ZFa73zZtb86pfPQcCiiuSwNyX4+evnLaEylOXjTB6y9X2kyJRRjSTACSH8olMyb1sU7GiroTURpcmwWZLKs7Bxgbt8g2+q8M976/Zb7nMceIZr3lhPMhCO0V1azpOMQpx5sxdI3y3Qi45ALlYLKMtpRBumwQTpcaJiIJ63Uxp//+Ktn3r14GT2ROPFsnu7YTGb2tN5bupPNTz7SYgSV1suM8fqHIza/5aF3r17U2f4BnY3o2n6DrfWL9IKe1qtabr3qwukpNJikxQgqpTIze/s4VFdzzLue2f7Fm86Em45sKdkQrietE7XF+Ga5CzjhGcY2nXXHHyslfDkRg5HVzdZ4Y57EYYqqgcPnJ0YGYZyr+sTUlCsYhQb0HfasaasiyEy1JRMOMXxsVo2YFEEWHPWtXMFYije+6Gjtutm6Y7qLCSLdbGkTN0fOgZwzNBpWg6upTGaS5a4v6Mq6nLFqsRN4V9dZwHt0szV28I+YkLoheQjDajo8lY6VtwfseKS6lKN6T0ayzrcQBZyInW8hyk6CIUQBEgwhCpBgCFGADAl5ElEt9iV4MwLWAeuBZ+pm6+irX8UklPyo1PWXb7gC+BjehfDvvuGWNTtK+gKiINVi9wNj5/yHy3Wz5XcaoJNOyYJx/eUbavCmOymk84Zb1jSW5IXEGEMTW/98vNvlasipK0kf4/rLN6xi4un9G66/fMN0zC11svrhuLfIeaqi+A7G9ZdveCHepLwTfytpHbn+8g0z/b6eKGi8ZRJEkUrRYvx2UvfyBrhdWYLXE1OkWuzV5a4haEoRjKnsv4436bLwZ/zPwPtCeki12CunrZonAV+Ha6+/fMN4i6aMp/3YdxFToVrsq9BaTeLipIfwprMc+dhKvDUl5g5tygFLdLO1v+SFBkzRLcb1l2+oBTqm+LAFxb6eGIfWv5/kFXvRAtv6ORIK8FYp2qda7JeXorQg89NiHCziMWM7iWrtixzUj4GIiVZ4kwafhl4n1xQcg2qxL5r0nbVGtdh34E0Ncojxl0gD+KlqsV+vm63Ca3KcBPz0MQp9Ax3L06+/fMPwqqBarf1GHuPXJjo6FAq016r0o9bKcJVj+9Skr+/27ncJ3ue2iMInA0d6pmqxU0Mz1Z90yvHHd8vh/+gM1749xOg5vxSgQblw/3QXFkBnHufnjwEp1WK/8zi/zgmnqGBcf/mGj/p50esv3zALIG+Ex9zmoNBAV6jmnN7Qa7b5eZ2TwHSdvfuaarHTqsWOTNPrld2Ug3H95RvOAj7l83XjD9V+7ArtHmkt8hi4gInGABrzvXSFa5Z1RV4/leWPTza90/haUaB7aPnnJ72CY6Wuv3yDAUSGfizgZXjN9unAufiZoMV1ueKB//edc7s2vXnkk4xee9TTHa7hgcqVe/fWLF34hh1rAzO24eal60y8BTHb8Dq7jwOb3rBjbcnmilUt9ruBr5Tq+YqUxrtm/zND64RMimqxTaAJSALPAD6Dd2Dmy3gnjM/FWzXpYbxlkPNDt+fxVrFy8f5k4sBTgc8BK/FWWTr8Z/Qf4BlTqWtUjVprrr98w7uAT+ItBXX8uC4Xb76HSw7dS1x78x4ciM1iTvpQwaR1huvYEp3L9rpTdGOu+7ttsZlvnSggNy9ddwbeG7sA79s0ibdU2QDwP2/YsfZLEzx2Nt4b/Ay8L4P80OObhv4do/R9Mo23PuFvgfcO/TTjHTYdI6fAsUxs0+TW0xZxy1nLcMdZW2/aaO2tAWhrl4iZR6kwJ97MVjm8L6ntwDlAFd7n+1PgrbrZyh39AOv6yze8CfjqdFRn5fNc3HY/FUOhuLf+HEztMDd9qOD9H69eTjw/iEKpttjMN4fc/G68tbzHczdH1hRvHPoBr+X74s1L1/W8YcfaH47z2D8DZ03tN/JN4QXulXjfkismunNYA3mHrQ01/OWc5egTYcZBpcA0IGsbZJ3ImHXITwxhYN7Qz8htr8MLyFuOfoABvHpaSht6MVN7LVvWCPF49XKSVsWY+zkY3DbjErZVLWVn5SIWDnjzs1muPe5i6DcvXfd8joRiPAUXsLx56brTmf5QHG3CUIx018p5J0YoDlMKoibkArmqwCsKbTTwdhemhdYaV3lNf16FcJVJyB17gdnmmpXsqlwIQNjNw9BjFHpwgqefaNj7YQPjbA/UyUTLOQH/AAt1EoOh4OUQBt6ystPyTucjEZ6IeyMQEk6KxkwHK/u3j7lfxPV2+ZR2mZs6QEesAbQmY0Y+ON5zv2HH2nvwlsAdjwu0jPPY3Ux2lPDxc8ux7+J56mN7ME+0cGQdiJjlrqIYNxbaaNxwy5p/4J0R/RuFp80sHaX4+5Knc0/d2eyPzWJ5/w5C7tiXrMv2srJvG09vvR3tOqTNmNOY7bzmdTtfvO4Yr3AK8Bu8taBvx1tbetfQf1/8hh1rb5vgsS8H3oW3jvdtQ8/zI+BOYBOFr07U4/z3ZOSBFN7a4m8FXgBcjrd+tkOBBeMdoC8WJprL6yvWb9Gq3OHQGlwXsjaEjR7C5i689+nwhzrRovelq2Hii7E03nrkvwGuA+7FO+K1C3idbra+UOhBk7609frLN4TwDpn14x0WK4rK5Xn9PT98X0ZHvrg0sx+D8VtgF9hQdWp6U+NZiVIe6nyyUC32IN4hy+nj/SF6f/CaH2Aa7/A7tapqsefjDWbcUOgI0SQeXwt8F++IYgXeRBDP0s1WuuiapnrN9/WXb7gS+EOxLzhk7g1//Z+zBzD/GMfFHOdLRQM25tNC+tf/8fl6T0qqxd4EnDaNL/kT3Wy9ahpfr2ymfBD8hlvW/BGv6fejD73uTzGcCUPRaVX1SCiOYfqu6X7gZAkFFHnC6oZb1vyvj9dsu+GWNUmAHIbOq7F7ZRrIw2cb8z+s8/E6J4PUNLyGDYR0s3VSrWQ53adNbbwOMgDajFxha0VuKBwar1+hIBnW666b5tqCaOOk7lV8q/IV3WyFdLN1fA/KnID8BONAEY/5zw23rBk+ulNh//QvMezIoA7/Mg8pBf0GXIJed6xrBYRn06Suxzhyn9XAh4FrOfbnd6lutt5TfGnB5uf8/Sl4h72mclpn7MVNel2u1vugxNT9gCkM59HN1kaOtDK/VC22Q+Evxxec7CtbFd1i3HDLmgHGGew2galeIy4moJutJN7I1MkYc+hSN1sm3mHODN5h+K8BFbrZ+nPJigwo31N0Xn/5hqk8wRduuGVNs68XFGOoFnuoazahU3SztWU66nkyKMVQyKmMkgnmaJoTndbjr+3t3TZDN1vSWk9BKY5KnTOF+07L8PaTzjFafQnF1PkOxg23rHkIr1N9rFP5XTfcsmaP39cTBSg13qhhUaSSnMe44ZY1WbwxO4XGM2ngUzfcsmaqsxaKyVLq6nFbDaVOunMQpXA8Fo6x8EJi33DLmuk4MysA1WL/C1c/fVQvzut31Opmq7c8VQWXrPP9JKJa7DhwGTAf+IdutraWuaTA+v+EVJuvVxy9fwAAAABJRU5ErkJggg==" id="imagea4aa732415" transform="scale(1 -1) translate(0 -578.16)" x="363.6" y="-43.2" width="142.56" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature60_fold0 -->
    <g transform="translate(125.905187 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 520.238687 638.149 
L 527.868187 638.149 
L 527.868187 27.789 
L 520.238687 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image0c6521e035" transform="scale(1 -1) translate(0 -609.84)" x="520.56" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(531.368187 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(531.368187 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(565.769125 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p3881f30ec4">
   <rect x="358.832187" y="27.789" width="151.912" height="610.36"/>
  </clipPath>
 </defs>
</svg>
