<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="774.099594pt" height="679.5765pt" viewBox="0 0 774.099594 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T09:07:06.899660</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 774.099594 679.5765 
L 774.099594 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 397.468594 638.149 
L 518.564594 638.149 
L 518.564594 27.789 
L 397.468594 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 448.371164 638.149 
L 448.371164 27.789 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 397.468594 609.084238 
L 518.564594 609.084238 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 397.468594 580.019476 
L 518.564594 580.019476 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 397.468594 550.954714 
L 518.564594 550.954714 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 397.468594 521.889952 
L 518.564594 521.889952 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 397.468594 492.82519 
L 518.564594 492.82519 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 397.468594 463.760429 
L 518.564594 463.760429 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 397.468594 434.695667 
L 518.564594 434.695667 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 397.468594 405.630905 
L 518.564594 405.630905 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 397.468594 376.566143 
L 518.564594 376.566143 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 397.468594 347.501381 
L 518.564594 347.501381 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 397.468594 318.436619 
L 518.564594 318.436619 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 397.468594 289.371857 
L 518.564594 289.371857 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 397.468594 260.307095 
L 518.564594 260.307095 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 397.468594 231.242333 
L 518.564594 231.242333 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 397.468594 202.177571 
L 518.564594 202.177571 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 397.468594 173.11281 
L 518.564594 173.11281 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 397.468594 144.048048 
L 518.564594 144.048048 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 397.468594 114.983286 
L 518.564594 114.983286 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 397.468594 85.918524 
L 518.564594 85.918524 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 397.468594 56.853762 
L 518.564594 56.853762 
" clip-path="url(#pf60499eef4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="md147e55c3f" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#md147e55c3f" x="398.842054" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(390.733851 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#md147e55c3f" x="448.371164" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(444.871789 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#md147e55c3f" x="497.900274" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(494.400899 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(335.335188 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(61.728906 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_18 -->
      <g style="fill: #333333" transform="translate(184.647969 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_range_CovalentRadius -->
      <g style="fill: #333333" transform="translate(28.280313 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-76" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2070.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2131.9375"/>
       <use xlink:href="#DejaVuSans-65" x="2159.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="2221.244141"/>
       <use xlink:href="#DejaVuSans-74" x="2284.623047"/>
       <use xlink:href="#DejaVuSans-52" x="2323.832031"/>
       <use xlink:href="#DejaVuSans-61" x="2391.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2452.34375"/>
       <use xlink:href="#DejaVuSans-69" x="2515.820312"/>
       <use xlink:href="#DejaVuSans-75" x="2543.603516"/>
       <use xlink:href="#DejaVuSans-73" x="2606.982422"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=1_88e+00 -->
      <g style="fill: #333333" transform="translate(70.715156 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-31" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-38" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-38" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_147 -->
      <g style="fill: #333333" transform="translate(176.376719 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(61.728906 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_range_NpValence -->
      <g style="fill: #333333" transform="translate(60.674688 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4e" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-70" x="1955.277344"/>
       <use xlink:href="#DejaVuSans-56" x="2018.753906"/>
       <use xlink:href="#DejaVuSans-61" x="2079.412109"/>
       <use xlink:href="#DejaVuSans-6c" x="2140.691406"/>
       <use xlink:href="#DejaVuSans-65" x="2168.474609"/>
       <use xlink:href="#DejaVuSans-6e" x="2229.998047"/>
       <use xlink:href="#DejaVuSans-63" x="2293.376953"/>
       <use xlink:href="#DejaVuSans-65" x="2348.357422"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_mode_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-64" x="2084.476562"/>
       <use xlink:href="#DejaVuSans-65" x="2147.953125"/>
       <use xlink:href="#DejaVuSans-6c" x="2209.476562"/>
       <use xlink:href="#DejaVuSans-65" x="2237.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2298.783203"/>
       <use xlink:href="#DejaVuSans-76" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-4e" x="2419.486328"/>
       <use xlink:href="#DejaVuSans-75" x="2494.291016"/>
       <use xlink:href="#DejaVuSans-6d" x="2557.669922"/>
       <use xlink:href="#DejaVuSans-62" x="2655.082031"/>
       <use xlink:href="#DejaVuSans-65" x="2718.558594"/>
       <use xlink:href="#DejaVuSans-72" x="2780.082031"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(170.26875 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(251.51875 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(122.952813 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- BondFractions_O_-_S_bond_frac_ -->
      <g style="fill: #333333" transform="translate(162.025938 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-46" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="306.910156"/>
       <use xlink:href="#DejaVuSans-61" x="348.023438"/>
       <use xlink:href="#DejaVuSans-63" x="409.302734"/>
       <use xlink:href="#DejaVuSans-74" x="464.283203"/>
       <use xlink:href="#DejaVuSans-69" x="503.492188"/>
       <use xlink:href="#DejaVuSans-6f" x="531.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="592.457031"/>
       <use xlink:href="#DejaVuSans-73" x="655.835938"/>
       <use xlink:href="#DejaVuSans-5f" x="707.935547"/>
       <use xlink:href="#DejaVuSans-4f" x="757.935547"/>
       <use xlink:href="#DejaVuSans-5f" x="836.646484"/>
       <use xlink:href="#DejaVuSans-2d" x="886.646484"/>
       <use xlink:href="#DejaVuSans-5f" x="922.730469"/>
       <use xlink:href="#DejaVuSans-53" x="972.730469"/>
       <use xlink:href="#DejaVuSans-5f" x="1036.207031"/>
       <use xlink:href="#DejaVuSans-62" x="1086.207031"/>
       <use xlink:href="#DejaVuSans-6f" x="1149.683594"/>
       <use xlink:href="#DejaVuSans-6e" x="1210.865234"/>
       <use xlink:href="#DejaVuSans-64" x="1274.244141"/>
       <use xlink:href="#DejaVuSans-5f" x="1337.720703"/>
       <use xlink:href="#DejaVuSans-66" x="1387.720703"/>
       <use xlink:href="#DejaVuSans-72" x="1422.925781"/>
       <use xlink:href="#DejaVuSans-61" x="1464.039062"/>
       <use xlink:href="#DejaVuSans-63" x="1525.318359"/>
       <use xlink:href="#DejaVuSans-5f" x="1580.298828"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(182.177969 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(61.728906 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_122 -->
      <g style="fill: #333333" transform="translate(176.376719 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(25.328906 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(75.598281 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(254.275156 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(132.759688 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(91.175938 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 397.468594 638.149 
L 518.564594 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageafef791cdf" transform="scale(1 -1) translate(0 -578.16)" x="400.32" y="-43.2" width="115.2" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature45_fold0 -->
    <g transform="translate(149.133594 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 526.133094 638.149 
L 533.762594 638.149 
L 533.762594 27.789 
L 526.133094 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image1fb1b6e032" transform="scale(1 -1) translate(0 -609.84)" x="526.32" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(537.262594 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(537.262594 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(571.663531 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pf60499eef4">
   <rect x="397.468594" y="27.789" width="121.096" height="610.36"/>
  </clipPath>
 </defs>
</svg>
