<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="785.818156pt" height="679.5765pt" viewBox="0 0 785.818156 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T11:10:42.300138</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 785.818156 679.5765 
L 785.818156 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 417.395156 638.149 
L 522.075156 638.149 
L 522.075156 27.789 
L 417.395156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 457.094163 638.149 
L 457.094163 27.789 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 417.395156 609.084238 
L 522.075156 609.084238 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 417.395156 580.019476 
L 522.075156 580.019476 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 417.395156 550.954714 
L 522.075156 550.954714 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 417.395156 521.889952 
L 522.075156 521.889952 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 417.395156 492.82519 
L 522.075156 492.82519 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 417.395156 463.760429 
L 522.075156 463.760429 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 417.395156 434.695667 
L 522.075156 434.695667 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 417.395156 405.630905 
L 522.075156 405.630905 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 417.395156 376.566143 
L 522.075156 376.566143 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 417.395156 347.501381 
L 522.075156 347.501381 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 417.395156 318.436619 
L 522.075156 318.436619 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 417.395156 289.371857 
L 522.075156 289.371857 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 417.395156 260.307095 
L 522.075156 260.307095 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 417.395156 231.242333 
L 522.075156 231.242333 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 417.395156 202.177571 
L 522.075156 202.177571 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 417.395156 173.11281 
L 522.075156 173.11281 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 417.395156 144.048048 
L 522.075156 144.048048 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 417.395156 114.983286 
L 522.075156 114.983286 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 417.395156 85.918524 
L 522.075156 85.918524 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 417.395156 56.853762 
L 522.075156 56.853762 
" clip-path="url(#p141a9b6272)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mc1d3b33c50" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mc1d3b33c50" x="418.595661" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(410.487458 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mc1d3b33c50" x="457.094163" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(453.594788 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mc1d3b33c50" x="495.592664" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(492.093289 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(347.05375 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_2 -->
      <g style="fill: #333333" transform="translate(45.255469 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-32" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- CrystalNNFingerprint_mean_wt_CN_5 -->
      <g style="fill: #333333" transform="translate(152.68625 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-35" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_rectangular_see-saw-like_CN_4 -->
      <g style="fill: #333333" transform="translate(7.2 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1465.060547"/>
       <use xlink:href="#DejaVuSans-63" x="1526.583984"/>
       <use xlink:href="#DejaVuSans-74" x="1581.564453"/>
       <use xlink:href="#DejaVuSans-61" x="1620.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="1682.052734"/>
       <use xlink:href="#DejaVuSans-67" x="1745.431641"/>
       <use xlink:href="#DejaVuSans-75" x="1808.908203"/>
       <use xlink:href="#DejaVuSans-6c" x="1872.287109"/>
       <use xlink:href="#DejaVuSans-61" x="1900.070312"/>
       <use xlink:href="#DejaVuSans-72" x="1961.349609"/>
       <use xlink:href="#DejaVuSans-5f" x="2002.462891"/>
       <use xlink:href="#DejaVuSans-73" x="2052.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2104.5625"/>
       <use xlink:href="#DejaVuSans-65" x="2166.085938"/>
       <use xlink:href="#DejaVuSans-2d" x="2227.609375"/>
       <use xlink:href="#DejaVuSans-73" x="2263.693359"/>
       <use xlink:href="#DejaVuSans-61" x="2315.792969"/>
       <use xlink:href="#DejaVuSans-77" x="2377.072266"/>
       <use xlink:href="#DejaVuSans-2d" x="2458.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="2494.943359"/>
       <use xlink:href="#DejaVuSans-69" x="2522.726562"/>
       <use xlink:href="#DejaVuSans-6b" x="2550.509766"/>
       <use xlink:href="#DejaVuSans-65" x="2604.794922"/>
       <use xlink:href="#DejaVuSans-5f" x="2666.318359"/>
       <use xlink:href="#DejaVuSans-43" x="2716.318359"/>
       <use xlink:href="#DejaVuSans-4e" x="2786.142578"/>
       <use xlink:href="#DejaVuSans-5f" x="2860.947266"/>
       <use xlink:href="#DejaVuSans-34" x="2910.947266"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(96.20125 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(204.574531 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_range_NUnfilled -->
      <g style="fill: #333333" transform="translate(90.28625 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4e" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-55" x="1955.277344"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.470703"/>
       <use xlink:href="#DejaVuSans-66" x="2091.849609"/>
       <use xlink:href="#DejaVuSans-69" x="2127.054688"/>
       <use xlink:href="#DejaVuSans-6c" x="2154.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2182.621094"/>
       <use xlink:href="#DejaVuSans-65" x="2210.404297"/>
       <use xlink:href="#DejaVuSans-64" x="2271.927734"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_70 -->
      <g style="fill: #333333" transform="translate(204.574531 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- BondFractions_O_-_Ta_bond_frac_ -->
      <g style="fill: #333333" transform="translate(176.44375 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-46" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="306.910156"/>
       <use xlink:href="#DejaVuSans-61" x="348.023438"/>
       <use xlink:href="#DejaVuSans-63" x="409.302734"/>
       <use xlink:href="#DejaVuSans-74" x="464.283203"/>
       <use xlink:href="#DejaVuSans-69" x="503.492188"/>
       <use xlink:href="#DejaVuSans-6f" x="531.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="592.457031"/>
       <use xlink:href="#DejaVuSans-73" x="655.835938"/>
       <use xlink:href="#DejaVuSans-5f" x="707.935547"/>
       <use xlink:href="#DejaVuSans-4f" x="757.935547"/>
       <use xlink:href="#DejaVuSans-5f" x="836.646484"/>
       <use xlink:href="#DejaVuSans-2d" x="886.646484"/>
       <use xlink:href="#DejaVuSans-5f" x="922.730469"/>
       <use xlink:href="#DejaVuSans-54" x="972.730469"/>
       <use xlink:href="#DejaVuSans-61" x="1017.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.59375"/>
       <use xlink:href="#DejaVuSans-62" x="1128.59375"/>
       <use xlink:href="#DejaVuSans-6f" x="1192.070312"/>
       <use xlink:href="#DejaVuSans-6e" x="1253.251953"/>
       <use xlink:href="#DejaVuSans-64" x="1316.630859"/>
       <use xlink:href="#DejaVuSans-5f" x="1380.107422"/>
       <use xlink:href="#DejaVuSans-66" x="1430.107422"/>
       <use xlink:href="#DejaVuSans-72" x="1465.3125"/>
       <use xlink:href="#DejaVuSans-61" x="1506.425781"/>
       <use xlink:href="#DejaVuSans-63" x="1567.705078"/>
       <use xlink:href="#DejaVuSans-5f" x="1622.685547"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- VoronoiFingerprint_std_dev_Voro_area_maximum -->
      <g style="fill: #333333" transform="translate(74.822344 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-61" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-78" x="2109.220703"/>
       <use xlink:href="#DejaVuSans-69" x="2168.400391"/>
       <use xlink:href="#DejaVuSans-6d" x="2196.183594"/>
       <use xlink:href="#DejaVuSans-75" x="2293.595703"/>
       <use xlink:href="#DejaVuSans-6d" x="2356.974609"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(26.232813 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(189.80125 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- OPSiteFingerprint_std_dev_sgl_bd_CN_1 -->
      <g style="fill: #333333" transform="translate(135.052969 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-73" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-67" x="1414.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1478.052734"/>
       <use xlink:href="#DejaVuSans-5f" x="1505.835938"/>
       <use xlink:href="#DejaVuSans-62" x="1555.835938"/>
       <use xlink:href="#DejaVuSans-64" x="1619.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1682.789062"/>
       <use xlink:href="#DejaVuSans-43" x="1732.789062"/>
       <use xlink:href="#DejaVuSans-4e" x="1802.613281"/>
       <use xlink:href="#DejaVuSans-5f" x="1877.417969"/>
       <use xlink:href="#DejaVuSans-31" x="1927.417969"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_maximum_NUnfilled -->
      <g style="fill: #333333" transform="translate(62.588125 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-55" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.556641"/>
       <use xlink:href="#DejaVuSans-66" x="2304.935547"/>
       <use xlink:href="#DejaVuSans-69" x="2340.140625"/>
       <use xlink:href="#DejaVuSans-6c" x="2367.923828"/>
       <use xlink:href="#DejaVuSans-6c" x="2395.707031"/>
       <use xlink:href="#DejaVuSans-65" x="2423.490234"/>
       <use xlink:href="#DejaVuSans-64" x="2485.013672"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(109.487656 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_avg_dev_NUnfilled -->
      <g style="fill: #333333" transform="translate(73.7275 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-55" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-6e" x="2155.814453"/>
       <use xlink:href="#DejaVuSans-66" x="2219.193359"/>
       <use xlink:href="#DejaVuSans-69" x="2254.398438"/>
       <use xlink:href="#DejaVuSans-6c" x="2282.181641"/>
       <use xlink:href="#DejaVuSans-6c" x="2309.964844"/>
       <use xlink:href="#DejaVuSans-65" x="2337.748047"/>
       <use xlink:href="#DejaVuSans-64" x="2399.271484"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(81.655469 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(95.524844 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(274.201719 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(152.68625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(111.1025 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 417.395156 638.149 
L 522.075156 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image3fa84a86d2" transform="scale(1 -1) translate(0 -578.16)" x="419.76" y="-43.2" width="100.08" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature69_fold0 -->
    <g transform="translate(160.852156 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-39" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 528.617656 638.149 
L 536.247156 638.149 
L 536.247156 27.789 
L 528.617656 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image104ae23cff" transform="scale(1 -1) translate(0 -609.84)" x="528.48" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(539.747156 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(539.747156 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(574.148094 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p141a9b6272">
   <rect x="417.395156" y="27.789" width="104.68" height="610.36"/>
  </clipPath>
 </defs>
</svg>
