<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="781.271031pt" height="679.5765pt" viewBox="0 0 781.271031 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T21:05:29.051604</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 781.271031 679.5765 
L 781.271031 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
L 520.984031 27.789 
L 409.392031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 451.025927 638.149 
L 451.025927 27.789 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 409.392031 609.084238 
L 520.984031 609.084238 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 409.392031 580.019476 
L 520.984031 580.019476 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 409.392031 550.954714 
L 520.984031 550.954714 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 409.392031 521.889952 
L 520.984031 521.889952 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 409.392031 492.82519 
L 520.984031 492.82519 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 409.392031 463.760429 
L 520.984031 463.760429 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 409.392031 434.695667 
L 520.984031 434.695667 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 409.392031 405.630905 
L 520.984031 405.630905 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 409.392031 376.566143 
L 520.984031 376.566143 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 409.392031 347.501381 
L 520.984031 347.501381 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 409.392031 318.436619 
L 520.984031 318.436619 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 409.392031 289.371857 
L 520.984031 289.371857 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 409.392031 260.307095 
L 520.984031 260.307095 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 409.392031 231.242333 
L 520.984031 231.242333 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 409.392031 202.177571 
L 520.984031 202.177571 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 409.392031 173.11281 
L 520.984031 173.11281 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 409.392031 144.048048 
L 520.984031 144.048048 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 409.392031 114.983286 
L 520.984031 114.983286 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 409.392031 85.918524 
L 520.984031 85.918524 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 409.392031 56.853762 
L 520.984031 56.853762 
" clip-path="url(#p0c50bed52f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m3aa5b2a33f" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m3aa5b2a33f" x="412.707804" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(404.599601 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m3aa5b2a33f" x="451.025927" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(447.526552 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m3aa5b2a33f" x="489.344049" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(485.844674 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(342.506625 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(101.484531 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(66.849688 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(88.198125 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(181.798125 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(134.87625 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(31.0875 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(134.87625 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_4 -->
      <g style="fill: #333333" transform="translate(130.982344 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-34" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(166.649062 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(57.759844 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(73.652344 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(221.222656 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(188.300156 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(18.229688 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(196.571406 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(87.521719 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(266.198594 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(144.683125 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(103.099375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAJQAAAMjCAYAAABDGzmEAABebUlEQVR4nO2dd5wkVbm/n1Op4+S4OQdYckYySBIVXBBFMXtV1Iuoo5guKmYd0Ys/r+ma9ZpXMCFKzmFZYIHNOU4O3TMdq+r8/uiZ2ZmdmQ1sT1f36Xr47IeZqurq99R86z3vSe8RUkpUQnzDNsjaaYQQSPkp+angV722qZzQvDYgn4jPD34BKbOYhoahCwz9K+JLSdtru8oJw2sD8krQ/DRCwLDXFQJMQ/fWqPJCLUFJIXBGV+FSMR9c/Kj1uMUExySIVnuiMz5TgFqCkuREpQH60M8StIHk9z21q4xQq8obFpMYckg64ErcrH2sh1aVFWp5KOQ+MQ0jAOlO88ScMkQxQU2MbugzvbahXFBLUK67r8tgmKyLK/yeg0KhVgyVsiGVhogFmoBkFjIOMug38gqFWoJygawLfan9TviCKhRqVXmBCd6PoA66L6hCoZaHsnSoCoA71NpzXIhakEx7bVnZoJagpISwua/rQEp0V/Ka5zbocLK3tpUJQoXpK6LVPhtXPpzrFh9biwvH5YFv/oT2Wou2+ulM62nDiVtuc7otfmbXkz+3ZPZTyBWDHpmuHKrEUA/n4u4JYiUBx3SuYU/NLNLorJl1FFVWTNtjzaxaWX3yjcAPCmyr0pS8oESrfeFI39ME3lZqGp31DnO6djG/bwcAG6bNI+Sk2BGajYt4A2J5oJA2q0zJCwroG/nJnbj61qXLgtgm7KEOTt11cNAQuAhkCnAKYGdZUPKCki3GKoTIBeKScV5KuC4z+3tImQE2NC5CSJfjt60jo1scFV+HgO8gV/izOvOEKq28CFIOoCPIuLl+p6GZmxJJvxUi5upM729jZuduuT00vW9Z95rNx8VfvB34ldfGq4QSrbxhxFfScv/Ky3Rt3v7Yve4P//laf0CvAJR8lTeaUGZ8zZXVDH56+nmuB+aUJUoJKjhRUC7AQh0vXOwoJaikqeVmGYxC6AKR9QVVKFQJygFwNY2gkKSsofdEgGU7mH6FVzCUElRW05CGBlKiSYkrNNKGjmP68XihUEpQcniaihC4o+aW27bj13kFQqkYasKecglobCy4LWWKYoIC7FHzyl0JtgSpWDmLGKWqPCAnKlfCcFeBACz9dx5aVFao1VP+tYzEhTHdTjrImy1/DnCBUM9D6QBDA8VCgvS1VEjUii1cKUGMXZLu+p1QhUQtQQmtGenmAnFn6J/tbvfarHJCqRhqGPHF9B3AEvmZwFFe21JuKCkoH+9Qq8rz8RxfUD55xReUT17xBeWTV5QT1NIPbHL0L6ek+FpaRm/pk2++7N4veG1TOaFUK++M659JPHn8caHRaREDyRSp/4r63eUFQikPtX7evND+OTYd02TGjdvP9MikskMpQU2PxQhmbN5+3ypu+d19LNvejm3o1O/e+VavbSsXlBocnt/ex4+/+w8iTgINyVUr1/G7M5exTev22rSyQSlBffRvjzHb2UIDOzHI0k8Db308wbVXnlPttW3lglKCmje4nU2zTd54/s1sqW7k8s3P8+l7/sW0eHLAa9vKBaUE1VcFl133cZJmLjvP/554AR3hStoygdd4bFrZoFRQ/ssTzxgR0zB/W3QiKSsQ9cikskMpQT0yffxsFV26hONxD6wpT5QS1Ek7O8aVKITEMAylqvZiRqkHHa8IQcSEjJtb+WJqxEwLpyLqLx0uEEp5KEeSS5YR1CFsgKkRSmc42tGMliufN722rxxQykNd8OIWCAdpzGSIZGxebKolm8jwYjRMNOMkUay8xYgyHuq2V9z3bkM3OL6jh+m9ccKpDKfs7uKiLbvpqIjQbvkOqhAo88a68KO+xiqywD8XzmV3RYTpyTQnVvYhgLSGXvWhrrb+/65v9tpWlVFCUJ+48JHvNkqJo2t874SjqEikMFJpHmuoZk84wBV7u3m8poJoxm7y2lbVUUJQGdO4mmSanZEQH3zwGabHcjttbKup5BsXnoqtCTQJ0zJpxCfimvxqhb/6c4pQIoaKB6xFEpjeFRsRE8Dc3hiXr91KRhNUZbOsjYbxxTS1KCGoH/3lpHhcCLcykRx3bnFXH0HboSE2iKPr13hgXlmhhKAAPvfYhfqGpspxxzc0VLPXEBjgpG6r/ZMHppUVyggK4O9Hz2VzXdXI72ua6vjH0fOZE++jDzZ7Z1n5oERQPsyM7iRfuPwsZvXGcIRgT3UFkVSaDVaI+/60bInX9pUDSnmoq59dR2Uyxc6aSvZUVwBw7TNrSSb7Mx6bVjYo5aHOaFvHr34S51enH0NPJMh5G3fymg3P8IGzz8x6bVu5oJSgDD1DNBTnrO5e7D6Npkyc2fZW4vpF6iw+LHKUEtRDs0/ErtjX0muvqefuBa9EBJWq2YsapZ70mlnTxh3bU91AVe/AKg/MKUuUEtSxe3eOO1ab7KPC5GkPzClLlBLU+etXUZfYt6hTcx3O3fEoe0KWv0l1gVAqhnph2hzevPr3bKxbQNIIMb93K20VFdQlMiGvbSsXlBLUimNPw64a5PpnHwGgJxTh3de+l+jmnnUem1Y2KCUox3HkW950o/jKha9jZn83j8xbyqyOONr00Le9tq1cUCqG6ooa71y0o4N1jTP415ITCKUc5u/olA/9ZMH4zYh9pgSlEo4BnPnOF18VHND/lrF04eB0P/Gro+q9tqmcUE5QPt6iVJXn4z2+oHzyii8on7ziC8onryjVDwXwzkueFst2bnfWzJgpQtm0XLx9z19u3HbdVV7bVS4o56EqY4NuZVqIa55ZywXrdoie6rorb5vzf7d5bVe5oJSgvjPrF194xfbdVKUy9EYjpE2L07btpq2x8SavbSsXlBJUv261CAS2PpQOSgj6KiIcv6vTW8PKCKUElZW42QmS1QVdx9+ao0AoJahUJKAZWQcA4UoYGgUQjj8aUCiUEtS2GU1GZV+aaH+GSCxLRW+WSCxLezTstWllg1LdBu3RIBlTp78qgG1qCFcSHrTZU+VnlS4USnmoxlRKxIbEBCA1wWCFSXN8fBINn6lBKUGFkymy1viEv9mAnwS4UCglKBtNpgLjc2lunOUnrisUSgmquSfB5rnTcUdtwhiPhtndUO2dUWWGUkH5CXv2ar87aSHSWkhFbICsadJdHaWyt89r08oGpTzUbKeX7//8F+jJGF1VUZK64MLHnuWijRu8Nq1sKG0PJZYbwF+Bi1Kg9c94hZB9Lpc+tB4tPdTSExIZziBa7Q/LFuNbntpbBpSuhxLL/wFkgctcMA1C+sV7VolN1TOwpUlPfZh4RQCkQKRNgNtEqy1Fq/02bw1Xm9IUlFgeBS4f/tUljE4KU2bZU9HIhqMb2DOriu0La1l/VCOuo3HVMyNrPX/qic1lQqlWeW/I/U8AklwxJBkq6aqLokkQQ+N4jqWxdlEDdcmRnGP+QPEUUpoeCnJrzRke9M2lHs9oFhpjFSOA9sYo2tCgsc/UUpqCkivWA0nISUowgEuIsNuNI8Y7oLRp8tszlg7PPnikoLaWGaUpKAC5Igz8VoAUICVptgXnAeNXne+tChEPBgYR4m2yxTin4LaWEUqtHP7B0l87l23ZpP352NMwh2q4lAl9pskXHnulHzsVgFINyick0Jchq5u8edUjdIcr0F0XI+vyzwXHe21a2VC6Vd4EPDdnuuy2q4gTIZJIo6UE/U4l8/e2e21a2aCUh8oYurRsST9j93yRQqn3pqhR6knP6ugR+7oS9lGVSHlgTXmilKB6K4JDP8mR/wskKaGUIy5qlHrSe2qq5K6KKmrTCYIZGxdBZ1WUXs3P2VoolPJQ0f6EyAR0tIwgi4mDiZVx6a7xs0oXCqUEZdjSbuoZHDP0Ek1mSYd9QRUKpQRVm8i4hjs+KBe2Op23xY5SggrHk7RVRcYdr+9NeGBNeaKUoFIm67S0pCuaC8KTpsH26iocQ/ddVIFQSlCf23HdSW1NFVgJSUwEyGR1NAkJmfQ7ogqEUt0GAE528He7ZkbekDYsHE1Q2xdz/3P3m/3kBgVCqdkGPt6jVJXn4z2+oHzyii8on7ziC8onrygpqMuuf+rK2k93pptu3v2w17aUG8q18ubetNfeMb1Ol5oGUtLQO0DHl2v8+eQFQikP9fsF3z56+4x6HSGYFk8Qzdh01kSZ9pG9/qK8AqFUx+b3Tzj7+abBFBdsaaPPMqlJpumoDPPgzHrfQxUIpQS1dVq9WNDZz2+PnQtDCz6X7exi1kBSgD/JrhAoVeUlghH98TmNI2ICeGlWPcu2+6teCoVSgtJcidTGF6nLn2BXMJQSlJXKTnhcKNaSLWaUElQ6NLTqZVhAQ/9PmOMzA/tMDUoF5THLhHgaMi5ouTgqEBB0V/izVwqFMh4qdEu/ngyZOTEBuBJcSToLGdNPfF8olBFUygpIJliggO0yaPlVXqFQRlAhJ7sIfXz/pYFkWk9f4Q0qU5QRVLg/vrppMIE2qnYTSN735JMct9Pf0bNQKBOUxwxdO61vLw/8+tesmH0ucSvA8bs7ODq+mX8sWcYd01bVX7X3XV1e26k6JTfbwLi5/8r3PvvAr1+34ZlwfyA8GLIzV9up6mM+86pXfvNnf/8xPQNLGNT32x8vFGNWfBfbnUX9AVere1XqLQ6AaLUjwMeB84AXgK/KFmN3ocukEiUlKPHJ+PTPPPqX3V94+I6RY4OGxccufDN1jk1zvI3A7nruXzILWwguWbOdGf0DVFi7iYs6BrQQgQG2vCb11gUAotX+G3DFqK/YDBwtW4xMQQumEKVW5X3/hmfvH3MgYmdY0NvJxTvXcvH1NxJ3gySHWnV3nLiQb//2PhZ3NlCp97JhehOzU7H5/wj+UlzxxetmMVZMAAuAS4C/FaIwKlJqQbkQEzhUKSWRTJouPTIiJoCMofPLM44mlJY42QiOJkYn0phsSos/1eUIKDVBvf/7J54/5kDCMFnXOIM1DTMJ2OPH8noiueGYDEFm9MQRWbn9Vam3SNlibAfu2u/yrcDdU2F4uVBSVZ78SsVO4+bXvL4vEP7F8g2rQv2BUALkta9+fv3Jt59x+ucv3/w8K5aeNuYz523YBUAsqGMMOgOG0BaPOv0G4JPAucBLwBf9+OnIKKmg/EA0fXBX9mOr7jUyONx+8iWk9ACvWr2VGx5YRV80zKZm66gPb3jjuoPfyedIUEZQ0Zu7shdv3mH8bsXXsWTOydgYbLIW8YPTLuVbD5/rx0YFoNRiqElxAsabV86fziORc9lqzafNmMYLoePZFprD7Zee7LV5ZYMygoqi//HUvZs4JvUCWwILeTJ6FoN6lLPij3B0d5vX5pUNJRWUH4i4prG7soZGu53Z1mq21TRw0vb1mDh0RqIHv4FPXlAmhgKIfrZfnr1jPXcfdSIANYkBrl31KKurl/DYLxb5MVQBUEpQi2/aIjdOnzX2oCuZ097Ltm81+YIqAMrEUACDgQmqNk2wt76i8MaUKUoJqjIzsbdtivcX2JLyRSlBzejvHX9QQCTrd34XCqUEtdMKOpbr5oZ3R/4JAsMLF3ymHKUE5QhbhpKp3FL0oX/BTJZdoQp1Wh5FjlKCOmFP56n9pkUgmaEmnqChb4CUFMRDuu+iCoRS3QYADZ9qd7sqagRSgiawXJv0p8J+l0GBUMpDAXR+uUk7ZvuuDzTG4s6Mjq7VvpgKi3IeysdblPNQPt7iC8onr/iC8skrvqB88ooy86GGEZ+MC2zXQR9KtOlIR36jSrlyFivqeSjbyYlpuLdcF7po6bO9NqtcUO/N1TSBpuXG8SQ5UWnSzzhWIBQUlICoCaaey7GZcWDAH3kpFOpVeWEjJybIeaeAAZbvoAqFeoKaKJ+mL6iCoZ6gnImyaRTejHJFuRhKS2apSWexNUF/cCgTS8pv5BUKpQT12tc/75xQ38TWgEW/riHiWdAEcoJkrj5Tg1KCilXVaLstk17LyAkJmasCJ6oGfaYEZWKob57xy0sM6dIVMMDQct0HmrbvZ5+CoIygprft/Kutabj6fkUSwhdUAVFGUBumzTOyukk0neSEtu0E91s6JT7er0xZixllYqiN0+bz3sfv4Jo1j2G6Lt2hKJ869yp+evw5uW4DQ6sGejw2U3nUeGvFNcHzN6wS169+iLBtY7ouzYMxbrv/94TcbG4IJmW/KFrt47w2VXVKX1DimlkdocrEmXs3iv0LU51KcVrfdsilDp6G6z4vWu0ve2Bl2VDygnLgOwOmJSbra0pYwdx4HkDWBfiEaLVnF8zAMqPkBWVr+qkvNc9nT0UtMHZWgYPk6TkL9x3ObX8mgMX4TAklL6iA6/z65J3r2F3VANiAQ05BNg8sWEztQHxfKvucF0sCT3ljrfqUvKCAWzK6NSBsh7gVJCcom85whE110/jJb76DGJ5sYOhJ4I2yxYh5Z67alL6g5B9Tc+Pfr0iiy4RhkesjkDQkYrz3qX+zM1qfu07wFJqoli3GX7w0V3WU6Yc6b+eLsi4VF9866/X8demZBBybK198iN8sPB6paSDEef4uCVOPMoJ6oaJZPrrweH533IUAJICfnXoF7WQhkQVXjt8IxifvlH6VN8RgJGw/OO+EccdNPQCORN5W4xTeqvJDGQ8Vq2mMRjLJLEBSE3RZRq4/03X9GZsFRBkPddNjb7Id1ySuCV6oCLEnaLE7ZLE7HPDatLJCGUEBbIpUxNZFgkgxeptFMdz/5FMAlBLUA785umrcfCiAiY75TAnqPWljAm/ke6iCoUxQPkLW2bcEHXLjd35QXjDUE5TtDolplIpsX1GFQr0qz8dT1BTU6ES0Uu4/q8VnClGvypM4gD4iKgEI2eGlSeWEch5KtlYbIPeMHHDlQ7K1pslDk8oKP0+5T15RzkP5eIsvKJ+84gvKJ6/4gvLJK8p1G8z4z079nI3bMgv6YtqeaNhd31Rb+9ivl/ibDhcI5QT1+hfX2/8+dj53Ni5jTle/9upnN/bBEn90uEAo1W1w+WtWpVceP9/qqoyMHKtKpHjtwy/av7j3TNND08oGpWKoRtc1RosJoD8cxDT9CVGFQqkHPRAITng8EQz4VV6BUEpQeyuCnLV2x5hjJ2/eQ6/l13aFQqmgvLk3zrUrN7BsVxcvzGliyZ4uLn5+C0/MbfbatLJBKUFN646xfmYTG+fOxNI0ts6cztNJh+jAgNemlQ1KCWr79AY2VFTgakM1uRA8u3g2dR2d3hpWRigVQ/UEA2TN8e9IyvBjqEKhlKCm9/UTyOyXwkBKmnr97D2FQilBSdPgjHVbRkSl2w5nvrSFykzG7zYoEEoJKhkMUN0/yFkvrWVJWzt1vf3M3dRJNKvOaECxo4yg+sXHxNkvrdMWx3uYNZAiFgwRq67kwYuPo6nTb+UVCiVaeRvFNwyXisxVqzfRr0f427Jj2FtdCcAWy8A5bi7/KZafBWwk9xLFkSsGvbRZVUpaUBvFN2ZL5N+SuMeGsYgT5oml81g7Z19HZlDCtvoqJDwyJpASywF+g1zxpgKbrTQlXuXJP2bhWJcow6l+dzTWjLtKExo7QxP2ll+HWH7FlJpYZpSsoDaKb9SAOFUgcNEJkAZgZlffuGuNbIqsPqkz/vCUGVmGlKyggEGQg7llwZIwA1QS58w1W1m4e1/PeFZK3rHyHlLapJ2bGwphbLlQ0hPsNoqvf9hG3mZjUkc3A1RjksVBZ3N9A0kzQH/U5Ootd+CiUekk9r+FDdQhV/g9n3mipAUFsFF8/Zw0mRVhsvVpqpCjnK6jw0x9NXfNOYc3bfzr6I+5wKPAa5Ar/PnmeaSkW3kAi+THHwYanhOf60tX2lU1MbCxsEgy3dlM2EnQFa4DucLvLS8ApRxDjeEE+bnqn5xylhuNbmcJT7OQ5wkzwMqZR9FW0+C1eWVDyXuo0XRWV/KZK27g6tX3M6d3L2ua5rPi2Auoivt9mIVCKUH1Bi2EGeR/z7hqzHFHDpR2oFhCKFPlAbiuQ1dQBynRHQekRLOzvBSdePGCT/5RykMdt7ebkx5fS1djJSHbIaXr1HfF2NYQBZZ4bV5ZoJSHiqQTJKtChOzcti5Bx2GwKkxlxt+EqlAoJaj1zc3sn/heaoK2ikqPLCo/lBJUTTI2NmHrEAMBf2ezQqGUoAbCUfbUjZ1t0BcOYU4+MOyTZ5R60hUZyc7mOjqrq6hMJBgMBRkIWlT6ixQKhlIeKpN1WbCznXTAYFdjPYmgxcJdbdgZx++HKhBKeSgd97yMaTx8yrotJC2TUCZLR1UFvaZ2m9e2lQslP9tgf171+hczC7sGzJAOaVfwUl2l8+8/HKXUi1PMKCeoYb552n2VH33qQj94KjDKCsrHG5QKyn28xxeUT17xBeWTV5QU1JwP77pp1kd2f99rO8oRpYLyYz+4ufrFaTN6c1vECpCSee17f7Hl9rlv89q2ckEpD7W+oakHTSP3T4Am2NrU/Fav7SonlBJU1rTEyG7okNvMWihVxKJHzac9VOON/OxTMNQaktAYKyafgqOWhxIiJyYpc/8ECNRpdJQCaglKSnAkOAz9kwhXkhRv3Ou1aeWCWlWe64IUjDglAbrrohPyd0UvEIoJSuTSYIxCAlKxYhYzaj3pUWIyHAdHCOoHBxA4fpheIJQSlCZdAtksx7XtpiKT4rVrnydhWQj8VS+FQilBhbJZ0prOk7PmAXDPgqP45l1/RCjW9ihmlHnS01u6Zw8aBrauE02nuP7Zlbxj5RP85phTxyQh85lalPFQzT292bZAgBmJfh740XeYMdiLRDBohnAxSIp3TQvJH/vdB1NMSb+668VtwZ3iM1/6x7T/Ti3s79wzvS/Gl/51J/MGdxCkjxC91GS7cDCRiD0p8a5zvbZZdUpaUEHid62dWfWp/7nwvMCJbduoECbXrXkUjTS5fKw2OikMMmhYuJgPJsUHKry2W2VKVlDrxW1HVdBx/mPTj+WY7k384ZhzaBjoxSC135XukMBAQ0OQubfw1pYPpRxDRTUcXE2jMpUioxss6t4z4biwwGHU9IP6AtpYdpSshwKeGaB++8Kenfxr0YmctHcjT89YPOFQsEMug53EBcSVBbWyzChZQS2RH3EHqT3v4g1rnn7Vqg1YdobOyjAPzVo2RlQuBi4BJDaQ/UtQ/uAFr2wuB5SZU37u9WtOeHberGfP3LmFK9av4u2rHkJ3wbINXEwk/b8NyV9f57WdqqOMoK689pljH1i4YHXKMskYudBQuC6/+v2veN1LqwjJ7/njeQWgZKu8/bnz9ye/EAuHRsQEIDWN1nMuAPwcm4VCGUFNxktNzZjjuhJ8pgq1BDVBpabL3PZnPoVBLUGNE47k0/f/CYHtK6pAKCWoszZvBR2imRSLuvdyzvZ1/HvuyYDutWllQyn3lI/jtWvWYLguDy5cyMa6aRi24Nt330k2t3WeTwFQSlB3LlvG3376E9Y2NpI2DE7cvZtvnH8+l+y4cdL9YX3yi1JV3mOz5/KO17+BjK5TlUzx9fMu4Gvnnu+1WWWFMh2bAOLzKTkmtwGAlMjPBv1OzQKhlIeaMC+GL6WCopSgpD5+KbpaJSx+1Hrc6cztmAKCGoR0sARGNuPvD1tAlIqhAMRXMtW6bXdKgeYirpGfCf3Za5vKCeUE5eMtalV5Pp7jC8onr/iC8skrvqB88opSY3kA+q0DWTcYyJXLlXJ+e09g87ea/fQrBUIpQYnPxTOEAgbZoY5NXYgtTTUZ/P7ygqFWlRcKmGRyOesQgC3HZbTzmVqU8lDYkqtXr+FD9zxOfTzBv5Yt5JZXXwAEvLasbFDKQ52way8fvO8Jfn72Sdz8hkvRkdz6t/u9NqusUMpDnb1xO1f/55vpiYYB+OuJR/HRux722KryQikPtbmhfkRMw/zknJM9sqY8UUpQCX38TN+E5c/+LSRKCWpvVTTXXTAKK+14ZE15opSgNkyvQ0u7VMWTRBNpKmNJ4pbJ4g/u8nNCFQilgnLDdvjqbx7g5B17MFyXmBmg9dVnslFk7oCZZ3ttXzmglIc6/6VtXLRxE7FKk3VzamlO9fORfz5Bvy79QKpAKOWhLnppC59540X8/cQlAFQPJrn9p39HOP7QS6FQxkOJj3d39lWGR8QE0BcJ8ZWrzmVWd68y5Sx2lHnQUaFX766uGnd87cxGVv7rhycnxbudhHiHExdvf5cH5pUNygiquadXX7Srd9zxGd39SCoQmJpOQAtg/O+geLufgWyKUEJQF75z86tcUxdbGqoQ7tjpBZ0VYVwsXAxyG8YaGGhmp/hUeOK7+RwJSgiqqXfgY51Vlfz80uOQ2tgiZcxcu0OO2iY995/7ygKbWRYoIaiO6ugXApksYqK5T0KQ0XX2JSOTSFxc9LsLaGLZoISg7v3pgvtqe+JSTlCauoEBLMdGwyHnp7LYuKlG+aV0wQ0tA5QQFMDOuqjT2Bnj8ufXjRyrSiRZ/tgqdGJAys2SclLor4jIn4e8s1RtlOnYTAbNvo/987H6dz7zPBv/9Si7ayo5bcsudlZWsODK96/cfcdpp3ptYzmgjIeSX6xpOGl3GwCLOro5f/1Wwtks83v6qJKa31NeIJQRFMC66Y3jjmU0g8GA6U8qLxBKCeqbl55NvxliaN0LNjo/OO9UNENPeG1buaBMDAVga4L3vONKLlu9mfr4IPcum89zs5s4Zf2ui722rVxQKp3P+y9+SF797Bq+fMX5dFRGeP3Kl1i2u52rd7zNj6EKhFIeqiqV5tr3vpGeSG5U5cXXNPHxfz7M1R7bVU4oFUP94ZRjRsQ0zI/OOcUja8oTpQS1ubFu3LG+cNADS8oXpQQ10bxMvwuqsKglKJfcPkHDGlKrdCWBUkE5QgJaTkiCXGeUQq3YUkCtdziZSuMOCWhETL6gColSgpK3VgXJpLNId1hM8oQdHUqVsdhRqmPTx3v8t9cnr/iC8skrvqB88oovKJ+8olY/1BB1H+75sO26X0fTEgFDa+74RnXSa5vKBeVaedU3tmebbYyIlNhC0K5rJE397P7bah/12rZyQLkqb17WNaKAEAITmOG4aLbrZ24tEEoJaua7t3do+208LIBKKf0R4gKhlKCmObLO2X9XdMAxlSpmUaPUk25IpekOjm1nJHSNXkPJtkdRotST1oUmXGBtVZiqrE1G0xgwdXTbzwRcKJQSVCxosWggScAZZGckiOW6zOlM8lBNpdemlQ1KCerMlzaKHUvmgxDU9Q2MHJ+RSHloVXmhTAz1l+bbP/DO5x8gnEmT0jR6TYO4oeMCjq4hbu73F78UAGUERTz1yYZ0GpHJ0B4MEDNNeiyLtkAAV9cgk/2w1yaWA8oIKpzIRgdEBRvqx26akNU1mtNZQJzmjWXlhTKCMqNWNKMb2Lo+7pyDgKChTFmLGWUecswMCtswWNDRiQt0Gxp7TZ0UsKUiCP6+wwVBGUHNHIgJKSVXP/kEW3RYH7TYGrRYFQ2yN2hh4Q+/FIKSnm3wgvji9bPZ8sstgTksTO8iSD+7mU0PzaT1CHeesIBvnH8qrqZRozn0VoVdTCMLZIDfAR+VLUbM42IoRcn2Qz2ofS9wMht/uS20gIXJzUSJ8wxnE6Mmd4EDy5/ZSHs0zM9OPZZeYYKua+R2tA4A7wYqgTd4VggFKdkqr0523WJi06k1ECZJD437xDTEIEEu2rAj94tkaPHnGI98tWi1rQKZXBaUrKBsjO0g0KSLg06G8bqQaKQsDSwNNAHjZyLEALsQ9pYLJSuoE+Qnf9hHhbMgs4UtLKaWDvT9tBFlgERzgCbHhaAGjru/qL4mW4yJ0uX7vExKVlAAO1hQn7VlPKAl6KGBZTxHBTF0bGrp5SheYPO06VSnbXAl6NpK4DHgj8BrZYvxNY+LoBwl3cobzSbtU64bzooFg7t5YP5J/PyUV9IWrabfCrI1GqDT1KT8clVJv0ClQMm28vanLRCVW2sXij8fewEPLzsOAAtoyNrsdCwI6mq8OUWOMm9s0gi6GcvkwaXLxp2bncyA6/iCKgDKCKpb0L1nRj2dwYl6ASQ4rj9tswAoIyjTMPo7KiJsDQXGZYTaHrDAkb/2xLAyQx1B6aF392kG/ULwVGWYNsug3TJ4uiJEu6GDZX7faxvLAWVaeQDXXvaMXD1nGhsrwri6hpASzXGpcVw6v13nDw4XAGVaeQB7mmrl/KwtGEyxI2jiCo1pWZuIv+qlYCglqBopeaEiRLw6RGPGod8yMBKQGvBHVwqFUoKKaZrcWV8hXFOnZ2jPzr6QRa3je6hCoUxQDrAjaErXHD8FOB7yt8srFEoJakDKv+CMH+t1JjjmMzUoJajOH8xcbg6kx8x5Eqksbsav8wqFUoICyGbsFfQmELE09CeQAxnkbbVKxYrFjFL9UKMRnxl4DVKsll+KbPfalnJCWUH5eINyVZ6Pt/iC8skrvqB88oqSgnrzq5+NvuXyZ5Z4bUc5olxQ/tVT/yUNw8AxdKxkmq0B68+3P3r+cq/tKheU6p/51CvucyIVkZEMLNlggDnxwdd5bFZZoVSVV287Wipg0R2N0FkZJRYKkoiE+c7Sv8332rZyQSlB9UdC9EYjZE0DR9dJBAP0R0P0m+I6r20rF5QSVE80Om65ecqy0LNul0cmlR1KCcrRJy5OKhpMF9iUskUpQaU1Dc3db6qKK9Fd6WdYKRBKCaquP0FS0wik0gRTaUTWpt80CA8k1eobKWKU6jY46YXtrF/cTN1ADE2CrWmINPQ5zjFe21YuKOWh+qIWVf1xtlTVsKW6BldKdN3BDVnv99q2ckEZD/WJ8x6OBBur+Nexi8iYuWJFUmmuWL0OJ+v4a/IKhDIeaiCd3bmuuWFETACDwQBrmxq4cO368SsXfKYEZQS1YVptVWyC1S0DloXl6HSKz/heqgAoI6i++irR3NuPvl+3gatpBGWKQbb7nZsFoCRiKNFqV5OxX0KIJhzZS8o5UX4xsgtA3NTzH1jyB83RCjGvKoWjjX1HdtdXskSsRpOiNiveJMGVBsabhPzVbz0pjOIUvaBEq63huL1YQ6aa1KOJneLTg9UMpm/A4CuYFh2VFg3rtnHsht3M7uyjoyrK/cctpK8iSFAm0JG4aDiEhMT9DeIts4X85de9LZ16FL2gcNz/Yf8hFVMDXdyJ4ByEBmELVwhOe2k7kWQGgHkdvTQ99BzrF0fQh+Z8abgMEibKIOB+RYq33CbkL/3EB3mkFGKoo8cdEQI05iHQcCUIwfSe+IiYhglnsrzziQfGJCCz9+Uz14DgVBldrhS/oDTxftz9Rk4cCVl5E5INaAKkJGEZTNTbFHEygJn7GCagDQlMPCfkLwfGf8LnSCh6QcmPmS/iuH/GcXNLzG0XUvZj8kuRPyM5A8e1GcyQNXXuXzZ3zGdrs73UZ3uwCTJIHV3MpYouBE4axGXelEhtlJlTPu/D7XJHcw2//MkvWLynh0p7gLmpXeg42Nh0UyVN5MIG+b0tXtuqMkXvoQ6Vme09UgrBLa+9AhlJMj+1g1gwyJPNx5IhQBAx0xfT1KOMh3rla1Ym56Tc4Ak722mIJXLVoxBYTppLOh4iKv/X7ykvAMp4qHQkdNvcrv6cmGBkKnBGD/Bk9WI/QVSBUEZQD/922afb6ionPPfHY87wBVUglBEUQNiRDO63k0JbXRWhnt5ej0wqO5QS1GAkyL2nH8umWU10VlewZv4Mnly2gAZb3ua1beVC8Q+9HAamI0mGAjyzbMG+Y1mbmGX4wysFQikPNb0jV7MZtkM0kQJgWmcvvZGw6aVd5YRSHqqhr4/X3f80i3Z1ErAdOqui7K0K8fTMxomjdZ+8o5SgTMdl2faOkd8b+gcIptNsqQ63eWhWWaFUlRecYE+XilSWqC3bPTCnLFFKUN3VFeO6/RNBk2rJ77ywpxxRSlBrGip/0F0dGfndFYJVi2fK96x5tRrjSyWAMmN5w9x84aOXTO8b/Gc4lWFXdXTr5x87f8HBP+WTL5QTlI+3KFXl+XiPLyifvOILyiev+ILyyStK9ZQD/HT6d3dtmTttxrrGZi7esEYu6ulYdkHbp9Z6bVe5oFQr77szvv/3T7359a+KhcO5A1Jyybp13P27k/zpvwVCqSrvnpOP2ycmACH419Kl/Lyxtd87q8oLpQS1oXE6APO6e7lwwxaqkkkQghfmLYwc5KM+eUKpGOq6Z1dhvxDknU89C0DSMPj6Ra/gqLY9HltWPigVQ62s+opsjI1dj+Bo0BZ2nDPjtyj18hQrSlV5oxJhjKC74OJnRCwUygjKuqnzLw/MHj8OnDR0QpkBZcpZ7CjxoMWHuv7UZBiv+dH5Z+JoEkYS+EjCdoY52ZiX5pUVSsQV9aa2PJqyaezvI+ja5CQlEEgEoEm/yisUSngo1zLY1hQlG8qwoa4aW9PQhsQEEg1/76BCoUQrz/hiUjoOaANpXKFRP5DgK39/gIt3bKCeNtIYslbersTLU+wo8ZAdTYPBLK7IFacrGuYTV1zADq2J9kCU++b4+1kXCiViKDKjFgYLQNforonyoesu5hWbl3JK7zp/LK9AKFHlRW/ulYNSz4nJ0Mfs6tkQG2Tzdz5CBYMAw03A4d5PG/g+0AqEkSs2HvaXi2vnA1nk73ceWSnUQIkqryKTxYoYUBUYt0Xs6zY9TWRfUC7IldkY+hcEbgJ2ARsQyx9HLG86pC8V19Yirr0f2AzsQFy7AnFt2WcVVkJQiXCQTMQCbayY6gfj3H73/6FxsFwZwzrjDOBrh/i1nwPOH/X764APHeJnlUWJGCoRHhpyMTSiTob3P/YoJ+3dTW/IwnIPJfHKmGr/3EP82omuO5dDF6SSKCEo3Xaxhzai+uNvf8Ertm0bOZegmggdE39wDBrgALxwiF/7AnD8BMfKGiWqPGwH4bicsGvXGDEBuISHEt4fDBegHfjkIX7rZ4HRgfga4JuH+FllUUJQ0oXK3TFe++SaCc+3BWrGXL7fvzhwDcjXAfORKya+ybgv/f0WYBHwWuAy4Hjk7ztfbhlUQYlug8hHuqXtQk0mzUvf+28ssiPnYmaQvqDNstjX/b6oAqCEh7KDuVCwszLKVde/ja2VdSR0i9WN07nk7e/mn/OO9djC8kGJoNyJWAgH0AWPzJ/H8Td9ZOTc9HiMmX1J74wrM5QQFFkXuzYErgQpIOPkoiNDkI4EmN/l5xsrFEpUedguwnZB18DSEGGdSsOGkMGJu3YxKxEv/UCxRFDCQzmJTLflyLrrn30aGTb55P13M6uvlzWNzQSzSVLCj8cLhRKCkrfV1YtP96Uv3vqCddWG9TA0te7ojjYymuC56gbmemph+aBGlQfIL1UHFvV2MyymYSxXsqa+0d/rpUAoIyiAaHp8a04iOXWHP7OkUCglKFsaaIzdyFonTU+4wiOLyg+lBLWltgGTBCYDGCSxiGORoj8c8Nq0skGJoHyYqoEMLgIdG4bmQO2J1BAZSI3PiO8zJSglqD/MXNQR0NKNi2K7qE0n2FDTRL+o5fLuT/suqkAoVeV957lrmu5vWOL0uzPoZjqZdANbjegzXttVTigx28CneFDKQ/l4jy8on7ziC8onr/iC8skrSnUbDPO9eX9ea9nu4sGQ+dsbN7z2zV7bU04o18r7nwV/lX1NNSRCASriCUJd/fLGLa/1PXGBUOpB/78Zf0j3Ntaw9IVdnHvfGhZsaCfeXCu+cuxd/89r28oFpQQ12FxnHv/0VjbPa+S+C45mV3M1xzy7AwzzfV7bVi4oFUOFUlnuuuIETCP3nmyrCbNjbgMzdnf4UzYLhFIeKhY0R8Q0jBOxcF1/fl2hUEpQbY3V444J4MV50wpuS7milKC6K8I4+1VuthAkKsJKlbOYUepBBzM2g4aBM7TKxRGClKFj6n5a6UKhlKCmdccwpKQ7FOClmkqer61mTyREQ28/157/qL8JYwFQqpUXTWfodFy6AgEyQqPLMujCwKiupN/UFnptXzmglKAiyRQ94TBtlskL0eBIvs1OQ2dZYtBj68oDZaq8mHh3fFFim0iZOi9VBHPZgHUNBHQFTOLhsN8XVQCU8FApcYMIoEUr0yne9tB9XGUY/GvOIh5YPI+uihA4kvbqqDIvTzFT8oKKi2u2G5gzbWpZuruTJXTyrquu4q9HHQWAcCRSE9gVQd9DFYDSEpRY3mIjvpHrBJC4GJhUIwmSIreY88G5c0fEBCA1geG4HL+3A/FxTRI0IGTaZNzVwBKgH/ik/GzwF4UvkHqUTjUgls924RsGcmTbMh2bAH3YCOyhxKwb6+rGfdTWNaxsFsI6DGYh6xhY2klABJgO/Fx8PnVeIYujKqUjKPj1RHWWwMUGEoQAOHv79nHXNMYTbK6tgVAApMyJSh93t5vzbXA5UkqCmjSvoUQnQYgYURrjCW548kkMJ7dYWHddpvUNkjFG7Uc8cTSVmfCoz2FRSjHUm2F8BnsXEwsHDYcEYb5zxqn8ddli6pJZXGGjS0k8HMTSBNhOrm8qaoI9ZqaqBL5QqIKoTOl4KLmiU8AVNkgXHRcNG50sYUwGaGQ3OhmWtnUBQzsEyVysNac7Rl9Ah74UVFqga4Nk3TvIJbp/AbhYfjborzDOA6XkoUCu+Icx6iXQAEdc8/kk5qdr2KFXsYdE5HhO2b6XZ2Y3I4WgMTZIbzBA3AX5jWq/62CKKR0PNQkB+cfPVsvfGAlq0cmwqyZIW2WYS1/cxCs27mRrdZTn5jaycGenWqsxipTS8lAHwCG99eHG8+ct7OjlR6eexNNzxk6qq06k/GmbBaDkPdQw1fLn85+Ys0x2VVVz2botY86dtLMNhGLrxYoUZTwUwNamWmrTGS7bspNlbV1sqq9hRmyAqCZ4Lhr22ryyQClBOZqB1LIM1lRS60pO74shdY2YYWBksn4rrgAoU+UB1Hd10xcMomccovEUmpRIIGXo/OXu08/w2r5yQCkPRcBi1pZ2lm5uRwCuJti+oIFHmxq8tqxsUEpQkUSa4zfv2yhIcyXTtnUzpzbqt/AKhFJVXk13fNyxYNZh1t4+v4VXIJQSlMZ++5sDji6w/B6DgqGUoGKVYXoaIiOicjVBZ3MFtulvR1UolIqh0o7T31cXrh6oCmJmbNIBA9vQGZTyY17bVi4ol3Dsc+c+4kayGWFlsqQti6wr3U8/eaG/dLhAKFXlAXzuobM1x3ZujhuBWFa6l/tiKizKeSgfb1HOQ/l4iy8on7ziC8onr/iC8skrSvVDDfN/039qSzekz+ruwwVMZyB1ltsS8tquckCpVp74Qlr7yOPPO1c9+AzHJLai4+Ii2CKmIaR7yomyxZ8TNcUoVeW956kXMot3dnBMYgs6uQkGGpIFcg9SZJ7y2LyyQClBmQi9KyTQkbijlgcLIChtfzyvACgVQ7VbAYKRCn543mvoD0WY093OpS88SWUqMcnqc598o5SHsmxHhoJh+sNREILt9c387YRX0EUtBlmvzSsLlPJQTVlbsN9MlT01DcRNE9O1PbKqvFBKUFXJpLRDISFcSX17P5GBNANVASqdHvbIRq/NKwuUElQGIQzb4ZjndlDfuW868HYW0UCbOv0jRYxSMRSmwaK128eIaRgXV9sm/vNPHlhVViglqGAqTTjpTHBGEMXFpHI5Yrnf4JtClBJUd9Bi19x69l+qoOGSIoJDiK1MS3tjXXmglKC6KivYXV9JT00QV5fYusAQWWaxFYEkSQCNkD+DcwopiaC88hPdfQ2OW5U2dPql7I1/ta52/2tmfqjtexcGA8x5YStGIMvOWZUgXRbHOgn3ZdFlD300I9R6h4qOohfUvI+0u3ZVVGy1XYQEM6DXzLqpzU2EA6I2neXcnV2E01nm1URk1WACQzhkEbxq+xNUyxgCG50B4lTQRT2AeFz84Moz5XvvnOj77hc/uVrD/Q8LO1tN4ucC7lwqP+L3ih4iRT3boPGm9tMHw4En0oPOyB54mpRQGcDNuBiuy/ldfcxNpqjq7GV2bz/hgSTLNz5MlMSoO7mYdLOBY9jLTExsNLTfnynf+4bR33e/+MkdOu6V+6J2ST3xJwWcv1R+JFWQQpc4Re3/ZcB6zErYI2ICcIWgMp7729qaxoP11ezQdeb29iGAyuQgQdK00cgOZhInSi4st6igD4nOUBB17ePiB9XD971H/Gz6WDEBCBJYpwPXTnFRlaG4BSURujM+z4Vw9nnVrKaRFlCRzm1fltYNXuIodjCbNppZy1I6qEcgGaASYGgmggAYvYfewon6E+yc/BbkqUjKU9SCiunSrZpgR/Mad19fk+m6WLqGFBLTtYnEJWmCY67fzXQEGbSh7oTcXCmZPlO+d+W+q8RTLmJcrBQiDfDPvBSoDChqQWW+Umv0WwZHJ5JYrovpSpYmU/SYuV0RdCk5r6uPhckMf1h2DHP6txOvjoy7jz3U9qikhyj9OGgZYEz89Er5tpSDdr1LTkESsMikLdwPL5UfeXyKi6oMRS0ogIidumdnY5QFwmGJzNJRH0bLxPdGLGQoKOT07l5X6+tPpnTp3n3U6XQ0V4/LwFJHGwJooI0qutxXyPcFJmrlXSjf8fssZshGW1JJsrmKVOVS+ZFvF6SgilDUrbzRNHyqZ6+tCbf3izUzJjpf/en+8Ecee2HQCQZobOtj8fo9hJMp6mljPi8NV3NsZLG7RH7F79ycIkpGUIfCf533kNSDAaS2z/Fe8uKjHL9rK2H2kqSabirc2fKbvqCmiKLv2DwceqorqB9MMr9zL46usaRtB8ft2gE4DNBEghoc2v2VL1NI0cdQh4XQ6Kys5IElRxFKDyKly4b6GSSoIUkNLo6cJ39wmtdmqoxSHsoGhIRj1m2jLxNh0+zpbJo9jevveYg5/Vvk4uyX1XqBihClBBXIOhy/bjPGUN/V7L2dWNksOyvrmNG7S51gsYhRSlDV8cSImIZp6uqjriPJgDu+f8on/yglqFB2aJdXKQkns1hZl0TQJJTKkhWmt8aVCUoJSho6WVNn1q5+osnhUZQk0nBB82u8QqBckJqKRkaJKYewBcL2N1MoBEoJqteyZHhwoinjgqwRnOC4T75RSlAJx+6xTRd3v3koBjbpbNpfOlwAlBLUdx84p/6FWdPcSrMXnZx+LNLIYIZL3I8EPDavLFBKUADfues0vTsTucaxstIMxYmHNNktK/116AVCqcFhH+9RzkP5eIsvKJ+84gvKJ6/4gvLJK0oNvQBc+OYNqWXdscCgqctOy/zUX/90zFe9tqmcUKqV99bXPOteumqziCYyuAI6Gqv454LmX/757ye/1WvbygVlPNTC9++Z84GNe8XPzlrGyvnTqEhleNf9qzmhK3Y94AuqQCjjoRZdv2HAioQia2aN6sOUkv+47zl+eP8ZfpKxAqGMh7Itw9w2vW7sQSH497HzvDGoTFGmlVc3kHDFBM62MZYsvDFljDKCGhDIc1/cMeaY5rpc/vxmjywqT5Sp8uIBS77p7hexXIcnFs+gZiDFGx9ZQyjjz1opJMoIqt7O6E3xQW7493Pc8O/nRo63V/jb5BUSZaq8hGYhJ2jLVSX9pL+FRBlB1WZscyCYS/MzOjbf2VjljUFlijKCmjaQYMOMGnpqAgxUmwxWGKyZWYtraPSJG67x2r5yQQlBhT6w+6kT9/Zqscogzy6dxebFM9ixcBrz7F4CWj+S0B96xLsf8trOckCJoPycrr5Tf3rGMRyVyjAnmWYXEEynyVgmr37+MXQkksA5XttZDighqI5QmGmuy5yhAFwA6UCAjQGLvmAAnSwSjbh4vzSJySzS0Uju0qg5NSR/3OWt9WpR8lXe0e/YYveEQ0xPZsady5omzzbOp5PppAhjYZOhURhEDIvoXJPBzkFxXapf3OSvU88TJS0o4xPx/84GgrpraPQGx2sipWk8PW8OIBigmgxBNByyVJIlisDAwg0YJP9VeOvVpKQFNSM++K6+gMHumTU8M7uOlL6vOGlNoyMUoCaxb0eFNCEEuSXpNgFARwMk4qwCm64sJS2oPsvcrgmBJiWxgMGf5jWxO2CxNRpmXVUUzXG5atXzI9cbZHAY7clcJBKB7Cu48YpS0kF5LBi4YEZ/X/tAxiaRhQFHMHvTLmYk0kgNLty4lmnx3O6eFkl0XOSQoAySQAoXgYDlHhZDKUp+gp349GCVCGl9cjC3u0Iga/PRe1Zy2Utb0V2HaneAuWxCAhILicQggcagdMlkoXZJSP7vNk8LoRAlLyiApe/bIdfXjNtCj5//4Xdct/lhXEySmOj0Zyvkry0PTCwbSjqGGmbG3h476I6dpjKnr5dXbV3DU1Unk6QCB9vxxTT1lHQMNcy9d55gXvq6Z90lfZ3i4TnzaEomed8zTyNcg7XN01nav86tkz9UoqzFjjIPuWIwLY+NJYSdcHC1IP885RxemruYp+qbefe6q/2dEwqEMoLKGJZ8atHRjFbOzvomXMvvBC8kSsRQAJsaK5yJ3FDG900FRRlBVcaTbnaCGZsxU5kilgTKVHnBZJbqjMNdc2ewJxIk6LicsbeLpoS/93QhUeb17aqt4HeLZrO2Jkq/ZdAesvjrvGnojnPwD/vkDWUEFUpmaQ+PzcvqCsHz9eM7PH2mDmUENWAwYf4CR/hpDQqJMoJa2pP4Re1+k+w0KWnsi3lkUXmijKD+/O9T3+OkM0yPJQjaDhXpLKfu7GDhtvbswT/tky+UERTApWu3fcB2HWzXJS0ls/Z0ur9+6Cx//K6AKDHbwKd4UMpD+XiPLyifvOILyiev+ILyySvKjOUBiFZbAN8G7pctxh3eWlOeKNPKE632XcBl+x3WZIuhRgFLBJWqvP3FBOB3ahYYJQQlWu3JlpL70+sKjBKCAi7y2gCfHKoIyqdI8AXlk1fUFpSUiK9nleoaKXbUFZSU4EqsTCZ7xntWS/H1iZYw+OQbNQUlZS63tBBkLIsnFyzl9A1r/MnlBUBRQe33uxCsnTnH91AFQJX4Quz/2/zODv77zl8zu7+Dfyw5jj8ddyozP9i9fdf/WzDHIxvLAlUENRYh2FNTQ0Oik+Pat3Bc+xauf/ZhvnjOq2fBAq+tUxo1qzwgZVqc8b4vcvH1n6AjXMmMeDdXrF3jtVnKo4qgJo2P7pl/LDde9lYk8PSs2QU0qTxRRVATowmuWfMk16x9ioQZ4qVpM8SW6hsdxPJ3eG2aqpT89BXRancDEy4PDqdTDH5m34boGU2nLxClKpMg4GTPRa54uFB2lgsqeKhJ15of1bF7zO+W61CdGiDgZAF+MsV2lSUqCGpipOTWu3837rDY10nlZyKbAlQQ1MT5eoRgZ3Xd2AsNk1ggSlbTAT469aaVHyoIKjrZiQ+99u187bzXsr5+GvctWMarr/8oA1YY03W+g1zxp0IaWS6UfFAOIFrtiQsxPKY3RHU8Lnu/UKvCS1S0qPJwJxHU2F+P6thZAFPKG1UENZ79Pa/jUD0w4I0tZYSaY3nDVZ2UWI6NaduYEtZWT/MVNcWo4qHGuiMhQBOga2QsC6EbfODhB9j63fmV3phXPqjpoYYwMlne++Q6mge6uXvurNJvfZQASgtKA1YcM4+GeJRLNzzfCCd6bZLyKC2ojKHzlpX3vP5rd1/zR1jktTllgSr9UA6TxIOyxfCn/hYQVYJynyLBF5RPXlFFUBu8NsAnhxKCki3GUZOdKqghPmoIaoj2CY41FdyKMkeJVt4wotWeDzwOrJMtxnle21OOKCUoH+9RqcrzKQJ8QfnkFV9QPnnFF5RPXlFucLhTvDczQJMp0AiQQBDf1Cy/548MFwilPNQO8b77XKrNaXQyjXZqiANVC+PiQ9O8tq1cUMpDZag/v5oYm/Q5DIowNbKfmU4b/Wi7KxR7eYoVpQRVQUqsMo8hLSwM6dKtVRMTUWbYuw/+YZ+8oJSgukUdAcel1o0hAAdBl1FFBT34dV5hUEpQaUJE3X07o+tIap0EGX+HjoKhVFwRkslxx0zpIibPR+aTZ5QSVJjEuGMWCULYHlhTniglqAwWzqiEPRLQyaD7u5wVDKUElSSMjUEGgww6GQz6qMfwBVUwSj4o7xcfsGy0BEgcFg8dFciRuEmSwfLKvLKjpAW1R9ywp57UNJM0AO2ksTEYnRRYxyEwdN5n6inpKq+W9DSLNIKchBrZhYHNUKYMNFxmsR6D8a0/n6mhpGds2uJ6GaORLCEkNjpJauiijTk4WNSznTADfPvYa+kM1vFiY+Xf//K3U199wvt3zYlb5mbhSrnp9mY/12YeKVlBbRWfn2/AZomBwMUkRR1t6Lg4CPqpJ04V9Wzjovd9glDK5bTNe6iKpeRjS2aK3Y1VSCGoHUgSaet5/g0vbl8dHczerbv8pb0pfDQwC7j3Peuu6j2YLV8/64HTrXTmZuHK1Qjx+Q89c0lpPtQ8ULKC2iU+6UoiQgASSRPbMXCwMUlRMVThCZ6asZAttdORUqI5Lt2hAP/1xouQIhdnCSl53VPreOs9LwDgCpzemoBumzrAIHDVe9Zddc9kdrSeed8fqrv6r9EdF4CMZabTocCcG5+9dKJVOMpTsjGUhibEyM8OBg4SRsSUOy45bfdGIpkkQgikodMYTzKjOzZyHykE66fXjfRdaRK9Ij7SzRABvjOZDV+94JH5kVhiREwAViYb0B2nbHOgl6ygAgyO/OygYaPjoo+IaRgNaBjcJyBX11jQPrYWkwLioX1dC4btjj699IdL75isNbzMyIzvhdcc97hDLYdqlKygNKQMMiwUQRfNCFzAHXdtb2hf5mnNddlRXzXm/Olb9lKR3DeonDXHPJYn3rPuqsnGbp7KBM1xMYOja/cfYjGUo2QF1U9IVrOXWrYTZJA+atnGIpIExqw/X1M3k/5gBABhO8QDFudt2MWS3V0s3tPFOx58nv5IcOR6RxO98QpruM7bDPzHZDZ84v6z2xOVkc9nrFxDUQKpUKBNatq781zckqFkg/I14jZrDhvTEWKAy5OcRRgbiww6LhYZVjdNY2tNEyHHxkZjTWM1gUzW3bRktra4qw/NhSdnNXDqS1tvOHpvb091f+bFK3vftOaHS+9oILeMfc171l013uXtx1cveCRqDaberjvuMx965pLHp7zwRUzJCgpgh/i8bGQXQZK008hLHMNc2sdMV3FxuW9xMz8+81x51N7uU3969+nPAJz8nm2PasgXn/7hvPd6VgAFKWlB7RGflgPUYJJBIpDYaBPsCWTSLmfKb5Vs9V5KlPRDzpD5dpg+NBx0shhkcYEBgvQRIYmJBLIEvDa1bChpDzXMbvGxP+jIQRv5tl0sIouGSZYsAaIkqGKXnC+/XtIvT6mghKCG2SBulQ6CBazBIs0gUTZwLCH65FL5JV9QBUCphyzRWMJzWEPTVSIMsJTn/UUKBUQpQTXQjrZfFsQQCWqJTfIJn3yjlKCMCVJqSgTmqGEan6lFKUElqMTZr0WXJeq38gpISU8B3p80MEgjIeIIbFxMdjIXDX9Xs0KhlKDCDLCBRdQQwyRLjArihJlOn9emlQ1KCQpsOZNNYitHDa16kcxmMxm0g47H+eQHpQSVJPKaCuJ/O5FHSRIZ6osKU4E56c7pPvlFqaB8tvzG3/uoejJNJS4GNhE5QOCGKvntlNe2lQtK9ZT7eI9SHsrHe3xB+eQVX1A+ecUXlE9eUarbAEC02kuAtaMONcoWo8sre8oNpTyUaLXfA6yDkfwZAugUrfZcL+0qJ5QSFPD9SY5vKagVZYxqgposO6uftbVAqCYoH4/xBeWTV3xB+eQVX1A+eaVcBOWPgBeI8hCU64pLr3v+oUvesanCa1NUR5mectFq/3Ci45WJBP+6/VvM6I2f01ulx66/8tXyV3eeUx4vkgeo9GAnzMm0dG8Xld0V7Awv5P55F3J8T1y87qon/c1fpghlJtiJVttlkg7M7/3krzx64jGkrKG0h1JS27XH/cHfXuEvKc4zKnmoSVk7d+Y+MQEIQVfdtLIoe6HxNIYSrXYCCO13eHiFynrZYhx9GLeTTOChKpJp6gZS7Gkae1yXDhdf+8QnAoGGb/7tlwvG7C503vt3iofmNocQIik/ZqjhwguEZ1WeaLX/Crz6EC7NABY5of0duFq2GCMCEK32x4CvT/hJV9LcFePknR2EguExp3ZEApyxtxuJS9pO8cNlS1iQSLB5eh2j9h1Cz9gcu72D5+pqniJlnym/XTuyJEu02m8DbiGXPvGPwI2yxYiJr2f/E/g4UAn8EmiRHzfHLJT46PIXXinhm65g2Y6qiLx3blN/3DSrpIaBC+hDRkhJIGnT0B2XZ+7tyUaC5k7gcz/91cJfHcKzKzheCurlfvF3ZYvxwaF7vAH47aRXSgmp3N9/TjzJ4v5BDNfFtAc5cftq+mqWjVy6MaTxj+MXjfdxrkTL2CzZ3M7aptpfy69WXj/03WcCjzL2Ez/HlX8C/rLfXb4hP25+fPiXjy5/oZncDIgR7/zg7AaemV4HtgvmqNBu6O8TGsxQ05Pg0vZupGVI4PSf/mrh0wd6UF5QinHENaN+vvqAVwox8qZvrwrx75l13DWrgccaa7np8b8i5L71nye2dU9yD5CGxgzpgOOO9qjLGS+/5ZPYtP+xV7FfVb+nYsiDGvv9SYbS+6dCJlIIkrm86GLou4qOUhRUz6if2w56tQACGlg6BHTQBF1WlFVNs9FdZ+SytH6AGS4SHMcFIfpHHZ1o6432AxwfzTi7Q1ln5LsmQnNzJ0Y1LQ5edg/wUlAvt8prGfXz7cDkizhdmfNQw5t4CDEiqlvPvxpbzyV4tdJJ7lgyN1e9jA4BpARXEk1meKayEgzto6Pu/lNg+37l+TzwXaBj1HEb+MJ+lt0NjEk/Hc0MhYXOfqvmh2yKxlJUJ1OISABy1eUvJi23h3gZQ80Adh3kMpfcXisnAUngv2SL8dQE9/k3cNS4T9su6BO8M0mbqkSCD616koQM8dDMRhkLhZMJ3QoFdE3sqq0gY+gI16UhliRuy7aBUOD18ksVj+z33bXA24FGYMWwbeLr2eah4xXA7+THzdX7m/DR5S+EgOv7AubZz06rDTzXXLO6Kpaa3xcOXI90LTRNIJBmxs409QwMzOuMbVjo2E9Jw9gO/Oynv1p40F2yvMDTjk3RagfJ7fg0XN/YssV4Wfu5TtixKeVQZ8J+1dlAltN2buTJnxznz+TMM572Q8kWIwVTmABzaPMzpMz9LCVkHM7euo1rn1kFlO0eP1NGKQblh48kF09JwNB4+6onufPok/7Xa7NURCVBHVIOqKPbOum2Mjfe84fjJt0UyOflo8z0FaAOJklVJyUB20E4Dg2723/w8VXvmXRTRZ8jQ5nZBnDg3nfZYvgBeAFQqcrzKQJ8QfnkFV9QPnnFF5RPXvEF5ZNXfEH55BXVBDXZapbnCmlEOaNUPxRMOEjsyhbDX91SIFTzUMgWQwMCwH8Bli+mwqKch/LxFuU8lI+3+ILyySu+oHzyikrTV0YQrfZRwM+As0cvCvWZepQLyifoNnBki6Hki1OMKFXliVa7j/GLL3XRap/sgTlliVKCIrdsaSKeKKgVZYxqgvJnZXpMUcYWotWeDWxjn0AGZYtxJPsG+0IrEEXnoUSrPYvcEu/RIogMBds+RU7RCYrJN/rxvUwJUIyCmnQwV7TavqiKnGKMoYpPNGL5t4APjToSB6qRK9TqxMsDxSioAzFppt8pQyx3GO/JKz2xpQQoxirvgIhW23wZH3t5nkQsP4cDPSOxfO/Luq/CFKOgDvbHv78gVuS49yDnmw5yvuwoRkEdjLO8NsBncopRUAeNS0SrfXkhDPE5fIpqtsGBtteYgCeAAWD+8MeBeZNc+/JmHIjlWfLbcLHJpYGsJRfU7wL+BiwjV45/Ap9Erij+KTdieS25fKLHAw8DH0KuSBVNK0+02tdweK2mM6bKllHkuxVnAHNH/V4NHDPq92XARcCJef7e/CKWW8BmcvZDLr/pJcC8Yqry/s9rAybAi+dzAmL54WxJ4gXvZJ+YhpmLWH5uMQlqKvt0iqdePzRqvTbgIEyf5HhR7ch05xTe++WK1QshDlD887e+y/hnkwHuLBpByRbjmoNfNQYHSJMr2FT94adihsNoW11gL7nU2pLcLItzkCuKe4NIuaIduI7crhaS3K4OlxZVUD7ETGAnB/coW2SLsWD/g4fZSswPcsW+7xPLNeBoYCvwOiCIXKFmtmG54nfA7/Y/XFSCki3G7kOcUbDwcG/9cuwh18w/0DMae1+5wgVeHPqtKLcfm2qKpso7DBzZctibIr7cchZ31VOEFKOgDiaWYw5yfiJebizUcpDzxd8BWWCKUVAHrPJki7GuUIYgV/zgIFdMtsqmbClGQR3IQ338AOcOxJEE6gYTe7g3IldkjuC+SlJUQfkQB/rjtxbMimHkCoep3OBIMUrKQ72MYNynwBSjoC6egnv6QiwQRSco2WLcCzwywakjGTD1534XiGKMoZAtxjn5vmWe7+czCUXnoaYI30MVCNUE5UxyvKugVpQxRTUFOB9MsGeeHEo17VMAVHzQGrmOSElul3UVy1i0KOehfLzFf3t98oovKJ+84gvKJ6/4gvLJK0XZU15MiFtTVjidSgnLEllNoDm2mwoFQ/Jm05+6MgG+oA5CyLHTi2JZgk4aCXRFLG2LlkmD6fe+T4Bf5R2A2o93/OecWBqAjohFPGDQMJih1paILyYLPzerBPAFdQDiAfNLCUPj+elVbK2LsLaxgvV1EU7e1g5wk8fmFSV5qfJEq62RW+16AvAScIpsMSYbVysZXM2o2FEfALGvduuLWMzq7IYlM/wqbwKO2EOJVvsicoOypwImOVHZotW+7kjv7SVn/8fqT7tBc4yYABDgSheRzYyZZy5abXHVdSt/87lzf/vIL074dk0hbS0m8lHl3TPJ8WLMpnLIpHXjnVXp5ITn1jfXIfV9exkvu3HHqnc+/aK7JGG80a2Yf5Zr1fZ877jvxApmbBExpTGUaLV7p/L+U8nWyoaKWCAEE4x1Pj5/BgxN2rvmbRtrjuuJnThoRnh4egOrGht4YfoJHDPQX/HAnJvHLZdXnSOKoUSrnTjIJdVHcn8vGQiFHKnpOUFJmZuiJ0G4LkFbYqZTQJhduvaUEazk0Rn78rduqOxmSftizMG9a87P7dBeNhyphwrlxYoiJKMJV3dshCNzk2EcwIW5XX0EnCxH7+0SAM1dXc1PNjeAISCkQVhn4/QG7po9nZrBuHXbOXeEvS1JYfG7DSZB6lqllc4i9wvKd9ZUcdHGzeytikiAuQNdlm1qYGn7AnhdsL25kU9c8SYea27sKbjxHvKyBSVa7f85xOtWvdzv8BRHpl1t/PpO3ZWkTJPtNblV6L2VIT3kjh+F2dTQzPq6aayb2Rz4zum/OnfK7S0SXvYEuwmm2h4IydiFAt+XLcYNL+uLD4JotQ1yiciGX5ZdwOzDXSRa94merp7qaF00kWYguC8Muuq5l7j7uMUkNZ0vPvBo9PG62oFkRHDf0rE5PI7ds531FY1kIkEuWLPRve9nR5fF6uOX5aFEq/2uw/3Ifr+/bwr3v8sytlwzeRlZUqpSqagmYDAcYFFnF0fvbeei9Zt5ZMlcZvb2s7ijk83R8NrXblrHzpoammL7arZANsM3/v4rPv/w3UTSWY7d21E2ocVheyjRas8g99bnAxeokC1GYsiznEKu5WkDK2WLYQ995xVAFPiDbDFc0WofR06k84FryaVhnkWukTBZD/Yy2WKsOVTD5n1odyJZWRFqr5wgppaSy59fQ1dYENF0Hli0BAAhJRdsWMsP//gjFvTt5t+Lz+T2c97I0o5u/jq7LrV+dvMaNFFJbkuPwFA5A0CS3AhDF7lc5feQy+ySHrr2BHK5NxNAPbCG3L6C9UPHh3c73QpcDoSH7tMwdE2Y3A6pGrmdtALAM1OxtP+wBCVa7ZOAZ/JtBPB54F3kvMkwO4G3AX8A6oaOpYB15B7w4ZIFGmSL0X8oF8/9cIecN5jkgYXTx/eWw0j/lBCMCdyFlLz2hecI2Bk2N07HkBFO29XBvxc0s66xel+/1kT3LCxrgMtli7Ejnzc9XFc80RLxfPBZxooJch5nBfvEBBDk5YkJcsNCNx/KhTM/07ekNpWgMp2FgQxa1qZmIM2snkEqU0O155Au9m8FSiF498oHePvjT7FkWw9r68I831xNerhj3XshDXM08NV83/RwOzYL3e9Unef7HdLGQ1khbtNtm6dm1CMsHVfo9AZ1eoMWC3sGiGRsugMGGcvAcFxsfd97ado2p+7cSsNAnHnbHN7x4LO844bX0TZR1ek9ed+N4nA9VDrfBhyEeJ7v99ShXJTVxKqEadIWDSP1sY2zTTUR5nUNkLEMQlmbV25uI5TJea1IOsX3V/ycpoEYGhKDLNP6Brjy6XXUxFN5LkpeyHv4crge6kLg0XwbAXyTXLxUP+pYJ7m46v/YF3Ta5ALPRS/jO1zga4dyYW849GWtMvsZXBf2ExRC0BkOgJSYjmRxzwDHtHey/Lm/sKx9N5XpnHDSBEgTBGBOVz9NfYN01kQmHBv0iG3AJ/N908MSlGwxHhOttkUua34+cMkFyj2i1f4v4BxyLZJB4BHZYiRFq11Fbm+RKPC9oe9+BbnW3InkWnlLyFWPB9rt8zeyxTikHAfyZjMpvhTZY2Xt6RlNGxf37KkMQcbluJ1b2BsNM20Anp91Cgu7eyCdIkmY3cxluME5oJtEsl0ObkMnggBCRMllxRtWlwt0AO3AXcDfh56DDcwBTibXwhskF1OuBtYOlTlNrkXokHvZrhx6Vn8m18qbQS723EauYZIg93d/eLgVnU9eVsemaLVnAUfaOpCAIVuMvPVHiVY7yiTVpGwxDisaDn42tnVuV3zu+pmNE9xMokmXMzesW//iwkVLTt/dw4y+BN1BizU1Qd7x4HNcvnojA0GLVQtn8T/nH89JO7ZHf7PirMGXVbASwque8mdli3HSy/rigyBabZ2cFxuOD7NAVLYYh+VV6z7dGzv3xV0Vd5y+GMT+oabkohfXcM+vTxBn3rBBPjF/PkjJ3D297I2EsQ2NpnSKq17ays5okP6A4z74s+PKoqf8SKav3A1cegjXpWWLETyC7zkshqYeH/Efb3p/0nhk3rRcdTc0dQUYSsXhcu/SpS5Ap6HbV6xZZfz96JPY3VTF5Wu3c3RbD7GgxenrtnDn8gv4r3v/dSEcd6QmlQQve0hAthiXHeJ1BRNTPqlOZemui+QEpZNbNWUK0AUYOoicwl65eYNz072PYdoOWUPnL8fO56sXn8Ldi2fw5NxqPnHf39PvXfmmB70tTeEomzGmw6U9ZGyTukZuVt3+88oFw821nkiV1Ahw44PPMac7RiBrc/SeLt64agP3Hr2URQP9ys4Zm4gjXfXioqgobd20cF2Q2vinlNOSBGiMJ/oHrUBwemyQGx96bt8lSK58alXfpWtvLJp+gkJwpGI4mCBLdy8U14miaeBK2G9iRDCZBT3XPZ62zNN6QqA7Y1eNJUyHE7p2lUfgNIojEtTQaPWk6+9ki2Edyf29pDccWsdw8jtbQtqBrAtph0uf3TzSQfmjv5y3Y01tU2cslESSwRYueyp1tkaN2HUbP7DTwyJ4Qj6qq2omTtv8wzzc2zP6qiOfMm0HrKEGowQcydyOPl6xYTe4+7zW1x68onHFsUfP2By1dj3XENqzekZz00/+fmGVN5Z7S95SIopW+6/Aq8j17jbLFqNkl1ANU/WZfil0nf5IAM12OW3THj761yf4v3OP4cElM2T3l2uVjB+PhLxlX5Etxmvyda9iIWYYWwiZ8y98fguXP7uF6kSKFWcs5c5TFnP6xg0U/+blhcdP53MAqtPO+/oDxr/uO34+9x0/f98JV+IKke+ZEErgu+wD0PuVyn8v2Nk5doaAlEzr6SdeVVE/+SfLF99DHQQ7FKqbsbevI1UR0F0hCMcHSQSsN71025zS7RKZQvw85T55xa/yfPKKLyifvOILyiev+ILyySt+K+8QEa32f+O47wa2o2snHO4M0HLhsFp5t1y+8mHg7FGHtt961ylz821UMSFa7TBSDiBGTYqSEiTflR83P+ihaUXJIQvqlstXrgWWTnAqcetdp0TyalURIVrtHeRWMY9FShBCm4r8AKXMIcVQt1y+8j4mFhPklvuozHgx7ePkgllRIhxqUH7BlFpRurzRawOKjYNWebdcvlInNyXlQGRvveuUkp1MNxmi1Y4MxU8HuiwqW4wx6+2GcrfvlC3Ghik1sAg5FA91KEIxb7l85ewjNabokLLtEK76zvAPotXuHlqveA+wXrTaUrTatmi1VQ8LRjgUQR3qatefHYEdxYkkegjpd94IIFrt/8fEE6R0YFC02r/Ns3VFyQEFdcvlK89n8oxw+1Pye7uM42Alz+UwH36GHzjI1W8QrfZVR25UcXMwD3U4CanKbkI+AFlXE6326kPMqvKHqTbHaw7WUz7/IOdH0JMDFwFsCX3g2wGcD9nCwEFjWnLXthCcglzRfUSWeoGcYJHnaIQAUzOR8thDzEyn/MjEpB7qlstXCnLpYA4JJxiZvdt4xzPTMj0fmpHqYE5yD/OTu7DQ5qaE0YVY/tq8WFxIDkUkh5niULTar3+Z1pQEB6ryHjisOwnBrvp5J4XcsUnudFwsadOrhe44bOvU5PdeGzCVHEhQ5xz23QxYW7mIx+tOYXt4Xw5WDdCKKFvpoaB9JXXouZwO30s9f7j2lArj6vRbLl/5Q+A/DvtGTpYBGSUWrmJXeDprqpdyTN8ajut9ibgZRToOXq18/PHCFRq5rG428G5g9bs2LX/gQJ+Rhj6VfUfHiVb7IdliHNKWHaLVNmWLkR36uRE4idwy/+uAGPBKoJFc+YZzYzUM/X87uax4n5ctRl+eyzHeViklt1y+soZcIvXql3OTmkQv73zqV1SlciuLBvUQd868nKQeAlwQuW3CLCdNRg8MIsR7yDWzTyaX0u97wCfftWn5SFPpxwtXzCK3+vg8cg/qj8CN79q0fGDo/FHAX9jXcDjSuV2/AG6xBd9F8qr3vfc1osSc6uGQJZcw3wH2AP9Pthg/yseNh/8IL3EEKZzP3/TIiJgAIk6S89sfGUrWtS8/dyaXKioC/JpcnswAUEkuf3jL8Od/vHCFAP4FXEYulXUF8A7gp0PnI8CTwEKGa9Qj563A44bkClcIUUTJVacCE6ghlyT3OOCHotXOy7ikdsvlKxuBaUdyk+Z4+7hj01IdXL/5N+Oz3k7+1r9v1M8nMfHshuU/XriigpzQKl6OrQdhGsDfT14EmlZMGXsLwVvycRONPOQerxscvyWcAEwcDHnIiWYHJvl5NFly1d/BdhI9IgLZoU5/dau8iZjsmR8W2q13ndIPPH0kN/nlSW+Y+ObAsv51Y44J6cLEsxc+N/zDuzYtXw/8Y4Jr/uddm5YngX+TS5Ocb54AuOz5zeXmnbLA7fm40XDscQZHIKrt9XO4d8HEvQyn9DzPhW0Psii2iekDu5GIx8glrv8qsHHoey9/16blf97vo1cDt5DLx70auIGhOOtdm5bb5AL6/wP6gT6OzNOmgTeQS+z/saxW8LwFNrlsgIfP8J7Ih/EJYDNwL3A/uYbPmbLFyMuGBuPmQ91y+cqXlYNcuC6fvOebBN2JV2hLQMgVJVOHiG9k5RRXeZ+WLcaX83Ej0WrXkUtwv4N9W6a9jlyD5tdAb6GmKk86we6Wy1e6HPpMAwA+cffXCU8SM9loGPKPpSOoVvt2XPc/0aZkpdlPZItxuJtYlgQHelqH7QJ1ObnXbgs2ltT0Ftli3IgzNS+1qmKCAwvqsOeRtwdq/kOSq95G/+vTo2Q1c8nLM1E5DmkDo1JlUkHdetcpNnA4dfyq2akf/e9QavitAqQD6V6z8pJq+xdi3uB/bz5ycwuMm38PJVuMT+T9pkXEwebn3Ad86rDuKFe4DA2HGJR40kDzkOKnBPA4cNEhXDtVG3cXDQd7YoezQZ96CbgOpZXnyoBsMV7JxJmQ92dKNkwqJg4oqFvvOqWPQ+9AvOkIbSlNtBHRffggV94gWwxlp60Mcyg+/RWHcqNb7zrliSO0pRh5+hA6De8FkC3Gf5Pb9HB/fgvossX4fp5tK0oOZY7zoeQb//GRGlKUCHEm8qCDka8a/kG2GMun2KKi55CSZdxy+coDXnTrXaeUTIfl4TJpj3luAcM7ZIvxs4IbVcQcajfwRAO15cKBXqayW2p+MA5JULfedcoVwGcnOb0yf+YUIUL834RxlMxt6l14g4qbQx6ouvWuU24l13s+3Jcigc/fetcpp06FYcWCbDGuR4jkiKiGR/c1ofRyqJeLn6f8EBGtdpDcFJrtwB353M1dJf4/c3xqhoesEnYAAAAASUVORK5CYII=" id="image33286f1e40" transform="scale(1 -1) translate(0 -578.16)" x="411.84" y="-43.2" width="106.56" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature10_fold0 -->
    <g transform="translate(156.305031 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.958531 638.149 
L 535.588031 638.149 
L 535.588031 27.789 
L 527.958531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image7c03ab1e43" transform="scale(1 -1) translate(0 -609.84)" x="527.76" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(539.088031 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(539.088031 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(573.488969 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p0c50bed52f">
   <rect x="409.392031" y="27.789" width="111.592" height="610.36"/>
  </clipPath>
 </defs>
</svg>
