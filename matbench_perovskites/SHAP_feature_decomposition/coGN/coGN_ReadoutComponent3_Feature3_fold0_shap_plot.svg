<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="771.609375pt" height="679.5765pt" viewBox="0 0 771.609375 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:42:47.435498</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 771.609375 679.5765 
L 771.609375 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 403.174375 638.149 
L 520.094375 638.149 
L 520.094375 27.789 
L 403.174375 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 427.450121 638.149 
L 427.450121 27.789 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 403.174375 609.084238 
L 520.094375 609.084238 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 403.174375 580.019476 
L 520.094375 580.019476 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 403.174375 550.954714 
L 520.094375 550.954714 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 403.174375 521.889952 
L 520.094375 521.889952 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 403.174375 492.82519 
L 520.094375 492.82519 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 403.174375 463.760429 
L 520.094375 463.760429 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 403.174375 434.695667 
L 520.094375 434.695667 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 403.174375 405.630905 
L 520.094375 405.630905 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 403.174375 376.566143 
L 520.094375 376.566143 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 403.174375 347.501381 
L 520.094375 347.501381 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 403.174375 318.436619 
L 520.094375 318.436619 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 403.174375 289.371857 
L 520.094375 289.371857 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 403.174375 260.307095 
L 520.094375 260.307095 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 403.174375 231.242333 
L 520.094375 231.242333 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 403.174375 202.177571 
L 520.094375 202.177571 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 403.174375 173.11281 
L 520.094375 173.11281 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 403.174375 144.048048 
L 520.094375 144.048048 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 403.174375 114.983286 
L 520.094375 114.983286 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 403.174375 85.918524 
L 520.094375 85.918524 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 403.174375 56.853762 
L 520.094375 56.853762 
" clip-path="url(#p62e03ef05f)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m84f3957eb8" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m84f3957eb8" x="427.450121" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(418.703402 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m84f3957eb8" x="483.402879" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(474.65616 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(338.952969 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_range_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(11.973437 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-64" x="2091.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2155.130859"/>
       <use xlink:href="#DejaVuSans-6c" x="2216.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2244.4375"/>
       <use xlink:href="#DejaVuSans-65" x="2305.960938"/>
       <use xlink:href="#DejaVuSans-76" x="2367.484375"/>
       <use xlink:href="#DejaVuSans-4e" x="2426.664062"/>
       <use xlink:href="#DejaVuSans-75" x="2501.46875"/>
       <use xlink:href="#DejaVuSans-6d" x="2564.847656"/>
       <use xlink:href="#DejaVuSans-62" x="2662.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2725.736328"/>
       <use xlink:href="#DejaVuSans-72" x="2787.259766"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_mean_NpUnfilled -->
      <g style="fill: #333333" transform="translate(68.746875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-55" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2084.769531"/>
       <use xlink:href="#DejaVuSans-66" x="2148.148438"/>
       <use xlink:href="#DejaVuSans-69" x="2183.353516"/>
       <use xlink:href="#DejaVuSans-6c" x="2211.136719"/>
       <use xlink:href="#DejaVuSans-6c" x="2238.919922"/>
       <use xlink:href="#DejaVuSans-65" x="2266.703125"/>
       <use xlink:href="#DejaVuSans-64" x="2328.226562"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(67.434687 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(117.421719 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_trigonal_bipyramidal_CN_5 -->
      <g style="fill: #333333" transform="translate(20.388906 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-62" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-69" x="1924.878906"/>
       <use xlink:href="#DejaVuSans-70" x="1952.662109"/>
       <use xlink:href="#DejaVuSans-79" x="2016.138672"/>
       <use xlink:href="#DejaVuSans-72" x="2075.318359"/>
       <use xlink:href="#DejaVuSans-61" x="2116.431641"/>
       <use xlink:href="#DejaVuSans-6d" x="2177.710938"/>
       <use xlink:href="#DejaVuSans-69" x="2275.123047"/>
       <use xlink:href="#DejaVuSans-64" x="2302.90625"/>
       <use xlink:href="#DejaVuSans-61" x="2366.382812"/>
       <use xlink:href="#DejaVuSans-6c" x="2427.662109"/>
       <use xlink:href="#DejaVuSans-5f" x="2455.445312"/>
       <use xlink:href="#DejaVuSans-43" x="2505.445312"/>
       <use xlink:href="#DejaVuSans-4e" x="2575.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2650.074219"/>
       <use xlink:href="#DejaVuSans-35" x="2700.074219"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_mean_square_pyramidal_CN_5 -->
      <g style="fill: #333333" transform="translate(38.048594 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-73" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-71" x="1478.296875"/>
       <use xlink:href="#DejaVuSans-75" x="1541.773438"/>
       <use xlink:href="#DejaVuSans-61" x="1605.152344"/>
       <use xlink:href="#DejaVuSans-72" x="1666.431641"/>
       <use xlink:href="#DejaVuSans-65" x="1705.294922"/>
       <use xlink:href="#DejaVuSans-5f" x="1766.818359"/>
       <use xlink:href="#DejaVuSans-70" x="1816.818359"/>
       <use xlink:href="#DejaVuSans-79" x="1880.294922"/>
       <use xlink:href="#DejaVuSans-72" x="1939.474609"/>
       <use xlink:href="#DejaVuSans-61" x="1980.587891"/>
       <use xlink:href="#DejaVuSans-6d" x="2041.867188"/>
       <use xlink:href="#DejaVuSans-69" x="2139.279297"/>
       <use xlink:href="#DejaVuSans-64" x="2167.0625"/>
       <use xlink:href="#DejaVuSans-61" x="2230.539062"/>
       <use xlink:href="#DejaVuSans-6c" x="2291.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="2319.601562"/>
       <use xlink:href="#DejaVuSans-43" x="2369.601562"/>
       <use xlink:href="#DejaVuSans-4e" x="2439.425781"/>
       <use xlink:href="#DejaVuSans-5f" x="2514.230469"/>
       <use xlink:href="#DejaVuSans-35" x="2564.230469"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_std_dev_hexagonal_planar_CN_6 -->
      <g style="fill: #333333" transform="translate(24.916562 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-68" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1594.947266"/>
       <use xlink:href="#DejaVuSans-78" x="1654.720703"/>
       <use xlink:href="#DejaVuSans-61" x="1713.900391"/>
       <use xlink:href="#DejaVuSans-67" x="1775.179688"/>
       <use xlink:href="#DejaVuSans-6f" x="1838.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="1899.837891"/>
       <use xlink:href="#DejaVuSans-61" x="1963.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2024.496094"/>
       <use xlink:href="#DejaVuSans-5f" x="2052.279297"/>
       <use xlink:href="#DejaVuSans-70" x="2102.279297"/>
       <use xlink:href="#DejaVuSans-6c" x="2165.755859"/>
       <use xlink:href="#DejaVuSans-61" x="2193.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="2254.818359"/>
       <use xlink:href="#DejaVuSans-61" x="2318.197266"/>
       <use xlink:href="#DejaVuSans-72" x="2379.476562"/>
       <use xlink:href="#DejaVuSans-5f" x="2420.589844"/>
       <use xlink:href="#DejaVuSans-43" x="2470.589844"/>
       <use xlink:href="#DejaVuSans-4e" x="2540.414062"/>
       <use xlink:href="#DejaVuSans-5f" x="2615.21875"/>
       <use xlink:href="#DejaVuSans-36" x="2665.21875"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- GeneralizedRDF_mean_Gaussian_center=8_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(7.2 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-38" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(190.35375 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(190.35375 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_maximum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(18.302812 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-76" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.705078"/>
       <use xlink:href="#DejaVuSans-6c" x="2354.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2382.669922"/>
       <use xlink:href="#DejaVuSans-6d" x="2446.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2543.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2604.984375"/>
       <use xlink:href="#DejaVuSans-70" x="2654.984375"/>
       <use xlink:href="#DejaVuSans-61" x="2718.460938"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_4_0_-1_0 -->
      <g style="fill: #333333" transform="translate(56.602031 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-34" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-2d" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2307.927734"/>
       <use xlink:href="#DejaVuSans-5f" x="2371.550781"/>
       <use xlink:href="#DejaVuSans-30" x="2421.550781"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(128.658594 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- StructuralHeterogeneity_min_relative_bond_length -->
      <g style="fill: #333333" transform="translate(48.887344 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-69" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-6e" x="1383.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.066406"/>
       <use xlink:href="#DejaVuSans-72" x="1497.066406"/>
       <use xlink:href="#DejaVuSans-65" x="1535.929688"/>
       <use xlink:href="#DejaVuSans-6c" x="1597.453125"/>
       <use xlink:href="#DejaVuSans-61" x="1625.236328"/>
       <use xlink:href="#DejaVuSans-74" x="1686.515625"/>
       <use xlink:href="#DejaVuSans-69" x="1725.724609"/>
       <use xlink:href="#DejaVuSans-76" x="1753.507812"/>
       <use xlink:href="#DejaVuSans-65" x="1812.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="1874.210938"/>
       <use xlink:href="#DejaVuSans-62" x="1924.210938"/>
       <use xlink:href="#DejaVuSans-6f" x="1987.6875"/>
       <use xlink:href="#DejaVuSans-6e" x="2048.869141"/>
       <use xlink:href="#DejaVuSans-64" x="2112.248047"/>
       <use xlink:href="#DejaVuSans-5f" x="2175.724609"/>
       <use xlink:href="#DejaVuSans-6c" x="2225.724609"/>
       <use xlink:href="#DejaVuSans-65" x="2253.507812"/>
       <use xlink:href="#DejaVuSans-6e" x="2315.03125"/>
       <use xlink:href="#DejaVuSans-67" x="2378.410156"/>
       <use xlink:href="#DejaVuSans-74" x="2441.886719"/>
       <use xlink:href="#DejaVuSans-68" x="2481.095703"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(67.434687 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(67.434687 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_4 -->
      <g style="fill: #333333" transform="translate(124.764687 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-34" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(61.535937 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(138.465469 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(257.224531 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 403.174375 638.149 
L 520.094375 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagea7e2dba62e" transform="scale(1 -1) translate(0 -578.16)" x="406.08" y="-43.2" width="110.88" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature3_fold0 -->
    <g transform="translate(158.859375 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.401875 638.149 
L 535.031375 638.149 
L 535.031375 27.789 
L 527.401875 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagec64e9cae0b" transform="scale(1 -1) translate(0 -609.84)" x="527.76" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(538.531375 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(538.531375 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(572.932312 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p62e03ef05f">
   <rect x="403.174375" y="27.789" width="116.92" height="610.36"/>
  </clipPath>
 </defs>
</svg>
