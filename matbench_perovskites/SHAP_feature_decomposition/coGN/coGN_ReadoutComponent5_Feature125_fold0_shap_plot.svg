<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="790.790281pt" height="679.5765pt" viewBox="0 0 790.790281 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T03:05:17.084251</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 790.790281 679.5765 
L 790.790281 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 415.323281 638.149 
L 521.875281 638.149 
L 521.875281 27.789 
L 415.323281 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 459.393443 638.149 
L 459.393443 27.789 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 415.323281 609.084238 
L 521.875281 609.084238 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 415.323281 580.019476 
L 521.875281 580.019476 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 415.323281 550.954714 
L 521.875281 550.954714 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 415.323281 521.889952 
L 521.875281 521.889952 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 415.323281 492.82519 
L 521.875281 492.82519 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 415.323281 463.760429 
L 521.875281 463.760429 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 415.323281 434.695667 
L 521.875281 434.695667 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 415.323281 405.630905 
L 521.875281 405.630905 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 415.323281 376.566143 
L 521.875281 376.566143 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 415.323281 347.501381 
L 521.875281 347.501381 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 415.323281 318.436619 
L 521.875281 318.436619 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 415.323281 289.371857 
L 521.875281 289.371857 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 415.323281 260.307095 
L 521.875281 260.307095 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 415.323281 231.242333 
L 521.875281 231.242333 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 415.323281 202.177571 
L 521.875281 202.177571 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 415.323281 173.11281 
L 521.875281 173.11281 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 415.323281 144.048048 
L 521.875281 144.048048 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 415.323281 114.983286 
L 521.875281 114.983286 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 415.323281 85.918524 
L 521.875281 85.918524 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 415.323281 56.853762 
L 521.875281 56.853762 
" clip-path="url(#p858e380ec7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="me373473af2" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#me373473af2" x="459.393443" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(455.894068 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#me373473af2" x="507.745449" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(504.246074 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(345.917875 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- GeneralizedRDF_mean_Gaussian_center=1_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(19.348906 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-31" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(91.389219 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(55.287812 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(24.160937 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- OPSiteFingerprint_std_dev_q2_CN_12 -->
      <g style="fill: #333333" transform="translate(149.828281 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-71" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-32" x="1425.953125"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-43" x="1539.576172"/>
       <use xlink:href="#DejaVuSans-4e" x="1609.400391"/>
       <use xlink:href="#DejaVuSans-5f" x="1684.205078"/>
       <use xlink:href="#DejaVuSans-31" x="1734.205078"/>
       <use xlink:href="#DejaVuSans-32" x="1797.828125"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_std_dev_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(93.715 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-54" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1583.527344"/>
       <use xlink:href="#DejaVuSans-73" x="1619.611328"/>
       <use xlink:href="#DejaVuSans-68" x="1671.710938"/>
       <use xlink:href="#DejaVuSans-61" x="1735.089844"/>
       <use xlink:href="#DejaVuSans-70" x="1796.369141"/>
       <use xlink:href="#DejaVuSans-65" x="1859.845703"/>
       <use xlink:href="#DejaVuSans-64" x="1921.369141"/>
       <use xlink:href="#DejaVuSans-5f" x="1984.845703"/>
       <use xlink:href="#DejaVuSans-43" x="2034.845703"/>
       <use xlink:href="#DejaVuSans-4e" x="2104.669922"/>
       <use xlink:href="#DejaVuSans-5f" x="2179.474609"/>
       <use xlink:href="#DejaVuSans-33" x="2229.474609"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_pentagonal_pyramidal_CN_6 -->
      <g style="fill: #333333" transform="translate(20.900781 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-70" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1489.673828"/>
       <use xlink:href="#DejaVuSans-6e" x="1551.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1614.576172"/>
       <use xlink:href="#DejaVuSans-61" x="1653.785156"/>
       <use xlink:href="#DejaVuSans-67" x="1715.064453"/>
       <use xlink:href="#DejaVuSans-6f" x="1778.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="1839.722656"/>
       <use xlink:href="#DejaVuSans-61" x="1903.101562"/>
       <use xlink:href="#DejaVuSans-6c" x="1964.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1992.164062"/>
       <use xlink:href="#DejaVuSans-70" x="2042.164062"/>
       <use xlink:href="#DejaVuSans-79" x="2105.640625"/>
       <use xlink:href="#DejaVuSans-72" x="2164.820312"/>
       <use xlink:href="#DejaVuSans-61" x="2205.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="2267.212891"/>
       <use xlink:href="#DejaVuSans-69" x="2364.625"/>
       <use xlink:href="#DejaVuSans-64" x="2392.408203"/>
       <use xlink:href="#DejaVuSans-61" x="2455.884766"/>
       <use xlink:href="#DejaVuSans-6c" x="2517.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="2544.947266"/>
       <use xlink:href="#DejaVuSans-43" x="2594.947266"/>
       <use xlink:href="#DejaVuSans-4e" x="2664.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="2739.576172"/>
       <use xlink:href="#DejaVuSans-36" x="2789.576172"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CrystalNNFingerprint_std_dev_tetrahedral_CN_4 -->
      <g style="fill: #333333" transform="translate(79.725781 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1570.777344"/>
       <use xlink:href="#DejaVuSans-74" x="1632.300781"/>
       <use xlink:href="#DejaVuSans-72" x="1671.509766"/>
       <use xlink:href="#DejaVuSans-61" x="1712.623047"/>
       <use xlink:href="#DejaVuSans-68" x="1773.902344"/>
       <use xlink:href="#DejaVuSans-65" x="1837.28125"/>
       <use xlink:href="#DejaVuSans-64" x="1898.804688"/>
       <use xlink:href="#DejaVuSans-72" x="1962.28125"/>
       <use xlink:href="#DejaVuSans-61" x="2003.394531"/>
       <use xlink:href="#DejaVuSans-6c" x="2064.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2092.457031"/>
       <use xlink:href="#DejaVuSans-43" x="2142.457031"/>
       <use xlink:href="#DejaVuSans-4e" x="2212.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="2287.085938"/>
       <use xlink:href="#DejaVuSans-34" x="2337.085938"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CrystalNNFingerprint_std_dev_pentagonal_pyramidal_CN_6 -->
      <g style="fill: #333333" transform="translate(7.2 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-70" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1595.044922"/>
       <use xlink:href="#DejaVuSans-6e" x="1656.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1719.947266"/>
       <use xlink:href="#DejaVuSans-61" x="1759.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1820.435547"/>
       <use xlink:href="#DejaVuSans-6f" x="1883.912109"/>
       <use xlink:href="#DejaVuSans-6e" x="1945.09375"/>
       <use xlink:href="#DejaVuSans-61" x="2008.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="2069.751953"/>
       <use xlink:href="#DejaVuSans-5f" x="2097.535156"/>
       <use xlink:href="#DejaVuSans-70" x="2147.535156"/>
       <use xlink:href="#DejaVuSans-79" x="2211.011719"/>
       <use xlink:href="#DejaVuSans-72" x="2270.191406"/>
       <use xlink:href="#DejaVuSans-61" x="2311.304688"/>
       <use xlink:href="#DejaVuSans-6d" x="2372.583984"/>
       <use xlink:href="#DejaVuSans-69" x="2469.996094"/>
       <use xlink:href="#DejaVuSans-64" x="2497.779297"/>
       <use xlink:href="#DejaVuSans-61" x="2561.255859"/>
       <use xlink:href="#DejaVuSans-6c" x="2622.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="2650.318359"/>
       <use xlink:href="#DejaVuSans-43" x="2700.318359"/>
       <use xlink:href="#DejaVuSans-4e" x="2770.142578"/>
       <use xlink:href="#DejaVuSans-5f" x="2844.947266"/>
       <use xlink:href="#DejaVuSans-36" x="2894.947266"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(140.8075 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(172.580312 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(202.502656 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(63.691094 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(194.231406 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(202.502656 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(79.583594 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(93.452969 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(272.129844 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(150.614375 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(109.030625 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 415.323281 638.149 
L 521.875281 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image766259e64e" transform="scale(1 -1) translate(0 -578.16)" x="417.6" y="-43.2" width="101.52" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature125_fold0 -->
    <g transform="translate(153.608281 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-35" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 528.534781 638.149 
L 536.164281 638.149 
L 536.164281 27.789 
L 528.534781 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagef206cf7df4" transform="scale(1 -1) translate(0 -609.84)" x="528.48" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(539.664281 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(539.664281 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(574.065219 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p858e380ec7">
   <rect x="415.323281" y="27.789" width="106.552" height="610.36"/>
  </clipPath>
 </defs>
</svg>
