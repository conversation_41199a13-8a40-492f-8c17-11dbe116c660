<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="776.858281pt" height="679.5765pt" viewBox="0 0 776.858281 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T22:24:32.537066</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 776.858281 679.5765 
L 776.858281 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 402.063281 638.149 
L 519.487281 638.149 
L 519.487281 27.789 
L 402.063281 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 455.17675 638.149 
L 455.17675 27.789 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 402.063281 609.084238 
L 519.487281 609.084238 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 402.063281 580.019476 
L 519.487281 580.019476 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 402.063281 550.954714 
L 519.487281 550.954714 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 402.063281 521.889952 
L 519.487281 521.889952 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 402.063281 492.82519 
L 519.487281 492.82519 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 402.063281 463.760429 
L 519.487281 463.760429 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 402.063281 434.695667 
L 519.487281 434.695667 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 402.063281 405.630905 
L 519.487281 405.630905 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 402.063281 376.566143 
L 519.487281 376.566143 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 402.063281 347.501381 
L 519.487281 347.501381 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 402.063281 318.436619 
L 519.487281 318.436619 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 402.063281 289.371857 
L 519.487281 289.371857 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 402.063281 260.307095 
L 519.487281 260.307095 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 402.063281 231.242333 
L 519.487281 231.242333 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 402.063281 202.177571 
L 519.487281 202.177571 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 402.063281 173.11281 
L 519.487281 173.11281 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 402.063281 144.048048 
L 519.487281 144.048048 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 402.063281 114.983286 
L 519.487281 114.983286 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 402.063281 85.918524 
L 519.487281 85.918524 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 402.063281 56.853762 
L 519.487281 56.853762 
" clip-path="url(#pba8ccfaf5e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m38ebc7499c" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m38ebc7499c" x="420.54479" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(412.436587 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m38ebc7499c" x="455.17675" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(451.677375 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m38ebc7499c" x="489.80871" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(486.309335 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(338.093875 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_avg_dev_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(28.331094 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-76" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-6f" x="2207.962891"/>
       <use xlink:href="#DejaVuSans-6c" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-75" x="2296.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2457.71875"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.242188"/>
       <use xlink:href="#DejaVuSans-70" x="2569.242188"/>
       <use xlink:href="#DejaVuSans-61" x="2632.71875"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(77.05875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- IonProperty_avg_ionic_char -->
      <g style="fill: #333333" transform="translate(200.855312 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-49"/>
       <use xlink:href="#DejaVuSans-6f" x="29.492188"/>
       <use xlink:href="#DejaVuSans-6e" x="90.673828"/>
       <use xlink:href="#DejaVuSans-50" x="154.052734"/>
       <use xlink:href="#DejaVuSans-72" x="212.605469"/>
       <use xlink:href="#DejaVuSans-6f" x="251.46875"/>
       <use xlink:href="#DejaVuSans-70" x="312.650391"/>
       <use xlink:href="#DejaVuSans-65" x="376.126953"/>
       <use xlink:href="#DejaVuSans-72" x="437.650391"/>
       <use xlink:href="#DejaVuSans-74" x="478.763672"/>
       <use xlink:href="#DejaVuSans-79" x="517.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="577.152344"/>
       <use xlink:href="#DejaVuSans-61" x="627.152344"/>
       <use xlink:href="#DejaVuSans-76" x="688.431641"/>
       <use xlink:href="#DejaVuSans-67" x="747.611328"/>
       <use xlink:href="#DejaVuSans-5f" x="811.087891"/>
       <use xlink:href="#DejaVuSans-69" x="861.087891"/>
       <use xlink:href="#DejaVuSans-6f" x="888.871094"/>
       <use xlink:href="#DejaVuSans-6e" x="950.052734"/>
       <use xlink:href="#DejaVuSans-69" x="1013.431641"/>
       <use xlink:href="#DejaVuSans-63" x="1041.214844"/>
       <use xlink:href="#DejaVuSans-5f" x="1096.195312"/>
       <use xlink:href="#DejaVuSans-63" x="1146.195312"/>
       <use xlink:href="#DejaVuSans-68" x="1201.175781"/>
       <use xlink:href="#DejaVuSans-61" x="1264.554688"/>
       <use xlink:href="#DejaVuSans-72" x="1325.833984"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_0 -->
      <g style="fill: #333333" transform="translate(66.323594 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-30" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(127.5475 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(127.537344 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_avg_dev_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-45" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6c" x="2071"/>
       <use xlink:href="#DejaVuSans-65" x="2098.783203"/>
       <use xlink:href="#DejaVuSans-63" x="2160.306641"/>
       <use xlink:href="#DejaVuSans-74" x="2215.287109"/>
       <use xlink:href="#DejaVuSans-72" x="2254.496094"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.359375"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.541016"/>
       <use xlink:href="#DejaVuSans-65" x="2417.919922"/>
       <use xlink:href="#DejaVuSans-67" x="2479.443359"/>
       <use xlink:href="#DejaVuSans-61" x="2542.919922"/>
       <use xlink:href="#DejaVuSans-74" x="2604.199219"/>
       <use xlink:href="#DejaVuSans-69" x="2643.408203"/>
       <use xlink:href="#DejaVuSans-76" x="2671.191406"/>
       <use xlink:href="#DejaVuSans-69" x="2730.371094"/>
       <use xlink:href="#DejaVuSans-74" x="2758.154297"/>
       <use xlink:href="#DejaVuSans-79" x="2797.363281"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(66.323594 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(24.693125 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(159.320312 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(66.323594 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_maximum_GSbandgap -->
      <g style="fill: #333333" transform="translate(30.888437 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-62" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-61" x="2298.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="2359.28125"/>
       <use xlink:href="#DejaVuSans-64" x="2422.660156"/>
       <use xlink:href="#DejaVuSans-67" x="2486.136719"/>
       <use xlink:href="#DejaVuSans-61" x="2549.613281"/>
       <use xlink:href="#DejaVuSans-70" x="2610.892578"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(111.746406 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(180.971406 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(189.242656 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(59.520938 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(80.192969 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(258.869844 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(137.354375 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(95.770625 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 402.063281 638.149 
L 519.487281 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image917cac7391" transform="scale(1 -1) translate(0 -578.16)" x="405.36" y="-43.2" width="110.88" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature11_fold0 -->
    <g transform="translate(151.892281 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 526.826281 638.149 
L 534.455781 638.149 
L 534.455781 27.789 
L 526.826281 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagebfacd5a521" transform="scale(1 -1) translate(0 -609.84)" x="527.04" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(537.955781 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(537.955781 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(572.356719 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pba8ccfaf5e">
   <rect x="402.063281" y="27.789" width="117.424" height="610.36"/>
  </clipPath>
 </defs>
</svg>
