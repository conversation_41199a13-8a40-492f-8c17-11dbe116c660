<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.082937pt" height="679.5765pt" viewBox="0 0 794.082937 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T11:18:23.098716</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.082937 679.5765 
L 794.082937 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.095937 638.149 
L 524.903937 638.149 
L 524.903937 27.789 
L 431.095937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 473.436559 638.149 
L 473.436559 27.789 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.095937 609.084238 
L 524.903937 609.084238 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.095937 580.019476 
L 524.903937 580.019476 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.095937 550.954714 
L 524.903937 550.954714 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.095937 521.889952 
L 524.903937 521.889952 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.095937 492.82519 
L 524.903937 492.82519 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.095937 463.760429 
L 524.903937 463.760429 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.095937 434.695667 
L 524.903937 434.695667 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.095937 405.630905 
L 524.903937 405.630905 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.095937 376.566143 
L 524.903937 376.566143 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.095937 347.501381 
L 524.903937 347.501381 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.095937 318.436619 
L 524.903937 318.436619 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.095937 289.371857 
L 524.903937 289.371857 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.095937 260.307095 
L 524.903937 260.307095 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.095937 231.242333 
L 524.903937 231.242333 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.095937 202.177571 
L 524.903937 202.177571 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.095937 173.11281 
L 524.903937 173.11281 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.095937 144.048048 
L 524.903937 144.048048 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.095937 114.983286 
L 524.903937 114.983286 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.095937 85.918524 
L 524.903937 85.918524 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.095937 56.853762 
L 524.903937 56.853762 
" clip-path="url(#p4cfb0eb312)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mef0ef2bacb" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mef0ef2bacb" x="473.436559" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(464.68984 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mef0ef2bacb" x="520.474864" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2.5 -->
      <g style="fill: #333333" transform="translate(511.728145 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.318531 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CrystalNNFingerprint_std_dev_q4_CN_12 -->
      <g style="fill: #333333" transform="translate(143.61875 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-71" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-34" x="1595.044922"/>
       <use xlink:href="#DejaVuSans-5f" x="1658.667969"/>
       <use xlink:href="#DejaVuSans-43" x="1708.667969"/>
       <use xlink:href="#DejaVuSans-4e" x="1778.492188"/>
       <use xlink:href="#DejaVuSans-5f" x="1853.296875"/>
       <use xlink:href="#DejaVuSans-31" x="1903.296875"/>
       <use xlink:href="#DejaVuSans-32" x="1966.919922"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- OPSiteFingerprint_std_dev_q2_CN_10 -->
      <g style="fill: #333333" transform="translate(165.600937 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-71" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-32" x="1425.953125"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-43" x="1539.576172"/>
       <use xlink:href="#DejaVuSans-4e" x="1609.400391"/>
       <use xlink:href="#DejaVuSans-5f" x="1684.205078"/>
       <use xlink:href="#DejaVuSans-31" x="1734.205078"/>
       <use xlink:href="#DejaVuSans-30" x="1797.828125"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(210.004062 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(123.188437 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(203.502031 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_183 -->
      <g style="fill: #333333" transform="translate(210.004062 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-33" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(218.275312 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CrystalNNFingerprint_mean_square_co-planar_CN_4 -->
      <g style="fill: #333333" transform="translate(69.916875 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-73" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-71" x="1478.296875"/>
       <use xlink:href="#DejaVuSans-75" x="1541.773438"/>
       <use xlink:href="#DejaVuSans-61" x="1605.152344"/>
       <use xlink:href="#DejaVuSans-72" x="1666.431641"/>
       <use xlink:href="#DejaVuSans-65" x="1705.294922"/>
       <use xlink:href="#DejaVuSans-5f" x="1766.818359"/>
       <use xlink:href="#DejaVuSans-63" x="1816.818359"/>
       <use xlink:href="#DejaVuSans-6f" x="1871.798828"/>
       <use xlink:href="#DejaVuSans-2d" x="1934.855469"/>
       <use xlink:href="#DejaVuSans-70" x="1970.939453"/>
       <use xlink:href="#DejaVuSans-6c" x="2034.416016"/>
       <use xlink:href="#DejaVuSans-61" x="2062.199219"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2186.857422"/>
       <use xlink:href="#DejaVuSans-72" x="2248.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="2289.25"/>
       <use xlink:href="#DejaVuSans-43" x="2339.25"/>
       <use xlink:href="#DejaVuSans-4e" x="2409.074219"/>
       <use xlink:href="#DejaVuSans-5f" x="2483.878906"/>
       <use xlink:href="#DejaVuSans-34" x="2533.878906"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CrystalNNFingerprint_std_dev_rectangular_see-saw-like_CN_4 -->
      <g style="fill: #333333" transform="translate(7.2 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-72" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1570.431641"/>
       <use xlink:href="#DejaVuSans-63" x="1631.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1686.935547"/>
       <use xlink:href="#DejaVuSans-61" x="1726.144531"/>
       <use xlink:href="#DejaVuSans-6e" x="1787.423828"/>
       <use xlink:href="#DejaVuSans-67" x="1850.802734"/>
       <use xlink:href="#DejaVuSans-75" x="1914.279297"/>
       <use xlink:href="#DejaVuSans-6c" x="1977.658203"/>
       <use xlink:href="#DejaVuSans-61" x="2005.441406"/>
       <use xlink:href="#DejaVuSans-72" x="2066.720703"/>
       <use xlink:href="#DejaVuSans-5f" x="2107.833984"/>
       <use xlink:href="#DejaVuSans-73" x="2157.833984"/>
       <use xlink:href="#DejaVuSans-65" x="2209.933594"/>
       <use xlink:href="#DejaVuSans-65" x="2271.457031"/>
       <use xlink:href="#DejaVuSans-2d" x="2332.980469"/>
       <use xlink:href="#DejaVuSans-73" x="2369.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2421.164062"/>
       <use xlink:href="#DejaVuSans-77" x="2482.443359"/>
       <use xlink:href="#DejaVuSans-2d" x="2564.230469"/>
       <use xlink:href="#DejaVuSans-6c" x="2600.314453"/>
       <use xlink:href="#DejaVuSans-69" x="2628.097656"/>
       <use xlink:href="#DejaVuSans-6b" x="2655.880859"/>
       <use xlink:href="#DejaVuSans-65" x="2710.166016"/>
       <use xlink:href="#DejaVuSans-5f" x="2771.689453"/>
       <use xlink:href="#DejaVuSans-43" x="2821.689453"/>
       <use xlink:href="#DejaVuSans-4e" x="2891.513672"/>
       <use xlink:href="#DejaVuSans-5f" x="2966.318359"/>
       <use xlink:href="#DejaVuSans-34" x="3016.318359"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(166.387031 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(16.0075 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(95.35625 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.580156 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(95.35625 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(218.275312 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(28.903906 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(109.225625 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(287.9025 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(166.387031 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(124.803281 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.095937 638.149 
L 524.903937 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageb9a41a660c" transform="scale(1 -1) translate(0 -578.16)" x="432.72" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature71_fold0 -->
    <g transform="translate(169.116938 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-37" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.766937 638.149 
L 538.396437 638.149 
L 538.396437 27.789 
L 530.766937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagef76cc5aec8" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.896437 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.896437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(576.297375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p4cfb0eb312">
   <rect x="431.095937" y="27.789" width="93.808" height="610.36"/>
  </clipPath>
 </defs>
</svg>
