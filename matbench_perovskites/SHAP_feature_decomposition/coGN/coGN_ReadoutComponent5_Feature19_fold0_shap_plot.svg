<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="777.717375pt" height="679.5765pt" viewBox="0 0 777.717375 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T05:01:16.571620</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 777.717375 679.5765 
L 777.717375 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 403.174375 638.149 
L 520.094375 638.149 
L 520.094375 27.789 
L 403.174375 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 450.151952 638.149 
L 450.151952 27.789 
" clip-path="url(#pc549266437)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 403.174375 609.084238 
L 520.094375 609.084238 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 403.174375 580.019476 
L 520.094375 580.019476 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 403.174375 550.954714 
L 520.094375 550.954714 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 403.174375 521.889952 
L 520.094375 521.889952 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 403.174375 492.82519 
L 520.094375 492.82519 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 403.174375 463.760429 
L 520.094375 463.760429 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 403.174375 434.695667 
L 520.094375 434.695667 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 403.174375 405.630905 
L 520.094375 405.630905 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 403.174375 376.566143 
L 520.094375 376.566143 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 403.174375 347.501381 
L 520.094375 347.501381 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 403.174375 318.436619 
L 520.094375 318.436619 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 403.174375 289.371857 
L 520.094375 289.371857 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 403.174375 260.307095 
L 520.094375 260.307095 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 403.174375 231.242333 
L 520.094375 231.242333 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 403.174375 202.177571 
L 520.094375 202.177571 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 403.174375 173.11281 
L 520.094375 173.11281 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 403.174375 144.048048 
L 520.094375 144.048048 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 403.174375 114.983286 
L 520.094375 114.983286 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 403.174375 85.918524 
L 520.094375 85.918524 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 403.174375 56.853762 
L 520.094375 56.853762 
" clip-path="url(#pc549266437)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m77208b46de" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m77208b46de" x="450.151952" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(446.652577 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m77208b46de" x="498.067168" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(494.567793 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(338.952969 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- BondFractions_O_-_Rh_bond_frac_ -->
      <g style="fill: #333333" transform="translate(158.712969 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-46" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="306.910156"/>
       <use xlink:href="#DejaVuSans-61" x="348.023438"/>
       <use xlink:href="#DejaVuSans-63" x="409.302734"/>
       <use xlink:href="#DejaVuSans-74" x="464.283203"/>
       <use xlink:href="#DejaVuSans-69" x="503.492188"/>
       <use xlink:href="#DejaVuSans-6f" x="531.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="592.457031"/>
       <use xlink:href="#DejaVuSans-73" x="655.835938"/>
       <use xlink:href="#DejaVuSans-5f" x="707.935547"/>
       <use xlink:href="#DejaVuSans-4f" x="757.935547"/>
       <use xlink:href="#DejaVuSans-5f" x="836.646484"/>
       <use xlink:href="#DejaVuSans-2d" x="886.646484"/>
       <use xlink:href="#DejaVuSans-5f" x="922.730469"/>
       <use xlink:href="#DejaVuSans-52" x="972.730469"/>
       <use xlink:href="#DejaVuSans-68" x="1042.212891"/>
       <use xlink:href="#DejaVuSans-5f" x="1105.591797"/>
       <use xlink:href="#DejaVuSans-62" x="1155.591797"/>
       <use xlink:href="#DejaVuSans-6f" x="1219.068359"/>
       <use xlink:href="#DejaVuSans-6e" x="1280.25"/>
       <use xlink:href="#DejaVuSans-64" x="1343.628906"/>
       <use xlink:href="#DejaVuSans-5f" x="1407.105469"/>
       <use xlink:href="#DejaVuSans-66" x="1457.105469"/>
       <use xlink:href="#DejaVuSans-72" x="1492.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1533.423828"/>
       <use xlink:href="#DejaVuSans-63" x="1594.703125"/>
       <use xlink:href="#DejaVuSans-5f" x="1649.683594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_mean -->
      <g style="fill: #333333" transform="translate(94.484844 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-65" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-61" x="2069.087891"/>
       <use xlink:href="#DejaVuSans-6e" x="2130.367188"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(190.35375 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(25.804219 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(182.0825 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(187.88375 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_maximum_GSbandgap -->
      <g style="fill: #333333" transform="translate(31.999531 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-62" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-61" x="2298.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="2359.28125"/>
       <use xlink:href="#DejaVuSans-64" x="2422.660156"/>
       <use xlink:href="#DejaVuSans-67" x="2486.136719"/>
       <use xlink:href="#DejaVuSans-61" x="2549.613281"/>
       <use xlink:href="#DejaVuSans-70" x="2610.892578"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- GeneralizedRDF_mean_Gaussian_center=8_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(7.2 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-38" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(67.434687 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(81.980469 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(128.658594 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_maximum_NUnfilled -->
      <g style="fill: #333333" transform="translate(48.367344 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-55" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.556641"/>
       <use xlink:href="#DejaVuSans-66" x="2304.935547"/>
       <use xlink:href="#DejaVuSans-69" x="2340.140625"/>
       <use xlink:href="#DejaVuSans-6c" x="2367.923828"/>
       <use xlink:href="#DejaVuSans-6c" x="2395.707031"/>
       <use xlink:href="#DejaVuSans-65" x="2423.490234"/>
       <use xlink:href="#DejaVuSans-64" x="2485.013672"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(67.434687 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(182.0825 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_maximum -->
      <g style="fill: #333333" transform="translate(65.852344 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-61" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-78" x="2068.84375"/>
       <use xlink:href="#DejaVuSans-69" x="2128.023438"/>
       <use xlink:href="#DejaVuSans-6d" x="2155.806641"/>
       <use xlink:href="#DejaVuSans-75" x="2253.21875"/>
       <use xlink:href="#DejaVuSans-6d" x="2316.597656"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(190.35375 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(81.304062 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(259.980937 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(138.465469 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(96.881719 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 403.174375 638.149 
L 520.094375 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAJoAAAMjCAYAAABd0gk3AABhtUlEQVR4nO2dd5gdZdn/P8/MnLpne9/0XggQQu/SpKioAWyor9hfu7hi5ydY0bXga1fsiqJEUREBKdJLgEAS0nvZ3s6efmbm+f1xdje72U2ySXZnsvPM57r2IueZcu4Zvud++n0LKSWqIJrMq4E/9X98g2w07nTTHpUQqghNNJkZILRfcVY2GmE37FENzW0DHGR/kR2ozGcCUEloPi7iC83HEXyh+TiCLzQfR/CF5uMISghNNJm62zaojhJCA1JuG6A6qghNuG2A6qgiNB+X8YXm4wiqCM2vOl1GFaGpsXLgGMZw2wCHGH14Q0peLP+8XNy7AYlAk2ATIE0pAoMIXWjksNHJEyZMHwKJBDIE7SjZZ4DLkCt6HX2aSYgaQrMs0EfX2srKZRi2xXHxDf0lJhYhQsTRsAHQMdHJD34WQISslkc7I4D9JLB44h9icqNG1SnEQdtom0rmDPss0QdFNcD+nwVgEgBYhFiuxns8CtR+Qf0tN8M2hxWPpsqDNPIkcoV94MM+oLjQItkcAMf1rh9WrpHFKnirQeR+8pNAiCzAAxNoomdQo42maRajPKsuJWd3PElVroM0QWwRwpYBssQIACHi6GSxAZNwf7tNYgE2Wk8I83fIFR92+nEmI2oI7QAkomEW9nxzRE1ZPMq5Q9d8K10NHCGqvDN/HM1lVBGaj8v4QvNxBF9oPo6gitDMQ5/iM5EoITTZaETdtkF1lBCaj/v4QvNxBF9oPo7gC83HEXyh+TiCLzQfR1BJaA+NUvao41YoijIRHwcQTWYcQDYaJW7bohLKCc3HHVSqOn1cxBeajyP4QvNxBCWFJj4V90MkOIxSnYHnSr9iT8u0CE1K2gJVPFI/94z3b37r027bpQLKeLT/Vt5iLYzvEmlRwgOzltJWFuWc1k1PuW2XKijj0br0D8o/Hn8WH3vd68gbhc1fF23cwANz5hXJ/xfxI0JOMMp4tA1ltXzmilcPigzggfkLQMo+F81SBmWEdu/MpcSjo6R90jS/Y+AAyghtVnuC2e1dIw+o0XJwHWWEZpDnx3+9g9q+Qk1pWBZvefoFl61SB2VCIgT0DKdv38Wab32T1XX1TO3tIaEH+cMpJ7htmhIo49E6igoboaQQLGxvpzaRoC8UBOk30ZxAGY/2xLSZiKJSHllwHJamc+KubaRzWQKm5bZpSqCM0AKRUh5cPH3w86rps0lpsKi5A4i5Z5giKFF1Xv6qxzP5YCHwVNDMEcllALANnb3lvsicQAmPVkYy2KdX85GH/87C3a2EzDwtFcV8/Oq30lE1WjQ0n/FGCY/WU1Qplu3expriOprOOp8/H3cSwaRG4713+eNoDuF5j/aTk/4v0nLiFTxcE2GmiPP30xfQVRxF2DZXvLwecrYQN6YswoYhPxv0ZTdBeNqjray/3vjMqctTFT1pvnTnY6yc10BXUQSkRGoady9ZDAjQhEbG9CNrTyCeFtq3T7zgqwvjca56fhN/OWU+a2oqIWsV/qx+56UJEAI0gfhK9pXuWuxdPC20l6umnGsLQSIU4HsXn7LvgATyFkhZ+PdAvgvJRW7YqQKeFtrbNq78ZG8owIqT5iH3T54i2dcRkIPe7SYn7VMJzy98LHvPzlStZUY21tcOPyBlodoM6GBLkLTJmyO1o9/F52jxtEcD6PnZ9OjiZOfIAwLIm4U3oAvDF9nE4vnhDQBTahKBwJZg9XcudW2g6pTyxrA/4TnBKCG0knhfoXrMD9GTbRWqTsvbTYdjBc9XnQB7i8v/pzwRH1EupPSTYDuEEkJ76M8n/nZBR/OI8pnd7fuGNnwmFCWEBnDWnm1AYQl3JF9In3jBrnVumqQUSrTRAK5Zs5lLt27mhNYuwqbJxsoynq2fyi/8WXVHUEZodak4S7rSg58Xd3TTEqsAf4bTEZSpOkfbGzCvq8MFQ9REGaH1hCIjynaUl7tgiZooI7Tt0Xq2llcMfk4bBl9+xcUuWqQWyrTREuFSPnDJeczqbaUkm+Hvi46jIxrzY284hDIeTTcly9dtoytSyb/nL6EhnuHT9z+DvCla6rZtKqCMR8tb8b3RTLThsk07uWzTToQtqd/T4vc5HcLzy4SG8seGn/48aEffaeuaIJ/YeXXbe2e4bZMqKCU0H/dQpo3m4y6+0HwcwReajyP4QvNxBGWGNwBWBr91schr90nNEEJaZr7YiJzR+yHTbbtUQBmhvRj4ZnF7tPL+jrpipCYw8pYxu609j7/G1hGUqTp7tUhPe3kJsj8ItxnQ2VpTzZOi6a8um6YEygits6RshOcyAzpS11/thj2qoUzVKe2CzqbEOynJZmiJldAbKEK3/SaaEygjNM2yOXPnRipkDwFyzEqFaBaV2DLhtmlKoIzQGuLtNMhmAuQBCJMhKLNsoVaZ5oObKPOSY0ZGDIhsgDAZwmRcskgtlBDa9Pds70oER8kDBSTDo5f7jC9KCE1EQiWPT5tNR2h4YORuvRhL6i5ZpRZKtNEWtXbz1+PnE44FOXnXOmqS3ewtqeKJKYs4/6VV/oCtAyghtJwRENFsjnQgzGOzTxos14BUKOoLzQGUqDqbi6OMNlqWQ5AXftXpBEp4NDOgixm9cbRsDmkYCCmRQjB/ZxsmvtCcwPMe7ZrrdpT2BnT6ykr40RkncONFp3P7krmEk72c0vUc1Yk4trjma4jlvuImEM/uGbjmuh1iezi4Ii/N14WEwfM1pZj6Pi0tau6g6e6/8cpdD6NhIQggyb9bkytuc89q7+Jlj3atmc+/bk9ZCZYuhokMYF19FT1aEQYWgoG1QsGfI5aPjJ3gc9R4VmhxQ39N1LLpiISImvkRx6O5DJfveRALDYEA7IGFaSc7a6kaeFZoEcteHTEtyjM55vQkmNIzPLTox5+8h3IzQZKi/ghpg6McW5y1VA082+sMSPn9HcVFNyxu7ynuMXR+cOfdbJxqsaWijks3v8jr168EIIiJLKRPQWI+I+SKkTFIfY4az3q0P/9yRs/SdLYqLe0f9AR0QnmLRGgq7eVzeXjeOayunU2aCDY6oEuJ+Q5N/uV0t+32Kp7tdQ7l+itW2ruLY6KzrGSwLJTPcfMdd1CTbGa6/I4/OzDBeNajDWVjSRFDRQaQDQS5b94yMpQc4Cqf8UQJob1cXiqFPdJzh3MWwg+W7AhKCC1gm/SJ4RGqKnsSnLZtC30i4ivNATzb6xxKiWXJS1evZ3FnN89Pn0l5X4qLXl7HlHQ3uyly2zwlUEJoRem8ff7GnfrCnlbO3bJ5sNxGYOPXnU6gRNXZVVVa2keINEFMNFJ6EBtBF8Vo/kZ1R1BieAPgntLbZI4QQgikEAjbxrAsSpJd9jny4/7KjQlGiaoTQEgkuhCy34NJTcMCpF91OoISVSdAKG+yfzwXW9NA84XmBMoILZgbuZhbSJusv5TbEZQRmh0wKc0k9xVISUU6QZSkn9DCAZRpoxnZ1M2lyP8Xy+XI6zoh08Qgywn2ZyoOfbXP0aKMRztTfuaLGZKtEZGgxOwjrMXJkf+O23apgjLDGz7uooxH83EXX2g+juALzccRfKH5OIIywxsAiOXi66e+p8/SDP3KTY9Hj+/8ot8Tcghlep2fPvVff+yeOfON0uj/bVkWVTt2PP/Vpy/z93E6gDJVZ+e06ftEBqDrtEyfvsw9i9RCGaG1lcbI25JAa5xgSy8526Yv6ocVdQpl2mhJy+baf62iIlEIjtwbDfKr1/q1plMo49FOf2nnoMgASlM5Tn1xl4sWqYUyQpvWHh9R1tDpL9xwCmWEFtCsEWVW0N8v4BTKCE3TLFIxHZBIJKkiHV0fKT6fiUGJzsApH9qsfSGd4vTUBgwK4kpngzxkHueyZeqghNDqW5Ml03NthIZksouZWSplr4tWqYUSVeeZu/aGAjI3olyIkZEgfSYGJYSWswLBh6fPBCCv6YPbnp6rqXfNJtVQoupsJv3l359yJk8vPJ6W8kqqEnFm7NnJDiPKLnFjaJq8Oeu2jV5HCY+WjZW+qSoQpLm8kqwu6I5E2TxtOvFIlB6skQNsPuOOt1dviOU/koj3vVg1R7zjrTewvqaYbEBnVmcbv/7TT5jR0UNNXzsBbMDEpBSTKIXN69mfFsmfvc/lJ/AM3vVoYvlXJLxfIEV9up01dSVkA4XNwtsqa7jmbR+mLtWJhoGGiYZBiG4M0v0B4cPvTYsPHO/yU3gG7woNPjIw7n/XotOx9OGP2lpcxqqGGQTJF4JXIQANg0KO9ULmAfsRJw32Ml4W2uD8UkqWjTgYME1mdnX4ScccwstC++FA63NpyzYufW7zsIOfeegfVCfj2EgKr0ECNlZ/BMhCqbjIQXs9jdc7A7+WiLdvK6rmgZor6IiG2FpXxtnbNjIn2UFrcS2v3/xXCqM8eUxKsAYzqaT/UCR/fq2r9nsIbwutn7ed+2B2WrQkWBpPEUtk6C0toqO6hJquXi555O7MyfJrfqKxCUaJAdtgX/LvIhy7urW+gtYh5dFUBi1SW+aWXSrh5TbaIPna2saZW/ZQlEgPlhUl01Tu6eKk1Mf8WQEHUMKj1cR7hLQ1Tn5mIz3lMUBQ1t1HKhRw2zRlUMKj7SiuSPcWhdEkVHQlqOjqQ5PQVhZz2zRlUEJo01Kt7Y8uno69X/nji6a5Yo+KKNHrBDjuQ3tkJGtyxXNbEFJy70lzsDR49ucz/Y0DDqBEGw0gEwzw8rQanpvbMFg2f2+nixaphRJVJ8CS3e0jyhbv7XDBEjVRxqNdunoLWUPj4cUzERJe8fI2XrF+J7DEbdOUQBmhRfImb35yLW96ci3QP+Ou+80zp1Cm6izOFAZrRf+fBGq6/V1QTqGM0DZUVTGro4OQZRIx8yxsbWVbZaXbZimDMkJrjwb++UJDA7PaupjR3s3LNbVsqYhuPvSVPuOBMuNoAJ+74CmRyefSCMTi9u7id61/7cjNnj4TglJC83EPZapOH3fxhebjCL7QfBzBF5qPIygzMwBwe+UfrM7SIi0f1Jm1u4sX6or+etPmq5a7bZcKKCO0H9X/Pp+ZUakZmsAAds+voaEz9Xq37VIFZarOzrpqw9D2zW1KTaAVB/jw0rvvddEsZVBGaDXZzIiybDBAeTxxrgvmKIcyQgvmzRFlpYkU0tx/gbfPRKCM0HqiYYoTGTSrIKziRJrOkmII6f7UiAMo0xmwQgEeXTKfhtZOyvqSrFrYQF9RhMrnE/6iNAdQx6MZOoZpUdKXIZrKE07nMKUklPP3DzuBEh7tda967vRUTRUfun8ldd2FtDwnr9nGzy9ahu230RxBCY/24vS6pyLp/KDIBnjTY6uJ4WdPcQIlhNZjBKhMjhzeKMrmyWUtvzPgAEoIrTidZXdZEfsrytbACkf8zoADKCG0Ze3dLOnoYkd9KTmjkNCiJxZi+5QywpbpezQHUKIzcFpXN3Y+T3dplO7SKEgJQiBsSTaV8T2aA3heaJZ4Zyx58XtYW1nG89UVHNcdZ2l7F1O743SHAly768lIXPxXSrqsUuwXQLwLecdLAIjltcBlOYr0JPWAeK5c3vKiqw80SfH0ngFLvHNBN6Vrr73q3fr9Mxp4/8ubmd2XHDy+qGUTp215iSgWNjoBugmTB+TNYD4FrADCAHkiJJgCiG+Uy1s+5coDTWI83UYzkd98aNpifV1FKXPjiWEiA9hQM5uwnsekCIMkeUrp38P+eeC79IsMIECaAH0An+wWn5rt2EN4BI8LLbhszdRagrZNaXbkzjpb08kaIWAgKcHA65AaMH//83XyA6cumBiLvYunhRYkd+dFazdzWlsX6NqI4Q1LE5Rkk4BEYCH2Dd5mgP/ufz+TCEAaeGoi7fYinhaahvjCifEte4LBIBfv2EsyGsHUCoLLGzrJSISEFiZEF3miBOkEbBu0y4B3Ay9CQYZpKjCJtgNvLZe3dLv4WJMST/c6dfmLeClMrbngaTmrtZtdM+pIxooGjwezOXqkQRBbanTeHES+iPzzX4fcYili+UyLYG+GygpgV7m8xd/dfgR4utc5wM/m/UMWZbLsmV5JsqgIS9cI5nLE4gm2d3Ulb93yJj9q8gTjaY82gBSCcDpPZUeccCbbP1hrE41nIRL1B2wdwNNttAFenl3InR7tzbC+rIxHp9Wzq6iItlCQQCbvC80BlPBoG6fWoNk2d540i53VZQAIKXnlqs0sefpld41TBCU8Wosh5AtTKwZFBoXq9Kn5Uwlaee83Uo8BlBDa8z+eqiVCI513PBomr/uBbJ1ACaEBnLpzd2HVxhDmdMYhGHLJIrVQRmgn7mrlVet3E8vmQUrmdsS5cu0OhLT8TQMOoERnAKCsM8tJeztZ2tyFLUCXUBZPsC4g/KrTAZTxaB0V5YXxMwoiA+gtihIzfaE5gTJCS0aCIwsFiKDuvDEKoozQSvv60KzhW+vCyQwNicxnXTJJKZRpoyV7k701e9tLe8tLMAMG4WSaivZu3r3l6u+6bZsKKOPRrt/7lrJsPJmcu3kXczftZMqOFrlz1lS/3nQIJVZv+LiPMh7Nx118ofk4gi80H0fwhebjCMoMbwBsFjfkSkVfwJS67KLyB8fJmz7stk2qoEyvc5f4mB1GCKM//3CAXjZR9+JJ8qtL3bZNBZTwaBvFLaESwqKUnsEymxDT6DnBPavUQok2Wh/J3gjDA/FpSDSC/oS6QyghNInQxIh96mCr4dCPCZQQmoUuuikfViaBxL4YLj4TjBJCy4O0MNnDFFJE6SPGDmawLVaNaDIfdds+FVBCaAKNHkoxSLGXBjqpIECKT735dSDlOW7bpwJKNFJsckwhRQAbgx4CmGhI2ir8jSlOoYTQyoGcMFhrzCajhYhHgvz5vIUs3dXMjspKt81TAiWEZpBnkzGbvnCYvqIQeyqL2VZfwcYpvsicQok2WhdREuEI0tCIZfMs2NvFZ+58nJK0nwfKKZQQmkVeWvttSA9YNmet2+WSReqhhNAMdM3URz5qKhRwwRo18aTQXhJNFS+Jb5gvilvMdeJL9WDbe2qG9zA7iiM8ung68/a0c+2Vzx9w78Af6/80yj49n8PFc6s3Vomb+jRKY4XpJYmOhUU7L51UxbItLSRkJetr6nhx6lQW7I4jpCRv23zpmnOttpqKqGw0cgB/mHLHzYZpfVq3CdiCuBTiLW9ofdPdLj/epMVzQlsjvi1NhjuhCN3MFS+yV85kJ3MxCVBMN1urGpC6Rkc0REUyy5s+eXWbbDRqfzv9z6eHM+ZT2pBXIyEnoOyq9jenHX4kT+C5qlOO8kg17CEuy9nKYgoiFPRRQXm8oJlYzkS3ZeFUQLfk+7T9fn8CghL8WYQjxHNCMzBHlFkYdBU0NAw9V+iJ2kIOrO2wAaRg42h+XsDu8bNULTwnNLCkNiSrcIAcHZSRD4xcembpgocXT6ctFmJzXRkUUvNgGdr/WbroGnquLXjsqvY3r5tQ0z2M54S2SH5KM0jLGHFi9KKTliYyv7e4niBDB2glv77keH542SnsjOj2Z//nkutko/E1gLdvvzqZDenTcwHtVlMXD5u6+JgmOd+lR/IEnusMjMZz4oZsCdVBCaQJYqMRJsfXXnsevz17KfKTo7g7n3FFibnOIEJICtnCouxLfNJVFHHNJtXwXNU5GmlCMsnwAdv1dVXcv2SuSxaphxIeLUdI05HsKQnSWh3jidlz+M3Zy8gFjBEBlH0mBiXaaE+JL2dn0RyspXmwrDsc5cTrm9hVVum30RxAiaozhy3LGTZaQXkmxbueedAli9RDCaFFEZpRyB48jNJMygVr1EQJoWmkSVE2rEwCp2xuhv7ZAJ+JRZHOQEBuYzHV7KaMVkwCtDKDqS0W8pMBP7yoAyghNIuQMAnQzGyamT1YHibpolVqoUTVqR9gDCOFv6bRKZQQmob169x+j2oDwm+eOYYSQjtN3vjeMBlSGGQxSBMgi45ByleaQyjRRgPIkvxvJbnzQSCQmEh5nPyC3xFwCCVmBobyrLi1uFhLpBZan7MOfbbPeKGc0HzcQYk2mo/7+ELzcQRfaD6OoJzQNtQ0fnhN/ad/6bYdqqFMZ2BDzSeCnUUV2VO2P4cObKycgW7l/zO/+/uXuG2bCigzjpaWIrNs50sEKazcmN+5k3ggfLHbdqmCMkKbFW8VT84+lYfmn0PGCHHS7tW8+qV/u22WMigjtO2V0/nn8ZcOfl454yRCZo7lLtqkEsp0BtbVzRtRtrZ+gQuWqIkyQitL940oK82MLPOZGJQR2hnbnqU01Tv4WbdNLln3sHsGKYYywxsZ443S1gyem3YCmUCYE/aspSLZRcD+i7/VzgGUEZoUy+X+ipKAkCt8oTmAMlWnj7soIzR/Ka27KCM0v350FyWElhXLLQ1IBcK0xyqHp4gVy5V4B26jRGcgI66W9y2+iO6iMurjzdw/9zg0meP1q5/myjd8RL5uT08euOynt89/yG1bvYoSQnt6+k0yGwxz/J7VvPI9n2Pl9MIsgbAlr9jWxpye5EDVav309vnKTMs5ieerDdFk1mysnsU5W54hks/x7P99jvt++iVq4z1ITfDfWTXk9qXv0d/75o2efydu4PmX+t5//fHJ8nQcDUnYKoSGv2TTan74158DYGuCbWVFQy/Z5ryV3sfz1cT0zvbS2r7eEeWXbVg1+G97eJe0ZKJtUhHPe7TPv+3Dry1LjxTa1opaQnkLw7KZ1TMs2Eu9Y8YphOeFJhuNx2t7WmiJVQ2WmZrOD8+8kmje5Mr1e4iYg8O58qe3z8+4YqjH8bzQoBDM5enpy9hbXEjTY9gWX7rvd3zqv0/QZw1uWE/89Pb5SrwPN1BieMMUy+29pfViem/zsPLtZVN5w5s/XPzMD+clXDJNGZQQWkvs3cU1ya74/u7KRqAhNeQK778El1FCaOAvE3Ibv03i4wi+0HwcwReajyP4QvNxBGWElhd+FFE3UUZoj806lf3712tqRm4q9pkYlBGa0DR+duZb6IiW0xuK8a9FF/L4vDPdNksZlBlHSwfeJH9yzjvYVT4FgEguxfse/w3Tun/gj6M5gDJCy4urbB3ExprZpANhFrVuJmRm/QFbh/D8erQBUugfL8b87sK2LUBhViAH+dDBL/MZJ5Rpo5XKO25tKaqK5sG0wO4IlbwnJFf4yaAcQpmq08ddlPFoPu7iC83HEXyh+TiCLzQfR1BmeAPg/th3PqAL7QfSDqAJE2zz2QtS15/mtl0qoJZH0yI/mJLspDifpiHVQQDt1Iej3z3RbbNUQBmP9reyW1MVecHaqgVYmo4m6ylJJUgZ1vOAv7RjglFGaEWSYHesnPpUDyW5NCkjxN5oGTN6t/lTUA6gjtDSJnWZNspzKQBK82nKcknagmXuGqYI6gjNSlNuD58FiVh5qrIjwyX4jD/KdAZMLDIBAzNiUiy6MAIpNCxM6Ue3dQJlhNYarRaldDIl3UxUJqnMd1IiOmkOVx36Yp+jRhmhZSoiojgfZ0PNdB6aezJ7iysJySw1lp+mxwmUaaNtr6viVw2vZVvNVAAeWHQ6p296iVk7drtsmRoo49E6oyWDIgNACJ6ds4S+QNGBL/IZN5QRWnkiOaLM1nVSIX/toxMoI7TOSAj2W+QZzOSImv7whhMoI7QdlZWiL2cSyOUBKO5LIeN9pIK+R3MCT3cGAp/tO9607FWUBrWKBdP59Y//SW08RS6gE86ZdMYi2GaJPwXlAJ71aOJz8RdNTbxEcUhD12lIpKnvTaJJSThXCANfmUhTauX5RdmPsy6b63k86dHKPtpZRCx0AiEdDA10jb5oiL6iEFtmVJOMhijtSzNnezvJmEauotKvPycYTwpNCuYDkLchWFgB1FxRzPNLpiNEoabsKo/RG4tg5DK0RkvpEY16mWyyDnhTn6PCk1WnkKxHysLT9Xc057V1I4Qgq+vEwyEkYAV08rpOXWcvvsgmFk96tJ5bK9Pic31PkZdnYOWgOEQ0b7G+tppt1ZVIIYjkcizbsZuAgONWveC30SYYT3o0APmV4jMDQjaQzZv0ZWnoibO1pgrZX3Wmg0FemjaFmr4uXicbwy6b63k86dEGyH25uBkIAPzvq1Jy85TSYcf7wiEinUl/q74DeNaj7U8ka44QlG7bVPek3TBHOZQRWjocRLeHLHKUEl1K0kWedurHDMq85YpMij1SYlsWksIvLGyaRIJ+P8AJlPFoxck4tX0JNCnRAcO2mdnVQ3mfv/DRCZTxaKet20TMNmipKCNr6BTl8lR3xylO+MNnTqCM0IysJc95fp3YOHMK3SVFlMcTzN++l7juL3x0AmWEJvJSWpbGks07ERQmDDIYZHR/CM0JlBFaIJCjLVpJNmgQMfNkdAMkTOlrPvTFPkeNMkIT0iIZDoIQ9On7Qm1oht9GcwJlep15GQExco1jTirzW3MVZYRm5bKpsnzPsLKAnUOz8v4UlAMoI7Tz5WdK6hNtTM3sJWxlqM51sKh3M2YwNMVt21RAGaEBxAPRa8haLOjbQkkmTqdd/Nezkte3uG2XCvh5BnwcQSmP5uMevtB8HMEXmo8j+ELzcQSlhCZuSn9dfCmdFTelXnbbFtVQptcpbk6bBHQdIQrBXmwJ8awmbylR4wW4jBLzL+Km1CwMXSeVB8sGBEQNiGhZwN+l7gBKCA3bWkVGFsIjBPSCR8uYYGhqPP8xgBpttGw+jK4VFqHZsvBfQwfL9iMJOYQaQosER/dco6zm8JkY1BCa7F9TKwBdFP6QINR4/GMBNd600ASa6I8sJPZVnba/6NEp1GgMSxsCBmSswehCIEHzk9o5hSIeTYAlh4isH0XGEI8FlBDawo7mwSDJQxG2RHwq/m4XTFIOJYTWHSkSP//rT0aUV6QTzGrZsdR5i9RDCaG1Fpdyz4JlI8o7i0ro1vWTXDBJOZQQGgj+eNwZhacdGN7QACk5tWP3GW5bpwJqCE0AujZ8gFYIEHD/4jO0mndu8pN2TjCeF9r/O/+393OACYCgmQcJZjQqQh9ukeKG+A5nrVMHTwvtx6d8/7qbz33NxWijP+ZnnvgHJ7VspzsSo+nxvyDyuenihvh/HTZTCTwttOJs5ssEQ4WJdKAsvS/D3Vk7N/CJJ//Flx5dAULw6i0vUpLLApztjrXextMzA1kj0IOkAaswz/n9u39JTSpOUT7LWbs2ATC3pw3DMpmS6CFi5uiFkQNuPkeNpz1awLbeePzWfau2H5x9HJdsXTMoMoBHps7nfase4jfHnY3EBvia85Z6H08L7W0vXr/m0r1bLhjobf5i6fl84PJ38HzdDGwESSPAnxacwYLOZn4/+3haS6sb5TdKbnbZbE+ixJ4BcXOm8JBDIsAHrDznb32ZGx6/l3efd1Vix++WFLtlnwp4uo02iAD2CxqU1wP8Z9ZxdGRyOV9kE4+nq85Domlsrplyp9tmqIA6QhuliaCZFkLip05xADWEJqXEtveJTUqwLAJmHhGJfdld49RAjTYa/VvtMiaDG4iBbChEpqlym8vGKYESQgtms+RCEYgE+jcQU5hkt/y5dKdQouosyfc3wzRR2EAc0As9Udv7QzvHCkoIrSNcLAkOeVQpwbQg5G9OcQolqk5sWfBmEX2fF9MMSJnu2qUQSng0PZnMk7cLu6A0UegQmLa/C8pBlBCaMLRd5C3IWZCzC395iZbL+UpzCCWqTrO0bAHZnIUtC73N/vhoNmKV27apghIeTX4+ZKNrzxrSKqS4ti2wrbz8aunIrVE+E4ISqzcGEDdnBIILETwpPx9OuW2PSiglNB/3UKLq9HEfX2g+juALzccRlBPaNvE1Y5f4ij/35DDKdAY2iK/XGVjNFiEANLIyBMY0+Tl/CYcDKDFgC6AJ2ZyXYXaUldEdigIIM4TVctZDgcYnLvAnPScYZYQmpc6O0lJymRDl8cIe4XTEoKfWyqFgE8JplBGahU6PFiMmcyRLdIQN4ZTJ/F09fgx4B1BGaFmgLJemsyo6GL4qU2QgTTXaqG6jTJURwiIRC9IXDrJqSg1tsQhSFyRK/FRQTqCMRwOdR+ZM40fnnkTe0BG25Mo1m5nd3eu2YUqghEdrF5/S+4wAvzjzBPJGYQhNaoK7TphHdyjksnVqoITQ0rB4Y2k1ifDIarIrGHbBIvVQQmgZIlpDd4LSZGZYuZCSklTWJavUQgmh2aTvrrT7+NB/n6comwPAsCze+vTLZANKvALXUaIzICmZkjOCLN3TwY//cD87KkqoiycpzeT4zwmz3DZPCZQQmo2OYdsI26YskyPSmkf2p1DcVl/usnVq4Pl6Y4do/HIRSSJ2jmWdOzixaxfLOnZQlkuQ12HOtnY+v/ieNW7b6XU8v3pjs7jB1CjRQcdGR8fGwMJG8OSUWaSMIHrCpqOY7gqhzbxu61Vxt232Ip72aFmx/O4QAT1PgCRFpAmTIEqSEBqS0myGgLQpNnNsmj6lPK+Jnl/OvjPqtt1exNNC62LuFSHS5Bg+VpYngIVka2WhfZYoCnLyxlY6SqLC1LWb3LDV63haaAINiQH75ehJB4N0hYt4Ykk1lhB0F4WRAtLBAFKI492x1tt4utdp0Jq3qA6ADWjYQrBuRh2dpTEArli9i50lpaT0EDtnlHD2tl0EbPuT7lrtTTzt0arobUgSJkoCgObK0kGRAfRESinLWWyvKqEok8AOaL+5butVq92y18t42qMhV3To4uPJXmqLYnQTL6obeYohKE6n+OFjr/AXQE4gnvZoADPkd2IaOuX0UJNuGXmCDT987DxfZBOM54UGIMkTIcOyjo3EkvtCbhh5k62Vfi4LJ/B21dlPEWlpo4mOSDnpoIFm9m96kjbrpla6a5wiKOHRIDu3iypWV80GoSEoDHhITaNqv6VDPhODEkIzCGfSxEjqRSOOhfP+lk4nUEJoEfriIKnvHbk/YHOVv3rDCZQQWrX8TkLHpr6nh5ZoiN5QkN5QgLw0SRRF3DZPCZToDABIBF+55Fx+d/IJg2WGZVFp+5mtnUAZoWUx+OuShcPKTF2nK+gHFnICJapOAB2LSH6k99L8oVpHUEhoNu9/cuWwslg2y8zePpcsUgtlqs4cgvc+8zwzenq5Z8FcqpIp1sxqoKIv+0eodds8z+P5pdwDvCy+1J6lpCqgW3SURFldV8v8XXv+fWnfhy532zYVUEZoAOvFlz9oYnxPgAxhXjhXfv4Rt21SBaWE5uMeynQGfNzFF5qPI/hC83EEX2g+jqDMONpti+4KPDRzStawbZEJGASSie2/feg8P8KLQyjT6/zouY/ImkSa6kSSZDBIVtfZVBx47rbHLz7FbdtUQImq87dTfvmFqlSG+ngCw5aUZrJUpdKU2pqfGNYhlBBacyD0uYae4XOampQENd2fUncIJYQmsOycMbI5miry49c6hRJCC+ay2tpZU4aV9cSiSMNyySL1UKLXaaMT0pL8Z9lipnT0kIiE2F5bybsf+TdwltvmKYESHi0fDoprH32MOS17eWFaLYlwkM/ccwdtAX9Pp1Oo4dGkJh+YOZctgWJe+981ZAI6W0P1xDJ+6HenUEJoieJwYGvlVN787EbaYhHKU1liWUFU99PzOIUSVef2ulLxyvXbsKJ55qZa2V0X4nenL6QjFDv0xT7jghIerTae5JT23YStQi/z6jUbqEyleWzOTN7ism2qoIRHe+OLm8SAyAY4b9sutlWXumSReightHXVVSPK8rpGT3GU0s+2+YNpDuD5qlN8Lrmg6oqLuesPd1HTlxgsf3jxfJ6dNYWoltOuffXfzXeteozna6fJ9li5XNayLfXclHmPvXX147ZhW1uD0u58ZMaiJ9/5wkf/4+KjTGo8u3pDfD6pYdvNFX29NblQERe3dnP+yxupjvexdloDz86ZSdrK8fisenq+8i76QhFe++ZP8HzDbJAShKAom6bpvj/w/pUFfe0uLk/9acmZ8z/xxLv3uPx4kw7vejTJu5HUzIp3s7qumL5ImLuHxN0A6IuEQAienjqXny+7sCAyGMy5ngxF+OCrruPyTauY0dvB1L7u6NKW7f8A/FUfh4mX22jvRNPYWVZJTtOIM9xz5wXsKYmBAEsIHpmxcNSb2JrG49PnD35e0NG8aEKt9ije9WhwH3D6zO4OIhmbpt/+kw3TG9hWV0Vtd5w1U2p4un4hSPjtyedzfNsuWovLRr3Rkrbdg//eU1LRPNUZ+z2Fdz2a4OuYpmlLyVtWvkxpJs9pG3fwxkee4xWrN3HRSxvI9OdXr07G+cZ9f6AyOTIOx0efvIcTWncCkDIC8pkpc6519Dk8gmc9mvxyUUp8Phl8rmHmLz7+8Kp3FCKk9R8DQplCJmIdm6KeHtkcLeb//v5Tnp06j4a+bhKBYPa8XRvt6T0dib3R4va1tdOf3FDVcP2Hn36/HxXmCPBsr3Mo31t8u3zFuvZhGaHWV1fw8bddRG2iTT73kxO969mPEZR4wUHLZP812/M6unn7k6vwReYMSrzkOW3dI9y2LiVRPyC3YyghNF1KuiOhYWVPz6znkflzXLJIPTzbGRhKcSIh3/HO5eLdj73A7PYeVs6o53sXnUJlxg+U7BRKCO3x2trc1uqy8PVvuGRYeXk655JF6qFE1YnQxNSexIjiE3e3uWCMmijh0VKRGB+5fyU/u+RUAkKQ0zQqu3p5zUsbAD+ztRMoIbSyZB+tM+uZk97XJovYUJvscc8oxVCi6iyys2yaVT+sLB0Osave327nFEoILRsIMVpIhL5Q1AVr1EQJoWma+bfjdu0cXmbbHL97s0sWqYcSc50A99d/W66etZiXps2kNJXkyhee4Y7Z8xI/eeRiP9e1AygjtI+dd++7dM3++evXrWZLRQUPTFtk/ea+s5XoDB0LKCM0H3dRoo3m4z6+0HwcwReajyP4QvNxBKV6XX+r//2UlKFvNqQVf8Pua/0knQ6iTK9zRd1trcVC1JTGM5iGTiKmY+bkZVe0v+det21TAWU8Wm06W1MVzwx+rojD3prwPfjNB0dQQmi/XnCnfmLa5KXZ02itKCWSzbFgZzMl8YyfZ8AhlPg1V7e2vPqludPZUV9NJhSkuyTGM4vn0FXszz45hRJCE1Z+Znt5ybAyW9PYXVfhkkXqoYTQsuGgFbBG7q2rTna6YI2aKCG0WF9f8pQ9LwwrK8n0ckLLapcsUg8lOgMtFfWZN+3+J9WpdrZWzKI428eJLavJWkVum6YMSggt1ttcnKSCeV1bmde1dbA8QZ2LVqmFElVnfaqvSRIlTxSJwEYjQxmSoNumKYMSQjN1PRgkg0EagUTDJkScICP3evpMDEoITTcQAeKIIeFFBTb5oMkVb9vqb1d3ACWEVpHTgzAyncDu6nJ2l0QCkes7v+mCWUrh+Un1O4t//MBiu+3CeanVdDKDBBVkAxpPLK7mr8tOYFvZTLrTedlSEgvKW0r8QFYThOeF9u+iH9rz8jtEMB8mxb6UPDld47XXv4XtlWXIrsxolz4mbyk51zFDPY6nq86fnPCPsxMlpcK0oqQYPgUVtGwue3EzUtcgqI92+TniU/HbHDFUATwttGQ4/K+KeAppx4CRnrtvIDiffsBFHG+bKNtUw9NCE1KaJdkMQbJUsWPYsebSGP9YtqCQjid3wLxj3m5XOIinhRbJm2dldUERXVSyhym8jBbs4Z8nz+Saj7wRU9dZtLcXrAPqqclJe72M5zsDd5d9z17a2yKmsH5YeW+oiI8t/wJP15SzIRTENjQwh72LX8hbSt7lqLEexvNznaYZ+PauaMMnKlJ7iLAvF8WDc89Gt3PsLA5T0xmXzT+c4mnv7jaef7mvTfxvY4AOuTswi1Zm0UUdL9ScSGW6k9Ludio7E/QGQte5bafX8bzQAFrNYP7epafRWR0kYnTTkNxLW3gKi/f2UZFKx1O3Vv3abRu9juerTgArFCQTjPKv+ZcOKy9N9vDCr2b7idUdQAmPZsOoCQXC1qgzAj4TgBJCywYDXwxnRy7SqO9qd8EaNVFCaIEUuRM270S39g3M1nd0Qz7iolVqoUQbTTdEpjKe4JJn19BRWkwkl6MskWJbbbnbpimDEkKLinwqGY5QmspQ39UDQM7QEba7dqmEEkKLa3qoIZWhNxpGCoGp6wRNE6kp0XI4JlBCaBEp4nury5nW3j1YZgnB2pn1B7nKZzxRQmhdsWl3JqsNcoEAtV29ZIIBNk6vR8v72wWcwvOT6gP8teE3csvs6YOfhW1z3Mat8rKOd/v1pwMo4dEAqnvbnhObtZNbqirQbUlDRyslZt9Mt+1SBWU8GsDm4pu1Dhl70RJsPrvv+te7bY9KKCU0H/fw2yc+juALzccRfKH5OIIvNB9HUGZ443/OeuT6GjP5rdUz64jk87xiw1ZZYqcj162/Luu2bSqghNDEzcncnGWzA1vK9gVHvnvhQvHDP69I43t1R/D88Ib4SsrCMDRSFq99dgOvf24TiVCAH1y0jCVde7jjrjP9XAMO4HmPpqFpthB8+N6VvO3plyilhxBZLvjVRv7vnJPdNk8ZPF9tDPjrp5dMZV1tDXupZxszAMmnH38YS7w94KZ9quB9oRkaaIJXPreN6a0DG4gFbVQjpCCD6HHTPlXwvNDQCk2wE7e2jji0NVqLxAg5bZKKeF9o/eyoGbl9896F8/B7As6gjNB++qqTiUf2hXtfX1/Bn05fhCDn7W73MYLne53YEgRkwzZvbnwdp63bQzJo8NysWt685lls36c5gueFFshbaJbNNS+spNsI0xWKMa+nhw//7V9cvmUdOsaocUV9xhfPC01KyIaCtFHJ1Q9voTyVZU95jO9ccRknte5iTsafgXICz7fRTF2jYncX7/jPespTBVFN6U7wyX88y62nX4SG5dedDuB5oZHJs3RrKwF7+G7h2niKvBXGRoJY/gOXrFMGz1ed5G12lcdGFGcMnYpcDwFSSMQHEFd9oD+FjwRE/39zwFuRK/5yWN8p3hACXgdUA39H3rHz6B5i8uNpj7bog615DI3qvjTWfr3Lu5fOYXH3DgwEgiCCIBAEhCjoTBMgQsCfEcvvHPOXijcUA88AfwT+D9iIeMOlB7/I+3haaAhhRLB55fpdgMBCYPf/d9GeTl6zcRUwtNMpKDh5yT7HJgCWI5bPHOO3XgecMORzCPja0T3I5MfbQgNK+nKkggMtBIHsF0+IHLXJvlGu2L9vMPh51hi/cs4Yy5TC80IzbcnfT5xNZshwmS3gktZHGD1fxUCZGPrZBJ4e41feM0rZv8d4rWfxttCk/JchYWt1KR9+03n8dels7l4ygz+ePYMqO4nEZnh6RUlBU0M/SwksR65Ije077/g38AUYzDp7P/Dho36WSY7nV9iWf7xT5uQ++TT09bLir79jQXcHgjwGvRhkkSA1pA08BpwErAH+F1iNXHH4L6nQ84wg7+gZp0eZ1Hh+eCMdMMgGdb7z+/uZZ23n7D07CPaPqUkC2EQR8g9i3Edt5R1ZwJ926MfzQsuHdAgbLN3VyXyrkyD7Bm5tBDp+Llgn8HYbDbCLAhAy6CoPsZ2pdFBOHoMEUboox0R4u+1wjOB5odEfPvTz115AurTQ+O8mRi8lSF0g98s05jMxeL4zIL6RlwPLuQGuXLOK3/3+54RNi05qMGixquRtnm9CuI33X7AcGOGHS9et5ct3/5OAqWMjiJBEcMCksD7jiOeFJkRhqrw60cftv/klEbOQrUeiESBPCn/doxN4vo0m+0f6L9qwflBkA2hAAM3bbYdjBM8LDQRIyZ6yshFHbAQGGeWX8DiB94UmwLBs3vrYi+yNDU/J82T9HIrkb5Wf8HYC7wvNlpiGTm8wRCRhkKCENFGaA9V84RWXuW2dMni+M0DO3EzQmPv9s89kb6yYizdvYXdpCd87+wxeu3Y1sMhtC5XA8x5NfiE6D0nNp/7+AHZe8D9XL+fTl17CeVu28rpnXvTTjjmE5wdsh3J75Y9zZ3TtDehYPFddk3x920dHbibwmRCUEpqPe3i+6vQ5NvCF5uMIvtB8HMEXmo8jeH8cbT/2io/35SiKCSQG6ZVT5LdPddsmFVCq17lLNFpx6rVAIeIGeQQl7E5Nl98tcts2r6OUR+ujRhvYMyCAEJIktVF3rVIDpdpo+ijRHTW1XoFrKPOWP/GKR83ecJjmqlLsoiyVtFBGByGy7BIfudht+7yOMlVnbTqvr1o0kze/+G9CdmGLnQSK6SVB9H5GBt3wGUeU8WjxSJDTd740KDIYiBUkiZA/8IU+44IyQsuGQkRkbtRjYtRgLz7jiTJVZ19xEWZmZJKUNFG/znQAZTyaBqRTxXRTjt0fkK+LCtqoJkuYB8SvfL1NIMp4tIreOLamsdeeSjMNQGHLnU4WiHMyLw/ErfWZAJTxaCWpNEVad3/QUK3/D0wECaJ0U+GLbAJRxqOlDZ0nT1hIR1E11Z19lPcmmLunhRAWvZQwgxa3TfQ0nhLaK962KV1sy9DegL72uV/NOX6g/Oo3vLzgBAk9oQoCpkV3aZTW6lIMXbJg515C2GT8UGYTiieEdtL79mozUn3WVMtma3kxIeSSi/9ns72pOJib3536QlTw9SLTHOxdCiCUzSFzaTBSBMwgDfI7EuAp8RMBlAPdJsLQkIss9NXnynf7VetR4InVG1e8+WUzU1qkPzR3CkHTImfoBCyLCzftIWsYiEyOV28cuSG9qqWHqng3J3W+zG5mYBJ8q0DcDMzOoSVAxPp7CNLA+txZ8n3Kh3E/UjzRGUgVhbWHZjeg2Ta5/ujbeV3nvgXTqOtN8sT8qeS0/R5VSoKZPEmjiETYwAwH0DB/C8wu+L6CyAAECBP9q0+JH/srPY4QTwitIxwWaAJ7PzFJIXhhShXZoMGfls0jEwzSWV5KR0UZeT2AkCCkTVKL0hcKEUAKABvtANkG5NsdeSAP4gmh9URCEkCzR+4HbuhNAoXlQIniIqSmgRD0lhezd2oVs1Lb2R6eRtg0B5PEjjYl1R9l7akJfAxP4wmhlSQLPUZb09DsfSI5c3sLZbk81fE0JzV3jrguXRKiWa9BA2KpDDn0ZwEMbPaXrIa9+XT5v6sm6hm8jieEFkymv1nUlwHA1gQNvUnO2bKXvoBBj6HLk7c2y8p4csR15ZleNCk5o2sNKSlefZb839OAVwj4RBDzfIH9K4Fca2DeeI587zyHH8tTeKLXCXDqO7Zs31VROiMVMihPZqlLZa58+ucz/gFw0nWbV739pQ0nZipK6AsXoiDotsXbnvkbdR3NdNFAC1O1M+T7vPEyjkE8I7RD8ZZrXpBfvu+PPNtwApamcWrzauZ276KVSopJUSR/6k+qTyCeqDrHQktpCf+eeR4doQq6A2U8MPVMdkXq6aQCDYt14jvKvAs3UOblnr1xD1Lse1xLM3io7kwEknaqWSQ/7oewmkCUEVplKjOirKe8CDDoocZ5gxRDGaHlgsERZcmiKGEsTII8Im7z22gTiCcm1ceCqWvES2JEk2kQkIgVlnBniJIlwHnyXWr0ilxCGY8mKQRNbquroq2umnwwQE1HF10UYyN8jzbBKOPRinsSWNEwkXQGBOiWzYwtXQg0JLbv0SYYZYRmaUKe+dwGsbuqGpBUdiQwLAuDPMW0jr4Pz2fcUKbqXDt7WqTYSjG1tZPq1gSaJQmTo4IeFsubRu7D8xlXlJkZANgkvirzhPs324GJThmdTJVf9ttnE4wyHg2glE4EFnmC5AgSJYFOyh+odQBl2mgABubiena9bGGgYWMjZCU7lHoHbqFU1enjHkpVnT7u4QvNxxF8ofk4gi80H0dQrsd1xXU7Ipa0/ymF+Mt9v5z5I7ftUQWlep2vfOvGvUY4Um9qAmFL8lLy4G3T/cFaB1DKo+mhcP2qyhJaokGEhBmJNGe9c5v5xC9mKfUe3ECZNtplb17/1fUVxTQXhZBCYGuCbSVRstGw7rZtKqCM0Pp0cUJ7ODCivC0SdsEa9VBGaOFMdmbYtEaUl6fTLlijHsoILa9pCxqSuWEhE4KWzdzukTvYfcYfZRrBlRYEk2kWdHZRk0jRHQmxtbyM0qy/5tEJlBFaTocFnV1M7Y/BMbUvxayePp5tqHbZMjVQpuoMWbbWsF+gl1jOxDSUeQWuooxHq8qaWk1bL8WJDF3lRbTVlBEwLQKjxFTzGX+UmBl4ouxL8S59anFV17722KbZ9bx4/Ex2FoXoTqcfvn/F0gtcNNHzKFFv1Pb2xYaKDGDOtmZC2TxVWZMqYZzvkmnKoITQNG3k+JkmIZzNE7VsYvbI4z7ji6fbaN8+60FR3NJqNTTME5W7bYb+rhLREL0lUTpCAWzb8ifWJxhvtdHE8umPitM3a5FAoDVUQy5kIDWBkJJSLUFvcRG1fe2U9Ka4+9Tz2VVVxt1TaggIyVl72uSs7vhpX73vjJVuP4YX8U7VKZaXvBA4fks4SKAtVEcmGkTqhQjcUtPoEqXsKa9j5awT2TKjgUu3PAD5DM2xMDtLirhjwUzRVhR91u3H8CreERpcXWn3GHsjU7DEyATpmpSEU4Xo3TvKZzCjcxdz4328emcrUAiyvKa6jM9c9vQvHbZbCbwktKCBhURgGdqoiTdtTQz5t45h5Znfm6A4V8iprkmwhSh3yF6l8JLQ7mzTquzKbCeBvE02NLyfkwsaZKKFEBuVqU4ygSB94WIsXSdi2RiWzQkdPZRmcle7YbzX8Y7Q5Ir2pfmXloUzPbI010M4mScVDmALiJdEyJTpxHJJ5nZuZlH7Ov689PUkwmF6DAMjb3Lt2i2Ecrkvf/ahc0y3H8WLeKvXuR+/r/3j1FKzY+feqikiWxShtaKEINAXjZIzDPoMnbvqqqiWedb/X4M/xDGBeHoc7drWN+0GtLsafiwTqTIeXjKP3ZqgIptjY3GMraXFVJomC5t7Jf151n0mBk8LbYCG1B65qr5WVGdyzEpniOVynNodZ2txEX2xCJZ3nfoxg3faaAchJO18EV1EsxlKcjk0wJCS+fEE0WyeTuQet230OkoI7YTer4SenTEfRhn0sDXJE39aPM15q9RCCaEBJCOldntRZER5X3Dkziif8UcZofWYpvXQtFqy+r5H7g4H2e1vt3MEJToDAHYwIKSus7GqHFMXWJqgNxgkp/mjGk6gjNDSAYPzdrdTmckPKc2yu6HKNZtUQp2qU8rm4SIrUJXJumCNeigjtKC017dERqYTaCvy22hOoIzQokJb/dDUKnoDhdaCDayqLCEVVKb14CrKvOUlmXzj+mz++t8vmEZlJkfK0EkbOpdu3mVDmdvmeR5lPNotd50oT9jVmj21tQtLCCoyOV67eTd9oUix27apgKdXb4zGFW/fckbAlvdnNNbd+5u5p7ltjyooJzQfd1Cm6vRxF19oPo7gC83HEXyh+TiCMuNoa8StoopdVglpAZIkUZknrDfIL/u9IQdQRmg17LaiZEQ7VUgEFXQJgWXhe3VHUEZoGrZ4mcVY/Y/cQh2z2eKvEXIIZX7N7dQMigxAotFKrYsWqYUyHs1CQ8PGwBr8bOIv43YKZYRWRJI8kcHgLzoWIfxkFk6hTNVpYYyIMAR+GiinUEZochSZ+TiHMkIrJsX++zpj+Ol5nMLzqzfaxIdMiOoGkixRUoSwEYTJEaOPErp1Xd7mJxuYYDzt0VrE9XYRpl5FM8W0o5EmRh8VdCORmAh60P7itp0q4NleZ7f4YGUZWREmA4CGSQ17MYkg0SkmTjflSKy4y6YqgWc9Wp7kM6F+kQ0gKAhugBh9SIoy+Ew4nvVoJqNvQR/a+9Sw+dwrrnpX89Uv7C7JmMW3//PUTzlmoGJ4tjOwW/zPy3WIRcYQDyYR5Ikw4Mj/vOhUvn/GubSVBTlr12bOW7eTq9beK0vI2wY8C1yFXLEX4NtnPTgdOAN46fonLlz/pVc+Jar2dt8Q7cucFU7nv/fGtjc84PxTTh48K7R28T9bSwjNkggsBAZ5nq+fjWkY1PbG2arPJiMLMwWVZjunJZ/A0mFVzUzO3Ltp6K0++u0zP2QB36NfoVLQVNKRfn/Djq7YgH9MFQUeuLrzTRc7+5STB8+20SyoAkGCYgQGNmEemr+Q8z7yCR6uOIWs3Dcd1WlUsy68hIiZ57jO3eSHVK8SvgV8kyHvypbiE3W7umND6+ZIMn/RP6O/XeTIw01CPNtGC2NEQBLERFDIA/WqNWuY3dFFTaekT48NO7/TKAR7KcmmyRIABuN0GOz3ngJ5W+j28JpAAKYmzgLWTcTzTHY869HSZNt1cmjYCEAimdfZwZVrV1OV7x1xfnH/KEdPOIrOsAjwGaBraEEupFv7Zy6WgGHKe8b3KbyDZ4UWgIyFRiHKBhR8TqGym55tpshKDZ4bstMsyqwlEQzx58Wnow+ZqhJwJfB2oLO/KKHb9vv3zKjYnO8Xm6UJUrHg/70q87a9E/1ckxUPdwbetqaMwHEaeTKU9kts3+/KBtaXzODPS07h9nPmMrerjZypc9evvpQvw9qsw8+A7yJXSIBvn/VgCFgAbL3+iQsTAN876b4Ti3tS55V3JH/7ur5rexx+xEmFh4X2jvWV2AtEv3fKE8BkeLvsqbq5rArpHddv/2C1GzaqhGc7Axpauw0LdAayCwvSRAhQSHltEmR+SzcL6XjRPSvVwbNCi2FcnMHIROlDAGmKsQhiERw8J0gOSdbPZucAnu0MhORPsxZSpikmvV+VOYCGBej3OWuZmni2jTZAXFy3ziC4wMAUPdQx9LdVRCdF8of+0lsH8LzQBsiL62ShnVaCjUaYBAKLkLzNF5oDeLbq3J8eKtGwKaGDMtoIkCOB3zxzCs92BvbHJEyCKvT+qSWLAFKdx3cdZTxahiAghvQ8BWn80O9OoYzQYnTTQXn/zCf0UYTmbyB2DGXqjnK6kcAupiARVNFGHc1um6UMygitg2pq2UMNrUBhtUUrDdS5a5YyKFN1ZtHlXqZjEsQkwF6m0UOZGmM7xwDKjKMB7BKNVhtTNYlGFS32TPlVP/iGQyglNB/3UKbq9HEXX2g+juALzccRfKH5OIIy42gAosm8A7i6/2NcNhplLpqjFMr0OkWTmYYRk5tSNhq+V3cAlV7yaDPoQjSZFzluiYKo5NEO9KDp89Zs7V7SkWjQLIsNlaXysk0Pn3b90+9d6aiBHkcljzYqs3a1hZY19zbISAStqIg5WVM8Neu8Z3928q3T3bbNSygvtFdubtb6yorZWFbM09UVbCgrpTKf5e+zz9jmtm1eQgmhiSZz5oGOJYNBNpWVkAgEQAhSAYOXqmuYkssp8W6cQpWXufVAB7oiBll9+Ny6pWmUp/om3CiVUEVoB9zpNKV3B0KOjP6+J1oyoQaphipCOyDHdexi+dpHh5WduHcTq6sbXLLImyg1MzAa9fE416y/hzN3rmF17Wzmde7mvG2rmP2R77ttmqdQXmjxaAUCOGfHGs7ZsQaAPSW1fj6ycUb5qvNfc09ha+W0wc+mpvPnpZdh+0obV45pjyaazGuAP0F/dFD4jGw0bhnP7+gM56nq6xz8bNgWNb27OGfnJsAPmzZeHFNTUKLJvICCsMJAhNF/CC2y0ag/xH2uAg6d40lKfnb7Dzl5zzY+d85VBCyN49t2s3zzi6yureFP806zHzhuYWMuEvsuAuRnghLg7W/bKsqamys21VZk/710Xkp+0vCTlh2CY0Joosm8FPj3YV4WB4qhf7smlAJZ4LdA45juICWfuf9OHq1cwO1/+wOVmUK2noSI0hqo5lsXn0dXTHL34iVYAWMwC2N5Oks+EsDSBNN7UmSEIB3U6Q0GKM+axMMGqaAhEeKfwAdko7G7/zkF8AXgQ4CGlL9DMh24nMJY36fkDYF/DphnfD75K0vX3l6fyohX7G2nOpOjN2DsfGpK9ZaZfanzS3J57aEZNXQUhQYfSTdtS7dtvSibl8XprL2zpuw/wP/KGwJjmukQTeYZwK3AicBjwAdlo7FhTO/zYPc9RoTmmhH1za289P2vU5QbXt5LGc1FVbzx7W9kfs9u7l5yPCAKlXhgeNP2hJZutpcWUZXMsLWiuFC4L0PQE7LROBtANJnvBX4yeKE94rHzwAJ5Q2Cb+ELqbRjabwKWzXvWbSNs7XOaeU3QFwpx/6xa9pREht9hv1uWJtP0xiIvyhsCSw/1LkSTGQN2wrDoN5uABbLROKr/R653BkST6WoSiFdveIGi3MiaLxOE8mSKqfE+dseqGRjzHS3D1JaKYmZ1J+ksGjWWx1miyazt//dVg6Wj/8ADwGsABPIjANMSqWEiAwjYEqQcKTIYMTRdmswCnCi+kZ872hfux4UwIsTSPOCEMVx7UFwXGrgbl2BqohuN3IjyHaUVJEIhWopjBO19eQdGa4yFTYtUQCdkWqMcJQUk+v/dOgaTWgEkYi9AKjCymSoBgSBgjWLNfvqVQkDBU3aNPHn0794PG2gfw7UHxXWhyUajh0LbarwwD33KPv4xfykBehFDLmuLhVhVNYWfnXMGOcMgZKX3eSAbjKH/g6VkfnucLRUxwnmzcN5wr/JN2WgM5NRuYkB0QoDYl56ln1XA3wrH+Si2tFuiYbYWR4ed1BoNIzXBstaegz5bSTLD3spigO/JGwKHFJpsNJ4G9k/K8VPZaBx1/oRjoo0GIJrMNsY+nmACnwReBSSBu4BZQA74NbCDg8xvDqWsu4+rVz/Blx78C1XJDI/PmMcbr/lfqnvSVKdSpKIaT0+fjRAQyJtWxLb1mu4EiVgETdcI50w6IgE7IOnICq2sJpEJdBWFct3Fob+iab+WjcawTk7/SpK3UxgS/i22nAFcBmwDfiNvCCQGz/1CambAtv8oBcct7O2T0xOZ3RWZ3Hf+sGTmy4s7+xorUtkTd8bC03eUF+nS0EyE2D6lPb52amd8ai6gi00NFc8lIqF/yBsC/xjje0U0mQHgzRSqy8eBvx1t+wyOIaHtj2gyFzJ6XqVpA724Q1y/GZg98HHUk6TkB3/4Mx+58rVYmkbQMskZAbAl37r7du6ae+JN/7395C8e6TP47OOYFdoA/Q3pJuBXstE4opyYB+vV/uQ3t1MfT/Ceq6+itaSE8mSKTz70EO26xreffL0f33acOKZnBgBko9EKvG2i7p+M5ViwtY3137qFTEBg2SHisRxXvvVDfHuivlRBjnmhTTRzuvaSCifotAUZLUyR7MXUggRG7V/6HCnKC21XrJpQRw2bSkoLBVIyRW5G278/6HNUKC80IxMhbuybwkEIuu0Gqnrj7hnlQVwfR3ObvdHKEWUpLcL26pHlPkeOKkJLHOjAH5eegNxvOH1zXQmLO1sm3CiVUEJostEoPtCxZCzAjy48mXik0Px/bmYtX7viXGa27j22x30mGX4bTdg8umAaDy+egZAF33bBti0YidRLbtvmJZQX2o7qSuubf79Tv2fBYvaWVPL2F/5LQgbsrzz9pqVu2+YllKg6D4phPN/4xLVibsvuB1+15rlMTWf2/K88+iZ/w8A4c8xPQY0XB5qGko2GP83kACp5tJmjlO1y2ghVUcajDdC/KuQE2Wjc4bYtKqGc0HzcQaWq08dFfKH5OIIvNB9HUE5oosl8o2gy73LbDtVQqjMwylhan2w0/Ih7DqCMRxNNo266POBku8/4MinnOkWTOQ94uf/jItlobB7DZaP+qESTWSIbDX+V4wQz6TyaaDKzwEYKPxID2CSazOTBrzooI7ep+4w7k0pooskMAcFRDkVFk7nsSO4pG43M0VnlMxYmldCA+w9y7AnHrPA5bCZbG+1gkYcCR3JD0WSKo9ryL5YXA20UdsNXI1f4CQpGYbJ5tJ7xvuFRiqyVQkDAMBAC4ojl/ka9UZhsQts/dtdQjmjHr2gyj2yIQywXQM0oRwzE8vlHdE8PM9mEdjB7D3hMNJkLDnLdAXdIHYKbD3LM32+wH5NNaAfzaAfjgG27o6g6LznIsSBi+dlHeF9PMmmEJprMjx3F5RPRI606yDEBPIZYHjrIOUoxaYRGIXjdwTjg2n/ZaLQd8KIm80jfwVgiS/ozDv1MJqGtOsRxIZrM3iO4b9kRXANjm1E4oiEXLzJpVm+IJtNkbBmadlDwNs0UYuNuBS4ADhSV+rb+8+6QjcZ/x27Q8meAU8dwps3YftBtwEzkivSYbXATsTwKvAs4HngE+ANyhd1/7ATgHYAF/AK5Yt1kEpoThl4nG41fjenMwnjZeA94W8gVx/4gemFo51FgaIfnNuSKdyOWnwf8h33ePA2cO5mqTif47GGcOxGC0BHLL5yA+4435zNcZADXIZbXU8haM7TJEAE+7gttOEc6fDKeHDTP1THCaO9JA0qAitHO94U2nNsP49yJqspXTNB9x5P7gc79ylYhV2xg9Hd4+2QS2lj/x8r+PwvIAN2HOP8lCh2HW4EbDsOeVYdx7li5ZlJ0BuSKBHAp8BCFrCorgCv7j/6QQhNkO7AF+Dhyxe8mU2fgR8D7D3GaLRuNUXum4x57Qyx/mEJb5WBI5Aqtv4d2NQVxbhoUk1heDXQjVxxWtpfJyLHfw9nHWATh5B6AUTJ+HQC5IgX8ZpTyo86xNFmYTFXnoRJ22bLRSDliSf/3jeGcKw99ihpMJqF98xDHj6gKFE3mqLkPx8CmQxxPIVf88xDnKMOkEZpsNBIUGvcH4kgzUBxpdXswgdrIFUVHeF9PMmmE1s8RhcoWTeZxBzomG40jbSd97iDHfneE9/Qsk01oB/MiB+s+H3+gA0OyAx8ecsWmA36nXPE/R3RPDzPZhHYwDvYsTx7k2JGHFpUrNGDoipGxTvwrx2Qa3jgUBxyLko3GDtE0+mHZaBxd5gq5ouyorleEyebR3nKQYwdrMx0Q0WROOUJbfA6DSSU02Wg8xOjtIikbjaYjvO3CozDJZ4xMKqEByEZDozCPOUCco2sCPHp0FvmMhUkz13m0iCbTZpSGv59nwBkmnUc7Cm4dpcxPM+wQyng0GNxI/DIFz9YmG406l01SBqWE5uMeKlWdPi7iC83HEXyh+TiCLzQfR5iwuc7+qZ1d7Bu7MoGobDQ8E6juG3P/rT9wUt2DK2dOOassm+vcOrVmhrwhkHXbrmORCel1iibTAEYVlFcGSG84895F33r1+S/bgSGLNUyb8t7EZ7tuqfyae5Ydm0xU1bnjQAdEk/nqCfpOR/nN+ctW20EDhNj3Z2j0xSJfddu2Y5GJEtrBdlt/Y4K+01FSofDIdWdCEPTHJUfFjc7AsRB24KiJpbNo9sgZrFB+ZCag5dc+U3PvrK/b6ys+LNdWfVS+4Y0P2uIbeaV2SLmx8LFONJm6bDRGy800aThp025OfK6Zr102PMLo/N0dFEJQFBBNpnjokX+03nTRVewtqaUkneSLj9wurtj00l1CfKhIftLRLYKu4dYK23uBi1367nHh1LY9fPiJR0mEQtx29hlkdZ0LN26iUw7fV/yFu+/99jve8GH2VJViBnR0s5J3VnyIe359M3Obd2yGOQ0uPYKjjLvQRJP5OIdeh/+K8f5ep4naeQTwxXvu4zP3PYClaQQsi7ov3ohxc0qWmNYPu24u/uCU9j2v2V19CZZRaKVYhkZ7ZYybLnwr9T29R7YxZhIyEW20s8ZwzqQf4rh3/r5UAiHLIprPc//C+WDZWEGDbkP/wIUfaDY2VVeUDYhsAKkJmkvLWFc1GSJUjQ9uzQxM+q7ZM7W1fPo1r6IrGsEWggfnzeWrl1xCVSYLCAgYbIwG7qjPxSt1a0hztL9XOj2epDbdN+l/cGPFrTbapH/BIU3ws7PP4OdnnEYsl+esnS285cVtBG2bDVWl/HbpfC7fsOq1/1l0PJY+ZCRECGa1dXLpS5uYMi3Ey9U/vHpx+3f/4t6TOMO4ejTRZB62gESTeb1oMr9/FGHYD+e7pogm0xJNpi2azEPFzjgoXUWFFAJS1+iLhrh34QxuPfcE2qNhFnT0ct72Zi7a9qJoLxqZSfusbZu4aOszTO3qEl85501//v4pP5Av1X209GjsOdYZ1yko0WR2MnpoydHYBszar6xZNhoT0gsTTeY5jNyIIvs3uxzevT6XDLxq44bc3ScvAU1Qls5x7q5OKjJ58gJmdHTTGg1w2tbHZE9ZTHz6VdeClGhScnzzTp7+3ucI2YUZup5QlNdffQM//+cP8nN6fjJaLlJPMN5V51hFBiNFBlAvmkxTNhoTUaU/MkqZEE1mmWw0eg7nRkt3tz62fNNW7jtpMXlN57ydnRTlCxuUAxL2VpTRHhRsL50hPvbsXYR6Q1S0GhSncywyV/LvucdTlMty4faXKcumuPHRv/Khy98XuGccHvJYZdz+h4om8/vjdCtdNJmrgHOBE4H1stHo6P+OWgr5AlYDJ1BIk7NKNhrb+yfyT6GwF2CraDKrgB8AMyhMiR2oWl8tmswLKPxInhvLQPLJe1pOiweL0W2Jlrd4eGYV8VCAkkyes3Z3UZvKsrOihKV7y7lr7pXM2tYH5NhVHuN/r2ykNVaoJU9q2c6Dv/saM+LdPDVtJqLJ3A2UIDGAEIIchdCoArgDKAX6gJ9R+KHWUogl+5hsNAbjx4kmcxqFXFXzKQQArKIQNamHwmKHlbLRsEWTuQQ4p//ZV/Ufi1AI2Z4GOmSjsf5Q72MsHHXV2R9frIvDiYA4NvIUwohngU/33/+m/jLJcOGsAE6mICoJrAMWH8F3bgUul43GxgOdcGf17aH/LqjJTBE6X77wFBLRYGFCvZ+QaXHNuj38Z1YN7376ZWZub6eqrZCp52uXn8bTs/uHNHQBUvKlB//CjESWt7/+rVB0xDWnDXwB+BqFGLLv4+Adri0URHfyGO79T+Cao00JPh4N8DsYf5HBvlj1IeBbwFeHlO3/EpdTENnAsSMRGcBsRt+WN4iEj1yxYQvYELTtYSIDyBo6q2pKEJZNdzRKdyxM2M4yL72DXVUx0AQUBwf/vn3Ba3i5dPYRmjuIBnyFgsDez6F79XMYm8gAXt1/36NiPIR25jjc41A4Od53xsEOSuSrl3TsYmcpzOzuGxwX23eCZE1lCSe0xekqifH0vGqWpdayesFMSqSAiFHwZgBC0F1aQklfjobeI00bOoxXjcdNRuGg72QsjMf/wBfH4R6HwskB3ucOdlAg/rO6agqbamNsqSxF2PsLrfDXGi0Mf1y9+lkeWnwyDy9cxmmdcYQ+0tmsn17O6589YG19ODw0HjcZhYO+k7EwHkK7irGlFDxcBhrlNnAjharzQIL7D4WkXQPsPMLvbAY+frATBHzr/114PpYNvZEQUgiwZOHPlIN731dXFhMPGixuaeG5GYUEyFHLpi45sqlzSstazt1yVMN6ALdQqPbvHMO5e4ENY7zvf4EfHalRA4zbOJpoMv8FXD4uN4PNFKrkU4G1stHY2f8dsyhE/3kRWAbUAU/JRmNNf6fkXKBVNhov9e9Kvw2YQqHXeaAkrVsp5ACoBB6VjcYh1/xPf/8e2RcI0jOtrFAwNIWBlIWfgwafevgftMfqmZLWaS8pLMNrjwa5e34DmSFLwHd96b3sMqZz1mc+n0ISpNDG0gATQZ7CHR8DohQC//2IQjurgkJCiXtko7Ft4H6iyTwRuIZCz/T3QBGFXmcnhdTej8pGIyeazPOAKyisa3q+/1gRkAKSFMY1nz3U+xgL4z1gO143M8ZzvZpoMucCGxm9kTxjQMhj5TWvfTFx3+JZRbmKaEFYthzuawUIW7LmGx/j/I9+nfO276Yms28kKaVr3LWogd5IiPO3rOXhH9/EHxeezZvWfWLST80diPFuZB9OepnR8gb0ykZDjPeiSNlobAZuHu3Q4YoM4PytLaX5iAFSEsnmWbq9lemdvcN6oELCO6+5QXYVRVhx/AL+Ob+edVXFrKor46/HTaU3HOTKNc9y++9vJREIcs/cU/ccxSMe84y3R4tScLmHwpKNhtFf3T1CwWVfJhuNsWT1PRr7LgXuoeDZErLROOJMK7M/1iKDls0HH3iOSP+swHMz6/jJBScjNUFJNs8Fa9fZe+oqtJXTpxW8niUL36wLTt+2iVv+9TuSwRA/OPlSGZax2J1/XOLZ1bbjvt1ujNVnXjYak3pe76z3bZevWrmR2vjw39UPLzqZF2bWgwbztu9Kz0/mIqtrS9kZ27dVQsPmiw/+hV+fdK68YH3bZT+75/T7nLbfadxajzbpI1cLmxEiA5jaVZgFQEq21tc+ffyODewpGr4ww0ZjZ2kVsVTKVEFk4J7QJn0AvMpUjpaykTXv5toK0CCWyWOZYvnuyljeEiNf88s1UyjJ5if9D26sTITQxtK4nvRpAztiYW47/wQ6iguzb6YQ3Hf8bNZNraK4L0NCiJ3ypmj3Cbtb9p+lAmDVtJkkwkeahmryMRHLceYCh2rUH1Go9mOJXcUhdteW89k3XsTUrjg90TB90RAg6QsaM+QXIjsBSpN9933lgT+96rMXv3GfHw9oYOlktMBRj9JOFiYq9obFQbylF+JvFN/YKxPFo+QVkxJ5Q2DY871Qe4N8cVoDH73yHcTDUY5r3U24N0VdVur//O3xk74ZMRYmqo12MCH1TNB3Osr529eNWj5sI0o/D81asGRKe7dc3XQDT976eU7duAU7GHuVKiIDdzanHDAAzGRi+dqn+deSZchhDX1JODdSaNc/9a619P+opzMOSyEmIRPl0Q42aPvuCfpOR3m5Zlrm1rt+iZA2A0s2zti2kWQwcKhLlWSihHbAlXyy0Vg5Qd/pKHeeePrS8lSC3V9+P3f++lvc/rvvsq28Gizrj27bdiwyYeHfRZN5LcMTpFpAaLIHdxmK+EY+du76dS9aujZrZ7Qst7u6arH8fHir23Ydi/h5BnwcwQ+W7OMIvtB8HMEXmo8j+ELzcYSjHrC98fKVr6Cwc7oSuPHme04Zrx3rPh7iqHqdN16+ch0jU0XLm+85xfeUPsM4YqHdePnKOIWdNaPxzM33nHL6EVs1iRBN5qeAr49y6P/JRuPm/c4NUtgkM53CdEIzcJFsNMa69W3SckSe58bLV36JA4sMCtvkPI9oMuch5WgiA7hJNJlXDTl3II7IDPZtp5sCrBdNZveEG+syR1rFHWo9mbjx8pWvP8J7TyYeHnVV4z7uGPLvtQc5r0w0md8dF4uOUQ5baDdevnIaYwsNOpYd05Mbyz5UVO2h73fuIc79yFFac0xzJB5t26FPATwQp/aQHKp5KyWiyZzT/+9DvQ9xJKFZJwtHIrSxbahQYQ5VEwdfIFCoVjeLJnOscd4921Y7LKHdePnKtx3O+Y9O+/JYvd+kQzSZS9HEWPem7j5EW24AzwZMPlyP9psxnykE9y+6eCZiuVezgxxO8JMxv2fRZI4Wa3fSM+YXcOPlKw8aCXH0u+usrF9yx6FPnJRM1DL4cw+jqp00jEloN16+ch5H0CsKmll2RKedd9/Ub8mE8RYzLa55Kq2/ecFhWznZOfz26m0TYYabjGlm4MbLV2Y4cHyxERRl+pjWu5clzes4vuVlbDTymkFOGISsHFJAWJoLkSsm7Yj4OIboOhDlhxuW/ljmkO6/35uNWWRISTJczPrwAtbXzEeTFgtaNxK0c4T79xVbUpCF9SGxXEOumHTdU9Fk/hkpRwRKHme2cnh5G45pRvVoN16+UgOeAI54vrIi2cWZO55lftsmyjPxEcd7jRi7QtX2kuS2KHLFIaMsHi23zV1hAOJdm5fnh5RFKERRrAcWUdi9FQdWvmvz8lHDnYsmU8O2LTTH1g1cIhuN/4z15P5UR0UUImK+Bngd+6a+QkALsJvCfKsAyoCB2AyCwn76O2Wj8YbxMb//xlJKbrx85cnAT4GTGIeB1lgmwQcf/xlF+QPH5UvpYW6feTUBK0dxrhdTDxKycve3R6qvoxCP9SzgBeCzwFIKudinUhgmfQa47F2blw9T8G1zVyzrv/ZsCi9vPFyOZQpeaHrNWeVz93bOuX/pXCxdm2hvNhpZCrFnoxQ83cTt6zuwt24HTpONxvbDvaVx4+UrKylEcz7ioHT7c3zz2oOKDMDu33ib14P0hCoKn4PiEiHtlVJodf2nzaKQ2aNmv8vPBJ4EjhsouG3uinLgAQq/0PFEf3Le1FOuenod33jtOUjNtcH7EKOnNRp/DvwjqgbWALHDvaUGvJZxFBnAM9NP5qW6g+eUWFu6bxmbremU5AvOaYjIBthfZAMsvm3uiqFtxysZf5EBML+5i6xhuCmyY4ki0WROP9yLNAphPccVSzfYUTFt9GMIni0/kTVlw4UYsPOjnn8QJMPjrI37cwyQChkUp48qQ43XOOx3rQF/pxBufVwpS/eOWq4jWdb9EnXp1sHxpbpUC12hikJ4AWk/sd8lL1AIR74/9w9t2FPIWTQuCbL2Z3NdJVvqK6no82yI2cNhh2w0ug73Iu3me07JUGjzNFGIYT8ubKqac8BjOpILWx5hYfc6Tm97luJcnLp0W7443/dWhHYhhcHhP1JINnY+hYWUfwE6KETzvhW4bOg937V5eZZCe+5mCpk+ejn6jCsW0HbJmm0/fHTh9BULd7XZM1o6EbbjQYAsChn9/kThB7WTQgy6HJBhvCNojj62agH/5gjbiQccsL3x8pWvoxDSYJQgYIfm+L1red3qfxCQo78DCewM126dkf7RgRV5jDLtUx1yd3WZE19lUsiDsPdIbyCazNkUxksvppBm8hYKq0RmURjmOAP4GIUEGWUUOoZXHW02uxF2HGpm4MbLV+ocRSjQM7Y9zeUbHhgxzpDGsCPyjkkZw3XZe7Z994X5Uz86wUMcj8hG4/yJ/AInOeSo4833nGIxttwBI5GShr7WQZH15+MiLQJmBLPqiO55DPD8z2Z9jP2TjY0v0ksig7Gv3rjgSG5emuohlIqbedjbGqy8cXekoU7IFSJi/ymAXDGpF/nVdo+c7RhHvjeRN3eDMW+3u/HylR8EDm9zsLQxTPP+G/9z1iuPwLZjmtdd85K867RFh54hOLI50QbZaDQfqW3HImOesLv5nlN+cPi3F5iBYOPhX3fss6OmvBVzDE3XwxfZn7wmMjj8FbYnjflMKSlLdm24+Z5TXjrM75gUvPCDaXVBawwNtUKNMebOlGw03nQUZh2zHJbQbr7nlFWHc/71j166f7gET5ELHWLLgJQgZRoY63L2Px2tTccqE7ndTgUOvgvKskHTasc6ku5VbwZHJrSxDbBK54fPHUeIgw9q6hqy0egb4908E9t3NA5baDffc4oEDj4DLiW6tJccqVGTiK8edD+AEEN/bG0HPK+AM0uAXOJIl4nOOOhRIez/d9+Zo6cW8RbfQgg5qtgKZUOHdQ5WE9wuG41d42vascURCe3me05p5uCJKW45MnMmF/0Zk0sRorW/4T8gsCxCzJaNxgNDzk1QmHP8NoUJ/23AW/pTe7/FDfud5GgD8X0P+PB+xdtuvueUAya08FGTo84z0L+R5fUUfqXP3XzPKZN6aslnYvj/iwvGxcLADroAAAAASUVORK5CYII=" id="image7537940507" transform="scale(1 -1) translate(0 -578.16)" x="406.08" y="-43.2" width="110.88" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature19_fold0 -->
    <g transform="translate(152.751375 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-39" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.401875 638.149 
L 535.031375 638.149 
L 535.031375 27.789 
L 527.401875 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image5058c64838" transform="scale(1 -1) translate(0 -609.84)" x="527.76" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(538.531375 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(538.531375 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(572.932312 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pc549266437">
   <rect x="403.174375" y="27.789" width="116.92" height="610.36"/>
  </clipPath>
 </defs>
</svg>
