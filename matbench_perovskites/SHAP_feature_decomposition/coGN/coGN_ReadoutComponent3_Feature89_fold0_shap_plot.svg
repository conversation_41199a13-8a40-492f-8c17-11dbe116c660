<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T18:04:21.488037</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 463.89871 638.149 
L 463.89871 27.789 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#pa738d8b457)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m96c2423382" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m96c2423382" x="463.89871" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(455.151992 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m96c2423382" x="508.398134" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(499.651415 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- OPSiteFingerprint_std_dev_q4_CN_12 -->
      <g style="fill: #333333" transform="translate(160.604062 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-71" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-34" x="1425.953125"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-43" x="1539.576172"/>
       <use xlink:href="#DejaVuSans-4e" x="1609.400391"/>
       <use xlink:href="#DejaVuSans-5f" x="1684.205078"/>
       <use xlink:href="#DejaVuSans-31" x="1734.205078"/>
       <use xlink:href="#DejaVuSans-32" x="1797.828125"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(74.466875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(213.278437 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_range_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(34.898125 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-64" x="2091.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2155.130859"/>
       <use xlink:href="#DejaVuSans-6c" x="2216.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2244.4375"/>
       <use xlink:href="#DejaVuSans-65" x="2305.960938"/>
       <use xlink:href="#DejaVuSans-76" x="2367.484375"/>
       <use xlink:href="#DejaVuSans-4e" x="2426.664062"/>
       <use xlink:href="#DejaVuSans-75" x="2501.46875"/>
       <use xlink:href="#DejaVuSans-6d" x="2564.847656"/>
       <use xlink:href="#DejaVuSans-62" x="2662.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2725.736328"/>
       <use xlink:href="#DejaVuSans-72" x="2787.259766"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_87 -->
      <g style="fill: #333333" transform="translate(213.278437 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_100 -->
      <g style="fill: #333333" transform="translate(205.007187 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(83.556719 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MaximumPackingEfficiency_max_packing_efficiency -->
      <g style="fill: #333333" transform="translate(64.3025 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-61" x="86.279297"/>
       <use xlink:href="#DejaVuSans-78" x="147.558594"/>
       <use xlink:href="#DejaVuSans-69" x="206.738281"/>
       <use xlink:href="#DejaVuSans-6d" x="234.521484"/>
       <use xlink:href="#DejaVuSans-75" x="331.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="395.3125"/>
       <use xlink:href="#DejaVuSans-50" x="492.724609"/>
       <use xlink:href="#DejaVuSans-61" x="548.527344"/>
       <use xlink:href="#DejaVuSans-63" x="609.806641"/>
       <use xlink:href="#DejaVuSans-6b" x="664.787109"/>
       <use xlink:href="#DejaVuSans-69" x="722.697266"/>
       <use xlink:href="#DejaVuSans-6e" x="750.480469"/>
       <use xlink:href="#DejaVuSans-67" x="813.859375"/>
       <use xlink:href="#DejaVuSans-45" x="877.335938"/>
       <use xlink:href="#DejaVuSans-66" x="940.519531"/>
       <use xlink:href="#DejaVuSans-66" x="975.724609"/>
       <use xlink:href="#DejaVuSans-69" x="1010.929688"/>
       <use xlink:href="#DejaVuSans-63" x="1038.712891"/>
       <use xlink:href="#DejaVuSans-69" x="1093.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1121.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="1183"/>
       <use xlink:href="#DejaVuSans-63" x="1246.378906"/>
       <use xlink:href="#DejaVuSans-79" x="1301.359375"/>
       <use xlink:href="#DejaVuSans-5f" x="1360.539062"/>
       <use xlink:href="#DejaVuSans-6d" x="1410.539062"/>
       <use xlink:href="#DejaVuSans-61" x="1507.951172"/>
       <use xlink:href="#DejaVuSans-78" x="1569.230469"/>
       <use xlink:href="#DejaVuSans-5f" x="1628.410156"/>
       <use xlink:href="#DejaVuSans-70" x="1678.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1741.886719"/>
       <use xlink:href="#DejaVuSans-63" x="1803.166016"/>
       <use xlink:href="#DejaVuSans-6b" x="1858.146484"/>
       <use xlink:href="#DejaVuSans-69" x="1916.056641"/>
       <use xlink:href="#DejaVuSans-6e" x="1943.839844"/>
       <use xlink:href="#DejaVuSans-67" x="2007.21875"/>
       <use xlink:href="#DejaVuSans-5f" x="2070.695312"/>
       <use xlink:href="#DejaVuSans-65" x="2120.695312"/>
       <use xlink:href="#DejaVuSans-66" x="2182.21875"/>
       <use xlink:href="#DejaVuSans-66" x="2217.423828"/>
       <use xlink:href="#DejaVuSans-69" x="2252.628906"/>
       <use xlink:href="#DejaVuSans-63" x="2280.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2335.392578"/>
       <use xlink:href="#DejaVuSans-65" x="2363.175781"/>
       <use xlink:href="#DejaVuSans-6e" x="2424.699219"/>
       <use xlink:href="#DejaVuSans-63" x="2488.078125"/>
       <use xlink:href="#DejaVuSans-79" x="2543.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_109 -->
      <g style="fill: #333333" transform="translate(205.007187 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-39" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(151.583281 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_147 -->
      <g style="fill: #333333" transform="translate(205.007187 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_tetrahedral_CN_4 -->
      <g style="fill: #333333" transform="translate(104.202344 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-74" x="1526.929688"/>
       <use xlink:href="#DejaVuSans-72" x="1566.138672"/>
       <use xlink:href="#DejaVuSans-61" x="1607.251953"/>
       <use xlink:href="#DejaVuSans-68" x="1668.53125"/>
       <use xlink:href="#DejaVuSans-65" x="1731.910156"/>
       <use xlink:href="#DejaVuSans-64" x="1793.433594"/>
       <use xlink:href="#DejaVuSans-72" x="1856.910156"/>
       <use xlink:href="#DejaVuSans-61" x="1898.023438"/>
       <use xlink:href="#DejaVuSans-6c" x="1959.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="1987.085938"/>
       <use xlink:href="#DejaVuSans-43" x="2037.085938"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.910156"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.714844"/>
       <use xlink:href="#DejaVuSans-34" x="2231.714844"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(90.359375 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- CrystalNNFingerprint_std_dev_see-saw-like_CN_4 -->
      <g style="fill: #333333" transform="translate(83.617656 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-73" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1583.667969"/>
       <use xlink:href="#DejaVuSans-65" x="1645.191406"/>
       <use xlink:href="#DejaVuSans-2d" x="1706.714844"/>
       <use xlink:href="#DejaVuSans-73" x="1742.798828"/>
       <use xlink:href="#DejaVuSans-61" x="1794.898438"/>
       <use xlink:href="#DejaVuSans-77" x="1856.177734"/>
       <use xlink:href="#DejaVuSans-2d" x="1937.964844"/>
       <use xlink:href="#DejaVuSans-6c" x="1974.048828"/>
       <use xlink:href="#DejaVuSans-69" x="2001.832031"/>
       <use xlink:href="#DejaVuSans-6b" x="2029.615234"/>
       <use xlink:href="#DejaVuSans-65" x="2083.900391"/>
       <use xlink:href="#DejaVuSans-5f" x="2145.423828"/>
       <use xlink:href="#DejaVuSans-43" x="2195.423828"/>
       <use xlink:href="#DejaVuSans-4e" x="2265.248047"/>
       <use xlink:href="#DejaVuSans-5f" x="2340.052734"/>
       <use xlink:href="#DejaVuSans-34" x="2390.052734"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(213.278437 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(90.359375 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(280.657031 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(66.063594 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageeebc264d75" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature89_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-38" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-39" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagea1ae99dd96" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pa738d8b457">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
