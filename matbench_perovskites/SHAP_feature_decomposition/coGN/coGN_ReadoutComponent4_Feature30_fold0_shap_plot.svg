<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.621906pt" height="679.5765pt" viewBox="0 0 794.621906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T04:22:15.794395</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.621906 679.5765 
L 794.621906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
L 525.658906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 489.69074 638.149 
L 489.69074 27.789 
" clip-path="url(#pad54c17654)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.658906 609.084238 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.658906 580.019476 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.658906 550.954714 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.658906 521.889952 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.658906 492.82519 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.658906 463.760429 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.658906 434.695667 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.658906 405.630905 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.658906 376.566143 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.658906 347.501381 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.658906 318.436619 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.658906 289.371857 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.658906 260.307095 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.658906 231.242333 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.658906 202.177571 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.658906 173.11281 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.658906 144.048048 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.658906 114.983286 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.658906 85.918524 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.658906 56.853762 
" clip-path="url(#pad54c17654)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="ma04fc22ccd" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#ma04fc22ccd" x="437.916933" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2.5 -->
      <g style="fill: #333333" transform="translate(424.561386 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#ma04fc22ccd" x="489.69074" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(480.944022 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8575 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_mean -->
      <g style="fill: #333333" transform="translate(122.729375 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-65" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-61" x="2069.087891"/>
       <use xlink:href="#DejaVuSans-6e" x="2130.367188"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- OxidationStates_minimum_oxidation_state -->
      <g style="fill: #333333" transform="translate(129.980938 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-6d" x="848.779297"/>
       <use xlink:href="#DejaVuSans-69" x="946.191406"/>
       <use xlink:href="#DejaVuSans-6e" x="973.974609"/>
       <use xlink:href="#DejaVuSans-69" x="1037.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1065.136719"/>
       <use xlink:href="#DejaVuSans-75" x="1162.548828"/>
       <use xlink:href="#DejaVuSans-6d" x="1225.927734"/>
       <use xlink:href="#DejaVuSans-5f" x="1323.339844"/>
       <use xlink:href="#DejaVuSans-6f" x="1373.339844"/>
       <use xlink:href="#DejaVuSans-78" x="1431.396484"/>
       <use xlink:href="#DejaVuSans-69" x="1490.576172"/>
       <use xlink:href="#DejaVuSans-64" x="1518.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
       <use xlink:href="#DejaVuSans-69" x="1682.324219"/>
       <use xlink:href="#DejaVuSans-6f" x="1710.107422"/>
       <use xlink:href="#DejaVuSans-6e" x="1771.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1834.667969"/>
       <use xlink:href="#DejaVuSans-73" x="1884.667969"/>
       <use xlink:href="#DejaVuSans-74" x="1936.767578"/>
       <use xlink:href="#DejaVuSans-61" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-74" x="2037.255859"/>
       <use xlink:href="#DejaVuSans-65" x="2076.464844"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(218.598281 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(166.71 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementFraction_Cd -->
      <g style="fill: #333333" transform="translate(278.371875 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-43" x="863.208984"/>
       <use xlink:href="#DejaVuSans-64" x="933.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_58 -->
      <g style="fill: #333333" transform="translate(218.598281 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_87 -->
      <g style="fill: #333333" transform="translate(218.598281 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- VoronoiFingerprint_std_dev_Voro_area_sum -->
      <g style="fill: #333333" transform="translate(126.672031 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-73" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-75" x="2002.628906"/>
       <use xlink:href="#DejaVuSans-6d" x="2066.007812"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=1_88e+00 -->
      <g style="fill: #333333" transform="translate(104.665469 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-31" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-38" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-38" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_154 -->
      <g style="fill: #333333" transform="translate(210.327031 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-34" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(188.675938 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(53.114375 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MaximumPackingEfficiency_max_packing_efficiency -->
      <g style="fill: #333333" transform="translate(69.622344 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-61" x="86.279297"/>
       <use xlink:href="#DejaVuSans-78" x="147.558594"/>
       <use xlink:href="#DejaVuSans-69" x="206.738281"/>
       <use xlink:href="#DejaVuSans-6d" x="234.521484"/>
       <use xlink:href="#DejaVuSans-75" x="331.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="395.3125"/>
       <use xlink:href="#DejaVuSans-50" x="492.724609"/>
       <use xlink:href="#DejaVuSans-61" x="548.527344"/>
       <use xlink:href="#DejaVuSans-63" x="609.806641"/>
       <use xlink:href="#DejaVuSans-6b" x="664.787109"/>
       <use xlink:href="#DejaVuSans-69" x="722.697266"/>
       <use xlink:href="#DejaVuSans-6e" x="750.480469"/>
       <use xlink:href="#DejaVuSans-67" x="813.859375"/>
       <use xlink:href="#DejaVuSans-45" x="877.335938"/>
       <use xlink:href="#DejaVuSans-66" x="940.519531"/>
       <use xlink:href="#DejaVuSans-66" x="975.724609"/>
       <use xlink:href="#DejaVuSans-69" x="1010.929688"/>
       <use xlink:href="#DejaVuSans-63" x="1038.712891"/>
       <use xlink:href="#DejaVuSans-69" x="1093.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1121.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="1183"/>
       <use xlink:href="#DejaVuSans-63" x="1246.378906"/>
       <use xlink:href="#DejaVuSans-79" x="1301.359375"/>
       <use xlink:href="#DejaVuSans-5f" x="1360.539062"/>
       <use xlink:href="#DejaVuSans-6d" x="1410.539062"/>
       <use xlink:href="#DejaVuSans-61" x="1507.951172"/>
       <use xlink:href="#DejaVuSans-78" x="1569.230469"/>
       <use xlink:href="#DejaVuSans-5f" x="1628.410156"/>
       <use xlink:href="#DejaVuSans-70" x="1678.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1741.886719"/>
       <use xlink:href="#DejaVuSans-63" x="1803.166016"/>
       <use xlink:href="#DejaVuSans-6b" x="1858.146484"/>
       <use xlink:href="#DejaVuSans-69" x="1916.056641"/>
       <use xlink:href="#DejaVuSans-6e" x="1943.839844"/>
       <use xlink:href="#DejaVuSans-67" x="2007.21875"/>
       <use xlink:href="#DejaVuSans-5f" x="2070.695312"/>
       <use xlink:href="#DejaVuSans-65" x="2120.695312"/>
       <use xlink:href="#DejaVuSans-66" x="2182.21875"/>
       <use xlink:href="#DejaVuSans-66" x="2217.423828"/>
       <use xlink:href="#DejaVuSans-69" x="2252.628906"/>
       <use xlink:href="#DejaVuSans-63" x="2280.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2335.392578"/>
       <use xlink:href="#DejaVuSans-65" x="2363.175781"/>
       <use xlink:href="#DejaVuSans-6e" x="2424.699219"/>
       <use xlink:href="#DejaVuSans-63" x="2488.078125"/>
       <use xlink:href="#DejaVuSans-79" x="2543.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_avg_dev_CovalentRadius -->
      <g style="fill: #333333" transform="translate(45.671875 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-43" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6f" x="2077.640625"/>
       <use xlink:href="#DejaVuSans-76" x="2138.822266"/>
       <use xlink:href="#DejaVuSans-61" x="2198.001953"/>
       <use xlink:href="#DejaVuSans-6c" x="2259.28125"/>
       <use xlink:href="#DejaVuSans-65" x="2287.064453"/>
       <use xlink:href="#DejaVuSans-6e" x="2348.587891"/>
       <use xlink:href="#DejaVuSans-74" x="2411.966797"/>
       <use xlink:href="#DejaVuSans-52" x="2451.175781"/>
       <use xlink:href="#DejaVuSans-61" x="2518.408203"/>
       <use xlink:href="#DejaVuSans-64" x="2579.6875"/>
       <use xlink:href="#DejaVuSans-69" x="2643.164062"/>
       <use xlink:href="#DejaVuSans-75" x="2670.947266"/>
       <use xlink:href="#DejaVuSans-73" x="2734.326172"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(156.903125 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- AtomicPackingEfficiency_dist_from_5_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-35" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(285.469063 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(285.976875 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(99.152656 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image04110a5449" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature30_fold0 -->
    <g transform="translate(169.655906 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.548906 638.149 
L 539.178406 638.149 
L 539.178406 27.789 
L 531.548906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image4d47033e50" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.678406 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.678406 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.079344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pad54c17654">
   <rect x="431.418906" y="27.789" width="94.24" height="610.36"/>
  </clipPath>
 </defs>
</svg>
