<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="774.599937pt" height="679.5765pt" viewBox="0 0 774.599937 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:26:04.299858</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 774.599937 679.5765 
L 774.599937 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 398.400937 638.149 
L 518.632938 638.149 
L 518.632938 27.789 
L 398.400937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 479.910129 638.149 
L 479.910129 27.789 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 398.400937 609.084238 
L 518.632938 609.084238 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 398.400937 580.019476 
L 518.632938 580.019476 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 398.400937 550.954714 
L 518.632938 550.954714 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 398.400937 521.889952 
L 518.632938 521.889952 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 398.400937 492.82519 
L 518.632938 492.82519 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 398.400937 463.760429 
L 518.632938 463.760429 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 398.400937 434.695667 
L 518.632938 434.695667 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 398.400937 405.630905 
L 518.632938 405.630905 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 398.400937 376.566143 
L 518.632938 376.566143 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 398.400937 347.501381 
L 518.632938 347.501381 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 398.400937 318.436619 
L 518.632938 318.436619 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 398.400937 289.371857 
L 518.632938 289.371857 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 398.400937 260.307095 
L 518.632938 260.307095 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 398.400937 231.242333 
L 518.632938 231.242333 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 398.400937 202.177571 
L 518.632938 202.177571 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 398.400937 173.11281 
L 518.632938 173.11281 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 398.400937 144.048048 
L 518.632938 144.048048 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 398.400937 114.983286 
L 518.632938 114.983286 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 398.400937 85.918524 
L 518.632938 85.918524 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 398.400937 56.853762 
L 518.632938 56.853762 
" clip-path="url(#pdb6c5c3cc8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m710872aef1" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m710872aef1" x="414.538079" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(401.182532 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m710872aef1" x="479.910129" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(471.163411 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(335.835531 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(62.66125 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(108.084063 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_avg_dev_MeltingT -->
      <g style="fill: #333333" transform="translate(57.908125 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-74" x="2183.402344"/>
       <use xlink:href="#DejaVuSans-69" x="2222.611328"/>
       <use xlink:href="#DejaVuSans-6e" x="2250.394531"/>
       <use xlink:href="#DejaVuSans-67" x="2313.773438"/>
       <use xlink:href="#DejaVuSans-54" x="2377.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- XRDPowderPattern_xrd_36 -->
      <g style="fill: #333333" transform="translate(202.029375 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-36" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(62.66125 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(77.207031 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_64 -->
      <g style="fill: #333333" transform="translate(185.580312 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(62.66125 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(123.885156 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(46.76875 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(20.096406 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- VoronoiFingerprint_std_dev_Voro_area_std_dev -->
      <g style="fill: #333333" transform="translate(70.759844 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-73" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-74" x="2002.628906"/>
       <use xlink:href="#DejaVuSans-64" x="2041.837891"/>
       <use xlink:href="#DejaVuSans-5f" x="2105.314453"/>
       <use xlink:href="#DejaVuSans-64" x="2155.314453"/>
       <use xlink:href="#DejaVuSans-65" x="2218.791016"/>
       <use xlink:href="#DejaVuSans-76" x="2280.314453"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(133.692031 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- VoronoiFingerprint_mean_Voro_vol_mean -->
      <g style="fill: #333333" transform="translate(107.885 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-76" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-6f" x="1631.392578"/>
       <use xlink:href="#DejaVuSans-6c" x="1692.574219"/>
       <use xlink:href="#DejaVuSans-5f" x="1720.357422"/>
       <use xlink:href="#DejaVuSans-6d" x="1770.357422"/>
       <use xlink:href="#DejaVuSans-65" x="1867.769531"/>
       <use xlink:href="#DejaVuSans-61" x="1929.292969"/>
       <use xlink:href="#DejaVuSans-6e" x="1990.572266"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_23 -->
      <g style="fill: #333333" transform="translate(185.580312 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- OxidationStates_range_oxidation_state -->
      <g style="fill: #333333" transform="translate(120.850469 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-72" x="848.779297"/>
       <use xlink:href="#DejaVuSans-61" x="889.892578"/>
       <use xlink:href="#DejaVuSans-6e" x="951.171875"/>
       <use xlink:href="#DejaVuSans-67" x="1014.550781"/>
       <use xlink:href="#DejaVuSans-65" x="1078.027344"/>
       <use xlink:href="#DejaVuSans-5f" x="1139.550781"/>
       <use xlink:href="#DejaVuSans-6f" x="1189.550781"/>
       <use xlink:href="#DejaVuSans-78" x="1247.607422"/>
       <use xlink:href="#DejaVuSans-69" x="1306.787109"/>
       <use xlink:href="#DejaVuSans-64" x="1334.570312"/>
       <use xlink:href="#DejaVuSans-61" x="1398.046875"/>
       <use xlink:href="#DejaVuSans-74" x="1459.326172"/>
       <use xlink:href="#DejaVuSans-69" x="1498.535156"/>
       <use xlink:href="#DejaVuSans-6f" x="1526.318359"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.5"/>
       <use xlink:href="#DejaVuSans-5f" x="1650.878906"/>
       <use xlink:href="#DejaVuSans-73" x="1700.878906"/>
       <use xlink:href="#DejaVuSans-74" x="1752.978516"/>
       <use xlink:href="#DejaVuSans-61" x="1792.1875"/>
       <use xlink:href="#DejaVuSans-74" x="1853.466797"/>
       <use xlink:href="#DejaVuSans-65" x="1892.675781"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_range_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-64" x="2091.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2155.130859"/>
       <use xlink:href="#DejaVuSans-6c" x="2216.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2244.4375"/>
       <use xlink:href="#DejaVuSans-65" x="2305.960938"/>
       <use xlink:href="#DejaVuSans-76" x="2367.484375"/>
       <use xlink:href="#DejaVuSans-4e" x="2426.664062"/>
       <use xlink:href="#DejaVuSans-75" x="2501.46875"/>
       <use xlink:href="#DejaVuSans-6d" x="2564.847656"/>
       <use xlink:href="#DejaVuSans-62" x="2662.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2725.736328"/>
       <use xlink:href="#DejaVuSans-72" x="2787.259766"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(155.657969 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(183.110312 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(255.2075 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 398.400937 638.149 
L 518.632938 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageaa6709eb58" transform="scale(1 -1) translate(0 -578.16)" x="401.76" y="-43.2" width="113.76" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature72_fold0 -->
    <g transform="translate(149.633938 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-37" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 526.147437 638.149 
L 533.776937 638.149 
L 533.776937 27.789 
L 526.147437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imageddd026e7fa" transform="scale(1 -1) translate(0 -609.84)" x="526.32" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(537.276937 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(537.276937 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(571.677875 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pdb6c5c3cc8">
   <rect x="398.400937" y="27.789" width="120.232" height="610.36"/>
  </clipPath>
 </defs>
</svg>
