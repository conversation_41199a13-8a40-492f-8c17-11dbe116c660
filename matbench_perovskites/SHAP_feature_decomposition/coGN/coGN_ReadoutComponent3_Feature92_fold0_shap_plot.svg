<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="828.134406pt" height="679.5765pt" viewBox="0 0 828.134406 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T18:12:40.667609</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 828.134406 679.5765 
L 828.134406 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 487.611406 638.149 
L 536.491406 638.149 
L 536.491406 27.789 
L 487.611406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 507.669154 638.149 
L 507.669154 27.789 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 487.611406 609.084238 
L 536.491406 609.084238 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 487.611406 580.019476 
L 536.491406 580.019476 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 487.611406 550.954714 
L 536.491406 550.954714 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 487.611406 521.889952 
L 536.491406 521.889952 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 487.611406 492.82519 
L 536.491406 492.82519 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 487.611406 463.760429 
L 536.491406 463.760429 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 487.611406 434.695667 
L 536.491406 434.695667 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 487.611406 405.630905 
L 536.491406 405.630905 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 487.611406 376.566143 
L 536.491406 376.566143 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 487.611406 347.501381 
L 536.491406 347.501381 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 487.611406 318.436619 
L 536.491406 318.436619 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 487.611406 289.371857 
L 536.491406 289.371857 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 487.611406 260.307095 
L 536.491406 260.307095 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 487.611406 231.242333 
L 536.491406 231.242333 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 487.611406 202.177571 
L 536.491406 202.177571 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 487.611406 173.11281 
L 536.491406 173.11281 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 487.611406 144.048048 
L 536.491406 144.048048 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 487.611406 114.983286 
L 536.491406 114.983286 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 487.611406 85.918524 
L 536.491406 85.918524 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 487.611406 56.853762 
L 536.491406 56.853762 
" clip-path="url(#pd555ac1445)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mb5e59d210a" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb5e59d210a" x="507.669154" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(498.922436 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mb5e59d210a" x="530.226399" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(521.479681 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(389.37 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_mean_NdUnfilled -->
      <g style="fill: #333333" transform="translate(153.183906 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-64" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-55" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2084.769531"/>
       <use xlink:href="#DejaVuSans-66" x="2148.148438"/>
       <use xlink:href="#DejaVuSans-69" x="2183.353516"/>
       <use xlink:href="#DejaVuSans-6c" x="2211.136719"/>
       <use xlink:href="#DejaVuSans-6c" x="2238.919922"/>
       <use xlink:href="#DejaVuSans-65" x="2266.703125"/>
       <use xlink:href="#DejaVuSans-64" x="2328.226562"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(274.790781 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(188.157969 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(222.9025 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- DensityFeatures_density -->
      <g style="fill: #333333" transform="translate(304.637969 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-64" x="860.072266"/>
       <use xlink:href="#DejaVuSans-65" x="923.548828"/>
       <use xlink:href="#DejaVuSans-6e" x="985.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1048.451172"/>
       <use xlink:href="#DejaVuSans-69" x="1100.550781"/>
       <use xlink:href="#DejaVuSans-74" x="1128.333984"/>
       <use xlink:href="#DejaVuSans-79" x="1167.542969"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- LocalPropertyDifference_std_dev_local_difference_in_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-63" x="115.144531"/>
       <use xlink:href="#DejaVuSans-61" x="170.125"/>
       <use xlink:href="#DejaVuSans-6c" x="231.404297"/>
       <use xlink:href="#DejaVuSans-50" x="259.1875"/>
       <use xlink:href="#DejaVuSans-72" x="317.740234"/>
       <use xlink:href="#DejaVuSans-6f" x="356.603516"/>
       <use xlink:href="#DejaVuSans-70" x="417.785156"/>
       <use xlink:href="#DejaVuSans-65" x="481.261719"/>
       <use xlink:href="#DejaVuSans-72" x="542.785156"/>
       <use xlink:href="#DejaVuSans-74" x="583.898438"/>
       <use xlink:href="#DejaVuSans-79" x="623.107422"/>
       <use xlink:href="#DejaVuSans-44" x="682.287109"/>
       <use xlink:href="#DejaVuSans-69" x="759.289062"/>
       <use xlink:href="#DejaVuSans-66" x="787.072266"/>
       <use xlink:href="#DejaVuSans-66" x="822.277344"/>
       <use xlink:href="#DejaVuSans-65" x="857.482422"/>
       <use xlink:href="#DejaVuSans-72" x="919.005859"/>
       <use xlink:href="#DejaVuSans-65" x="957.869141"/>
       <use xlink:href="#DejaVuSans-6e" x="1019.392578"/>
       <use xlink:href="#DejaVuSans-63" x="1082.771484"/>
       <use xlink:href="#DejaVuSans-65" x="1137.751953"/>
       <use xlink:href="#DejaVuSans-5f" x="1199.275391"/>
       <use xlink:href="#DejaVuSans-73" x="1249.275391"/>
       <use xlink:href="#DejaVuSans-74" x="1301.375"/>
       <use xlink:href="#DejaVuSans-64" x="1340.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1404.060547"/>
       <use xlink:href="#DejaVuSans-64" x="1454.060547"/>
       <use xlink:href="#DejaVuSans-65" x="1517.537109"/>
       <use xlink:href="#DejaVuSans-76" x="1579.060547"/>
       <use xlink:href="#DejaVuSans-5f" x="1638.240234"/>
       <use xlink:href="#DejaVuSans-6c" x="1688.240234"/>
       <use xlink:href="#DejaVuSans-6f" x="1716.023438"/>
       <use xlink:href="#DejaVuSans-63" x="1777.205078"/>
       <use xlink:href="#DejaVuSans-61" x="1832.185547"/>
       <use xlink:href="#DejaVuSans-6c" x="1893.464844"/>
       <use xlink:href="#DejaVuSans-5f" x="1921.248047"/>
       <use xlink:href="#DejaVuSans-64" x="1971.248047"/>
       <use xlink:href="#DejaVuSans-69" x="2034.724609"/>
       <use xlink:href="#DejaVuSans-66" x="2062.507812"/>
       <use xlink:href="#DejaVuSans-66" x="2097.712891"/>
       <use xlink:href="#DejaVuSans-65" x="2132.917969"/>
       <use xlink:href="#DejaVuSans-72" x="2194.441406"/>
       <use xlink:href="#DejaVuSans-65" x="2233.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="2294.828125"/>
       <use xlink:href="#DejaVuSans-63" x="2358.207031"/>
       <use xlink:href="#DejaVuSans-65" x="2413.1875"/>
       <use xlink:href="#DejaVuSans-5f" x="2474.710938"/>
       <use xlink:href="#DejaVuSans-69" x="2524.710938"/>
       <use xlink:href="#DejaVuSans-6e" x="2552.494141"/>
       <use xlink:href="#DejaVuSans-5f" x="2615.873047"/>
       <use xlink:href="#DejaVuSans-45" x="2665.873047"/>
       <use xlink:href="#DejaVuSans-6c" x="2729.056641"/>
       <use xlink:href="#DejaVuSans-65" x="2756.839844"/>
       <use xlink:href="#DejaVuSans-63" x="2818.363281"/>
       <use xlink:href="#DejaVuSans-74" x="2873.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2912.552734"/>
       <use xlink:href="#DejaVuSans-6f" x="2951.416016"/>
       <use xlink:href="#DejaVuSans-6e" x="3012.597656"/>
       <use xlink:href="#DejaVuSans-65" x="3075.976562"/>
       <use xlink:href="#DejaVuSans-67" x="3137.5"/>
       <use xlink:href="#DejaVuSans-61" x="3200.976562"/>
       <use xlink:href="#DejaVuSans-74" x="3262.255859"/>
       <use xlink:href="#DejaVuSans-69" x="3301.464844"/>
       <use xlink:href="#DejaVuSans-76" x="3329.248047"/>
       <use xlink:href="#DejaVuSans-69" x="3388.427734"/>
       <use xlink:href="#DejaVuSans-74" x="3416.210938"/>
       <use xlink:href="#DejaVuSans-79" x="3455.419922"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(151.871719 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CrystalNNFingerprint_std_dev_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(155.35125 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6f" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-63" x="1592.75"/>
       <use xlink:href="#DejaVuSans-74" x="1647.730469"/>
       <use xlink:href="#DejaVuSans-61" x="1686.939453"/>
       <use xlink:href="#DejaVuSans-68" x="1748.21875"/>
       <use xlink:href="#DejaVuSans-65" x="1811.597656"/>
       <use xlink:href="#DejaVuSans-64" x="1873.121094"/>
       <use xlink:href="#DejaVuSans-72" x="1936.597656"/>
       <use xlink:href="#DejaVuSans-61" x="1977.710938"/>
       <use xlink:href="#DejaVuSans-6c" x="2038.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2066.773438"/>
       <use xlink:href="#DejaVuSans-43" x="2116.773438"/>
       <use xlink:href="#DejaVuSans-4e" x="2186.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2261.402344"/>
       <use xlink:href="#DejaVuSans-36" x="2311.402344"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(63.3925 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(167.800781 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(201.85875 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(135.979219 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(166.4175 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(344.417969 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(68.712344 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(151.751875 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(197.294531 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- AtomicPackingEfficiency_dist_from_3_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(63.3925 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-33" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(124.551406 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(272.320781 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 487.611406 638.149 
L 536.491406 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagee8b2ce3529" transform="scale(1 -1) translate(0 -578.16)" x="487.44" y="-43.2" width="48.96" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature92_fold0 -->
    <g transform="translate(203.168406 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-39" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 539.546406 638.149 
L 547.175906 638.149 
L 547.175906 27.789 
L 539.546406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imageef0e803828" transform="scale(1 -1) translate(0 -609.84)" x="539.28" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(550.675906 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(550.675906 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(585.076844 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pd555ac1445">
   <rect x="487.611406" y="27.789" width="48.88" height="610.36"/>
  </clipPath>
 </defs>
</svg>
