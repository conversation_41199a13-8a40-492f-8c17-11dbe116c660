<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="781.271031pt" height="679.5765pt" viewBox="0 0 781.271031 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T11:47:37.416143</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 781.271031 679.5765 
L 781.271031 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
L 520.984031 27.789 
L 409.392031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 455.414998 638.149 
L 455.414998 27.789 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 409.392031 609.084238 
L 520.984031 609.084238 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 409.392031 580.019476 
L 520.984031 580.019476 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 409.392031 550.954714 
L 520.984031 550.954714 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 409.392031 521.889952 
L 520.984031 521.889952 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 409.392031 492.82519 
L 520.984031 492.82519 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 409.392031 463.760429 
L 520.984031 463.760429 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 409.392031 434.695667 
L 520.984031 434.695667 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 409.392031 405.630905 
L 520.984031 405.630905 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 409.392031 376.566143 
L 520.984031 376.566143 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 409.392031 347.501381 
L 520.984031 347.501381 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 409.392031 318.436619 
L 520.984031 318.436619 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 409.392031 289.371857 
L 520.984031 289.371857 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 409.392031 260.307095 
L 520.984031 260.307095 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 409.392031 231.242333 
L 520.984031 231.242333 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 409.392031 202.177571 
L 520.984031 202.177571 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 409.392031 173.11281 
L 520.984031 173.11281 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 409.392031 144.048048 
L 520.984031 144.048048 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 409.392031 114.983286 
L 520.984031 114.983286 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 409.392031 85.918524 
L 520.984031 85.918524 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 409.392031 56.853762 
L 520.984031 56.853762 
" clip-path="url(#p0028cf72c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m2c157b8809" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2c157b8809" x="455.414998" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(451.915623 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m2c157b8809" x="511.089486" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(507.590111 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(342.506625 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(181.798125 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(196.571406 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_40 -->
      <g style="fill: #333333" transform="translate(196.571406 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- BondOrientationParameter_std_dev_BOOP_Q_l=1 -->
      <g style="fill: #333333" transform="translate(64.928125 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-73" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-74" x="1443.546875"/>
       <use xlink:href="#DejaVuSans-64" x="1482.755859"/>
       <use xlink:href="#DejaVuSans-5f" x="1546.232422"/>
       <use xlink:href="#DejaVuSans-64" x="1596.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1659.708984"/>
       <use xlink:href="#DejaVuSans-76" x="1721.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="1780.412109"/>
       <use xlink:href="#DejaVuSans-42" x="1830.412109"/>
       <use xlink:href="#DejaVuSans-4f" x="1897.265625"/>
       <use xlink:href="#DejaVuSans-4f" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-50" x="2054.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="2114.990234"/>
       <use xlink:href="#DejaVuSans-51" x="2164.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2243.701172"/>
       <use xlink:href="#DejaVuSans-6c" x="2293.701172"/>
       <use xlink:href="#DejaVuSans-3d" x="2321.484375"/>
       <use xlink:href="#DejaVuSans-31" x="2405.273438"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(221.222656 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(182.192187 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(166.649062 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(130.982344 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(196.571406 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(73.652344 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(188.300156 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(66.849688 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(46.545312 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(57.759844 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(194.101406 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(87.521719 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(266.198594 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(144.683125 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(103.099375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image751ffda4b8" transform="scale(1 -1) translate(0 -578.16)" x="411.84" y="-43.2" width="106.56" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature77_fold0 -->
    <g transform="translate(156.305031 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-37" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-37" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.958531 638.149 
L 535.588031 638.149 
L 535.588031 27.789 
L 527.958531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image7aecf6aab7" transform="scale(1 -1) translate(0 -609.84)" x="527.76" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(539.088031 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(539.088031 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(573.488969 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p0028cf72c7">
   <rect x="409.392031" y="27.789" width="111.592" height="610.36"/>
  </clipPath>
 </defs>
</svg>
