<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.621906pt" height="679.5765pt" viewBox="0 0 794.621906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:53:13.166327</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.621906 679.5765 
L 794.621906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
L 525.658906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 452.115407 638.149 
L 452.115407 27.789 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.658906 609.084238 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.658906 580.019476 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.658906 550.954714 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.658906 521.889952 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.658906 492.82519 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.658906 463.760429 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.658906 434.695667 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.658906 405.630905 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.658906 376.566143 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.658906 347.501381 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.658906 318.436619 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.658906 289.371857 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.658906 260.307095 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.658906 231.242333 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.658906 202.177571 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.658906 173.11281 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.658906 144.048048 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.658906 114.983286 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.658906 85.918524 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.658906 56.853762 
" clip-path="url(#p96e2eb5436)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m138d8cea65" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m138d8cea65" x="452.115407" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(448.616032 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m138d8cea65" x="517.337534" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(513.838159 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8575 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_range_AtomicWeight -->
      <g style="fill: #333333" transform="translate(73.024688 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-41" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1947.130859"/>
       <use xlink:href="#DejaVuSans-6f" x="1986.339844"/>
       <use xlink:href="#DejaVuSans-6d" x="2047.521484"/>
       <use xlink:href="#DejaVuSans-69" x="2144.933594"/>
       <use xlink:href="#DejaVuSans-63" x="2172.716797"/>
       <use xlink:href="#DejaVuSans-57" x="2227.697266"/>
       <use xlink:href="#DejaVuSans-65" x="2320.699219"/>
       <use xlink:href="#DejaVuSans-69" x="2382.222656"/>
       <use xlink:href="#DejaVuSans-67" x="2410.005859"/>
       <use xlink:href="#DejaVuSans-68" x="2473.482422"/>
       <use xlink:href="#DejaVuSans-74" x="2536.861328"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(156.903125 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(95.679219 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(15.479375 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_avg_dev_Row -->
      <g style="fill: #333333" transform="translate(119.859219 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-52" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6f" x="2072.798828"/>
       <use xlink:href="#DejaVuSans-77" x="2133.980469"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_std_dev_pentagonal_pyramidal_CN_6 -->
      <g style="fill: #333333" transform="translate(23.295625 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-70" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1595.044922"/>
       <use xlink:href="#DejaVuSans-6e" x="1656.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1719.947266"/>
       <use xlink:href="#DejaVuSans-61" x="1759.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1820.435547"/>
       <use xlink:href="#DejaVuSans-6f" x="1883.912109"/>
       <use xlink:href="#DejaVuSans-6e" x="1945.09375"/>
       <use xlink:href="#DejaVuSans-61" x="2008.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="2069.751953"/>
       <use xlink:href="#DejaVuSans-5f" x="2097.535156"/>
       <use xlink:href="#DejaVuSans-70" x="2147.535156"/>
       <use xlink:href="#DejaVuSans-79" x="2211.011719"/>
       <use xlink:href="#DejaVuSans-72" x="2270.191406"/>
       <use xlink:href="#DejaVuSans-61" x="2311.304688"/>
       <use xlink:href="#DejaVuSans-6d" x="2372.583984"/>
       <use xlink:href="#DejaVuSans-69" x="2469.996094"/>
       <use xlink:href="#DejaVuSans-64" x="2497.779297"/>
       <use xlink:href="#DejaVuSans-61" x="2561.255859"/>
       <use xlink:href="#DejaVuSans-6c" x="2622.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="2650.318359"/>
       <use xlink:href="#DejaVuSans-43" x="2700.318359"/>
       <use xlink:href="#DejaVuSans-4e" x="2770.142578"/>
       <use xlink:href="#DejaVuSans-5f" x="2844.947266"/>
       <use xlink:href="#DejaVuSans-36" x="2894.947266"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_4 -->
      <g style="fill: #333333" transform="translate(153.009219 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-34" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(156.903125 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(106.414375 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- AtomicPackingEfficiency_dist_from_5_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-35" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_range_NValence -->
      <g style="fill: #333333" transform="translate(102.877969 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4e" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-56" x="1955.277344"/>
       <use xlink:href="#DejaVuSans-61" x="2015.935547"/>
       <use xlink:href="#DejaVuSans-6c" x="2077.214844"/>
       <use xlink:href="#DejaVuSans-65" x="2104.998047"/>
       <use xlink:href="#DejaVuSans-6e" x="2166.521484"/>
       <use xlink:href="#DejaVuSans-63" x="2229.900391"/>
       <use xlink:href="#DejaVuSans-65" x="2284.880859"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- AverageBondLength_std_dev_Average_bond_length -->
      <g style="fill: #333333" transform="translate(72.236562 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-73" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-74" x="1114.298828"/>
       <use xlink:href="#DejaVuSans-64" x="1153.507812"/>
       <use xlink:href="#DejaVuSans-5f" x="1216.984375"/>
       <use xlink:href="#DejaVuSans-64" x="1266.984375"/>
       <use xlink:href="#DejaVuSans-65" x="1330.460938"/>
       <use xlink:href="#DejaVuSans-76" x="1391.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1451.164062"/>
       <use xlink:href="#DejaVuSans-41" x="1501.164062"/>
       <use xlink:href="#DejaVuSans-76" x="1563.697266"/>
       <use xlink:href="#DejaVuSans-65" x="1622.876953"/>
       <use xlink:href="#DejaVuSans-72" x="1684.400391"/>
       <use xlink:href="#DejaVuSans-61" x="1725.513672"/>
       <use xlink:href="#DejaVuSans-67" x="1786.792969"/>
       <use xlink:href="#DejaVuSans-65" x="1850.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1911.792969"/>
       <use xlink:href="#DejaVuSans-62" x="1961.792969"/>
       <use xlink:href="#DejaVuSans-6f" x="2025.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="2086.451172"/>
       <use xlink:href="#DejaVuSans-64" x="2149.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="2213.306641"/>
       <use xlink:href="#DejaVuSans-6c" x="2263.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2291.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2352.613281"/>
       <use xlink:href="#DejaVuSans-67" x="2415.992188"/>
       <use xlink:href="#DejaVuSans-74" x="2479.46875"/>
       <use xlink:href="#DejaVuSans-68" x="2518.677734"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(111.608281 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(166.71 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(218.598281 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_172 -->
      <g style="fill: #333333" transform="translate(210.327031 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(71.383438 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_maximum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(46.547344 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-76" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.705078"/>
       <use xlink:href="#DejaVuSans-6c" x="2354.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2382.669922"/>
       <use xlink:href="#DejaVuSans-6d" x="2446.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2543.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2604.984375"/>
       <use xlink:href="#DejaVuSans-70" x="2654.984375"/>
       <use xlink:href="#DejaVuSans-61" x="2718.460938"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(53.114375 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(156.903125 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagefbcf8f7d0c" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature44_fold0 -->
    <g transform="translate(169.655906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-34" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.548906 638.149 
L 539.178406 638.149 
L 539.178406 27.789 
L 531.548906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagea5e6ed0b92" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.678406 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.678406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.079344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p96e2eb5436">
   <rect x="431.418906" y="27.789" width="94.24" height="610.36"/>
  </clipPath>
 </defs>
</svg>
