<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.621906pt" height="679.5765pt" viewBox="0 0 794.621906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T18:18:53.193065</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.621906 679.5765 
L 794.621906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
L 525.658906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 463.8876 638.149 
L 463.8876 27.789 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.658906 609.084238 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.658906 580.019476 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.658906 550.954714 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.658906 521.889952 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.658906 492.82519 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.658906 463.760429 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.658906 434.695667 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.658906 405.630905 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.658906 376.566143 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.658906 347.501381 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.658906 318.436619 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.658906 289.371857 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.658906 260.307095 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.658906 231.242333 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.658906 202.177571 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.658906 173.11281 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.658906 144.048048 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.658906 114.983286 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.658906 85.918524 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.658906 56.853762 
" clip-path="url(#p2b75d86df7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m5cb2b938fa" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m5cb2b938fa" x="463.8876" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(455.140881 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m5cb2b938fa" x="502.583438" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(493.836719 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8575 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_avg_dev_Column -->
      <g style="fill: #333333" transform="translate(97.109219 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-43" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6f" x="2077.640625"/>
       <use xlink:href="#DejaVuSans-6c" x="2138.822266"/>
       <use xlink:href="#DejaVuSans-75" x="2166.605469"/>
       <use xlink:href="#DejaVuSans-6d" x="2229.984375"/>
       <use xlink:href="#DejaVuSans-6e" x="2327.396484"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_3 -->
      <g style="fill: #333333" transform="translate(59.279219 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-33" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_avg_dev_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(57.686719 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-76" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-6f" x="2207.962891"/>
       <use xlink:href="#DejaVuSans-6c" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-75" x="2296.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2457.71875"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.242188"/>
       <use xlink:href="#DejaVuSans-70" x="2569.242188"/>
       <use xlink:href="#DejaVuSans-61" x="2632.71875"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_range_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(74.245469 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-47" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-53" x="1957.962891"/>
       <use xlink:href="#DejaVuSans-76" x="2021.439453"/>
       <use xlink:href="#DejaVuSans-6f" x="2080.619141"/>
       <use xlink:href="#DejaVuSans-6c" x="2141.800781"/>
       <use xlink:href="#DejaVuSans-75" x="2169.583984"/>
       <use xlink:href="#DejaVuSans-6d" x="2232.962891"/>
       <use xlink:href="#DejaVuSans-65" x="2330.375"/>
       <use xlink:href="#DejaVuSans-5f" x="2391.898438"/>
       <use xlink:href="#DejaVuSans-70" x="2441.898438"/>
       <use xlink:href="#DejaVuSans-61" x="2505.375"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_Column -->
      <g style="fill: #333333" transform="translate(114.602344 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-6c" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-75" x="2032.083984"/>
       <use xlink:href="#DejaVuSans-6d" x="2095.462891"/>
       <use xlink:href="#DejaVuSans-6e" x="2192.875"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(16.330469 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- VoronoiFingerprint_mean_Voro_area_maximum -->
      <g style="fill: #333333" transform="translate(102.546875 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-72" x="1633.492188"/>
       <use xlink:href="#DejaVuSans-65" x="1672.355469"/>
       <use xlink:href="#DejaVuSans-61" x="1733.878906"/>
       <use xlink:href="#DejaVuSans-5f" x="1795.158203"/>
       <use xlink:href="#DejaVuSans-6d" x="1845.158203"/>
       <use xlink:href="#DejaVuSans-61" x="1942.570312"/>
       <use xlink:href="#DejaVuSans-78" x="2003.849609"/>
       <use xlink:href="#DejaVuSans-69" x="2063.029297"/>
       <use xlink:href="#DejaVuSans-6d" x="2090.8125"/>
       <use xlink:href="#DejaVuSans-75" x="2188.224609"/>
       <use xlink:href="#DejaVuSans-6d" x="2251.603516"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(95.679219 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(79.786719 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(188.675938 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(54.04875 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(243.249531 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(15.479375 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(12.519844 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(153.009219 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- AtomicPackingEfficiency_dist_from_3_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-33" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(287.449531 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(95.679219 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image074e9d60b4" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature95_fold0 -->
    <g transform="translate(169.655906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-39" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.548906 638.149 
L 539.178406 638.149 
L 539.178406 27.789 
L 531.548906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imaged9dd965129" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.678406 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.678406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.079344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p2b75d86df7">
   <rect x="431.418906" y="27.789" width="94.24" height="610.36"/>
  </clipPath>
 </defs>
</svg>
