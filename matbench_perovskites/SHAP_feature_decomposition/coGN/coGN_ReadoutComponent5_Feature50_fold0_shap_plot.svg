<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T09:37:14.671349</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 465.895719 638.149 
L 465.895719 27.789 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#p953ecff32d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mf843bef406" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mf843bef406" x="431.380866" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(423.272663 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mf843bef406" x="465.895719" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(462.396344 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mf843bef406" x="500.410573" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(496.911198 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(86.428906 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_74 -->
      <g style="fill: #333333" transform="translate(209.467812 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_88 -->
      <g style="fill: #333333" transform="translate(209.467812 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_63 -->
      <g style="fill: #333333" transform="translate(209.467812 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_mean_NdValence -->
      <g style="fill: #333333" transform="translate(86.428906 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-64" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_avg_dev_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(48.55625 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-76" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-6f" x="2207.962891"/>
       <use xlink:href="#DejaVuSans-6c" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-75" x="2296.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2457.71875"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.242188"/>
       <use xlink:href="#DejaVuSans-70" x="2569.242188"/>
       <use xlink:href="#DejaVuSans-61" x="2632.71875"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_maximum_CovalentRadius -->
      <g style="fill: #333333" transform="translate(25.402031 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-43" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-6f" x="2163.382812"/>
       <use xlink:href="#DejaVuSans-76" x="2224.564453"/>
       <use xlink:href="#DejaVuSans-61" x="2283.744141"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.023438"/>
       <use xlink:href="#DejaVuSans-65" x="2372.806641"/>
       <use xlink:href="#DejaVuSans-6e" x="2434.330078"/>
       <use xlink:href="#DejaVuSans-74" x="2497.708984"/>
       <use xlink:href="#DejaVuSans-52" x="2536.917969"/>
       <use xlink:href="#DejaVuSans-61" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-64" x="2665.429688"/>
       <use xlink:href="#DejaVuSans-69" x="2728.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2756.689453"/>
       <use xlink:href="#DejaVuSans-73" x="2820.068359"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(86.54875 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(201.196562 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_maximum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(37.416875 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-76" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.705078"/>
       <use xlink:href="#DejaVuSans-6c" x="2354.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2382.669922"/>
       <use xlink:href="#DejaVuSans-6d" x="2446.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2543.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2604.984375"/>
       <use xlink:href="#DejaVuSans-70" x="2654.984375"/>
       <use xlink:href="#DejaVuSans-61" x="2718.460938"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(179.545469 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(201.196562 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(57.796406 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(100.418125 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(279.095 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(157.579531 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(115.995781 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image27e7067616" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature50_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image4a9fb17aeb" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p953ecff32d">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
