<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="785.946156pt" height="679.5765pt" viewBox="0 0 785.946156 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:57:57.161817</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 785.946156 679.5765 
L 785.946156 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
L 522.851156 27.789 
L 416.875156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 476.861617 638.149 
L 476.861617 27.789 
" clip-path="url(#p2084be647a)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 416.875156 609.084238 
L 522.851156 609.084238 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 416.875156 580.019476 
L 522.851156 580.019476 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 416.875156 550.954714 
L 522.851156 550.954714 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 416.875156 521.889952 
L 522.851156 521.889952 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 416.875156 492.82519 
L 522.851156 492.82519 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 416.875156 463.760429 
L 522.851156 463.760429 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 416.875156 434.695667 
L 522.851156 434.695667 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 416.875156 405.630905 
L 522.851156 405.630905 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 416.875156 376.566143 
L 522.851156 376.566143 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 416.875156 347.501381 
L 522.851156 347.501381 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 416.875156 318.436619 
L 522.851156 318.436619 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 416.875156 289.371857 
L 522.851156 289.371857 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 416.875156 260.307095 
L 522.851156 260.307095 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 416.875156 231.242333 
L 522.851156 231.242333 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 416.875156 202.177571 
L 522.851156 202.177571 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 416.875156 173.11281 
L 522.851156 173.11281 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 416.875156 144.048048 
L 522.851156 144.048048 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 416.875156 114.983286 
L 522.851156 114.983286 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 416.875156 85.918524 
L 522.851156 85.918524 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 416.875156 56.853762 
L 522.851156 56.853762 
" clip-path="url(#p2084be647a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mc9163057ba" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mc9163057ba" x="434.203048" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(420.847501 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mc9163057ba" x="476.861617" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(468.114898 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mc9163057ba" x="519.520186" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(510.773467 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(347.18175 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(65.87875 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- GaussianSymmFunc_mean_G2_0_05 -->
      <g style="fill: #333333" transform="translate(156.05 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-6d" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-65" x="1157.439453"/>
       <use xlink:href="#DejaVuSans-61" x="1218.962891"/>
       <use xlink:href="#DejaVuSans-6e" x="1280.242188"/>
       <use xlink:href="#DejaVuSans-5f" x="1343.621094"/>
       <use xlink:href="#DejaVuSans-47" x="1393.621094"/>
       <use xlink:href="#DejaVuSans-32" x="1471.111328"/>
       <use xlink:href="#DejaVuSans-5f" x="1534.734375"/>
       <use xlink:href="#DejaVuSans-30" x="1584.734375"/>
       <use xlink:href="#DejaVuSans-5f" x="1648.357422"/>
       <use xlink:href="#DejaVuSans-30" x="1698.357422"/>
       <use xlink:href="#DejaVuSans-35" x="1761.980469"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_mean_NdUnfilled -->
      <g style="fill: #333333" transform="translate(82.447656 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-64" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-55" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2084.769531"/>
       <use xlink:href="#DejaVuSans-66" x="2148.148438"/>
       <use xlink:href="#DejaVuSans-69" x="2183.353516"/>
       <use xlink:href="#DejaVuSans-6c" x="2211.136719"/>
       <use xlink:href="#DejaVuSans-6c" x="2238.919922"/>
       <use xlink:href="#DejaVuSans-65" x="2266.703125"/>
       <use xlink:href="#DejaVuSans-64" x="2328.226562"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=7_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(7.2 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-37" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- DensityFeatures_density -->
      <g style="fill: #333333" transform="translate(233.901719 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-64" x="860.072266"/>
       <use xlink:href="#DejaVuSans-65" x="923.548828"/>
       <use xlink:href="#DejaVuSans-6e" x="985.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1048.451172"/>
       <use xlink:href="#DejaVuSans-69" x="1100.550781"/>
       <use xlink:href="#DejaVuSans-74" x="1128.333984"/>
       <use xlink:href="#DejaVuSans-79" x="1167.542969"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- OPSiteFingerprint_std_dev_trigonal_pyramidal_CN_4 -->
      <g style="fill: #333333" transform="translate(54.235625 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-74" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-72" x="1401.685547"/>
       <use xlink:href="#DejaVuSans-69" x="1442.798828"/>
       <use xlink:href="#DejaVuSans-67" x="1470.582031"/>
       <use xlink:href="#DejaVuSans-6f" x="1534.058594"/>
       <use xlink:href="#DejaVuSans-6e" x="1595.240234"/>
       <use xlink:href="#DejaVuSans-61" x="1658.619141"/>
       <use xlink:href="#DejaVuSans-6c" x="1719.898438"/>
       <use xlink:href="#DejaVuSans-5f" x="1747.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1797.681641"/>
       <use xlink:href="#DejaVuSans-79" x="1861.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1920.337891"/>
       <use xlink:href="#DejaVuSans-61" x="1961.451172"/>
       <use xlink:href="#DejaVuSans-6d" x="2022.730469"/>
       <use xlink:href="#DejaVuSans-69" x="2120.142578"/>
       <use xlink:href="#DejaVuSans-64" x="2147.925781"/>
       <use xlink:href="#DejaVuSans-61" x="2211.402344"/>
       <use xlink:href="#DejaVuSans-6c" x="2272.681641"/>
       <use xlink:href="#DejaVuSans-5f" x="2300.464844"/>
       <use xlink:href="#DejaVuSans-43" x="2350.464844"/>
       <use xlink:href="#DejaVuSans-4e" x="2420.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="2495.09375"/>
       <use xlink:href="#DejaVuSans-34" x="2545.09375"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(74.332812 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- OPSiteFingerprint_std_dev_q2_CN_12 -->
      <g style="fill: #333333" transform="translate(151.380156 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-71" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-32" x="1425.953125"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-43" x="1539.576172"/>
       <use xlink:href="#DejaVuSans-4e" x="1609.400391"/>
       <use xlink:href="#DejaVuSans-5f" x="1684.205078"/>
       <use xlink:href="#DejaVuSans-31" x="1734.205078"/>
       <use xlink:href="#DejaVuSans-32" x="1797.828125"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(81.135469 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(81.135469 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_23 -->
      <g style="fill: #333333" transform="translate(204.054531 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(152.16625 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(90.700625 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=4_43e+00 -->
      <g style="fill: #333333" transform="translate(90.121719 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-34" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-34" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-33" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(93.875469 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- AverageBondLength_std_dev_Average_bond_length -->
      <g style="fill: #333333" transform="translate(57.692812 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-73" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-74" x="1114.298828"/>
       <use xlink:href="#DejaVuSans-64" x="1153.507812"/>
       <use xlink:href="#DejaVuSans-5f" x="1216.984375"/>
       <use xlink:href="#DejaVuSans-64" x="1266.984375"/>
       <use xlink:href="#DejaVuSans-65" x="1330.460938"/>
       <use xlink:href="#DejaVuSans-76" x="1391.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1451.164062"/>
       <use xlink:href="#DejaVuSans-41" x="1501.164062"/>
       <use xlink:href="#DejaVuSans-76" x="1563.697266"/>
       <use xlink:href="#DejaVuSans-65" x="1622.876953"/>
       <use xlink:href="#DejaVuSans-72" x="1684.400391"/>
       <use xlink:href="#DejaVuSans-61" x="1725.513672"/>
       <use xlink:href="#DejaVuSans-67" x="1786.792969"/>
       <use xlink:href="#DejaVuSans-65" x="1850.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1911.792969"/>
       <use xlink:href="#DejaVuSans-62" x="1961.792969"/>
       <use xlink:href="#DejaVuSans-6f" x="2025.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="2086.451172"/>
       <use xlink:href="#DejaVuSans-64" x="2149.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="2213.306641"/>
       <use xlink:href="#DejaVuSans-6c" x="2263.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2291.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2352.613281"/>
       <use xlink:href="#DejaVuSans-67" x="2415.992188"/>
       <use xlink:href="#DejaVuSans-74" x="2479.46875"/>
       <use xlink:href="#DejaVuSans-68" x="2518.677734"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(271.433125 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(272.905781 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- Stoichiometry_2-norm -->
      <g style="fill: #333333" transform="translate(249.27625 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-32" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(142.359375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image94e46d7a74" transform="scale(1 -1) translate(0 -578.16)" x="419.04" y="-43.2" width="101.52" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature86_fold0 -->
    <g transform="translate(160.980156 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-38" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.474656 638.149 
L 537.104156 638.149 
L 537.104156 27.789 
L 529.474656 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image717b233bbc" transform="scale(1 -1) translate(0 -609.84)" x="529.2" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(540.604156 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(540.604156 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.005094 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p2084be647a">
   <rect x="416.875156" y="27.789" width="105.976" height="610.36"/>
  </clipPath>
 </defs>
</svg>
