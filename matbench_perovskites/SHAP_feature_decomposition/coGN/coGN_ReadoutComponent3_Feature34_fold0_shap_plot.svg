<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:30:48.933426</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 482.111556 638.149 
L 482.111556 27.789 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#p4e4e114554)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m75a7603844" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m75a7603844" x="444.163582" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(430.808035 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m75a7603844" x="482.111556" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(473.364838 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m75a7603844" x="520.059531" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(511.312812 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- MaximumPackingEfficiency_max_packing_efficiency -->
      <g style="fill: #333333" transform="translate(64.3025 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-61" x="86.279297"/>
       <use xlink:href="#DejaVuSans-78" x="147.558594"/>
       <use xlink:href="#DejaVuSans-69" x="206.738281"/>
       <use xlink:href="#DejaVuSans-6d" x="234.521484"/>
       <use xlink:href="#DejaVuSans-75" x="331.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="395.3125"/>
       <use xlink:href="#DejaVuSans-50" x="492.724609"/>
       <use xlink:href="#DejaVuSans-61" x="548.527344"/>
       <use xlink:href="#DejaVuSans-63" x="609.806641"/>
       <use xlink:href="#DejaVuSans-6b" x="664.787109"/>
       <use xlink:href="#DejaVuSans-69" x="722.697266"/>
       <use xlink:href="#DejaVuSans-6e" x="750.480469"/>
       <use xlink:href="#DejaVuSans-67" x="813.859375"/>
       <use xlink:href="#DejaVuSans-45" x="877.335938"/>
       <use xlink:href="#DejaVuSans-66" x="940.519531"/>
       <use xlink:href="#DejaVuSans-66" x="975.724609"/>
       <use xlink:href="#DejaVuSans-69" x="1010.929688"/>
       <use xlink:href="#DejaVuSans-63" x="1038.712891"/>
       <use xlink:href="#DejaVuSans-69" x="1093.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1121.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="1183"/>
       <use xlink:href="#DejaVuSans-63" x="1246.378906"/>
       <use xlink:href="#DejaVuSans-79" x="1301.359375"/>
       <use xlink:href="#DejaVuSans-5f" x="1360.539062"/>
       <use xlink:href="#DejaVuSans-6d" x="1410.539062"/>
       <use xlink:href="#DejaVuSans-61" x="1507.951172"/>
       <use xlink:href="#DejaVuSans-78" x="1569.230469"/>
       <use xlink:href="#DejaVuSans-5f" x="1628.410156"/>
       <use xlink:href="#DejaVuSans-70" x="1678.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1741.886719"/>
       <use xlink:href="#DejaVuSans-63" x="1803.166016"/>
       <use xlink:href="#DejaVuSans-6b" x="1858.146484"/>
       <use xlink:href="#DejaVuSans-69" x="1916.056641"/>
       <use xlink:href="#DejaVuSans-6e" x="1943.839844"/>
       <use xlink:href="#DejaVuSans-67" x="2007.21875"/>
       <use xlink:href="#DejaVuSans-5f" x="2070.695312"/>
       <use xlink:href="#DejaVuSans-65" x="2120.695312"/>
       <use xlink:href="#DejaVuSans-66" x="2182.21875"/>
       <use xlink:href="#DejaVuSans-66" x="2217.423828"/>
       <use xlink:href="#DejaVuSans-69" x="2252.628906"/>
       <use xlink:href="#DejaVuSans-63" x="2280.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2335.392578"/>
       <use xlink:href="#DejaVuSans-65" x="2363.175781"/>
       <use xlink:href="#DejaVuSans-6e" x="2424.699219"/>
       <use xlink:href="#DejaVuSans-63" x="2488.078125"/>
       <use xlink:href="#DejaVuSans-79" x="2543.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(75.102656 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(135.782188 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- OPSiteFingerprint_mean_q2_CN_12 -->
      <g style="fill: #333333" transform="translate(174.304844 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="923.511719"/>
       <use xlink:href="#DejaVuSans-65" x="1020.923828"/>
       <use xlink:href="#DejaVuSans-61" x="1082.447266"/>
       <use xlink:href="#DejaVuSans-6e" x="1143.726562"/>
       <use xlink:href="#DejaVuSans-5f" x="1207.105469"/>
       <use xlink:href="#DejaVuSans-71" x="1257.105469"/>
       <use xlink:href="#DejaVuSans-32" x="1320.582031"/>
       <use xlink:href="#DejaVuSans-5f" x="1384.205078"/>
       <use xlink:href="#DejaVuSans-43" x="1434.205078"/>
       <use xlink:href="#DejaVuSans-4e" x="1504.029297"/>
       <use xlink:href="#DejaVuSans-5f" x="1578.833984"/>
       <use xlink:href="#DejaVuSans-31" x="1628.833984"/>
       <use xlink:href="#DejaVuSans-32" x="1692.457031"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- Stoichiometry_2-norm -->
      <g style="fill: #333333" transform="translate(258.500156 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-32" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- XRDPowderPattern_xrd_38 -->
      <g style="fill: #333333" transform="translate(229.7275 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-38" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(83.556719 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(92.587656 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-69" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-6e" x="2035.347656"/>
       <use xlink:href="#DejaVuSans-69" x="2098.726562"/>
       <use xlink:href="#DejaVuSans-6d" x="2126.509766"/>
       <use xlink:href="#DejaVuSans-75" x="2223.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="2287.300781"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- StructuralHeterogeneity_mean_neighbor_distance_variation -->
      <g style="fill: #333333" transform="translate(12.162344 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-65" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-61" x="1417.427734"/>
       <use xlink:href="#DejaVuSans-6e" x="1478.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="1542.085938"/>
       <use xlink:href="#DejaVuSans-6e" x="1592.085938"/>
       <use xlink:href="#DejaVuSans-65" x="1655.464844"/>
       <use xlink:href="#DejaVuSans-69" x="1716.988281"/>
       <use xlink:href="#DejaVuSans-67" x="1744.771484"/>
       <use xlink:href="#DejaVuSans-68" x="1808.248047"/>
       <use xlink:href="#DejaVuSans-62" x="1871.626953"/>
       <use xlink:href="#DejaVuSans-6f" x="1935.103516"/>
       <use xlink:href="#DejaVuSans-72" x="1996.285156"/>
       <use xlink:href="#DejaVuSans-5f" x="2037.398438"/>
       <use xlink:href="#DejaVuSans-64" x="2087.398438"/>
       <use xlink:href="#DejaVuSans-69" x="2150.875"/>
       <use xlink:href="#DejaVuSans-73" x="2178.658203"/>
       <use xlink:href="#DejaVuSans-74" x="2230.757812"/>
       <use xlink:href="#DejaVuSans-61" x="2269.966797"/>
       <use xlink:href="#DejaVuSans-6e" x="2331.246094"/>
       <use xlink:href="#DejaVuSans-63" x="2394.625"/>
       <use xlink:href="#DejaVuSans-65" x="2449.605469"/>
       <use xlink:href="#DejaVuSans-5f" x="2511.128906"/>
       <use xlink:href="#DejaVuSans-76" x="2561.128906"/>
       <use xlink:href="#DejaVuSans-61" x="2620.308594"/>
       <use xlink:href="#DejaVuSans-72" x="2681.587891"/>
       <use xlink:href="#DejaVuSans-69" x="2722.701172"/>
       <use xlink:href="#DejaVuSans-61" x="2750.484375"/>
       <use xlink:href="#DejaVuSans-74" x="2811.763672"/>
       <use xlink:href="#DejaVuSans-69" x="2850.972656"/>
       <use xlink:href="#DejaVuSans-6f" x="2878.755859"/>
       <use xlink:href="#DejaVuSans-6e" x="2939.9375"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(63.039062 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_mean_NdUnfilled -->
      <g style="fill: #333333" transform="translate(91.671562 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-64" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-55" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2084.769531"/>
       <use xlink:href="#DejaVuSans-66" x="2148.148438"/>
       <use xlink:href="#DejaVuSans-69" x="2183.353516"/>
       <use xlink:href="#DejaVuSans-6c" x="2211.136719"/>
       <use xlink:href="#DejaVuSans-6c" x="2238.919922"/>
       <use xlink:href="#DejaVuSans-65" x="2266.703125"/>
       <use xlink:href="#DejaVuSans-64" x="2328.226562"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(210.808437 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_avg_dev_MeltingT -->
      <g style="fill: #333333" transform="translate(85.60625 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-74" x="2183.402344"/>
       <use xlink:href="#DejaVuSans-69" x="2222.611328"/>
       <use xlink:href="#DejaVuSans-6e" x="2250.394531"/>
       <use xlink:href="#DejaVuSans-67" x="2313.773438"/>
       <use xlink:href="#DejaVuSans-54" x="2377.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(99.924531 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(66.063594 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(106.288437 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(280.149219 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- AverageBondLength_std_dev_Average_bond_length -->
      <g style="fill: #333333" transform="translate(66.916719 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-73" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-74" x="1114.298828"/>
       <use xlink:href="#DejaVuSans-64" x="1153.507812"/>
       <use xlink:href="#DejaVuSans-5f" x="1216.984375"/>
       <use xlink:href="#DejaVuSans-64" x="1266.984375"/>
       <use xlink:href="#DejaVuSans-65" x="1330.460938"/>
       <use xlink:href="#DejaVuSans-76" x="1391.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1451.164062"/>
       <use xlink:href="#DejaVuSans-41" x="1501.164062"/>
       <use xlink:href="#DejaVuSans-76" x="1563.697266"/>
       <use xlink:href="#DejaVuSans-65" x="1622.876953"/>
       <use xlink:href="#DejaVuSans-72" x="1684.400391"/>
       <use xlink:href="#DejaVuSans-61" x="1725.513672"/>
       <use xlink:href="#DejaVuSans-67" x="1786.792969"/>
       <use xlink:href="#DejaVuSans-65" x="1850.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1911.792969"/>
       <use xlink:href="#DejaVuSans-62" x="1961.792969"/>
       <use xlink:href="#DejaVuSans-6f" x="2025.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="2086.451172"/>
       <use xlink:href="#DejaVuSans-64" x="2149.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="2213.306641"/>
       <use xlink:href="#DejaVuSans-6c" x="2263.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2291.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2352.613281"/>
       <use xlink:href="#DejaVuSans-67" x="2415.992188"/>
       <use xlink:href="#DejaVuSans-74" x="2479.46875"/>
       <use xlink:href="#DejaVuSans-68" x="2518.677734"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- ElementProperty_MagpieData_maximum_GSbandgap -->
      <g style="fill: #333333" transform="translate(54.924219 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-62" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-61" x="2298.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="2359.28125"/>
       <use xlink:href="#DejaVuSans-64" x="2422.660156"/>
       <use xlink:href="#DejaVuSans-67" x="2486.136719"/>
       <use xlink:href="#DejaVuSans-61" x="2549.613281"/>
       <use xlink:href="#DejaVuSans-70" x="2610.892578"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099062 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image833bf02b1d" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature34_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-34" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imageee49bbb843" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p4e4e114554">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
