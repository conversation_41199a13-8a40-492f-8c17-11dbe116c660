<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="769.134687pt" height="679.5765pt" viewBox="0 0 769.134687 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T02:21:34.959267</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 769.134687 679.5765 
L 769.134687 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 379.339688 638.149 
L 514.547688 638.149 
L 514.547688 27.789 
L 379.339688 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 472.050237 638.149 
L 472.050237 27.789 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 379.339688 609.084238 
L 514.547688 609.084238 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 379.339688 580.019476 
L 514.547688 580.019476 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 379.339688 550.954714 
L 514.547688 550.954714 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 379.339688 521.889952 
L 514.547688 521.889952 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 379.339688 492.82519 
L 514.547688 492.82519 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 379.339688 463.760429 
L 514.547688 463.760429 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 379.339688 434.695667 
L 514.547688 434.695667 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 379.339688 405.630905 
L 514.547688 405.630905 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 379.339688 376.566143 
L 514.547688 376.566143 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 379.339688 347.501381 
L 514.547688 347.501381 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 379.339688 318.436619 
L 514.547688 318.436619 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 379.339688 289.371857 
L 514.547688 289.371857 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 379.339688 260.307095 
L 514.547688 260.307095 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 379.339688 231.242333 
L 514.547688 231.242333 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 379.339688 202.177571 
L 514.547688 202.177571 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 379.339688 173.11281 
L 514.547688 173.11281 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 379.339688 144.048048 
L 514.547688 144.048048 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 379.339688 114.983286 
L 514.547688 114.983286 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 379.339688 85.918524 
L 514.547688 85.918524 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 379.339688 56.853762 
L 514.547688 56.853762 
" clip-path="url(#p651f0ec771)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mf58b2c861f" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mf58b2c861f" x="386.360349" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −4 -->
      <g style="fill: #333333" transform="translate(378.252146 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mf58b2c861f" x="429.205293" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(421.09709 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mf58b2c861f" x="472.050237" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(468.550862 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(324.262281 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_104 -->
      <g style="fill: #333333" transform="translate(158.247813 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-34" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(93.587031 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_72 -->
      <g style="fill: #333333" transform="translate(166.519063 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(43.6 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- VoronoiFingerprint_std_dev_Voro_area_maximum -->
      <g style="fill: #333333" transform="translate(36.766875 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-61" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-78" x="2109.220703"/>
       <use xlink:href="#DejaVuSans-69" x="2168.400391"/>
       <use xlink:href="#DejaVuSans-6d" x="2196.183594"/>
       <use xlink:href="#DejaVuSans-75" x="2293.595703"/>
       <use xlink:href="#DejaVuSans-6d" x="2356.974609"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_square_pyramidal_CN_5 -->
      <g style="fill: #333333" transform="translate(14.213906 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-73" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-71" x="1478.296875"/>
       <use xlink:href="#DejaVuSans-75" x="1541.773438"/>
       <use xlink:href="#DejaVuSans-61" x="1605.152344"/>
       <use xlink:href="#DejaVuSans-72" x="1666.431641"/>
       <use xlink:href="#DejaVuSans-65" x="1705.294922"/>
       <use xlink:href="#DejaVuSans-5f" x="1766.818359"/>
       <use xlink:href="#DejaVuSans-70" x="1816.818359"/>
       <use xlink:href="#DejaVuSans-79" x="1880.294922"/>
       <use xlink:href="#DejaVuSans-72" x="1939.474609"/>
       <use xlink:href="#DejaVuSans-61" x="1980.587891"/>
       <use xlink:href="#DejaVuSans-6d" x="2041.867188"/>
       <use xlink:href="#DejaVuSans-69" x="2139.279297"/>
       <use xlink:href="#DejaVuSans-64" x="2167.0625"/>
       <use xlink:href="#DejaVuSans-61" x="2230.539062"/>
       <use xlink:href="#DejaVuSans-6c" x="2291.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="2319.601562"/>
       <use xlink:href="#DejaVuSans-43" x="2369.601562"/>
       <use xlink:href="#DejaVuSans-4e" x="2439.425781"/>
       <use xlink:href="#DejaVuSans-5f" x="2514.230469"/>
       <use xlink:href="#DejaVuSans-35" x="2564.230469"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(104.823906 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_172 -->
      <g style="fill: #333333" transform="translate(158.247813 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(7.2 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(79.88625 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(166.519063 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(136.596719 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(55.405625 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_mode_Column -->
      <g style="fill: #333333" transform="translate(62.521094 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-6c" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-75" x="2032.083984"/>
       <use xlink:href="#DejaVuSans-6d" x="2095.462891"/>
       <use xlink:href="#DejaVuSans-6e" x="2192.875"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(43.6 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(104.823906 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(19.304219 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- Miedema_Miedema_deltaH_inter -->
      <g style="fill: #333333" transform="translate(143.929531 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-69" x="86.279297"/>
       <use xlink:href="#DejaVuSans-65" x="114.0625"/>
       <use xlink:href="#DejaVuSans-64" x="175.585938"/>
       <use xlink:href="#DejaVuSans-65" x="239.0625"/>
       <use xlink:href="#DejaVuSans-6d" x="300.585938"/>
       <use xlink:href="#DejaVuSans-61" x="397.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="459.277344"/>
       <use xlink:href="#DejaVuSans-4d" x="509.277344"/>
       <use xlink:href="#DejaVuSans-69" x="595.556641"/>
       <use xlink:href="#DejaVuSans-65" x="623.339844"/>
       <use xlink:href="#DejaVuSans-64" x="684.863281"/>
       <use xlink:href="#DejaVuSans-65" x="748.339844"/>
       <use xlink:href="#DejaVuSans-6d" x="809.863281"/>
       <use xlink:href="#DejaVuSans-61" x="907.275391"/>
       <use xlink:href="#DejaVuSans-5f" x="968.554688"/>
       <use xlink:href="#DejaVuSans-64" x="1018.554688"/>
       <use xlink:href="#DejaVuSans-65" x="1082.03125"/>
       <use xlink:href="#DejaVuSans-6c" x="1143.554688"/>
       <use xlink:href="#DejaVuSans-74" x="1171.337891"/>
       <use xlink:href="#DejaVuSans-61" x="1210.546875"/>
       <use xlink:href="#DejaVuSans-48" x="1271.826172"/>
       <use xlink:href="#DejaVuSans-5f" x="1347.021484"/>
       <use xlink:href="#DejaVuSans-69" x="1397.021484"/>
       <use xlink:href="#DejaVuSans-6e" x="1424.804688"/>
       <use xlink:href="#DejaVuSans-74" x="1488.183594"/>
       <use xlink:href="#DejaVuSans-65" x="1527.392578"/>
       <use xlink:href="#DejaVuSans-72" x="1588.916016"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(233.897656 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(47.073438 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 379.339688 638.149 
L 514.547688 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image8edc5d4682" transform="scale(1 -1) translate(0 -578.16)" x="383.04" y="-43.2" width="127.44" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature125_fold0 -->
    <g transform="translate(131.952688 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-35" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 522.998187 638.149 
L 530.627687 638.149 
L 530.627687 27.789 
L 522.998187 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image70c234408e" transform="scale(1 -1) translate(0 -609.84)" x="522.72" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(534.127687 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(534.127687 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(568.528625 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p651f0ec771">
   <rect x="379.339688" y="27.789" width="135.208" height="610.36"/>
  </clipPath>
 </defs>
</svg>
