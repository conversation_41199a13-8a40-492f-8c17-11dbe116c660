<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="792.126156pt" height="679.5765pt" viewBox="0 0 792.126156 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T21:56:54.600832</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 792.126156 679.5765 
L 792.126156 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 416.875156 638.149 
L 522.995156 638.149 
L 522.995156 27.789 
L 416.875156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 464.02473 638.149 
L 464.02473 27.789 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 416.875156 609.084238 
L 522.995156 609.084238 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 416.875156 580.019476 
L 522.995156 580.019476 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 416.875156 550.954714 
L 522.995156 550.954714 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 416.875156 521.889952 
L 522.995156 521.889952 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 416.875156 492.82519 
L 522.995156 492.82519 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 416.875156 463.760429 
L 522.995156 463.760429 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 416.875156 434.695667 
L 522.995156 434.695667 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 416.875156 405.630905 
L 522.995156 405.630905 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 416.875156 376.566143 
L 522.995156 376.566143 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 416.875156 347.501381 
L 522.995156 347.501381 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 416.875156 318.436619 
L 522.995156 318.436619 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 416.875156 289.371857 
L 522.995156 289.371857 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 416.875156 260.307095 
L 522.995156 260.307095 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 416.875156 231.242333 
L 522.995156 231.242333 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 416.875156 202.177571 
L 522.995156 202.177571 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 416.875156 173.11281 
L 522.995156 173.11281 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 416.875156 144.048048 
L 522.995156 144.048048 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 416.875156 114.983286 
L 522.995156 114.983286 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 416.875156 85.918524 
L 522.995156 85.918524 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 416.875156 56.853762 
L 522.995156 56.853762 
" clip-path="url(#p8e1b781d4e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m1183f49234" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m1183f49234" x="421.145181" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2.5 -->
      <g style="fill: #333333" transform="translate(407.789634 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m1183f49234" x="464.02473" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(455.278012 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m1183f49234" x="506.90428" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 2.5 -->
      <g style="fill: #333333" transform="translate(498.157561 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(347.25375 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(14.683125 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_122 -->
      <g style="fill: #333333" transform="translate(195.783281 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_99 -->
      <g style="fill: #333333" transform="translate(204.054531 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_123 -->
      <g style="fill: #333333" transform="translate(195.783281 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-33" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- VoronoiFingerprint_mean_Voro_vol_maximum -->
      <g style="fill: #333333" transform="translate(97.726719 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-76" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-6f" x="1631.392578"/>
       <use xlink:href="#DejaVuSans-6c" x="1692.574219"/>
       <use xlink:href="#DejaVuSans-5f" x="1720.357422"/>
       <use xlink:href="#DejaVuSans-6d" x="1770.357422"/>
       <use xlink:href="#DejaVuSans-61" x="1867.769531"/>
       <use xlink:href="#DejaVuSans-78" x="1929.048828"/>
       <use xlink:href="#DejaVuSans-69" x="1988.228516"/>
       <use xlink:href="#DejaVuSans-6d" x="2016.011719"/>
       <use xlink:href="#DejaVuSans-75" x="2113.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="2176.802734"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(81.135469 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_24 -->
      <g style="fill: #333333" transform="translate(204.054531 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(142.359375 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- VoronoiFingerprint_mean_Voro_vol_std_dev -->
      <g style="fill: #333333" transform="translate(112.658437 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-76" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-6f" x="1631.392578"/>
       <use xlink:href="#DejaVuSans-6c" x="1692.574219"/>
       <use xlink:href="#DejaVuSans-5f" x="1720.357422"/>
       <use xlink:href="#DejaVuSans-73" x="1770.357422"/>
       <use xlink:href="#DejaVuSans-74" x="1822.457031"/>
       <use xlink:href="#DejaVuSans-64" x="1861.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1925.142578"/>
       <use xlink:href="#DejaVuSans-64" x="1975.142578"/>
       <use xlink:href="#DejaVuSans-65" x="2038.619141"/>
       <use xlink:href="#DejaVuSans-76" x="2100.142578"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(108.967656 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(195.783281 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(81.135469 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=0_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(7.2 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-30" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(204.054531 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(81.135469 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- VoronoiFingerprint_std_dev_Voro_area_sum -->
      <g style="fill: #333333" transform="translate(112.128281 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-73" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-75" x="2002.628906"/>
       <use xlink:href="#DejaVuSans-6d" x="2066.007812"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(95.004844 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(273.681719 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(152.16625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(110.5825 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 416.875156 638.149 
L 522.995156 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagec6fb54ec3a" transform="scale(1 -1) translate(0 -578.16)" x="419.04" y="-43.2" width="101.52" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature115_fold0 -->
    <g transform="translate(154.944156 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-35" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.627656 638.149 
L 537.257156 638.149 
L 537.257156 27.789 
L 529.627656 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image6e2ee210d2" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(540.757156 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(540.757156 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.158094 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p8e1b781d4e">
   <rect x="416.875156" y="27.789" width="106.12" height="610.36"/>
  </clipPath>
 </defs>
</svg>
