<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.621906pt" height="679.5765pt" viewBox="0 0 794.621906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T07:02:30.091810</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.621906 679.5765 
L 794.621906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
L 525.658906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 495.626999 638.149 
L 495.626999 27.789 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.658906 609.084238 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.658906 580.019476 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.658906 550.954714 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.658906 521.889952 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.658906 492.82519 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.658906 463.760429 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.658906 434.695667 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.658906 405.630905 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.658906 376.566143 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.658906 347.501381 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.658906 318.436619 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.658906 289.371857 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.658906 260.307095 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.658906 231.242333 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.658906 202.177571 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.658906 173.11281 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.658906 144.048048 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.658906 114.983286 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.658906 85.918524 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.658906 56.853762 
" clip-path="url(#p95d9e18afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mc8113e7dcf" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mc8113e7dcf" x="436.402305" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −5 -->
      <g style="fill: #333333" transform="translate(428.294102 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-35" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mc8113e7dcf" x="495.626999" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(492.127624 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8575 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- GeneralizedRDF_mean_Gaussian_center=3_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(35.444531 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-33" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(166.71 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(66.926875 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(12.519844 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- VoronoiFingerprint_mean_Voro_vol_std_dev -->
      <g style="fill: #333333" transform="translate(127.202188 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-76" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-6f" x="1631.392578"/>
       <use xlink:href="#DejaVuSans-6c" x="1692.574219"/>
       <use xlink:href="#DejaVuSans-5f" x="1720.357422"/>
       <use xlink:href="#DejaVuSans-73" x="1770.357422"/>
       <use xlink:href="#DejaVuSans-74" x="1822.457031"/>
       <use xlink:href="#DejaVuSans-64" x="1861.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1925.142578"/>
       <use xlink:href="#DejaVuSans-64" x="1975.142578"/>
       <use xlink:href="#DejaVuSans-65" x="2038.619141"/>
       <use xlink:href="#DejaVuSans-76" x="2100.142578"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_54 -->
      <g style="fill: #333333" transform="translate(218.598281 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_122 -->
      <g style="fill: #333333" transform="translate(210.327031 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- VoronoiFingerprint_std_dev_Voro_area_sum -->
      <g style="fill: #333333" transform="translate(126.672031 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-73" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-75" x="2002.628906"/>
       <use xlink:href="#DejaVuSans-6d" x="2066.007812"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_16 -->
      <g style="fill: #333333" transform="translate(218.598281 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- AtomicPackingEfficiency_dist_from_5_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-35" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(80.4225 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(53.114375 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_167 -->
      <g style="fill: #333333" transform="translate(210.327031 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(156.892969 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- AtomicPackingEfficiency_dist_from_3_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-33" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(95.679219 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(156.903125 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(285.976875 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(99.152656 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image6fce11b72f" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature61_fold0 -->
    <g transform="translate(169.655906 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.548906 638.149 
L 539.178406 638.149 
L 539.178406 27.789 
L 531.548906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagee3330f5713" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.678406 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.678406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.079344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p95d9e18afd">
   <rect x="431.418906" y="27.789" width="94.24" height="610.36"/>
  </clipPath>
 </defs>
</svg>
