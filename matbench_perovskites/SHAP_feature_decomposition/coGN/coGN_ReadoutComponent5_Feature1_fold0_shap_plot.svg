<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="746.642031pt" height="679.5765pt" viewBox="0 0 746.642031 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T05:08:32.924160</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 746.642031 679.5765 
L 746.642031 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 362.007031 638.149 
L 511.327031 638.149 
L 511.327031 27.789 
L 362.007031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 426.115533 638.149 
L 426.115533 27.789 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 362.007031 609.084238 
L 511.327031 609.084238 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 362.007031 580.019476 
L 511.327031 580.019476 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 362.007031 550.954714 
L 511.327031 550.954714 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 362.007031 521.889952 
L 511.327031 521.889952 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 362.007031 492.82519 
L 511.327031 492.82519 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 362.007031 463.760429 
L 511.327031 463.760429 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 362.007031 434.695667 
L 511.327031 434.695667 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 362.007031 405.630905 
L 511.327031 405.630905 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 362.007031 376.566143 
L 511.327031 376.566143 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 362.007031 347.501381 
L 511.327031 347.501381 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 362.007031 318.436619 
L 511.327031 318.436619 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 362.007031 289.371857 
L 511.327031 289.371857 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 362.007031 260.307095 
L 511.327031 260.307095 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 362.007031 231.242333 
L 511.327031 231.242333 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 362.007031 202.177571 
L 511.327031 202.177571 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 362.007031 173.11281 
L 511.327031 173.11281 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 362.007031 144.048048 
L 511.327031 144.048048 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 362.007031 114.983286 
L 511.327031 114.983286 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 362.007031 85.918524 
L 511.327031 85.918524 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 362.007031 56.853762 
L 511.327031 56.853762 
" clip-path="url(#p3f034bfd6c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="me53432ee8f" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#me53432ee8f" x="426.115533" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(417.368815 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#me53432ee8f" x="491.724788" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(482.978069 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(313.985625 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(35.8325 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- AGNIFingerPrint_mean_AGNI_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(55.157812 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-6d" x="836.027344"/>
       <use xlink:href="#DejaVuSans-65" x="933.439453"/>
       <use xlink:href="#DejaVuSans-61" x="994.962891"/>
       <use xlink:href="#DejaVuSans-6e" x="1056.242188"/>
       <use xlink:href="#DejaVuSans-5f" x="1119.621094"/>
       <use xlink:href="#DejaVuSans-41" x="1169.621094"/>
       <use xlink:href="#DejaVuSans-47" x="1236.279297"/>
       <use xlink:href="#DejaVuSans-4e" x="1313.769531"/>
       <use xlink:href="#DejaVuSans-49" x="1388.574219"/>
       <use xlink:href="#DejaVuSans-5f" x="1418.066406"/>
       <use xlink:href="#DejaVuSans-65" x="1468.066406"/>
       <use xlink:href="#DejaVuSans-74" x="1529.589844"/>
       <use xlink:href="#DejaVuSans-61" x="1568.798828"/>
       <use xlink:href="#DejaVuSans-3d" x="1630.078125"/>
       <use xlink:href="#DejaVuSans-38" x="1713.867188"/>
       <use xlink:href="#DejaVuSans-5f" x="1777.490234"/>
       <use xlink:href="#DejaVuSans-30" x="1827.490234"/>
       <use xlink:href="#DejaVuSans-30" x="1891.113281"/>
       <use xlink:href="#DejaVuSans-65" x="1954.736328"/>
       <use xlink:href="#DejaVuSans-2d" x="2016.259766"/>
       <use xlink:href="#DejaVuSans-30" x="2052.34375"/>
       <use xlink:href="#DejaVuSans-31" x="2115.966797"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(134.413125 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- VoronoiFingerprint_std_dev_Voro_area_maximum -->
      <g style="fill: #333333" transform="translate(19.434219 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-61" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-78" x="2109.220703"/>
       <use xlink:href="#DejaVuSans-69" x="2168.400391"/>
       <use xlink:href="#DejaVuSans-6d" x="2196.183594"/>
       <use xlink:href="#DejaVuSans-75" x="2293.595703"/>
       <use xlink:href="#DejaVuSans-6d" x="2356.974609"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(149.186406 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- OxidationStates_minimum_oxidation_state -->
      <g style="fill: #333333" transform="translate(60.569063 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-6d" x="848.779297"/>
       <use xlink:href="#DejaVuSans-69" x="946.191406"/>
       <use xlink:href="#DejaVuSans-6e" x="973.974609"/>
       <use xlink:href="#DejaVuSans-69" x="1037.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1065.136719"/>
       <use xlink:href="#DejaVuSans-75" x="1162.548828"/>
       <use xlink:href="#DejaVuSans-6d" x="1225.927734"/>
       <use xlink:href="#DejaVuSans-5f" x="1323.339844"/>
       <use xlink:href="#DejaVuSans-6f" x="1373.339844"/>
       <use xlink:href="#DejaVuSans-78" x="1431.396484"/>
       <use xlink:href="#DejaVuSans-69" x="1490.576172"/>
       <use xlink:href="#DejaVuSans-64" x="1518.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
       <use xlink:href="#DejaVuSans-69" x="1682.324219"/>
       <use xlink:href="#DejaVuSans-6f" x="1710.107422"/>
       <use xlink:href="#DejaVuSans-6e" x="1771.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1834.667969"/>
       <use xlink:href="#DejaVuSans-73" x="1884.667969"/>
       <use xlink:href="#DejaVuSans-74" x="1936.767578"/>
       <use xlink:href="#DejaVuSans-61" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-74" x="2037.255859"/>
       <use xlink:href="#DejaVuSans-65" x="2076.464844"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- BondOrientationParameter_mean_BOOP_Q_l=2 -->
      <g style="fill: #333333" transform="translate(31.243906 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-6d" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-65" x="1488.859375"/>
       <use xlink:href="#DejaVuSans-61" x="1550.382812"/>
       <use xlink:href="#DejaVuSans-6e" x="1611.662109"/>
       <use xlink:href="#DejaVuSans-5f" x="1675.041016"/>
       <use xlink:href="#DejaVuSans-42" x="1725.041016"/>
       <use xlink:href="#DejaVuSans-4f" x="1791.894531"/>
       <use xlink:href="#DejaVuSans-4f" x="1870.605469"/>
       <use xlink:href="#DejaVuSans-50" x="1949.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.619141"/>
       <use xlink:href="#DejaVuSans-51" x="2059.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="2138.330078"/>
       <use xlink:href="#DejaVuSans-6c" x="2188.330078"/>
       <use xlink:href="#DejaVuSans-3d" x="2216.113281"/>
       <use xlink:href="#DejaVuSans-32" x="2299.902344"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(76.254375 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_maximum_NUnfilled -->
      <g style="fill: #333333" transform="translate(7.2 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-55" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.556641"/>
       <use xlink:href="#DejaVuSans-66" x="2304.935547"/>
       <use xlink:href="#DejaVuSans-69" x="2340.140625"/>
       <use xlink:href="#DejaVuSans-6c" x="2367.923828"/>
       <use xlink:href="#DejaVuSans-6c" x="2395.707031"/>
       <use xlink:href="#DejaVuSans-65" x="2423.490234"/>
       <use xlink:href="#DejaVuSans-64" x="2485.013672"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- BondOrientationParameter_std_dev_BOOP_Q_l=6 -->
      <g style="fill: #333333" transform="translate(17.543125 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-73" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-74" x="1443.546875"/>
       <use xlink:href="#DejaVuSans-64" x="1482.755859"/>
       <use xlink:href="#DejaVuSans-5f" x="1546.232422"/>
       <use xlink:href="#DejaVuSans-64" x="1596.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1659.708984"/>
       <use xlink:href="#DejaVuSans-76" x="1721.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="1780.412109"/>
       <use xlink:href="#DejaVuSans-42" x="1830.412109"/>
       <use xlink:href="#DejaVuSans-4f" x="1897.265625"/>
       <use xlink:href="#DejaVuSans-4f" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-50" x="2054.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="2114.990234"/>
       <use xlink:href="#DejaVuSans-51" x="2164.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2243.701172"/>
       <use xlink:href="#DejaVuSans-6c" x="2293.701172"/>
       <use xlink:href="#DejaVuSans-3d" x="2321.484375"/>
       <use xlink:href="#DejaVuSans-36" x="2405.273438"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(149.186406 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(140.915156 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(54.099531 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(87.481094 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(87.49125 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(26.267344 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(40.136719 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(218.813594 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(97.298125 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(55.714375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 362.007031 638.149 
L 511.327031 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagedd176d6155" transform="scale(1 -1) translate(0 -578.16)" x="366.48" y="-43.2" width="140.4" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature1_fold0 -->
    <g transform="translate(133.892031 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 520.659531 638.149 
L 528.289031 638.149 
L 528.289031 27.789 
L 520.659531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagee0735a3f07" transform="scale(1 -1) translate(0 -609.84)" x="520.56" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(531.789031 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(531.789031 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(566.189969 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p3f034bfd6c">
   <rect x="362.007031" y="27.789" width="149.32" height="610.36"/>
  </clipPath>
 </defs>
</svg>
