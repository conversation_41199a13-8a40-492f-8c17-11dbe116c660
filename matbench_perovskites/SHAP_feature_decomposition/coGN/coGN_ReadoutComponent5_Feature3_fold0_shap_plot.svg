<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="783.366531pt" height="679.5765pt" viewBox="0 0 783.366531 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T08:28:38.528556</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 783.366531 679.5765 
L 783.366531 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 423.139531 638.149 
L 523.643531 638.149 
L 523.643531 27.789 
L 423.139531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 466.56203 638.149 
L 466.56203 27.789 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 423.139531 609.084238 
L 523.643531 609.084238 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 423.139531 580.019476 
L 523.643531 580.019476 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 423.139531 550.954714 
L 523.643531 550.954714 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 423.139531 521.889952 
L 523.643531 521.889952 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 423.139531 492.82519 
L 523.643531 492.82519 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 423.139531 463.760429 
L 523.643531 463.760429 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 423.139531 434.695667 
L 523.643531 434.695667 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 423.139531 405.630905 
L 523.643531 405.630905 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 423.139531 376.566143 
L 523.643531 376.566143 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 423.139531 347.501381 
L 523.643531 347.501381 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 423.139531 318.436619 
L 523.643531 318.436619 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 423.139531 289.371857 
L 523.643531 289.371857 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 423.139531 260.307095 
L 523.643531 260.307095 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 423.139531 231.242333 
L 523.643531 231.242333 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 423.139531 202.177571 
L 523.643531 202.177571 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 423.139531 173.11281 
L 523.643531 173.11281 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 423.139531 144.048048 
L 523.643531 144.048048 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 423.139531 114.983286 
L 523.643531 114.983286 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 423.139531 85.918524 
L 523.643531 85.918524 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 423.139531 56.853762 
L 523.643531 56.853762 
" clip-path="url(#p67dc7fd978)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mf3b95a58b3" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mf3b95a58b3" x="466.56203" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(463.062655 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mf3b95a58b3" x="511.407852" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(507.908477 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.710125 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_31 -->
      <g style="fill: #333333" transform="translate(210.318906 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(148.62375 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(195.545625 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_mode_Electronegativity -->
      <g style="fill: #333333" transform="translate(45.767344 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(148.62375 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_88 -->
      <g style="fill: #333333" transform="translate(210.318906 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(44.835 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(202.047656 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_183 -->
      <g style="fill: #333333" transform="translate(202.047656 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-33" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(87.399844 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(50.999844 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(87.399844 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(148.62375 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- VoronoiFingerprint_std_dev_Voro_area_sum -->
      <g style="fill: #333333" transform="translate(118.392656 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-73" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-75" x="2002.628906"/>
       <use xlink:href="#DejaVuSans-6d" x="2066.007812"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(210.318906 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(101.269219 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(279.946094 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(158.430625 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(116.846875 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 423.139531 638.149 
L 523.643531 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image6e3ec1a890" transform="scale(1 -1) translate(0 -578.16)" x="425.52" y="-43.2" width="95.76" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature3_fold0 -->
    <g transform="translate(170.616531 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.925031 638.149 
L 537.554531 638.149 
L 537.554531 27.789 
L 529.925031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagecb21ae56b4" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.054531 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.054531 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.455469 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p67dc7fd978">
   <rect x="423.139531" y="27.789" width="100.504" height="610.36"/>
  </clipPath>
 </defs>
</svg>
