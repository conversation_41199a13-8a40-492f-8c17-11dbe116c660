<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="755.962469pt" height="679.5765pt" viewBox="0 0 755.962469 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T04:58:08.673514</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 755.962469 679.5765 
L 755.962469 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 367.235469 638.149 
L 512.523469 638.149 
L 512.523469 27.789 
L 367.235469 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 465.901828 638.149 
L 465.901828 27.789 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 367.235469 609.084238 
L 512.523469 609.084238 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 367.235469 580.019476 
L 512.523469 580.019476 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 367.235469 550.954714 
L 512.523469 550.954714 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 367.235469 521.889952 
L 512.523469 521.889952 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 367.235469 492.82519 
L 512.523469 492.82519 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 367.235469 463.760429 
L 512.523469 463.760429 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 367.235469 434.695667 
L 512.523469 434.695667 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 367.235469 405.630905 
L 512.523469 405.630905 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 367.235469 376.566143 
L 512.523469 376.566143 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 367.235469 347.501381 
L 512.523469 347.501381 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 367.235469 318.436619 
L 512.523469 318.436619 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 367.235469 289.371857 
L 512.523469 289.371857 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 367.235469 260.307095 
L 512.523469 260.307095 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 367.235469 231.242333 
L 512.523469 231.242333 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 367.235469 202.177571 
L 512.523469 202.177571 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 367.235469 173.11281 
L 512.523469 173.11281 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 367.235469 144.048048 
L 512.523469 144.048048 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 367.235469 114.983286 
L 512.523469 114.983286 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 367.235469 85.918524 
L 512.523469 85.918524 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 367.235469 56.853762 
L 512.523469 56.853762 
" clip-path="url(#pf6c292d988)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m2690bbede8" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2690bbede8" x="378.230594" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(370.122391 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m2690bbede8" x="422.066211" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(413.958008 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m2690bbede8" x="465.901828" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(462.402453 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_25">
      <g>
       <use xlink:href="#m2690bbede8" x="509.737445" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(506.23807 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_5">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(317.198062 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_6">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(92.719687 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_7">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_maximum -->
      <g style="fill: #333333" transform="translate(29.913437 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-61" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-78" x="2068.84375"/>
       <use xlink:href="#DejaVuSans-69" x="2128.023438"/>
       <use xlink:href="#DejaVuSans-6d" x="2155.806641"/>
       <use xlink:href="#DejaVuSans-75" x="2253.21875"/>
       <use xlink:href="#DejaVuSans-6d" x="2316.597656"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_167 -->
      <g style="fill: #333333" transform="translate(146.143594 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_9">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(76.918594 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_37 -->
      <g style="fill: #333333" transform="translate(154.414844 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_11">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(92.719687 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_12">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(31.495781 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_13">
      <!-- VoronoiFingerprint_std_dev_Voro_vol_maximum -->
      <g style="fill: #333333" transform="translate(34.38625 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-76" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-6f" x="1736.763672"/>
       <use xlink:href="#DejaVuSans-6c" x="1797.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1825.728516"/>
       <use xlink:href="#DejaVuSans-6d" x="1875.728516"/>
       <use xlink:href="#DejaVuSans-61" x="1973.140625"/>
       <use xlink:href="#DejaVuSans-78" x="2034.419922"/>
       <use xlink:href="#DejaVuSans-69" x="2093.599609"/>
       <use xlink:href="#DejaVuSans-6d" x="2121.382812"/>
       <use xlink:href="#DejaVuSans-75" x="2218.794922"/>
       <use xlink:href="#DejaVuSans-6d" x="2282.173828"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_40 -->
      <g style="fill: #333333" transform="translate(154.414844 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_15">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(140.035625 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(31.495781 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(154.414844 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(16.239062 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_19">
      <!-- VoronoiFingerprint_mean_Voro_area_minimum -->
      <g style="fill: #333333" transform="translate(42.174062 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-72" x="1633.492188"/>
       <use xlink:href="#DejaVuSans-65" x="1672.355469"/>
       <use xlink:href="#DejaVuSans-61" x="1733.878906"/>
       <use xlink:href="#DejaVuSans-5f" x="1795.158203"/>
       <use xlink:href="#DejaVuSans-6d" x="1845.158203"/>
       <use xlink:href="#DejaVuSans-69" x="1942.570312"/>
       <use xlink:href="#DejaVuSans-6e" x="1970.353516"/>
       <use xlink:href="#DejaVuSans-69" x="2033.732422"/>
       <use xlink:href="#DejaVuSans-6d" x="2061.515625"/>
       <use xlink:href="#DejaVuSans-75" x="2158.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2222.306641"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_20">
      <!-- OxidationStates_minimum_oxidation_state -->
      <g style="fill: #333333" transform="translate(65.7975 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-6d" x="848.779297"/>
       <use xlink:href="#DejaVuSans-69" x="946.191406"/>
       <use xlink:href="#DejaVuSans-6e" x="973.974609"/>
       <use xlink:href="#DejaVuSans-69" x="1037.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1065.136719"/>
       <use xlink:href="#DejaVuSans-75" x="1162.548828"/>
       <use xlink:href="#DejaVuSans-6d" x="1225.927734"/>
       <use xlink:href="#DejaVuSans-5f" x="1323.339844"/>
       <use xlink:href="#DejaVuSans-6f" x="1373.339844"/>
       <use xlink:href="#DejaVuSans-78" x="1431.396484"/>
       <use xlink:href="#DejaVuSans-69" x="1490.576172"/>
       <use xlink:href="#DejaVuSans-64" x="1518.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
       <use xlink:href="#DejaVuSans-69" x="1682.324219"/>
       <use xlink:href="#DejaVuSans-6f" x="1710.107422"/>
       <use xlink:href="#DejaVuSans-6e" x="1771.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1834.667969"/>
       <use xlink:href="#DejaVuSans-73" x="1884.667969"/>
       <use xlink:href="#DejaVuSans-74" x="1936.767578"/>
       <use xlink:href="#DejaVuSans-61" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-74" x="2037.255859"/>
       <use xlink:href="#DejaVuSans-65" x="2076.464844"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_21">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(92.719687 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_22">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(33.724063 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-69" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-6e" x="2035.347656"/>
       <use xlink:href="#DejaVuSans-69" x="2098.726562"/>
       <use xlink:href="#DejaVuSans-6d" x="2126.509766"/>
       <use xlink:href="#DejaVuSans-75" x="2223.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="2287.300781"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(7.2 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_24">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(221.793437 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_25">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(34.969219 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 367.235469 638.149 
L 512.523469 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image96df4745b2" transform="scale(1 -1) translate(0 -578.16)" x="371.52" y="-43.2" width="136.8" height="578.16"/>
   <g id="text_26">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature36_fold0 -->
    <g transform="translate(130.996469 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 521.603969 638.149 
L 529.233469 638.149 
L 529.233469 27.789 
L 521.603969 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image0a0e5e1ae0" transform="scale(1 -1) translate(0 -609.84)" x="521.28" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- Low -->
      <g transform="translate(532.733469 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_27"/>
     <g id="text_28">
      <!-- High -->
      <g transform="translate(532.733469 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_29">
     <!-- Feature value -->
     <g transform="translate(567.134406 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pf6c292d988">
   <rect x="367.235469" y="27.789" width="145.288" height="610.36"/>
  </clipPath>
 </defs>
</svg>
