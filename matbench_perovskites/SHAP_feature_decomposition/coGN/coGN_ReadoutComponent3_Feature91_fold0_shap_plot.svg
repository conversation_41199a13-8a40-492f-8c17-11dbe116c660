<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T18:10:26.422771</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 488.272089 638.149 
L 488.272089 27.789 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#p3103d1b9a2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m7e6e9449e4" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m7e6e9449e4" x="453.654635" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.25 -->
      <g style="fill: #333333" transform="translate(436.799713 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-32" x="179.199219"/>
       <use xlink:href="#DejaVuSans-35" x="242.822266"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m7e6e9449e4" x="488.272089" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(476.025996 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m7e6e9449e4" x="522.889544" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(510.64345 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_mode_Electronegativity -->
      <g style="fill: #333333" transform="translate(48.726875 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_Column -->
      <g style="fill: #333333" transform="translate(109.2825 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-6c" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-75" x="2032.083984"/>
       <use xlink:href="#DejaVuSans-6d" x="2095.462891"/>
       <use xlink:href="#DejaVuSans-6e" x="2192.875"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(11.010625 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_134 -->
      <g style="fill: #333333" transform="translate(205.007187 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-34" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_70 -->
      <g style="fill: #333333" transform="translate(213.278437 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_122 -->
      <g style="fill: #333333" transform="translate(205.007187 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(90.359375 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_avg_dev_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(52.366875 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-76" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-6f" x="2207.962891"/>
       <use xlink:href="#DejaVuSans-6c" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-75" x="2296.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2457.71875"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.242188"/>
       <use xlink:href="#DejaVuSans-70" x="2569.242188"/>
       <use xlink:href="#DejaVuSans-61" x="2632.71875"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(183.356094 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(83.556719 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- AtomicOrbitals_LUMO_energy -->
      <g style="fill: #333333" transform="translate(210.712969 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-4c" x="788.679688"/>
       <use xlink:href="#DejaVuSans-55" x="839.392578"/>
       <use xlink:href="#DejaVuSans-4d" x="912.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="998.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1077.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1127.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="1189.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1252.478516"/>
       <use xlink:href="#DejaVuSans-72" x="1314.001953"/>
       <use xlink:href="#DejaVuSans-67" x="1353.365234"/>
       <use xlink:href="#DejaVuSans-79" x="1416.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(151.583281 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(66.063594 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=4_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(16.423906 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-34" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(151.583281 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(213.278437 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(213.278437 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(213.278437 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(280.149219 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagece96d01930" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature91_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-39" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image0ff29696fb" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p3103d1b9a2">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
