<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.335719pt" height="679.5765pt" viewBox="0 0 794.335719 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T21:49:44.701567</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.335719 679.5765 
L 794.335719 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 421.136719 638.149 
L 523.152719 638.149 
L 523.152719 27.789 
L 421.136719 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 467.380475 638.149 
L 467.380475 27.789 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 421.136719 609.084238 
L 523.152719 609.084238 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 421.136719 580.019476 
L 523.152719 580.019476 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 421.136719 550.954714 
L 523.152719 550.954714 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 421.136719 521.889952 
L 523.152719 521.889952 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 421.136719 492.82519 
L 523.152719 492.82519 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 421.136719 463.760429 
L 523.152719 463.760429 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 421.136719 434.695667 
L 523.152719 434.695667 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 421.136719 405.630905 
L 523.152719 405.630905 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 421.136719 376.566143 
L 523.152719 376.566143 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 421.136719 347.501381 
L 523.152719 347.501381 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 421.136719 318.436619 
L 523.152719 318.436619 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 421.136719 289.371857 
L 523.152719 289.371857 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 421.136719 260.307095 
L 523.152719 260.307095 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 421.136719 231.242333 
L 523.152719 231.242333 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 421.136719 202.177571 
L 523.152719 202.177571 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 421.136719 173.11281 
L 523.152719 173.11281 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 421.136719 144.048048 
L 523.152719 144.048048 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 421.136719 114.983286 
L 523.152719 114.983286 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 421.136719 85.918524 
L 523.152719 85.918524 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 421.136719 56.853762 
L 523.152719 56.853762 
" clip-path="url(#p8f988ad4d2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m3aef7a54b9" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m3aef7a54b9" x="467.380475" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(463.8811 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m3aef7a54b9" x="515.657627" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(512.158252 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(349.463312 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CrystalNNFingerprint_std_dev_q6_CN_10 -->
      <g style="fill: #333333" transform="translate(133.659531 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-71" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-36" x="1595.044922"/>
       <use xlink:href="#DejaVuSans-5f" x="1658.667969"/>
       <use xlink:href="#DejaVuSans-43" x="1708.667969"/>
       <use xlink:href="#DejaVuSans-4e" x="1778.492188"/>
       <use xlink:href="#DejaVuSans-5f" x="1853.296875"/>
       <use xlink:href="#DejaVuSans-31" x="1903.296875"/>
       <use xlink:href="#DejaVuSans-30" x="1966.919922"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_avg_dev_NpUnfilled -->
      <g style="fill: #333333" transform="translate(69.216094 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-55" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-6e" x="2219.291016"/>
       <use xlink:href="#DejaVuSans-66" x="2282.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2317.875"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2373.441406"/>
       <use xlink:href="#DejaVuSans-65" x="2401.224609"/>
       <use xlink:href="#DejaVuSans-64" x="2462.748047"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- StructuralHeterogeneity_mean_neighbor_distance_variation -->
      <g style="fill: #333333" transform="translate(7.2 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-65" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-61" x="1417.427734"/>
       <use xlink:href="#DejaVuSans-6e" x="1478.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="1542.085938"/>
       <use xlink:href="#DejaVuSans-6e" x="1592.085938"/>
       <use xlink:href="#DejaVuSans-65" x="1655.464844"/>
       <use xlink:href="#DejaVuSans-69" x="1716.988281"/>
       <use xlink:href="#DejaVuSans-67" x="1744.771484"/>
       <use xlink:href="#DejaVuSans-68" x="1808.248047"/>
       <use xlink:href="#DejaVuSans-62" x="1871.626953"/>
       <use xlink:href="#DejaVuSans-6f" x="1935.103516"/>
       <use xlink:href="#DejaVuSans-72" x="1996.285156"/>
       <use xlink:href="#DejaVuSans-5f" x="2037.398438"/>
       <use xlink:href="#DejaVuSans-64" x="2087.398438"/>
       <use xlink:href="#DejaVuSans-69" x="2150.875"/>
       <use xlink:href="#DejaVuSans-73" x="2178.658203"/>
       <use xlink:href="#DejaVuSans-74" x="2230.757812"/>
       <use xlink:href="#DejaVuSans-61" x="2269.966797"/>
       <use xlink:href="#DejaVuSans-6e" x="2331.246094"/>
       <use xlink:href="#DejaVuSans-63" x="2394.625"/>
       <use xlink:href="#DejaVuSans-65" x="2449.605469"/>
       <use xlink:href="#DejaVuSans-5f" x="2511.128906"/>
       <use xlink:href="#DejaVuSans-76" x="2561.128906"/>
       <use xlink:href="#DejaVuSans-61" x="2620.308594"/>
       <use xlink:href="#DejaVuSans-72" x="2681.587891"/>
       <use xlink:href="#DejaVuSans-69" x="2722.701172"/>
       <use xlink:href="#DejaVuSans-61" x="2750.484375"/>
       <use xlink:href="#DejaVuSans-74" x="2811.763672"/>
       <use xlink:href="#DejaVuSans-69" x="2850.972656"/>
       <use xlink:href="#DejaVuSans-6f" x="2878.755859"/>
       <use xlink:href="#DejaVuSans-6e" x="2939.9375"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_70 -->
      <g style="fill: #333333" transform="translate(208.316094 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- GaussianSymmFunc_std_dev_G2_0_05 -->
      <g style="fill: #333333" transform="translate(146.610781 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-35" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(99.942813 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_31 -->
      <g style="fill: #333333" transform="translate(208.316094 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_avg_dev_Electronegativity -->
      <g style="fill: #333333" transform="translate(26.273438 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-45" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6c" x="2071"/>
       <use xlink:href="#DejaVuSans-65" x="2098.783203"/>
       <use xlink:href="#DejaVuSans-63" x="2160.306641"/>
       <use xlink:href="#DejaVuSans-74" x="2215.287109"/>
       <use xlink:href="#DejaVuSans-72" x="2254.496094"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.359375"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.541016"/>
       <use xlink:href="#DejaVuSans-65" x="2417.919922"/>
       <use xlink:href="#DejaVuSans-67" x="2479.443359"/>
       <use xlink:href="#DejaVuSans-61" x="2542.919922"/>
       <use xlink:href="#DejaVuSans-74" x="2604.199219"/>
       <use xlink:href="#DejaVuSans-69" x="2643.408203"/>
       <use xlink:href="#DejaVuSans-76" x="2671.191406"/>
       <use xlink:href="#DejaVuSans-69" x="2730.371094"/>
       <use xlink:href="#DejaVuSans-74" x="2758.154297"/>
       <use xlink:href="#DejaVuSans-79" x="2797.363281"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(69.504531 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- GaussianSymmFunc_mean_G2_80_0 -->
      <g style="fill: #333333" transform="translate(160.311562 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-6d" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-65" x="1157.439453"/>
       <use xlink:href="#DejaVuSans-61" x="1218.962891"/>
       <use xlink:href="#DejaVuSans-6e" x="1280.242188"/>
       <use xlink:href="#DejaVuSans-5f" x="1343.621094"/>
       <use xlink:href="#DejaVuSans-47" x="1393.621094"/>
       <use xlink:href="#DejaVuSans-32" x="1471.111328"/>
       <use xlink:href="#DejaVuSans-5f" x="1534.734375"/>
       <use xlink:href="#DejaVuSans-38" x="1584.734375"/>
       <use xlink:href="#DejaVuSans-30" x="1648.357422"/>
       <use xlink:href="#DejaVuSans-5f" x="1711.980469"/>
       <use xlink:href="#DejaVuSans-30" x="1761.980469"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(200.044844 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(85.277187 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(200.044844 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(208.316094 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- OPSiteFingerprint_std_dev_q2_CN_10 -->
      <g style="fill: #333333" transform="translate(155.641719 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-71" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-32" x="1425.953125"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-43" x="1539.576172"/>
       <use xlink:href="#DejaVuSans-4e" x="1609.400391"/>
       <use xlink:href="#DejaVuSans-5f" x="1684.205078"/>
       <use xlink:href="#DejaVuSans-31" x="1734.205078"/>
       <use xlink:href="#DejaVuSans-30" x="1797.828125"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(85.397031 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(99.266406 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(277.943281 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(156.427812 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(114.844063 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 421.136719 638.149 
L 523.152719 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagedc3ff0398f" transform="scale(1 -1) translate(0 -578.16)" x="423.36" y="-43.2" width="97.2" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature114_fold0 -->
    <g transform="translate(157.153719 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-34" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.528719 638.149 
L 537.158219 638.149 
L 537.158219 27.789 
L 529.528719 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image0853277318" transform="scale(1 -1) translate(0 -609.84)" x="529.2" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.658219 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.658219 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.059156 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p8f988ad4d2">
   <rect x="421.136719" y="27.789" width="102.016" height="610.36"/>
  </clipPath>
 </defs>
</svg>
