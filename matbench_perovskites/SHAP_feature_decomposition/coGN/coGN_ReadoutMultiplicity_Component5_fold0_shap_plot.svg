<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="793.274031pt" height="679.5765pt" viewBox="0 0 793.274031 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T19:06:18.689507</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 793.274031 679.5765 
L 793.274031 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 423.139531 638.149 
L 523.643531 638.149 
L 523.643531 27.789 
L 423.139531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 464.113685 638.149 
L 464.113685 27.789 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 423.139531 609.084238 
L 523.643531 609.084238 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 423.139531 580.019476 
L 523.643531 580.019476 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 423.139531 550.954714 
L 523.643531 550.954714 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 423.139531 521.889952 
L 523.643531 521.889952 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 423.139531 492.82519 
L 523.643531 492.82519 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 423.139531 463.760429 
L 523.643531 463.760429 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 423.139531 434.695667 
L 523.643531 434.695667 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 423.139531 405.630905 
L 523.643531 405.630905 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 423.139531 376.566143 
L 523.643531 376.566143 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 423.139531 347.501381 
L 523.643531 347.501381 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 423.139531 318.436619 
L 523.643531 318.436619 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 423.139531 289.371857 
L 523.643531 289.371857 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 423.139531 260.307095 
L 523.643531 260.307095 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 423.139531 231.242333 
L 523.643531 231.242333 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 423.139531 202.177571 
L 523.643531 202.177571 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 423.139531 173.11281 
L 523.643531 173.11281 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 423.139531 144.048048 
L 523.643531 144.048048 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 423.139531 114.983286 
L 523.643531 114.983286 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 423.139531 85.918524 
L 523.643531 85.918524 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 423.139531 56.853762 
L 523.643531 56.853762 
" clip-path="url(#p97a2ecf5b9)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="meab9147f00" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#meab9147f00" x="464.113685" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(455.366966 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#meab9147f00" x="521.520051" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(512.773332 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.710125 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- XRDPowderPattern_xrd_98 -->
      <g style="fill: #333333" transform="translate(226.767969 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-39" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-38" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(114.97 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-4c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1480.160156"/>
       <use xlink:href="#DejaVuSans-73" x="1516.244141"/>
       <use xlink:href="#DejaVuSans-68" x="1568.34375"/>
       <use xlink:href="#DejaVuSans-61" x="1631.722656"/>
       <use xlink:href="#DejaVuSans-70" x="1693.001953"/>
       <use xlink:href="#DejaVuSans-65" x="1756.478516"/>
       <use xlink:href="#DejaVuSans-64" x="1818.001953"/>
       <use xlink:href="#DejaVuSans-5f" x="1881.478516"/>
       <use xlink:href="#DejaVuSans-43" x="1931.478516"/>
       <use xlink:href="#DejaVuSans-4e" x="2001.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="2076.107422"/>
       <use xlink:href="#DejaVuSans-32" x="2126.107422"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_76 -->
      <g style="fill: #333333" transform="translate(210.318906 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_110 -->
      <g style="fill: #333333" transform="translate(202.047656 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- StructuralHeterogeneity_mean_neighbor_distance_variation -->
      <g style="fill: #333333" transform="translate(9.202812 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-65" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-61" x="1417.427734"/>
       <use xlink:href="#DejaVuSans-6e" x="1478.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="1542.085938"/>
       <use xlink:href="#DejaVuSans-6e" x="1592.085938"/>
       <use xlink:href="#DejaVuSans-65" x="1655.464844"/>
       <use xlink:href="#DejaVuSans-69" x="1716.988281"/>
       <use xlink:href="#DejaVuSans-67" x="1744.771484"/>
       <use xlink:href="#DejaVuSans-68" x="1808.248047"/>
       <use xlink:href="#DejaVuSans-62" x="1871.626953"/>
       <use xlink:href="#DejaVuSans-6f" x="1935.103516"/>
       <use xlink:href="#DejaVuSans-72" x="1996.285156"/>
       <use xlink:href="#DejaVuSans-5f" x="2037.398438"/>
       <use xlink:href="#DejaVuSans-64" x="2087.398438"/>
       <use xlink:href="#DejaVuSans-69" x="2150.875"/>
       <use xlink:href="#DejaVuSans-73" x="2178.658203"/>
       <use xlink:href="#DejaVuSans-74" x="2230.757812"/>
       <use xlink:href="#DejaVuSans-61" x="2269.966797"/>
       <use xlink:href="#DejaVuSans-6e" x="2331.246094"/>
       <use xlink:href="#DejaVuSans-63" x="2394.625"/>
       <use xlink:href="#DejaVuSans-65" x="2449.605469"/>
       <use xlink:href="#DejaVuSans-5f" x="2511.128906"/>
       <use xlink:href="#DejaVuSans-76" x="2561.128906"/>
       <use xlink:href="#DejaVuSans-61" x="2620.308594"/>
       <use xlink:href="#DejaVuSans-72" x="2681.587891"/>
       <use xlink:href="#DejaVuSans-69" x="2722.701172"/>
       <use xlink:href="#DejaVuSans-61" x="2750.484375"/>
       <use xlink:href="#DejaVuSans-74" x="2811.763672"/>
       <use xlink:href="#DejaVuSans-69" x="2850.972656"/>
       <use xlink:href="#DejaVuSans-6f" x="2878.755859"/>
       <use xlink:href="#DejaVuSans-6e" x="2939.9375"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- AtomicPackingEfficiency_mean_simul__packing_efficiency -->
      <g style="fill: #333333" transform="translate(23.817656 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-6d" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-65" x="1362.451172"/>
       <use xlink:href="#DejaVuSans-61" x="1423.974609"/>
       <use xlink:href="#DejaVuSans-6e" x="1485.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.632812"/>
       <use xlink:href="#DejaVuSans-73" x="1598.632812"/>
       <use xlink:href="#DejaVuSans-69" x="1650.732422"/>
       <use xlink:href="#DejaVuSans-6d" x="1678.515625"/>
       <use xlink:href="#DejaVuSans-75" x="1775.927734"/>
       <use xlink:href="#DejaVuSans-6c" x="1839.306641"/>
       <use xlink:href="#DejaVuSans-5f" x="1867.089844"/>
       <use xlink:href="#DejaVuSans-5f" x="1917.089844"/>
       <use xlink:href="#DejaVuSans-70" x="1967.089844"/>
       <use xlink:href="#DejaVuSans-61" x="2030.566406"/>
       <use xlink:href="#DejaVuSans-63" x="2091.845703"/>
       <use xlink:href="#DejaVuSans-6b" x="2146.826172"/>
       <use xlink:href="#DejaVuSans-69" x="2204.736328"/>
       <use xlink:href="#DejaVuSans-6e" x="2232.519531"/>
       <use xlink:href="#DejaVuSans-67" x="2295.898438"/>
       <use xlink:href="#DejaVuSans-5f" x="2359.375"/>
       <use xlink:href="#DejaVuSans-65" x="2409.375"/>
       <use xlink:href="#DejaVuSans-66" x="2470.898438"/>
       <use xlink:href="#DejaVuSans-66" x="2506.103516"/>
       <use xlink:href="#DejaVuSans-69" x="2541.308594"/>
       <use xlink:href="#DejaVuSans-63" x="2569.091797"/>
       <use xlink:href="#DejaVuSans-69" x="2624.072266"/>
       <use xlink:href="#DejaVuSans-65" x="2651.855469"/>
       <use xlink:href="#DejaVuSans-6e" x="2713.378906"/>
       <use xlink:href="#DejaVuSans-63" x="2776.757812"/>
       <use xlink:href="#DejaVuSans-79" x="2831.738281"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- BondFractions_Ca_-_N_bond_frac_ -->
      <g style="fill: #333333" transform="translate(179.413437 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-46" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="306.910156"/>
       <use xlink:href="#DejaVuSans-61" x="348.023438"/>
       <use xlink:href="#DejaVuSans-63" x="409.302734"/>
       <use xlink:href="#DejaVuSans-74" x="464.283203"/>
       <use xlink:href="#DejaVuSans-69" x="503.492188"/>
       <use xlink:href="#DejaVuSans-6f" x="531.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="592.457031"/>
       <use xlink:href="#DejaVuSans-73" x="655.835938"/>
       <use xlink:href="#DejaVuSans-5f" x="707.935547"/>
       <use xlink:href="#DejaVuSans-43" x="757.935547"/>
       <use xlink:href="#DejaVuSans-61" x="827.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="889.039062"/>
       <use xlink:href="#DejaVuSans-2d" x="939.039062"/>
       <use xlink:href="#DejaVuSans-5f" x="975.123047"/>
       <use xlink:href="#DejaVuSans-4e" x="1025.123047"/>
       <use xlink:href="#DejaVuSans-5f" x="1099.927734"/>
       <use xlink:href="#DejaVuSans-62" x="1149.927734"/>
       <use xlink:href="#DejaVuSans-6f" x="1213.404297"/>
       <use xlink:href="#DejaVuSans-6e" x="1274.585938"/>
       <use xlink:href="#DejaVuSans-64" x="1337.964844"/>
       <use xlink:href="#DejaVuSans-5f" x="1401.441406"/>
       <use xlink:href="#DejaVuSans-66" x="1451.441406"/>
       <use xlink:href="#DejaVuSans-72" x="1486.646484"/>
       <use xlink:href="#DejaVuSans-61" x="1527.759766"/>
       <use xlink:href="#DejaVuSans-63" x="1589.039062"/>
       <use xlink:href="#DejaVuSans-5f" x="1644.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(195.545625 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_avg_dev_NpUnfilled -->
      <g style="fill: #333333" transform="translate(71.218906 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-55" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-6e" x="2219.291016"/>
       <use xlink:href="#DejaVuSans-66" x="2282.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2317.875"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2373.441406"/>
       <use xlink:href="#DejaVuSans-65" x="2401.224609"/>
       <use xlink:href="#DejaVuSans-64" x="2462.748047"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_31 -->
      <g style="fill: #333333" transform="translate(210.318906 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(277.6975 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(202.047656 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(63.104062 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(210.318906 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(87.399844 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(101.269219 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(279.946094 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(158.430625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(116.846875 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 423.139531 638.149 
L 523.643531 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image617d479305" transform="scale(1 -1) translate(0 -578.16)" x="425.52" y="-43.2" width="95.76" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutMultiplicity_Component5_fold0 -->
    <g transform="translate(160.709031 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-4d" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-75" x="1803.263672"/>
     <use xlink:href="#DejaVuSans-6c" x="1866.642578"/>
     <use xlink:href="#DejaVuSans-74" x="1894.425781"/>
     <use xlink:href="#DejaVuSans-69" x="1933.634766"/>
     <use xlink:href="#DejaVuSans-70" x="1961.417969"/>
     <use xlink:href="#DejaVuSans-6c" x="2024.894531"/>
     <use xlink:href="#DejaVuSans-69" x="2052.677734"/>
     <use xlink:href="#DejaVuSans-63" x="2080.460938"/>
     <use xlink:href="#DejaVuSans-69" x="2135.441406"/>
     <use xlink:href="#DejaVuSans-74" x="2163.224609"/>
     <use xlink:href="#DejaVuSans-79" x="2202.433594"/>
     <use xlink:href="#DejaVuSans-5f" x="2261.613281"/>
     <use xlink:href="#DejaVuSans-43" x="2311.613281"/>
     <use xlink:href="#DejaVuSans-6f" x="2381.4375"/>
     <use xlink:href="#DejaVuSans-6d" x="2442.619141"/>
     <use xlink:href="#DejaVuSans-70" x="2540.03125"/>
     <use xlink:href="#DejaVuSans-6f" x="2603.507812"/>
     <use xlink:href="#DejaVuSans-6e" x="2664.689453"/>
     <use xlink:href="#DejaVuSans-65" x="2728.068359"/>
     <use xlink:href="#DejaVuSans-6e" x="2789.591797"/>
     <use xlink:href="#DejaVuSans-74" x="2852.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2892.179688"/>
     <use xlink:href="#DejaVuSans-5f" x="2955.802734"/>
     <use xlink:href="#DejaVuSans-66" x="3005.802734"/>
     <use xlink:href="#DejaVuSans-6f" x="3041.007812"/>
     <use xlink:href="#DejaVuSans-6c" x="3102.189453"/>
     <use xlink:href="#DejaVuSans-64" x="3129.972656"/>
     <use xlink:href="#DejaVuSans-30" x="3193.449219"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.925031 638.149 
L 537.554531 638.149 
L 537.554531 27.789 
L 529.925031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image85c0f5fca0" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.054531 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.054531 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.455469 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p97a2ecf5b9">
   <rect x="423.139531" y="27.789" width="100.504" height="610.36"/>
  </clipPath>
 </defs>
</svg>
