<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="786.018156pt" height="679.5765pt" viewBox="0 0 786.018156 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T07:36:18.928548</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 786.018156 679.5765 
L 786.018156 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 416.875156 638.149 
L 522.995156 638.149 
L 522.995156 27.789 
L 416.875156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 462.637631 638.149 
L 462.637631 27.789 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 416.875156 609.084238 
L 522.995156 609.084238 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 416.875156 580.019476 
L 522.995156 580.019476 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 416.875156 550.954714 
L 522.995156 550.954714 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 416.875156 521.889952 
L 522.995156 521.889952 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 416.875156 492.82519 
L 522.995156 492.82519 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 416.875156 463.760429 
L 522.995156 463.760429 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 416.875156 434.695667 
L 522.995156 434.695667 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 416.875156 405.630905 
L 522.995156 405.630905 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 416.875156 376.566143 
L 522.995156 376.566143 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 416.875156 347.501381 
L 522.995156 347.501381 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 416.875156 318.436619 
L 522.995156 318.436619 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 416.875156 289.371857 
L 522.995156 289.371857 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 416.875156 260.307095 
L 522.995156 260.307095 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 416.875156 231.242333 
L 522.995156 231.242333 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 416.875156 202.177571 
L 522.995156 202.177571 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 416.875156 173.11281 
L 522.995156 173.11281 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 416.875156 144.048048 
L 522.995156 144.048048 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 416.875156 114.983286 
L 522.995156 114.983286 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 416.875156 85.918524 
L 522.995156 85.918524 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 416.875156 56.853762 
L 522.995156 56.853762 
" clip-path="url(#p558b5c6d9c)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m2dcc2bb647" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2dcc2bb647" x="462.637631" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(459.138256 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m2dcc2bb647" x="521.964491" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(518.465116 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(347.25375 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(81.135469 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(95.68125 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(142.359375 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(204.054531 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(142.349219 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(53.815156 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(65.242969 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_avg_dev_NpValence -->
      <g style="fill: #333333" transform="translate(63.5225 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-56" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-61" x="2206.755859"/>
       <use xlink:href="#DejaVuSans-6c" x="2268.035156"/>
       <use xlink:href="#DejaVuSans-65" x="2295.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="2357.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2420.720703"/>
       <use xlink:href="#DejaVuSans-65" x="2475.701172"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_maximum_NUnfilled -->
      <g style="fill: #333333" transform="translate(62.068125 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-55" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.556641"/>
       <use xlink:href="#DejaVuSans-66" x="2304.935547"/>
       <use xlink:href="#DejaVuSans-69" x="2340.140625"/>
       <use xlink:href="#DejaVuSans-6c" x="2367.923828"/>
       <use xlink:href="#DejaVuSans-6c" x="2395.707031"/>
       <use xlink:href="#DejaVuSans-65" x="2423.490234"/>
       <use xlink:href="#DejaVuSans-64" x="2485.013672"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=0_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(7.2 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-30" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CrystalNNFingerprint_std_dev_pentagonal_pyramidal_CN_6 -->
      <g style="fill: #333333" transform="translate(8.751875 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-70" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1595.044922"/>
       <use xlink:href="#DejaVuSans-6e" x="1656.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1719.947266"/>
       <use xlink:href="#DejaVuSans-61" x="1759.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1820.435547"/>
       <use xlink:href="#DejaVuSans-6f" x="1883.912109"/>
       <use xlink:href="#DejaVuSans-6e" x="1945.09375"/>
       <use xlink:href="#DejaVuSans-61" x="2008.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="2069.751953"/>
       <use xlink:href="#DejaVuSans-5f" x="2097.535156"/>
       <use xlink:href="#DejaVuSans-70" x="2147.535156"/>
       <use xlink:href="#DejaVuSans-79" x="2211.011719"/>
       <use xlink:href="#DejaVuSans-72" x="2270.191406"/>
       <use xlink:href="#DejaVuSans-61" x="2311.304688"/>
       <use xlink:href="#DejaVuSans-6d" x="2372.583984"/>
       <use xlink:href="#DejaVuSans-69" x="2469.996094"/>
       <use xlink:href="#DejaVuSans-64" x="2497.779297"/>
       <use xlink:href="#DejaVuSans-61" x="2561.255859"/>
       <use xlink:href="#DejaVuSans-6c" x="2622.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="2650.318359"/>
       <use xlink:href="#DejaVuSans-43" x="2700.318359"/>
       <use xlink:href="#DejaVuSans-4e" x="2770.142578"/>
       <use xlink:href="#DejaVuSans-5f" x="2844.947266"/>
       <use xlink:href="#DejaVuSans-36" x="2894.947266"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(83.36375 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-69" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-6e" x="2035.347656"/>
       <use xlink:href="#DejaVuSans-69" x="2098.726562"/>
       <use xlink:href="#DejaVuSans-6d" x="2126.509766"/>
       <use xlink:href="#DejaVuSans-75" x="2223.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="2287.300781"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(195.783281 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(204.054531 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(81.135469 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(96.325156 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-38" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-30" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-30" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2d" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2157.714844"/>
       <use xlink:href="#DejaVuSans-31" x="2221.337891"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(95.004844 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(273.681719 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(152.16625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(110.5825 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 416.875156 638.149 
L 522.995156 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image2d67c41241" transform="scale(1 -1) translate(0 -578.16)" x="419.04" y="-43.2" width="101.52" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature35_fold0 -->
    <g transform="translate(161.052156 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.627656 638.149 
L 537.257156 638.149 
L 537.257156 27.789 
L 529.627656 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image1a3686db20" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.757156 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.757156 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.158094 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p558b5c6d9c">
   <rect x="416.875156" y="27.789" width="106.12" height="610.36"/>
  </clipPath>
 </defs>
</svg>
