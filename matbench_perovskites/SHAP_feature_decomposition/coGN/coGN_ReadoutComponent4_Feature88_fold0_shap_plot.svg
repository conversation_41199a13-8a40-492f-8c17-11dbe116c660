<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="755.962469pt" height="679.5765pt" viewBox="0 0 755.962469 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T08:27:46.982020</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 755.962469 679.5765 
L 755.962469 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 367.235469 638.149 
L 512.523469 638.149 
L 512.523469 27.789 
L 367.235469 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 466.811168 638.149 
L 466.811168 27.789 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 367.235469 609.084238 
L 512.523469 609.084238 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 367.235469 580.019476 
L 512.523469 580.019476 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 367.235469 550.954714 
L 512.523469 550.954714 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 367.235469 521.889952 
L 512.523469 521.889952 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 367.235469 492.82519 
L 512.523469 492.82519 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 367.235469 463.760429 
L 512.523469 463.760429 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 367.235469 434.695667 
L 512.523469 434.695667 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 367.235469 405.630905 
L 512.523469 405.630905 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 367.235469 376.566143 
L 512.523469 376.566143 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 367.235469 347.501381 
L 512.523469 347.501381 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 367.235469 318.436619 
L 512.523469 318.436619 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 367.235469 289.371857 
L 512.523469 289.371857 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 367.235469 260.307095 
L 512.523469 260.307095 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 367.235469 231.242333 
L 512.523469 231.242333 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 367.235469 202.177571 
L 512.523469 202.177571 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 367.235469 173.11281 
L 512.523469 173.11281 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 367.235469 144.048048 
L 512.523469 144.048048 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 367.235469 114.983286 
L 512.523469 114.983286 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 367.235469 85.918524 
L 512.523469 85.918524 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 367.235469 56.853762 
L 512.523469 56.853762 
" clip-path="url(#pd39aa9d629)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m73e75f7319" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m73e75f7319" x="401.161124" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −5 -->
      <g style="fill: #333333" transform="translate(393.052921 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-35" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m73e75f7319" x="466.811168" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(463.311793 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(317.198062 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- XRDPowderPattern_xrd_32 -->
      <g style="fill: #333333" transform="translate(170.863906 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(31.495781 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(7.2 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(44.235781 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(24.693125 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(88.825781 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(16.239062 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- DensityFeatures_density -->
      <g style="fill: #333333" transform="translate(184.262031 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-64" x="860.072266"/>
       <use xlink:href="#DejaVuSans-65" x="923.548828"/>
       <use xlink:href="#DejaVuSans-6e" x="985.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1048.451172"/>
       <use xlink:href="#DejaVuSans-69" x="1100.550781"/>
       <use xlink:href="#DejaVuSans-74" x="1128.333984"/>
       <use xlink:href="#DejaVuSans-79" x="1167.542969"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(31.495781 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(154.414844 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- OxidationStates_minimum_oxidation_state -->
      <g style="fill: #333333" transform="translate(65.7975 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-6d" x="848.779297"/>
       <use xlink:href="#DejaVuSans-69" x="946.191406"/>
       <use xlink:href="#DejaVuSans-6e" x="973.974609"/>
       <use xlink:href="#DejaVuSans-69" x="1037.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1065.136719"/>
       <use xlink:href="#DejaVuSans-75" x="1162.548828"/>
       <use xlink:href="#DejaVuSans-6d" x="1225.927734"/>
       <use xlink:href="#DejaVuSans-5f" x="1323.339844"/>
       <use xlink:href="#DejaVuSans-6f" x="1373.339844"/>
       <use xlink:href="#DejaVuSans-78" x="1431.396484"/>
       <use xlink:href="#DejaVuSans-69" x="1490.576172"/>
       <use xlink:href="#DejaVuSans-64" x="1518.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
       <use xlink:href="#DejaVuSans-69" x="1682.324219"/>
       <use xlink:href="#DejaVuSans-6f" x="1710.107422"/>
       <use xlink:href="#DejaVuSans-6e" x="1771.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1834.667969"/>
       <use xlink:href="#DejaVuSans-73" x="1884.667969"/>
       <use xlink:href="#DejaVuSans-74" x="1936.767578"/>
       <use xlink:href="#DejaVuSans-61" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-74" x="2037.255859"/>
       <use xlink:href="#DejaVuSans-65" x="2076.464844"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_avg_dev_MeltingT -->
      <g style="fill: #333333" transform="translate(26.742656 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-74" x="2183.402344"/>
       <use xlink:href="#DejaVuSans-69" x="2222.611328"/>
       <use xlink:href="#DejaVuSans-6e" x="2250.394531"/>
       <use xlink:href="#DejaVuSans-67" x="2313.773438"/>
       <use xlink:href="#DejaVuSans-54" x="2377.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_mode_NUnfilled -->
      <g style="fill: #333333" transform="translate(41.058906 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(31.495781 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(92.719687 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(140.035625 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- Stoichiometry_2-norm -->
      <g style="fill: #333333" transform="translate(199.636562 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-32" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(221.793437 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_mode_MeltingT -->
      <g style="fill: #333333" transform="translate(44.23375 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(34.969219 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 367.235469 638.149 
L 512.523469 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image637a230c80" transform="scale(1 -1) translate(0 -578.16)" x="371.52" y="-43.2" width="136.8" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature88_fold0 -->
    <g transform="translate(130.996469 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-38" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-38" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 521.603969 638.149 
L 529.233469 638.149 
L 529.233469 27.789 
L 521.603969 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image6668248190" transform="scale(1 -1) translate(0 -609.84)" x="521.28" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(532.733469 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(532.733469 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(567.134406 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pd39aa9d629">
   <rect x="367.235469" y="27.789" width="145.288" height="610.36"/>
  </clipPath>
 </defs>
</svg>
