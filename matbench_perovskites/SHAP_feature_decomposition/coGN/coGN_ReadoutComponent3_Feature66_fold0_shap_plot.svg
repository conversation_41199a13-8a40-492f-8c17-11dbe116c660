<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:11:33.633381</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 486.137927 638.149 
L 486.137927 27.789 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#pbed94dedd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m6dc79ad08b" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m6dc79ad08b" x="454.13388" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.25 -->
      <g style="fill: #333333" transform="translate(437.278959 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-32" x="179.199219"/>
       <use xlink:href="#DejaVuSans-35" x="242.822266"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m6dc79ad08b" x="486.137927" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(473.891833 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m6dc79ad08b" x="518.141973" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(505.89588 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_mean_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(66.049375 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-76" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2073.441406"/>
       <use xlink:href="#DejaVuSans-6c" x="2134.623047"/>
       <use xlink:href="#DejaVuSans-75" x="2162.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="2225.785156"/>
       <use xlink:href="#DejaVuSans-65" x="2323.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="2384.720703"/>
       <use xlink:href="#DejaVuSans-70" x="2434.720703"/>
       <use xlink:href="#DejaVuSans-61" x="2498.197266"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(143.87875 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(86.54875 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(179.545469 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_wt_CN_5 -->
      <g style="fill: #333333" transform="translate(157.579531 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-35" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(86.54875 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_21 -->
      <g style="fill: #333333" transform="translate(209.467812 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(70.65625 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_maximum_CovalentRadius -->
      <g style="fill: #333333" transform="translate(25.402031 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-43" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-6f" x="2163.382812"/>
       <use xlink:href="#DejaVuSans-76" x="2224.564453"/>
       <use xlink:href="#DejaVuSans-61" x="2283.744141"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.023438"/>
       <use xlink:href="#DejaVuSans-65" x="2372.806641"/>
       <use xlink:href="#DejaVuSans-6e" x="2434.330078"/>
       <use xlink:href="#DejaVuSans-74" x="2497.708984"/>
       <use xlink:href="#DejaVuSans-52" x="2536.917969"/>
       <use xlink:href="#DejaVuSans-61" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-64" x="2665.429688"/>
       <use xlink:href="#DejaVuSans-69" x="2728.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2756.689453"/>
       <use xlink:href="#DejaVuSans-73" x="2820.068359"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_4 -->
      <g style="fill: #333333" transform="translate(143.87875 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-34" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(96.113906 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(43.983906 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=5_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(12.613281 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-35" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(157.579531 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_mode_MeltingT -->
      <g style="fill: #333333" transform="translate(99.286719 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(98.354375 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_mode_NUnfilled -->
      <g style="fill: #333333" transform="translate(96.111875 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(80.65 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image4afec86ad0" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature66_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image3f4be727c9" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pbed94dedd2">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
