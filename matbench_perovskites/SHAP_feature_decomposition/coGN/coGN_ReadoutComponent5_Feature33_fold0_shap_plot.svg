<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="776.858281pt" height="679.5765pt" viewBox="0 0 776.858281 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T07:15:47.660873</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 776.858281 679.5765 
L 776.858281 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 402.063281 638.149 
L 519.487281 638.149 
L 519.487281 27.789 
L 402.063281 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 444.907913 638.149 
L 444.907913 27.789 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 402.063281 609.084238 
L 519.487281 609.084238 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 402.063281 580.019476 
L 519.487281 580.019476 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 402.063281 550.954714 
L 519.487281 550.954714 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 402.063281 521.889952 
L 519.487281 521.889952 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 402.063281 492.82519 
L 519.487281 492.82519 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 402.063281 463.760429 
L 519.487281 463.760429 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 402.063281 434.695667 
L 519.487281 434.695667 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 402.063281 405.630905 
L 519.487281 405.630905 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 402.063281 376.566143 
L 519.487281 376.566143 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 402.063281 347.501381 
L 519.487281 347.501381 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 402.063281 318.436619 
L 519.487281 318.436619 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 402.063281 289.371857 
L 519.487281 289.371857 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 402.063281 260.307095 
L 519.487281 260.307095 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 402.063281 231.242333 
L 519.487281 231.242333 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 402.063281 202.177571 
L 519.487281 202.177571 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 402.063281 173.11281 
L 519.487281 173.11281 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 402.063281 144.048048 
L 519.487281 144.048048 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 402.063281 114.983286 
L 519.487281 114.983286 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 402.063281 85.918524 
L 519.487281 85.918524 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 402.063281 56.853762 
L 519.487281 56.853762 
" clip-path="url(#pdfb19f9f77)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m051242cbcc" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m051242cbcc" x="444.907913" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(441.408538 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m051242cbcc" x="507.674748" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(504.175373 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(338.093875 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_104 -->
      <g style="fill: #333333" transform="translate(180.971406 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-34" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(66.323594 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(159.320312 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(93.89375 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-4c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1480.160156"/>
       <use xlink:href="#DejaVuSans-73" x="1516.244141"/>
       <use xlink:href="#DejaVuSans-68" x="1568.34375"/>
       <use xlink:href="#DejaVuSans-61" x="1631.722656"/>
       <use xlink:href="#DejaVuSans-70" x="1693.001953"/>
       <use xlink:href="#DejaVuSans-65" x="1756.478516"/>
       <use xlink:href="#DejaVuSans-64" x="1818.001953"/>
       <use xlink:href="#DejaVuSans-5f" x="1881.478516"/>
       <use xlink:href="#DejaVuSans-43" x="1931.478516"/>
       <use xlink:href="#DejaVuSans-4e" x="2001.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="2076.107422"/>
       <use xlink:href="#DejaVuSans-32" x="2126.107422"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(127.5475 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(174.469375 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(189.242656 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_avg_dev_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-45" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6c" x="2071"/>
       <use xlink:href="#DejaVuSans-65" x="2098.783203"/>
       <use xlink:href="#DejaVuSans-63" x="2160.306641"/>
       <use xlink:href="#DejaVuSans-74" x="2215.287109"/>
       <use xlink:href="#DejaVuSans-72" x="2254.496094"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.359375"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.541016"/>
       <use xlink:href="#DejaVuSans-65" x="2417.919922"/>
       <use xlink:href="#DejaVuSans-67" x="2479.443359"/>
       <use xlink:href="#DejaVuSans-61" x="2542.919922"/>
       <use xlink:href="#DejaVuSans-74" x="2604.199219"/>
       <use xlink:href="#DejaVuSans-69" x="2643.408203"/>
       <use xlink:href="#DejaVuSans-76" x="2671.191406"/>
       <use xlink:href="#DejaVuSans-69" x="2730.371094"/>
       <use xlink:href="#DejaVuSans-74" x="2758.154297"/>
       <use xlink:href="#DejaVuSans-79" x="2797.363281"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(127.5475 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(59.520938 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_maximum_GSbandgap -->
      <g style="fill: #333333" transform="translate(30.888437 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-62" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-61" x="2298.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="2359.28125"/>
       <use xlink:href="#DejaVuSans-64" x="2422.660156"/>
       <use xlink:href="#DejaVuSans-67" x="2486.136719"/>
       <use xlink:href="#DejaVuSans-61" x="2549.613281"/>
       <use xlink:href="#DejaVuSans-70" x="2610.892578"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(66.323594 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(189.242656 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(180.971406 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(111.746406 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(258.093906 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(80.192969 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(258.869844 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(95.770625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(137.354375 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 402.063281 638.149 
L 519.487281 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagea1bbb4569b" transform="scale(1 -1) translate(0 -578.16)" x="405.36" y="-43.2" width="110.88" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature33_fold0 -->
    <g transform="translate(151.892281 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-33" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 526.826281 638.149 
L 534.455781 638.149 
L 534.455781 27.789 
L 526.826281 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image2014c5dc27" transform="scale(1 -1) translate(0 -609.84)" x="527.04" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(537.955781 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(537.955781 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(572.356719 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pdfb19f9f77">
   <rect x="402.063281" y="27.789" width="117.424" height="610.36"/>
  </clipPath>
 </defs>
</svg>
