<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T18:25:21.315014</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 498.255569 638.149 
L 498.255569 27.789 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#pdd2e2c4ee2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mb6bdd5de27" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb6bdd5de27" x="429.354052" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(415.998505 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mb6bdd5de27" x="498.255569" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(489.50885 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_range_CovalentRadius -->
      <g style="fill: #333333" transform="translate(56.910781 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-76" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2070.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2131.9375"/>
       <use xlink:href="#DejaVuSans-65" x="2159.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="2221.244141"/>
       <use xlink:href="#DejaVuSans-74" x="2284.623047"/>
       <use xlink:href="#DejaVuSans-52" x="2323.832031"/>
       <use xlink:href="#DejaVuSans-61" x="2391.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2452.34375"/>
       <use xlink:href="#DejaVuSans-69" x="2515.820312"/>
       <use xlink:href="#DejaVuSans-75" x="2543.603516"/>
       <use xlink:href="#DejaVuSans-73" x="2606.982422"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_9 -->
      <g style="fill: #333333" transform="translate(221.549688 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CoulombMatrix_coulomb_matrix_eig_0 -->
      <g style="fill: #333333" transform="translate(151.583281 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-30" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- XRDPowderPattern_xrd_38 -->
      <g style="fill: #333333" transform="translate(229.7275 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-38" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(84.460625 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(103.099375 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(90.359375 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_72 -->
      <g style="fill: #333333" transform="translate(213.278437 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(74.466875 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_55 -->
      <g style="fill: #333333" transform="translate(213.278437 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(90.359375 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(151.583281 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(102.165 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_avg_dev_Electronegativity -->
      <g style="fill: #333333" transform="translate(31.235781 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-45" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6c" x="2071"/>
       <use xlink:href="#DejaVuSans-65" x="2098.783203"/>
       <use xlink:href="#DejaVuSans-63" x="2160.306641"/>
       <use xlink:href="#DejaVuSans-74" x="2215.287109"/>
       <use xlink:href="#DejaVuSans-72" x="2254.496094"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.359375"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.541016"/>
       <use xlink:href="#DejaVuSans-65" x="2417.919922"/>
       <use xlink:href="#DejaVuSans-67" x="2479.443359"/>
       <use xlink:href="#DejaVuSans-61" x="2542.919922"/>
       <use xlink:href="#DejaVuSans-74" x="2604.199219"/>
       <use xlink:href="#DejaVuSans-69" x="2643.408203"/>
       <use xlink:href="#DejaVuSans-76" x="2671.191406"/>
       <use xlink:href="#DejaVuSans-69" x="2730.371094"/>
       <use xlink:href="#DejaVuSans-74" x="2758.154297"/>
       <use xlink:href="#DejaVuSans-79" x="2797.363281"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(63.039062 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(210.808437 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(66.063594 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(282.129687 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAIIAAAMjCAYAAABpq8grAABoiElEQVR4nO2dd5gc1ZW331uh83RPHo00ygEJCRACkzM2OCwOgnW2d53DmrXXlhMLGOck+9s1zsY5J+x1BgewySCikITySJqcp3N1hfv90T1JMwKF6Sph7vs8I01XVVed6fvrc89N5wopJQr/uenEX4XzyIwdMrWmkfwFr21/2d1B2iOUEPzn64t/+LXB5uY375w7B0fXWdrTB5lM9kOPXlETlE1GUA9+JtM9p+nNTyyaz0BNEoD+VIKVBzoTQdqkhBAApWiIQijMuvYDmK7LnqYGeuprA7VJCSEA9qQauPSx7WiVannO0Cib21oDtUkL9OnPUC7Y3T4uAgAh4YSe/gAtUkLwnZ8s/uYLE5Y17bjuugFYM4ESgs9kET/uqE9NO95fEw/AmgmUEHymP57S+2ti7GpuwBPlY73JBFvntfCxtX88PSi7VLDoM4ad95oKef6weg2PLWhF8yTFkMnCkTS25/wVmO4ufEB5BJ8JeY6osxza0lk0T+JqGnNyBVoKRWK2FQrKLuURfKbRdMOPp5Is2N/PC3d1orseWxbPYc/CJv7v3HXh9wdkl/IIfuNpFLwQ52xpJ2bZhB2XdTs7mds1zO6WFiH+e8QLwiwlBJ/ZmmiSi7oGpx1v7RkG1wXNFAGYpYTgN6PxqJZ0StOOD8bD4HnguCx+1X1Fv+1SMUKVyYh3rwDvy4LixSUiWus5z2OksYbUcB7DLdcCnak4X73sNEhEwHP5+W0/CUvx6fGux0O4iBJwDfLmz82GnWoYuopkxLvDIHebFOfZhHF0h++c+WKsUATPcXkkHCcdC3PrumVIMVHcF+7byu3f/tjhPuYlyJt/fay2qqqhulwCzJN46HhsWryK5X37AIhKhzozzy2nL58iAoC/LzoRTxx2qPDO2TBUVQ3VJV/+TyCBTDjGgv5eEu1Zzt3/EKORCF8/81kwQ6Frh++pM7NhqPII1eUfwCbQsIXOOTs3s6uljYvaH8D0XLY0zgV3eoHrrnMkz/jEbBiqhFBFauTnJYhLHcKfCMnSIJ7GKe3bAfj8uou56gVvAAm4HkhZ/nElL3nsHiSM/8yAC+wDzkHefO9s2KqCRZ+5t/GzsqdW8pIXvomQJ7EjJtLQx88L6SFL+Q752eb5ftqlYgSfGTBC3v+efoH2rvu2cemjewg5LrevWsAXLj6FBi/LsqFu/vq9M30VASgh+E5PfT2LRgRX3r11/NjzH9mNdF3mh7fzjme/anpvkw+oGMFnOmoaWLene9rxc/Z086fGk23vk7XhAMxSQvCb5MBIKRsxpx2POA4Ls7lABpxACcF39JCQu+akyIUnxOAJga6XyIrQrPQJHA0qRvCZDk33luRG+OkFJ7J2by8xq4SmOawo9lITCi8Nyi4lBJ9p9IRI1yToaqrjnuULQQgWDw+xJF/gi79amw7KLlU1+IwZiV60O5liXyo13rW8t66enanaQO1SQvCZdz/8Lw8U5fSxhbSuz3C1fyghBMDKrp5px+oz2QAsmUAJIQDmDg6zYHB4/HVdLk/JtgO0SAWLgXDfkvnytXfeL3RNIxcy2dxQyz0rlgVqkxJCAPzLjm0tPzjvrL4+QycXMmmyXS7eu+c2WByYTWr0MUA+8azf35gNR1Z/4s5LLwnaFiUEBaCCRUUFJQQFoISgqKCEcBzw/Dd1BLLMbTIqWAyQG8740+aIrq0x9SKeG2LECBc/fsfF0SBsUUIIiB8t+mmouy1s/fWEdUhNQwKn7X8CMu6HP3735Tf4bY+qGgKivT6W+dsJ60DTEJQL4qEFK6nzStcHYY8SQkAktJwhtakfvwBGayKB2KOEEBBpTcy4eKUzFkw6ZiWEgOiLNzF8kEdwPY9sOBlIC0INOgWE4TnSclzO37OZkilYNNRFQzbLY3UrgBP9t8f3JyoAaBnp5g1b07zj3h9NOV5arfPZU377kvc+esWv/LRHVQ0BUYwnxWldm6cdf1bHFrAK/+a3PcojBIC4Nu2lTj9HvPWRX/OzVefy56Vrac6N8vqH/0zEdXH1kO/z1pQQgsBGjEajfOvUy/jmusvGD/9x6Tq+8dsvsXXunCuBV/tpkqoafEZcm6kH0ByXX5x47pRz6UicW5aeRigR970zQQnBR8S1mW8TMQbxJB6QCU0v755oE0MBZGpXQvATU/t3Cg5ooAOt6amJN6O2xYu238dZW3f4bpqKEaqMuC53GZ78MZ6sI6SDoYNpoOdLfPS2n3Jf2wq+u/osPCl59eN3cVL/PrYkT+K7i37q9dU3dtYP5p/3hn1XPF5tO5VHqCLiutynceUteLIeUxfoGug6aBqu7VDSNL618nR21tSxO1nP9edcwSfOfB6NAwUGaxLi7B072oaa45t/XvfTy576aceGEkKVENfnk3jy3eUXArRJPceuh5Rw04kXsae2acr7vnXS2dQWsuxrbOBnZ61lZXc3fc2p71fbXiWE6tEEGOPZ0jw5JUWaDlje9AGmkqYjwjYlw2AoESeVzWObetU381BCqB57EKJzPJmm7ZYFAYiSgwCWD4wQdqZu6rWus4/mfJrBZJizdu1nZ9scEunCP6ptrBJClZAfiUk08RwEQyDLkw0KNiDH8yfetXgu1/z1QU7sGaIxW+DSHQf4xO1/4msXXUzc9Zg/MsqoiPa1dY28uNr2qqlqPiCuzcWwnR1oYh6VoWfdctAdl5O7BrhkVydo8LbNf+SBpafwfyefRnPf8IGF3dnl79x22fS9AathoxKCf4j/zrh4aGgCJGiuy5JckXklh5iU1BTzLO8fIRsO8T//t9bXeQmqavAR+fEaHR2JXv7YV2ULXNYzyBm9AywfHMHTDPY21BGzfN+3Q3Uo+Y6DQVi4GBoX9gySLJWIWCVqPI+aQoGtTQ0UEb67aeURfEZ+qsZDh6Z8kZqSTSqdJZ7PE7EsavIFlg4OE+4duMdvu5RHCIJi6d7T9+05K5YvESkWx7fq8YSgAcjjdvltkvIIASA/kTr7Xx+5z4sVilP2a9KkxHBckgXb98SbSggBsW3eYjRvesbdoq5hpmLv8tseJYSAGIrXynx0hvkIiRjvvO95vifeVEIIiIbBAeGgk4+Uk7F7QpCOxTht9xOB2KOEEBCaFJx3zxNEci69qTpKhDh90x6GQ5FAevhUqyEgRiMh+mp1znhw1/gxN2Szr64lEHuUEAKiLTN060+fdfJz5w8Mc/beA+xprOPW1Su4cP+eQPZsUGMNAfL+c26ThuOxraWB+cNpmnN5nljUpn//V2t8F4MSQsBsPO3XhYIWjUQ8y+uoa2z937+c0xeEHUoICkC1GhQVlBAUgBKCooISggJQ/QiB0SeuE+95/nOd0/o7tfZUPflw/H+//ruz3xWUParVEADXXPDXf3/zg7/7dlPeYSyl1tamuXx77bmf/NKtF14ThE2qagiAVz72p2+35LO4Zo6RuIOnFTixv5OT+zo/EJRNSggBsGi0k47aKH3xKLtr6zHJcyAVY21PcDmZVYwQAHe3reTHq0/jO2vKiTJasyP8+ldfoaiHArNJeYQAuKttBd9Zcy665/Ks7v2YnuQ/L30ZBS2QfNyA8giBsLlhHmv7OrnhvjvpSdUyb7iP9nickgjOIyghBMB5XftYoCf43EVX4FaWwLWmh7ji4XsDs0lVDQGgxxrYVV8/LgKA7mQ97S1zArNJCSEAhhJ1FIzpe0G31zUHYE0ZJQSf2dTwsZcYpQJ1VmnauVgpkMlJgBKC74Ryue91aw5n7dtLfaEIUqJ7Hhfs2EzL8OBT36BKqGDRZ3bV14fefe/f6GyaS9toD/MHelk02E19cYCPXvSqwOxSQvCZ5lyP9pNzXsj+htbxYy9/4E+ct3uI4biJ+GRJUnI8NPEzdO0N8ppw3g+7lBD8RKxf5iw8y5gsAoDfn3QuC/oPcM+iheUUOxFDw5Mvx3ZfLj6S90BINGFjiN+BuBdD2wH0A49STtrVITcYxxRgKCH4gVi/xINHu8y6RC6anHY6E6lhKKrz6Px5Ewc1AaYAiVbOwSR0EFehcdUMT9grNjqvlRuMO4/WRBUs+sP3wUw02SWWdfUiDlr8urJnJy993dVw8JCTmPSLpJKmj/HsbJNYDPxUbHSO+outhFBtxHoNOCdvmmjYNKULvOq+P1CXH0FIj9Xd2xkJ5dnX0FL2ApNLRBzRYORcjmEPIDUxxQ/E+m4bc46Gw7C2BM3T0SghhMXVV76M7595ztTrx5JzjnmA8ftQFofOTCIpAHPlBmPkaExUHsEf3qRjYwmd4YTFaDSKRwhXJtjT2DT96vEUKmMH5IQI4FCe4rqjFQGoYNEf5M2/08T6tpB0fl9jZ075yvmvoCU9yqqe7aztHuCupcunXu8BzpQ4QpbF4YEQDh5DaGIH8KPK1XfKDcbWYzFRVQ0+89VTvu3FNVO8/JG/ouMhgS+e8xw+9pwXUdJ00hGjXDU4ngQG5bXRGVzG7KOE4DO313yweFa+LxzynCnHH5mzjJtXnM/H/n5RINPVVIzgMyOJWs08SAQAJ/Xs4fz92wKwqIwSgs901KX+4IrpH7uGR4MVyELoyvMVvvKOrW998b5E05QNwsd+H0gEs0E4KCEEQn1xFEEeWQkWBRJBiYfmLH/K91YLJYQASEciaGho5NDII8ghcFl3oD0wm5QQAuD7ay/BRUcQIm/GsbUYA9FGaqxCYDapDqUAcPUY/3HF21meLjASqwfp0RsxOX33Fs596rdXBeURAmBOdgg3VF8WAYDQaLFcHmlbFphNSggB0JwdpbEwfXOOuTnfM++Oo4QQALcuXY2YoVNp3vBwANaUUUIIgBM6d3XlRHHKBBXTLrIvbPQEZZMaawiIb6342gM75y4+3QvFOLt9B4+nau1rH1gf2OJHJQQFoKoGRQUlBAWghKCooISgAFQXc6Ak3jvySgHPjhftt/Tc2GQHaYtqNQSA+JglGkdzXjEaxjE0YgWbguP+Pf/ZuouCsklVDQGQHM6VBusSlDQN6XgMJSOYIePCIG1SQggAQ9eMxtECUhc4IYP6jEXW1Kl//3DrU7+7OighBIGA/toYtqHj6hpDNRGijkepaH88KJOUEAIgHQtPO5aLmNgGrw7AHEAJwXeMa0Z/On3cEZASMcPsZr9QQvCZlpL7r5QcsB0oOZUFrxJKLlf/Y5N5S93XvhKEXUoIPmO7leZ6wYGiA1mrLAgpWdJfYLgm8dYg7FJC8Jn+aEhwcJKbkgumRq42gi3NQOxSQvAZYczwkUtAEyRyLoUZAkk/UELwGaHPsMZVFyzK5FmT24IVN9kUvXaj73apLmZ/0T5pSVlyoegCYHgeH77lTp63dQ+DqRpMcwivYMuLh9/v65dUDTr5iPlfAxnREEdqGjg2eBJXSgZElJwTJjFQJCRM7ITt+9J4VTX4SDykxxEaFMoiAHjtvdt4ySN7kFLDxiAnI+ilELdp10+f715FlEeoMuK6fDSK2zUva9VGdJ1RXZuSFuf5W9oPfgeyZDInpodHQv8mDVuSJomOvMvAu7xBfiVXDTuVR6gyumDnSYO52mLIZDhi0pwtcma2wPnZPKuKFqUZ0vXr0uPRBWvQ7BgJssyhG0fo5xqUHqmWnUoIVURcl59TbzvzQp6kNxYiJCWr+zPEXQ8TaHVc7l+98KB3SQaba7BCJjkaANCQeEIjyfCyPnH1/GrYqqoGv5CSpqI9JblqztB5cPVCTNNg9a4uWvtGsGp1Nq9ezMWPPT7l2rGNQj30qnx5lRCqiPxorEe/Pt9raaKlJWdha+WiLWmCx5qTpMPlXsS9dXHWLWohWbBY276fVR17WdjfT5yB8n0q90tTv3+O/J991bBVVQ1VxpMsfawxkQu7Hj0Rk4KusbsuPi4CgO5EhPZUlJKhE3JcTuzdTooDRBgiR5Ru0YrhOQ9nqFtbLTuVR6gy8qOxHJCABOL6nPdAS41ghuHmoUiIgi4JFR2Wjuxmk7nMObf0VdME4j7YqTyCj8SyBUsIjYgzw9YKArISlm/p5h9NZ3Ju6cO+jj4pIfhI7vON0bahHK2jBQx30kpox6W7NsrFm/bS4nVTlIbv/f6qavAbTxL2JCt602TDBpos9xss2jLEC/f9nRXaToazy7/nt1nKI/hMptKBpAFJyyFRcsg7kjsWN7PI3YuLyYWFj/y733YpIfjMSNQgo2vl7RiAjIAhTRDJFqgv5DgQnvdUt6gKqmrwG6HJbk0IDQ0BuJW9F0q6wS8WXsIpnb2BmKU8gs84GpRCJq4Q4yIAcMIm++JNDBt1NwZhl5qY4jPiv9M/Q2j/KnQNPW8hXIkXNvBiIWr6hu3RG1sDSZ+jPILPyI8nX1pfKCHDBk5dHLsxgVsTIW451Nhqb+hnFAnXo34kj+Z6ICXxQomW0Tyuof8mKJtUsBgAaVOnNWdRny8hhUCTkqxp0BM1/jsom5RHCIARU/cGIiEMICQleVOnJ6QhP5HcHpRNyiMEQKMmQv3xkNNvVmYnCUDn8SBtUq2GAIl+KPthT4iLnZBxhXtNeDRIW5QQFICKERQVlBAUgBKCooISggJQzcdAeM/zHwwVC8PWQ81ziNslzuzqdD5+7xXBJEaooDxCAPS4OetHq08mIgUDiRRfPu1048On3+zrWseDUUIIgM5kPe/f9AhzdZMTHZdX72zn3nltwWTIqKCqhgB49oFu7lxz4vjr9IIYc7oD28UHUB4hEA60NE875kWjfG/x958dgDmAEkIgONr0FdCWGcL2sr8PwBxACSEQxAzd+oWwyQmF3ukK8QklhAAQ2vSPXZOwuXGJ7ylzxp8f1IOfqXxgzW9/IUrONK/QNDLKSLg+sBFA1WrwmQbk+rruASJCEvFc0uEwidE8hutQU8wG5hGUEHxGhEyRr4sxL5NB81xaR4fpSdayYN8g2UhIeYRnCplwhJjjcFrHw5y77x6ijkV77XyuffaraOkd1sUNeYkQoFGQ10VjftmlYgSfqU1nmTfaxbN3307UsQBYNHKAVzx+C5+/4hIMzQVDgEtU3FDwrTmphOAzPakkddmOacfPaS/PW3WiEbAlhACN5/lllxKCH4j1ArH+Tkdc6e2qi/FY49wpp7+37nzmX/tVEKL8E9WhVH6nuKHgVX5ur6aJKkaoNmJ9CCgC4p1nvI7fLlvMgiccumta0L0CV7z+/dy/YPnU9+gaGB44EhhPrnahuKFgyxuiVRmuVkKoPjcAYm+ykZXpPLaukwmF+eHal/LnFc1lEUhZ9gST0QQT+dTGqVp5qaqh+rwAYHvdHAy3vL71Hy1NlDSDx+dUciHM1Gh0Z25JihsKkWoYqYRQfT4OcFbPbgpm+Zu/NxGjpaODM3ZXUia6cjxJ99j+TocSgrwhWpUJLEoI1Ube/DNgtNYqMD/TTkMuz8s3b+PZu/fynlvuYP7gSLm5WHAgb0PegdIhV0XfXy0z1QIXvxDr24C/fnjdG1bMSWd47q49ANiaxj3LFtBTE+d9L7kcPGBslxdPgiNLgA3UyBuiVSssJQSfuXnuTbI+C4szfVOOW4bOa97wr9y/bEH5gJTgulJ+IOKL11ZVg8944SJOXpDVJ6YoOmiEHJvmkUxZAGN7QUrO88su1Xz0mXw0idVWwOlsIKrbaEhyIkySHHmplwWAAM/7svxg5G6/7FJC8Jn+WAs7kmFe3HWAvJiYkDSgJXnh5u3uX3+6plIm/i5zUELwmX5dc5vzJT0uCyx2u4jLAlkRZY8+l3lDlpqh9EwhhnzhP1Yv5gSnnRqZR0OSlHmWcoAtc1sDi9yVEHzm+vsu+8NFne2EmbpnfI1jsXx0KCCrlBAC4fLHZ06VFLZKPlsygRJCACTTRUoHBYMlTHbHo9mATFLBYhDYOuTdGA42Oi4OOhk9SmMsdFJQNimPEACPLGvBwqBImAJRslqM352ylNc/fOWBoGxSHiEAdsdacE4JsfbAfvKmyf2LFtMVqgvUJiWEAHCNiGcPhrTvrHkWwvVYOFKCZCmwZJugBp0C482X3D3UZMs6V2jkZeH/vnDHJS8O0h4lBAWggkVFBSUEBaCEoKighKAAVPMxMO4RXwsN1kS3ootFnif/+MLR114RpD2q1RAA94qvaaWw5qZKBQzpUdJ0umpq5AtG/j0wD62qhgAYqNEzqVKe/po4+2pr6a1J0FjIi++1fve6oGxSQggAU4poTyJJJO8RdiQhB4aNCEP1qQ8HZZOKEQKgpEsRtyUxxyaWtQEYTUSpSxfVVLVnEhGvRE3RmnIslS1QveUrT40SQgDUiDS6N73US9EQZ769420BmKSE4DffXfbLC+rcUfKxqbWyBwwl4zSn818Kwi4VI/iMlst8Nucl0Y1RCqEEA01JtixdSLRgYeQKtOWDiROUR/CZZDZ3SrFYC5kUWkmnuTPHc+99gNGGBINzGimZIc58ffv/+W2XEoLPNHhWWMiJj10Cd8xbStbK0FebIBMyaSvZL/TbLtWz6CN3RD73jZ5Uyxvb+vJIYPvyuXz8stPZXVsDgOZJzhoYZk7eYjBklm7/1kLfNvNQHsFHrJB4w9xsBoHH/gWN/P605eMiAPA0wQMNteyIhdjUmAyd+rauNr9sU8Gij0iioimfw0Gnt6WWwUg5p1Lcdri0o5cFmRw9sSjLR9LcXrTpjUXa8amMlBD8QlxpGOIyAExcakoFFmdy3A68ans783MFAFoKFhJ4bnc/v17YqhvvG3q+m4r9hbBhA0JuMA6ZV+eYzFMxQpUR61+QpebnB1gS7TAW0MoAYUcwVJPgljNP5fGGJCcPTt8f/CenLGEwEsa0PQ5YHnpIx22KWwjxHeCdcoNhTXvTMaBihGoi1s+RiF/lqYsOMA/dcOhNxOmN1RAulDjnwS2sGkrP+NZsyKSnNk5vXYyUKXBtD0puGHgLcO1sm6qEUF0ud4iYwzQAYEZLhDyLnkSKrngtxZCOa5q4ByXb7I1H2FeXAKBk6BAvxxKh7Pgi2RfNtqEqRqguHRouYYrk4mGceo0Tu3ZTdFIk8g5CK3sD2zDwPA/N85BC8NNTlkzJxGqNZeE1x7+3s740TnmE6vI3ndJd9fQTlkVyUQMZksTz5aHnluFBFgwdACFwdR3bNOmpMeivmdimIWw7FDMl0ASlsmfIAx+bbUOVR6gm8maJWH9xkoE3Ls9v/kKhsMpoSg/SV/mGD9LKqx74GQ8vOIWemmYWjHTw10XzwF7NysE0I6ZBjy0RmpAiFbrF07XbgZ/KDUb7bJuqWg0+8tvF35aX7fsTD8lLGEu6HiXDPPbSV2dw02nn8oWTLoHaOOd3DNFhCrnrS62+eG3lEXwkbLv86sQXsmZLD6MkKIRCjGqN7DHn8JqXXUo+HAIPVg5laChaDFvuXr9sUzGCj7QMHHDT4Vq6muvJxCIUQiFsw8CU8M47HgcgIiVrBtN06oJHv7tsqV+2KY/gIydae2r0x8L5rc1LiGrulHNn7+tlyeAoqzSBFIL5hcLlftqmYgSf+WPDV9yIY2hZb/oGbgeW1HHvySsZdW3v/350gq/bA6uqwWek5XW1ZNIY3lSPEHUtti6dT6RY5Hs/vsl3T62E4DMR6dyMlCQLBeJ2kZBrkyrlqClYJAslXCAlP+u7m1ZC8JnhROt7iiEDXULMckgVLEIlia0bOKZBTnqBJFtUQvCZK3tf6uxrrkMXUzOv7ljaSltXJ42aXpU9m54K1WoIgIbsKCeITWyLnIxFFD1i8a/tPyVrN9C66X8Cid6VEALAJkRMFjizUNmOoTwnhS5zEa0B2aSqhgDwPCEPRBZNOZbRaxhwEnYwFimPEAh9taE3DzmnfmNvvI1F+QMMhuro1udy4eAm32YtH4zqUAqIPzZ9dY9R0hcXQyFqCgXciHPTpQPveFNQ9ighBMyNp90irn7w8sALQQlBAahgUVFBCUEBKCEoKqjmY0CI96dTCHEOnrxFfiZZldVLR2SPChb9R7w/XUAQQQKaAE/eIj+dfG6QNqmqwWfE+9O/QRJBiPKnLwQgLxfXZH2diHIwSgj+8y8Rzy57AtMAXaPRyvGS7fc70etGHfERK5AyUULwmbpCjmI4DIZe9gaaYCBZS8kMc//XrtPnj/ar+QjPBE4cOAC6Bq4HtsPKngM0Z0f5/cp1nPmWj/HWe28JpIpQQvCZA/FasF1wPHDhidpWTu7dz0u33UvBDPP7lacHYpdqNfiMeF9aoolyFi2AuAmhihOQkkihSDEW1eQHTF8LRnkEHwm9b/DHpudWRCCJSHtCBABCUIxFwfWcQ92jWigh+IgtzH+1NQ2QICXSk1CYYS6K52niE5avk5VUz6KfSE9DVL57QmDpJhSdclNS18CT5bWxEih5nfj4RVVC8JVDlKvlQOigohD4mopXVQ1+IhAcp8G5EoLfiBm+6HrwxRC8Bc8kDvVpzyQOQFyf+3r1jDnoWaofofqI6wtNOE4XEgNNlDuTxjD0yUmyKm8AdFFpWsryAQEI0Q+8TG4wbpttG5VH8AMp+zB0A0MvF3zYKBdy2OAbf/wmz9v1SLnlMPYjBIT1ymttTAQATcDfxEbnvNk2UQmhyojrC+8ej/+1yi9irIAFg9EEv/rJ/2PRcN/Em3SmVhdCcFCQ+dnZtlMJofosAGYqTADW9bQTdh1evG3TxEHjKYul5qkuOFKUEKrPe8d/k0wRw+sfvp1n7ynnTuqPJ8sHxfg/T8bnZtNAUEKoOvIjURvE1TgeSA8sh6seu5ttX9rAN3/7DQSwpWkev1x9RqWHsSICT076qQSX5cj+c3KD8e3ZtlO1GnxEfCAtx0YdL9u7mefv3Ux7bSPfetYlpJMVbz9WHrYHnpMnGv5X+X7zD1W3TQnBP8QHM5IZ9nsEIGqWWwqON95ilNdFfOtmVlWDnwgxfUxhjKINeYdxoRxKMFVCCcFPPE+WO4tm+Ngl4LiH7GWsNmr00U+kC45gfCh6MoYGUaO8JSzyMBoOs4vyCD7SlB31wq7Hms69JKzC1JOeBK/SsygByUf8tE0JwUf646mutqJFNpYiG45OPTneVARAyg9FP+SnbUoIfhKJLLQdi5BdnPm8rFQJguW+2oUSgq/Ij0RlpJCVz9/1yMwXaOVRRnl9dLevhqGE4DvtqWau2rGJtb37xo+FXJvVw12VBbHBLIxWrQafKZkmnz7zBdz//Y+xraEVR2jEnBLPfumG8uonQSA9fEoIfiPgt8vWsuZ1H+XFux6iP1rDz054FmErO1Y1LAjELNXF7C/ivwaGiITrDu45XJjpe0/7l5d9PiCzlBD8RrwvrQEuemUmkifBtkbl5xprA7VLCSEYIu8Z+LrUxGW2Zr7F+3TylqDtUUJQAKr5qKighKAAlBAUFZQQFIDqUAqcx0I3bCaurwGBcEreYHaRcZF8ve8RvBJCgDxmXn+3aYTWdLpzKGhh6mRaiyd7XALw1KpqCBAvHD17h7mQeivN6uxeEk6eUdkkusW7p28TW2WURwiQDAlOze6iVmYBaHRHmVMapguzoxXq/bRFeYQAaSiOkqqIYIyYtMCorfXbFuURAkR44GAwTB0WYUxs6hhGaP5/P5UQAiQkJQM04mAC5f0g+2nClY7vc9pV1RAQH7ngH17eiIyLYAyJxhx72Hd7lBAC4GMX3REqJuLC8kzKc9c9HCQlNCTl9Ah+o6qGABgNGe9M5vPUmkMMhepI51PjniEqC+QiTb5vDayEEAB7Y7FPnWql+frl60Fo1AznOGXTHmKFEkURKc9d9BlVNfjM5a/bl4gaujZSmwShITzJ6q0HqM0XCUmPiOcgbI0HxCdv8tMuJQSf2RUxM7ZhIiqLXVt6hmkcyIyfFwCehkP2Cj/tUlWDT4jrM5eELeev86IROqSkpc8m7HnE8jNt2KIRgkZf7VNT1apD5EOZs07qav/mYCi2cm/LHIFhCOyJz7quaPHqHQdoGs5ywW1bpix+1vQSy92HaGDkUYHo03D+V8BtyJvz1bJXCWGWER+1zLBTenBlT89JfbF6umtjEDOg4HLw0pVn7e/m3ZvupWagRLLbQkqdBCMUk1BXGKXZ7qCeETxCaDgZgfdc5M13V8NuFSPMPq9pHR0+aShcx2AqCpHKJl4zfN/CQrCreTEPn3gCf7/4JCKtI6zlARZaHfRrc6klDYBGCZdIDfCVahmthDD7nJEqFhGAbWgTSTb16b3GIc1gW3MTI5EIaIIHFq1BEiJhZ4nbFrJSYZT/lQAnI9ZXJa5TQph97hqMxZFAyHEnciuGtXExGJ7H2sEMSUeSD4fY3VhP3jSxdQOJJOQVqfGyaLjAFGfyEPLmqmzzo4Qw+/x4IJG8P1VK0zhcqCxsraxqiugQN3hO1xCLspNyJAjBUCzKSd07yFMP6BTRGRYNAHiE0LGGgLdUy2glhFlGXhd2Ch+pOXNra+uaGmf43taePhvLLqfVkxLd9RAzxAsLhnpYu2cvDhFs4gjdolYO3yExfqZRukDgtSJv3jT9nbOD6keoEu6Holtg+dljr2s/OLImUnI2xx2PqOPiTppzIKSkvXEOSIGLUR51cC1Pl7+4wC97lUfwiZFP1j7e87lGMYiUBSlJlEqEHYeI45CyLDzD5I7lJ+GhAwVs3L6nvOksojyCz1zUO5xMlZyMGQkTPig7ykA8iYbDfr2NGtf5k592KY/gM7/+ycpsKZ93p3XkScn5O7agkaPgNbJcfuJ1ftqlhBAALY79+bbeATSn3Dw0HJsXPfAPovk0O1mJK/131KpqCIBiY9MHYoMj713Tvp9l27tJZiw0CR0sBiDEIdLvVRHlEQLgqz9Z4TlCyjU79lKXz6NPqSYkEao2tnRIlBAC4jO3nqnFMjYxxyFKiTA2YWxiWITJPvUNZhklhABJeaNAeSzBwMPAQyCIkfV9SFgJIUCSjE47puOSJamE8ExCohGlMJ5jU8clRo4hwr4vbFCthgAZoJ5mRglTxMXEwEbHoV6L+J50U3mEAMnr/DlPHAOPcGV5S4feKE9wr/G92aCEECDnOO+7rM8M3d5utHBAa2S30ejUaf1BLHRScxYVZZRHUABKCIoKSggKQAlBUUH1IwTMuy75x7va0oXPA7IzHn3B//v7Bb5OSBlDtRoC5EOn31L47VmrI4+3NaFJybP2dnHpQzs7bth0+Xy/bVEeIUB+c/bqyCML54y/vnPFAkzbbQvCFhUjBMjWeU0AzB8YZWnvEJrn8dBiv3OllFEeISAu+/cdIXPJXAzX5UBDEoDVnQMUQgaQ8N0e5RECor5v+H40jXw4NL4SaktbEyu7BwOxRwkhICL9IzW5sDnt+EOTYgY/UVVDQKQksTXd+2nJZMnrKfqTcXY111GaYdW0HyghBEC/eOX2zwh3TvhBG4DBSB3/t/z5PDR/PvtTcSDlu01KCAEQF96KsLTHXzcUhzmn835yoQRN6Ryw2nebVIwQAPoM6VNacv3jv994+i0qF/M/O73i396rSXfa8b54OYmaBPbWRJ/ns1lKCH6Tp+ZTeyKLyWvR8WMlzWBTyykAmJ7EsUon+22XEoLPlIyIyBsJYl5h/FjIczij+2EARsMh9s1psPy2Sw06VQuxXrgw4kLNkF6P5jloMt13ILKypc3pp8kZRCIYMusJeRZRr8BX1r0RCeRB2hoi6TGWrf0zV2+6/P1VNVcJYRYR658L/NqG8MHNMQEMMJ9h5rCAR7H0MHfWns9QopZCNESNnaE/1VDuZTwIOekeQA449+pNlz86m6arqmG2EOuXA7+nIgJx0A+Ah0FC6yFMiS3xEyHiMNxYQzEepr92IuPuwV/NyfcA4sC9N55+S5RZRAlh9rgS0BymFNoUUvTS5HUBcHrmQYZTB3Ucicl5FZ+UCHDp0Ro6E0oIs8dhLlMrF7MpHWxt+ljD7D/v8FBCmD1+AgwYzJhtF4A0zYzSMv565eCOqRccfrz2xNWbLr/ryE08NEoIs4W8eRQ4EfiDBOlRSbo66aeWLvqNZrIiDsAZ3Q9yVuf91BeGSBSyjMai7G+s4+D9WyRUcrDiAT8GTptt81WrocqMiNe81kG+RRP69nrvu6/fFnq33JFcw7MyD9Ja6sFDsCe6hJtXXc5DJy0jXCzSuHtfcZGUJQE1QPHqTZdXfYtgJQSf2W681+uvmSOSpRG6a5ppr11AR0sTOxe2IjWNRZ3duJn0ho33XvY5P+1So48+M2gk5FkjdwsDycn5LdwRP42O+Lm0jfQSzoP0bPYlYnV+26WE4DO5aENSWCI7FlKev+dBzt/zIC6CL5/2Zu5fNAeQ3/HbLhUs+sxzht+Rc2f42D2hY2uC7S31/OQXa3f5bZcSQgDkxPTYb2vjSkxPctaergAsUlVDIPRFmhfm7ei+pMgikGxpWMX9804HYP5QOhCblBAC4IT8Z/a//Nxbdp1jsWxHSx2bFs4hWnI4b1cHjdnCU9+gCighBIQdi2r3tab40VkT8xPvXjqXq//6YCD2qBghIIxQ6G9/WrNkyrGSafCbtcsDsUcJISDO6x19c3aGBS6ddf4vdwMlhMC4etPlMlmcvh2w7gbT06uEECBr9/dOO7a8dygAS5QQAmVe7+CbztjdRW2+SLxY4sTOfk7d2zXrI4uHgxp0Og543RWbrg65Tvprfzjru0HZoISgAFTVoKighKAAlBAUFZQQAuYnC3+y/C913wwFbYcKFgPiZw3ffTyuG6uTmQIlUycTCckX970qsC+m8ggB8I3lv9fqsVYvyW8nYg6RT9rsaJsjvjfnh/ZTv7s6qNHHADhj111Wk26xKXUythYCC1bs72Lv3ObAykN5hABoY1g/EGvB9UwMy8OwPKSrU5v2f7/HMZRHCAADV4xSi+FMxGeaI6kf9X8H2PHnB/bkZzC9RhOaN0OQ7gaTWg+UEAJhOJki6k7fCDwbDa4VqYQQAIOhFMtKuzjReoTTC/fQZu9DSsmmlYEkZgdUjBAIaRGmTvbRYpdbi61uF3sXxslGTgzMJuURAuDcwQcJyaldBv+y624G6uNsuOjuVwVhkxKCz9wb/fQbhJgeCxieQ7RYQnft7wVglhKC33g4X9uTWoQ8KEHOiNnEeTv3EROHkzln9lExgs9YyYR++7LVLBnpoqnUiY5NkRo+e/7LcEUYZmpW+oAadPKRdvH2v9296KKL21traMrlcIWO9EJIT2NzaxNuzOBnSxfgeLbMNSTL6ZaKpQKG/gti4dfJ95kHJ1OZNZQQqkhWvMmQ8A8X5yyLRtGv1dExt5F1HfuBcjnvaJnLzeuexcNNdczpHuVnF6zCFQJMvZxlTQKuBMcDIUHTyr9LWU6kowkIaZUcfAIkEo1hNO1hyhlX/gF8UG4wnCezVVUNVcTAyhkUQ30sJ6OnuH/BQi7bu508scp5h2W9XfxhQSsjsRh3zp1DuZ9Jgu1C3CgXtACkBq4HZqXQs2PlKsH2oMYEXYPy2Xom0u+dQTn139RlVQehgsUqkRZveVOEbKhIApsY3fEUqwZ6cTAZS6HpYHLv/KXENRNDgq1PKg5HQqGSQkuIyd94KB5UQ0igeFDG96mefrHY6Fz2ZPYqIVQJDXk6gEACgpJm0JidPqgUL5a/2RlzBufsHFRte7LiHWaozqela53W+Djhye1VVAUN570uOjoOJnnqrDy20KddJw2d5+1q5/J9nZjuQd/0sf2dpCyLwCjvBoc5Q7HNdGwCD/jWk9urqAox+a10ntpPajjE6WFBYR+jUWPKF9cyDHa2tRFzHNb2D/GiPQcmTgogopdFUHLLr02t/NoQE7HC2HWhyrmxnwkfkQFeJTcYuSezV7Ua/ECsFz1izqX7afjTXCn1HLX0NKR4cOUyrNBEL6MrBL9ZsYjtDQnSYaNcNUjKnsGTHo6TQ9d+gq79EMQgGl1EQ8PYXkx+MJQTGx0hNxhHVaBKCD6yLfZRPV4oOWnRwN62Jh5ftmjKec1xOdBYx+/mNn2o/YtzPuKnbapq8JFV+evcfj2Ga3ic23MbCSsz9QJX0nCgX/otAlBC8B2pl34jzBz19hCv2/RDzt17D6d0PcaLN/8GxxRESsFMZFYdSj7ToOeuLGLaGZpIl+awuD1HnAPU0kHIc8lG9e4g7FIewWcW5z/jxIo6QyzEIYyHSYYW2iMnopckZk1ifhB2KSEEgC0i047JUgytYHHtn8+q2sDSk6GEEAAj8ekTUwp6iFx8ukD8QgkhAPa11FPSJ3oZPWDzkoXUD2YO/aYqo4LFAMhoEf62eg3zh4cwXJcDjQ2MJBJEA1zgooQQACErzUiymW3zD5q+Htz6FlU1BIHnRGQ0d9ACFylJ5J90OKCqKCEEgIX29bO37qK5dwij5BAuWJy8Yx/D8ZrA+vvVWENA/Lbpu3LpwMTWjQcaatkxr+Xkqx993uYg7FFCCJAfzPtxT8LzmvOmIQfq61r+85HLBoKyRQlBAagYQVFBCUEBKCEoKighKADVs3hc8It539k6kqhdBVCXGb7nyq7XneO3DarVEAAv+9ct78Qw/seQ0DA4LC/etU00jqYRQH8yiWXpwy/vfm29nzYpj+AzV75i26ujhvk/GDqG67EoPSTiw5IBoxmkJDmUpxi1fd8bWsUIPuMK/XuGlHgSsqbBQDRFiz3Aidnd1Dtp0qE4NQX3qW80yygh+EyN44r+WIz+WJTRcJgHVizmt2esJUyJZYX9zCn14QYwDKmqBp8RmkbJmLr07e/LVtPcWSBeLFFnpUlY/m8LrITgM0VtqhOeOzDCsx96gkU9AxRCJgPJGqwnX8dYFZQQfCZRssi6EWxdJ5Ev8o7f/oW5Tgc6NsNWC6bbSE/D9N3kq40Sgs+4QuOEgWEOpGo4+4knOMW5mxDljUBb2I+ZP4mBhP/bAqtg0WccTSPmupwwNMJFBx4aFwGUcym0spfWPv8nsSqP4DOaEDhC0BuP8ofVa4l5g8wdsChQi4FFlEEsMX3P6Krb5fsTn8GseGvXb4bCIbY01dOZrOEfy9Zxb/P5DLOAIkmyNDHAcoT0uPG0W3xtQyoh+Mg827miOxbBMsqO2HQczt2+e8o1Ep06kWfe3r0dftqmhOATS97W9YeBkEE2FgYg5jjUl0qIGcZ6pC7wovG5f4h837fyUUKoMmKjExGfLJ1X0tznPNFWz9xsFs3ziLkunq7z4LLFB71DYlBCuhpNVnvpiegNvpSRGn2sImKj80I8+Qtcz6TocG7fEM/d1cn+VA3ZeLmvQPM8zt26nZUd3bSMDlNrZdGQ7NWbGTSTxPUsa3Lb/rNJfuHGatqqPEKVEBudOJ78PmBqlgMITu4dQgKN+eJ4ijxP07hjzSpuuuxiOhpr0So5sFrcUTQJo1qSUS3xhWrbq4RQPU5CkASQngBXYmk6nhDogO6NZz8jbNk8++9bEH0mFuWmo45Ekx5myaMzNa/qxqp+hOqxC0kJCGmaxPWgPZlgfrq8rC3iOMzrGGTN1k4S2TyOCZ7w6KGBBfRgEcaQLrlYiFrbqrqxyiNUCbnBGEAT70eCGzExpMdgOMKvly8gr2vU5guc//gWYsUCzfG9XFb8A88p/Y7W8DYyRClhUjJ0MnUhlmT33lZte5UQqojcYPwPuliC691cVyjK3pBOrSP55bL5bGpIsSTdSTQ2wLqRxwh5NhqSBVYHCaObNGEyCVi3/7H/bpU3XlJtW1XVUGXkBmMvGFeufWPPg4YZXrc5HCLmSG5vrMc0s8wtTV8BXef2c2/DOua4w6mTnQ/7MjlBeQSfeOSmRaf1xMMstAoQ0aA5xo/OeBbeDN/FkoiQKI46Fw6+y7cZKkoIPrJwcDiTiYboaEmBELQ3JWguDJI14uPXeAg6wgs5MddV46dtSgg+cte3lyZ7UxOF3pmsp8EZIO7kyOlRbKFjC52dTYtok5+avlVsFVFC8Jna4sT8g9+vWsfdC1cggLhbwJQum1pPw3anp/OvNipY9JlTeoYYSsXIhQxcXeeyN17LR2/9I+fteIL9yTayRpJEn6/OAFBC8J26kscLdvRwIBXF1jQWjOZ5cOFprGnvp260gHBDOLqazv5PjyPA9CRLhidS6emex6DTiOm5eBrYpv9CUDGCzxR0DcOdtJJJSuYMDGMbglzUYLQmhHHwlj4+oDyCz6RNg/pCiZDronmSdVt3sWbPfvpSNSChtT9NUfe/WJQQfKYIFAydiOuhabCmvYP6QpH6wkSAqIf9nyOiqgafyYeMX+6MRhgyDQqGTjY2PRF3IaxmMf/T8/BX264q6WJn2tTpCpmM1OhkohNiyEVCmOR8Xw6tpqodB9zW9EV7ON5gaJ6kKdtbOnfov8J+26CEoABU1aCooISgAJQQFBWUEBSAEkLgiA8XXiE+XsyJjxR+FqgdqtUQHOJjBQ9NE0jK2/h4EnltJJANfZRHCAjx4fzzQRN4lHeE9wAE4iMH7/HjD2qsISiE+A0zOWNNn74ppA8ojxAUnixXB6aAsAbGeI0QSNWgPEJQ6EIjrIGolLsmQJNgOYEEbcojBIUhxLgIxtAAcfBBf1BCCArvEOUd0CagSghBUXLLcYInwfbA8cD1wAXxUcv3clFCCArXg7wDJQ9cCY4Eh3LgWCxl/TZHCSEAFr+l8zYQ5QBxzCuMJc7wJJj69GlLVUYJwWc+efpv/jtkctGMscBYx5Luf8Como8+8ZGzf7e+0Rr6ZU/dMtb0dLCzvhakRAbTSJiGEoJPxO38L0/t289F+zbTk0jSXVNHX6KOki4YSEQohCpFIQDhv6NWg05V5sbTb9EKXtFpS4+KBcVOzuncxCWv/RgdycbxazxgX30CTxcQ0sqxgkBiy08S0q+T7zervuJFCWE2Eetf2B9t+Pbf5p9b35do5tTuRzgwp43NrauIFG2W9O7HDhf5+DkvmPbWnpoIuZgJpg5jax8PLpuZqxEX6ASulhuM3xy16UoIs4RYv8RF7PrxqitFX6qF0zseYk/rfLbMXUVdutwa7KyvIxcyuas+hRQC0/MwJBQ0wXk72vnuOSdBTAftqKoGCSyTG4w9R/Nm1WqYPa4fiDWKwXjZ5a8Y2sW21hVErBKalKSjEWzTICQlC/IFFhYtTsoVOTFf5NR0jjP397Kmq59j6FoUwIeO9s1KCLNHSZMTVXlJN5FCQ6t4XHvShl4LLJtGe2IvN03TeHzFQlpHssfaxXzUCRmVEGaPDzcUhry56S4AHmk5mZM6t2KZ5eVrMWsiU4ozQ10/nEqwdW7TsTzfA2442jcrIcwW8uZODXnepe2371nWu4M9dQsJZyxO6N5ONhIiZDus7txByCmhzxCXFQV01tdM72mc/DMzReAx4Fy5weg6WvNVsOgDN578I+fxFat1Qxj8230/44T+vXz53FfwSNuJAFia4B9z6hiOmeUWgwSk52HqC+X7Qr5s4KGE4BPvv/ROGdZKZGP1DOgeO1Nx0rFaCmGDjmQU29Qrs5YE4P8kVtWz6BPCsJZ5jrlrGEmXYXLvovloEQNPExP9A6XKUHQA3c7KI/jMK9Y/vGJLqn775hVzyp1Hk3E9sDwQIK/z1yOoYNFnfnzzqTt0Q/ubsCoTU4ouFJzyRBVXVkYgXd+/nUoIAfDwN+ZfKl0XCpXC9wBbln+kBEeO+G2TEkJQHOpLLwHDaPHVFpQQguPg+GAMz0N+KGL7a4wSQnBoM8SCAipr33xHCSE4ZHkdQ+XV+P/BFInqRwgKISQSMcUzlBwwRCDteSWEoHClJKyV1zR4EpAQ0cudSgGgqoag0LSLcDwI6xA3IWqO9SMMB2GO6lkMEHFD3gYMhBhbHS3lDbFAvpzKIwSIvCFmomtLEWxGF68KSgSgPIKigvIICkAJQVFBCUEBKCEoKqgOpYBY9I7+lOa6w4Ymyl2JUrq7EtH/RNO+Lz9Vk/HbHuURAsJw3RGha8KUkpCUFDVNT5bsLyFkWrxn2PdOJSWEgDCBZselzpPUSImmaaQRYDlgiFrx3pHz/bRHVQ0BcMJ/9CVqpcQABkyDHfEInhDl2UmuVx5/CGm34WP5KCEEgPC85QbQp2vsilVEAOXZyxNL43z11koIAZCT8t4R06A3ZMw4QcUAHNvfUUgVIwRACIw+XaOt5GB40wvcLS90UdPZ//kRLHJcFjguy/PWxFrISowgK6ud/ERVDQEQwhO1lZ0d6xyX00dz5HQNKSVbxvaA9HnKmvIIAWBr2hS/PxYedkXDCEMrxw0+r3pTQvCZi97SHTGAwqRjeU2wpSbKcMhAagIMDTQN8d6RNX7ZpYTgIz845ctJPZMpSCHYGzI5oOuMaoL9YXN6vkUBSHm/X7apGMEHSmL9zRq85JxkE5rj8N3VZ9PbuoS0rpFGozhT8iwhQGhRv2xUM5SqyKVv6RK/+fo77BjoTzQtZFX/vvFzX1l7KR+84OVAOWN/JhqauhxeH+tu1p4lP1Gzqdq2KiEcK2L96cCXgNMrmQ3EWEpliYaJx5amRZwwsB9jUrItD8Fpr/0Ye2ubCbkOJ/S3c+fCleVAUVKOEyJ6+bVdKSNTlLugAUKGLGdpxUPTeoFr5Qbj20f7Z6gY4VgQ668HHgDOoBzeCUH5QzUAs7J8bU996xQRAGhIXrTzQa7Y9RC/++VGrr/vd+VORl2vdDOLcirNsAEJE2IGFLzyuaILuZJAEwJN05FyLvAtsdH5x1H/KcojHCVifSPljKeTdmWbuSOoK9HA3OzglGNF3STi2oDgi6c9m6sve83MmVKSJuiV72vRKYvDAEYtSIXLQoFK2l4BcILcYOw40j9HeYSjp41pIpiZ1uzQtGNF3aTs1wXXXHhl+eBM2dMmOxJDm/q/M+N4xMlPYfeMKCEcPY8DkzKeHdqzihnOpUp5POD7a84hE45NPTkmBsHkbQDLBR/WytlVAELTltZL4PeHZ/5UlBCOFnmzA1wFTPq6H341K4DrLno1H7j4ZTNf4HnluGCsuvBkuYpwPciWYCyB95gXKc94e7PcYBRmvuGTo/oRjgV5831AQyVeeC5wgQWXa9CslasNTZsUN0yuPBxN584Fq+mJ1ky/r1fZ7Gu4CCkTDF3ieOC5Hi4OcaMLXWyj5NyLqe9GiHbgHrnBOOqATwWLVaZTf8MHrUTiE635XkKugy49LD3EL9dczrvP/xd6wyaEJn0fZWXXt7FM/Z9N+TLqoITgE53Ga7ruXbJ2zu9Xny3aG+aRM8N0Gjqdhl52+YZWdhxuxdUbGriulBvrfKm+lRB85ryXb85nahujmqaRB/aGDOzJU9WEKAeBjgeet0V+rs6XgSclhAA4663dckDXGdGg0ZUMGxqWppE3jXIqXgBPIj9V49tgtGo1BICLIOl5nOB4NEiJGzIYjYUnRAAgQGx0lBD+mRlBepObay2WM70jyZUcSyvgSFFCCAAh5ZSmpOlJEo6HNrZHg+NOTFvyCdWPEACOpo33HNsCtiSiOJOntRt6WQw+ojxCAOieHHEqvw+ZxlQRQKUZ6fq6sEEJIQBqBXMHtfIIhHaoKEDXfQsUQQkhEAq6ZvToOiNCUOM4mAcHiiENXG/AT5uUEALg8S+15GIStpkG2w0DTVZGFc3KrCTLgUjI1wztKlgMiEbXcYrCNHKaBpZLtGBTCJsgIeR5l1ufT/ra06d6FgNk1dt7/+4IcYEupZTw/u1fbvlsULYoISgAFSMoKighKAAlBEUFJQQFoJqPgfHn+s99+YRs+1tN6Yrd8cbieSMf8W2d40yoVkMA/Dn+ye0RLbZiONxI0TSoLQ6yMr9NLrBuDMxDq6ohACJ6YkU8a9I2OMqynkGQCbbUrhLvufAvK4KySQnBZ25a/asLZSnCE4uauG/NAh5d0YruuXTUzmNt+64fBmWXihF8JjaQ++u+efVkEuVcSYVIiB0Lm5BSoOfjsad4e9VQQvCZ0WRCkzFzyjGpCUIUSdnZXEBmKSH4TZi8KMoairEonqajSQ/ddqgtWoRL+cBaDkoIPqMJQbouiVHZJNxDx9N1ssSodbSFgdkV1IOfifxm7jfe7UQSM85K2l/bSKuTTvhvVRklBB95Yk7LZweTcYRjE86X0CZNSxwNx3hg6VKfsytOoKoGHzl1734xVF9LeEBD9ySeJuidl2K4IcGOxjpW7jOf+iZVQnkEH0mWiiLaL9C9ct2geZLmjlHunD+PlJUmbCshPCPYumAJB+8Fr0tJ3WiOVz18L1IPrjiUEHzie6f8yuhsbp7YpKOCJ6CjsZafnHIBMWsi2Ym4Plsn3jvyVfGe4cV+2KdihCrTL67VtrYlXzISX/ZT03bJx8PEs8XxPCo7lrSQjkeIOA5WXEdclynn6Y8YEDHB8d4irsuVE2e4nkSrvFETAolEk+AJD8eRCDFCSLsHmwy6uItEuA9NRIF2YDcQpZyeKw9kASk3GHlQo49VZUv9R768vWn+29rr2kj1p0mNOBiOg2uAkAIpIJ2IcNNlZ7JqXw93z69nW319+c26gGS4PMUdyllUcmProyTEzfK5MQ9TdCBtQSIE0UrupfIO9DOn7avcFfgu8HZVNVSJfnFNI1rpbYZlsr1tHplkinDJwTUEaBpSF6AJknmLM59ox3HtCRFAOXNK3p6aPCNaWRkbM8uvJxdwxICoWT43dlwTTyYCKG8290bg3UoIVSIbM9/42LzljESj1OTzFMY35Jh+7bq9nbTXzpBUq3TQQtgx73AoLx4+6iXUz1VCqBKm7d7VmBsmUSySi0Tob0oiNYk2w9LWRelOFg2mp58wDiqeSrf0tONjzJyA83DYo4RQJdpKH7tjRWdXJh+PoHseDaMZUrEcCI+WUh9CekTcImuzmzkn8xAvfWgri4dGJ24gKOdgHsOT5RzMYa38+8EUHMiUporh8OK/fuBTqtVQRWLFUGr54K4fGm7h5Q/NXURJ6CLiFTg3vQnJ1FqitTTANX97kHdceR6WES6fzDnlwheUM69FjXJQWLAh4pWzsgrKwii55fMFSxIyJ7YD0jUQwgV6gRzllsNmYBNwAPil3GAMKyFUkSb5MdkErzwTXhmd97UXDGvJ35m6S94IE3OsKddmtBqEqVHc2CTEtaPfImz8O65X/qa7Xh9zknPlBsObkv55FlHNRx+5NXWTbG9qoTTH4a13/xq9krr/r8tOY7+zGGHavG7HVYEMPCmP4CNzSz1si7Zx5/KTeXzuElb27qMz1UR7w1xWdHRzweMPBmabChZ9xNSKXLr7QQzHZTBey11LTqG9YS6RUonafAED56lvUiWUEHxkV6LVW1HYxUvu2ER9OgtA68Awr/jrvQhPYutmYPW0qhp8xHPovq3l7Hlr9nVxQkcvu9taKEZChF2XmoJFNhwPzDYlBB/JRpvmE3a9VlHgL2edNN7buGf+HEJFi8jo0fcIHSuqavCRV3S8VM4fbee+k5ZNdDlXcE2dtBbdH5BpSgh+0xlulZpenHbc1Q1SbnaGfmZ/UELwmawWpXWkD92d2kJYPLiXmlz2qLbhmQ2UEHymNbvn1pyV4srHfk1ruoeIXeTEnm2cu/M+XD28NCi7VLDoM/evOv95lz72kNdpLOKKx/5AbWmUfqOZPTVLCOve74KyS3UxB8Bt9Z9xzVJUGxZ1SAQ1epqTcltptG8MbF2DqhoC4OKh9+n99RHv7MJ9XJL7O0vtvTzSvPTjQdqkPIICUB5BUUEJQQEoISgqKCEoANWPcNxwS/0XPpXTal4Zx7nq8oE33e/381WrIWBuq924Ph+p++WS9ACG9GivbaRXj3qv7ni1r/u8qaohYNKh2l+e3ruP+kKOZLHAST0HaKKk3ZL8wm/9tENVDQHT6BWxEeT1CLg6AljUO8AjTU0v8NMOJYSAKRk6aA7CnSgKzYF52bza5e2ZRKowiuVNX/fYlMnRIa5Z5ZcdSggBU5crYonpKXMcDO6rb3vALztU1RAwjqfRYdQTMUoUjRBR16a+kKEgw9Q5tm8peZUQAqakQz4WYqCmBjtkEM2XGArFaRrJEitOn9JWLZQQAuQz5/xdvywCvW215JKV7LtS0tCXoT6bJZf0L8uaihECRBse7XhkwaoJEQAIwVBjAkNzKRWjvrUclEfwAXFdbjmafAJD1yjaOWqiNfIDpkwW7abRltS066WuIROSmlF3hrtVB+URqox2zei7iJs7SIQ1oibURuPCtr3Gdw38oSA9mY9EObiT33Rslg91YEfhD3O/Uz/jjWcZNdZQRcSHc2/A0G4ifFBdLyUIQU2xxLV/2YSUYJZsdM9jaW8P84YGWJDpZyTikvdqOMm6tupVhKoaZpHoNZkTiyaP40IECdGQGP+6S8m8dIGo43IgFcMydDKREHFrlFwoSTEa5iX33UNtPg+ARRhXWqRTUTrEBz5sIj7VIj9ZtXUPyiMcIWKj0wC8m9Hi63HkHMI6hHSElOj5EtLQiBRdCggIaWBoiIjOv+zooSlnkQ2Z2JrGYMykJxbivPYu5uYszntsM2u6Dkx5VgmDXalG1qTbCUmbXhrJkkDDGXIIn3mGfNuuWfu7lBAOH7HR0YCHSFunUPLKOYvikxNeeZh9eexJuQ3XFAossErMTxcYCYcwpYcmJY6mkdN1WtMZnvfIw5y5ZwfeQQ7aRdAlmijW25w+uAsPwRZOQKKh4bqgRc+Qb7Nn429TweKRcRFwCnZl0XJk6pQBo+hMEQFAa8FibraIpWtEPBdDlrPohjyPhONQ1DXO2vsEGuV7ysoPgIdAkzBiJAHQkDQyhINBjKIuEe+ZrT9MxQhHxkG7ch10doaQbsQwmC9KM53CkJKwVcCQHhKw0SlVkmUJPHQsJKAzvRlZefSsdUErj3Bk/A1oR5+U/3gSTtgg6k1NcfB4XYJMSGdInz7hSAIddQ0UDROLMCXCVJInI9GxiGJFBcuGu8evH6ABDZcCYSmQG2frD1NCOALkBsMGLiYZ+j6GsLDccpJLy0UUHIzBPHEhaXIdYkjihsCJmfxpbhOba2Lktal+IWMY6ELjZ2edjzVD2jwNQbxo0VIaokiY3SwCJBEKDujPPUO+bdaW0atgcRYx3jf8BTdsvsN0PaG7LsVUDBwP3XLRwhoNgzmW5y1irouQUGc7JFyXaNHi+fc/xNKuvin3y4TDaJZNlKFCCPnuEVpvWiuvrkrGLSWEKqK/Z+gfsXjo/GwijO64uIaOUXKozZYQrsfCwQxvfPQhXBklUSyybtceIna5nD3g8bY2lnR1stbdUPUOJVU1VBH3c/UXWI7zlcW9o+i2h7BcopZDVoOaXKHw0nsfc2LZEj1NdaQyBRxbx8Igq4fZnWwmVcrghORpftiqPIIPiA+OvrlGyK+ZUjCElJot29yNdV1fn/8Tx6mv0cN5j3RdHN1zqR/MEB8pMFoXZ9noXs4b/C9fRiBV89EH5CdTXwe+fvBxT9MLDaP9iXA6RP1gBk8TCCkRQjDQVEvY8WW8CVBCCJS5/cVVNeHMgWHRQC5h4ukCpCRU8jBlkZb8qG/uWsUIAfLC/Gs6BiONWBG9LAIAISiFdeYPdVEIVScT+0woIQSM5nmUQtOLQVo6BSPsnx2+PUkxIyHLIWKXph3Pa1HyuunbFCUVIwRMJhHG1TWQHojy99LVyuMLUeku98sOJYSAsU0dR9OwTR3Nk0gBUtNoG+3lsq7/aPfLDlU1BEy2JlbxCOXd46UQ1Fh5uuvrfe3gUUIIGCtsYkdCGK6HYbukCkW2L1uCVyjt89MOVTUETM3QyDeG5jS9qX1xG56mobkuSw/slq/b/W++bA4+hupiPg64cd2f9PqhrqGw4STqRgd+dmn/Na/w2wYlBAWgYgRFBSUEBaCEoKighKAAVPMxML695FcXxnLWbYYrhatrsq+p5g3vePz53w7KHtVqCIBfNf7YNFy75OiTl0lI9i9qanznpucMBmGTqhoCIG5lBqeKAECQ6h7pDsQglBACoUYWp+fTA9yw6V+unINQQggARxfYxtSP3goZ9LQ2BGSRChYDoT5bwLGz5aVuhkHIddi3sJl8Ior4ZNFGE7qwvS3etdGT/LJJeQSfuWnlb2ttGSZVsGjM5bCSEfYvaqEuk2c0FgZDN9B1IcPGmuiHc1JsdNR09n9G4qPZy/NamGwkQiYawbM9MuEwOxe1IjSNkOvhjq2xjYWIj+SKkKr65EXlEXwm5No/HkzEyUXCaFISsWyW7elG6x/h90tbkIArBLrjgRDkaiKh+g9lz6+2XUoIPlNXKgipTff2Q8kYe5trsQ0dNI1SyCgn3dJ1hhPhf8Q/WnhtNe1SVUOVsMWrPgB8QuIIiWvreK6HeGihfho7E2EK2tR8CcPxyNQbTM68IgQlQ3wH+F617FVCqAJF8XpPQwjQkeiYZE0Bpo48J+4WaLDTdOiN44Xdn4zx11OefEKSY+hVDRpV1TDLWOJ1XoiMMChiksGkgEBU8qDAaMSjJ1SPJiWm4/D4/Gb++1UXk449RTwoQWx0qrb0SQlhFimKl+ZBFyWaKNGMRTMlmrBoxqaGztA8tsbX4Og6nqZhGwYNuQJtI0Xqs1Y5QDwUZX9giY3OV6phuxp0OlLE+tOAJSVS7RJxlUb+UkFprYepe8SA6CHf+mhiCV3heVOOhTyb+9atYChVw+bGFHcsbUZqT/n9lMAocIXcYNx5bH9QGSWEw0WsF8APgFc6xHEJEWK4fIpyyVg0AYfepe+x+HI6I3OmHEvaedxanatf8UJ2NCaPxrI/yw3GZUfzxsmoquHweQ7wSomGQwKT7Hi9D2BTy5OJAKClOIzuTSxnFFIytzDEvMGBoxUBwHPERqfpaN88hmo1HD5rgEp2VIFgIqeVh4HHRPNPYCNwkJjISR9x1Cty0ug++sMpPCFotDLEXYvPXXTBsdp2NvCbY7mBEsLhcyeAhg14eJjojK1inmjZGWQwyI2/tkngkgAgRJGsSDG/MDH35LYTlvC5y4654/Cvx3oDVTUcLvLm+4HrBbJkMopDAg+tkgHVppw52UGfJAIAgyxUMqdGpINtTq0+fnbaycdq2RflBiP31Jc9OUoIR4K8+aNAq451MlBjU3uxQ+QaG+0JjSEpKE5LtVuOIyaqkZri1Ez7sdJR5dR2gVuBWrnBuPpobnAwqtUwixTF+u1hWDFZDBLIMwcNyBFnV3Q+CwrD44LZPLeFF77j37HMp6ilK5t9ACvlBmP7bNuuhDDLlMRVrok3ZVjJIUT5S+zx24arSNp5mqw0pusQ9ko8MbeZT7zgEu5aumDqGAOAlCTzJdJRE/m+UNW6mVWwOMuE5C90S1w1YuKlxpqXOiVcBAKNkO2QNmOkzXJi9UIkRNe8RlaN2DxsO+RDU6ct1mcsclFzukBmGRUjVIGw/EWth7HaReRd8CxCjzg03mXT8KJkvoDhloPHTDzCg6ctp2NeI5mwSeHg6sH1GIqZWKZedSEoj1AlDPmzrUAcpnYz2dGbmDuSxtY19i6dg2uUz25pTiGnVQuQtBzSugZCPLeq9lbz5orpWDjS1oSwdJ1C9MlHHCNjez8I0SA3GEPVtEtVDT4T97zXPbRyIabnsejARFr+Nb0jaN7UwL0ha5EOacPVFgEoIfhOXan0p1xNhJDjsnJPFyc9sY+wVWLhYIZ3/PUR4kWbeNGmZSRPb8zw5AejagPQf1Z+svTncsWe/mnHu+pquOJ9L9uJYB669ga5wfiJXzapGCEA6oeyMx53PZAfCK3w2RxAVQ2BYBmaPHguUknX8AzfNoefhhJCABiu1dFXn8DWyx9/0TDoj0exDFn1oPBQKCEEwPOG3rxACg9PdxmJGVimoJgwWdGbaQzKJiWEgJhXGl6YqQtbo40J8nWG3ZofWbROvj2wyF21GhSA8giKCkoICkAJQVFBCUEBKCH4zp/Nb65+44V3519/yb3Fn9b96DlB2zOGajX4yH+cedvvf3PRSc/vq00Qth2Wdw1yyX07tnz24eesCdo25RF84t+ec/9z7lq37PkdTbWUTINMLMJDy+ZhJaOrv3D6rfGg7VNC8InRWOTW0Vhk2vGHFrfSsqc/HYBJU1BC8AkJJHPWtOOm7VGbLwVeDoEb8Exg0Tu7bzyxY5DX3voYpj2xCDaRt1h/5xPj8xaDRAWLPrD6bfvll796K5Yw2N+U4vZTF2E6Ls9+cC+eJqizcowkwm97dc/LvxqUjcoj+EDYkQggLB0y0RCuEKRyFom8BcIlGw5j2KWXBGmjmqHkAyXDYDAe5W/rFuLFI5w6NIzUNYaaE2glh6wpCGWdQMtCVQ1VRmx0mlPpXG9NwebTP7iNeLG86NUKm+w+YQ6rH9hfXvBSKrQ/X77F170eJ6M8QrXxvLvysQivu+3xcREAhC2bxr40w001tHSOYJvmorFzXeLay8MUfyWh4BBZNkd+bLjaZiohVBvXXWSbJm0DaR5f0MTPz13FcCLC2ds7uXzzXrRKI8LUJJ1iw48kxiviWEhMQER1SkMD4p3fb5T/W9XMq6pqqDLiMyWJpvHiu7fx+zNWlFPsVrhkyz7e/cO70aVH1MnSTIZaMkQq+RQ0SkgERSLU0h+LyG8UDvWcY7ZTCaF6iM/aA0ADQqA77rT+gohl83/X/5SYZ1FLvpKWEwxKLGArEcodjjZxRmnBw8MgL8tNPbfbxPmyTfhCSfST9fL/3XYstqqqoQpU9lh4GxURADN2GmlSEvK8SmZWjwQ5YuTIE2OIOTRTJE8DkhBhPCSgERY6LjrOXJPcxxxKZIk8p0/8J2EGcgbaowbevRapBwysv8UYSSNvLj6lzcojzC5iozMCpA7n2lN29/DNr/6aGgrjGVQEHjWkEZRI04hEEMEiTJHIpOvAI0xmvCMoT4I0STxMNDya2EdoPH+ThsAAHAleAcgDXwE+hLxZgupQmlXERuc/OUwRALzhbw+SOCjvkkQjQwqbGBouIWwMXEzsKdcZlNBhPNdjnCxx8uhIHHRMMgjcynmPcsIvTwAxoBG4DviPsfspIcwuGw/3QuFJ1u3tQTDdI0ugSIw4WczxFH5T0Scl6BojhIVEkKQfjYPXUs3o+deP/aJihNklC9Q92QVzB9Nc8lg7mieRksq3151yzUQ1oRHCwiaEjYkxqfAlGhz0Pg8NAbgc9q6BvWO/KCHMLsuAQ+7kurxzkI/94HbCTrkAB+MJjKxH0i1gVgp5zNWblNCQaHg0sJdBllAkiomFgYVGjnLxlZ26h8DGQKdImkbq6MJg8rC3zkHCyQGfHXuhqoZZpJLQ4hKg/HU/KBB/8b3bx0UAgBAMxuJYhMhXUvia2EQoEKUc6IfIEqKAoECGGBahSsQQxcMCitiAhYHARuLRwl5cDBxClaSgBqA7oN9KufBvAE5G3vzQmCnKI8wycoNxG5UvmPh0SY5/1YQglZ8+MSUdjjAvM4wOFAmRYIQQLhKNMBnilPMoCDxMSriY5DAIIYlSriJC5IDSP+rk9y88WruVEKpI2+DoGaOR8P2uqZOPhLhrVRtr9k9NkLFpaStNA1lqKFCDRY4ETWzGYGJcQiIQ5L0Yo++L4RbClL6tyR9M6WU81q1dVD9ClWl+/6DX35QSAM3DGS56fD+XP7QHKeBPpy2lKxnn+h/cRViWWCx3YVNHggEa6UDHwcMgSy0p+ZWqJk9QHqHK9CejJaQMtw2mee1tj/LJq87nZ+evBqB1KMNLb38cAFfCQvkp0S/e3mvR2HyA1eg4RMlIHa2l2nYqIVQZXcrbXU9eHrEcfnremim5FGuzBS58bB8aLoVyrw9N8stVL/SZUK2GKuNcG3+ukJJdc+vZ3To1Qdq2Bc08Ma+BTDKEF2JnQCYCSgi+ELXdQ6bQ/dUFq/AMDVtq+302awpKCD5wyIBcSvrqyru7iHBwibRACcEXwkVr5g0dhWDt9h5CeRstEvqgz2ZNQQnBB1YMjsxt6xud8dyL73wCs+Tx4r5XPeizWVNQQvCB+762pPesLfuJWFNHEk/d2U3rSB7tOOjKUc1Hn7CEzL7u1scS96xq40BTknW7unnj7x5mJB4mns/O+tY8R4rqWfSRy16+Ve5sa+aSh/YwZyhHb32Cnroov/vlmmAjRZRH8JWTugdqpK5lvnfZKXhC8KwdHZyzrWNZ0HaB8giBcNHrdsUHElH5+I3z8kHbMoYSggJQrQZFBSUEBaCEoKigWg3HIb+d89WVOxvqXpMQXPfmx182c/f0LKM8wnHEHvFp8dHTfy2Tsrjtgq591xQN3Oeuf3DmhQ2zjGo1HEd8Y8m3nRe179L1SplI4I6FC7ljwdLnff4fz/5TNZ+tPMJxxDl9XeMigPL6hvP37SOv63+o9rOVEI4jdG/mcODUfT1Vf7YSwnHEX5ZM3+kvrcWIuNPXOc42SgjHCa+56B83fu/UU3Ary2LHfn6zbi37GuuqPiilhHCc0J+Kvuy1Dz+Mg8GoiLM/1UBnqpYLdz7BQ8urvyeoEsJxwql7OuIndGfYNH8JSbIsGu1n/ugQtqlTCB/rOqanRgnhOKGWSGw0nGT14H4mtxyWDfSytnNf1Z+vhHAcsOG0W9+7cHCEX56zhrp8btr51vRI1W1QQjgOCJXyn/zes07kjJ3tlGZIcnHHggU0vLP/ndW0QQnhOCAcCumX797HFY9tI08Mp1IsttD4wHMu51crV2B67ueraYMadDoOaM0UWTgySpw0KYYZjCf4zqkXsKe+lb8vbEN3PVxNU6uhfUNcZUi8HwIXCbgR+BTy5qr25nx33o8uDkUMNM2mjkFsTeOD//IaepPlVEwLijZxV/JotLpF9cwZdBJXvQPkx0HUAEJOyzomKOcZKv9WPj82+BOqnHOgkuZOolUypU4ksJvIaeQBLhId0Cjpgp+dfBq/XnMG97ctojtVDwjq03k+dPNf0KXOCdlu5o908emLL+NA69Jp5t9ZF8PSdTQhcIVWAvkLNPHf8iOx9tn4eJ4ZMYK46hsgbgQ9CWUXK6b9SAReJaOZV/ldVn4cBA5aJcGVQKLhItARGAh0IEw5b4kBhJBEKsdMQq7Bax6+n5N79tJd3wgIEnmLT/z8b1y+bwtRx6NP1HPlVa/nNytm3vmvPm8jxzInCkII8Uo8uVlcn5+VWdD//EIQ/6oD/z5xQMI0bzD53PTzAhem5TvUGPv45CRvMvEeweTchpIQ77v9txiODRLO336AVYNdaJ5GxHLoj8fY0trIkKmTPygcWLu/nZU9ndSVJtkgBAiRAN7+JH/9YfPPL4RyCR1UwR553DX9HUd6D0HEtjHdcla1uFUibluMhMpbPmpjcxCEYFMqxv6IyZChs3bPLv7zr7fyjvvvR3dmFPCT5nU8XP75hSB/XgIemzgwVhnMxMzHx/zEVLxJR6dfMf16lz+dcBKFUBiA+5fOpSeaImmXc2I15IucdqCc/3JlXy9z+rvJW1ne/bc/E3EcUsUiI+ZBei6L5yeH+GOOiGdGsCiuqgXuBrFq7NBYMDi16DXGxCAreYzLn46BxERQRCArxwRlR6NXrh/LZzj2rlIlyNQAhwfb5nLVa95JR10DSAEePGtXB9f96c+ELRiI1OJosDjXyerBsiBcIUCWbfrYhRfwhXPPGasSQMo8mrhafiT2rVn5iJ4RQjgUYn2bhI0CWoCzKEd7wqsUpiwXmaw45DEX0C/gjyD6QLu7fFx/BLwGgbFFkz86og/0uy3f+GoxkXrLw/Pn0FzK8K577phyXgJ3ty3i9S96Ef1fbK1aX8IzWwjHCd9e9AvZVxMjbGZ47cMPTzknge+ecjpfOn2d3H3TkqpV5f/8McLTAE9A1HHYU1c/4/nGUYuWXL6q31glhOOAvrAmhRC86sEtuJOaoR6CDAmSxRJmydlTTRuUEI4DHm9t3FVDifmjaWzCWJU9W0pE8DBBOPz9l2uXV9MGJYTjgNtOO3F1c3po/HW5BVLen8XEoi8ZrboNSgjHAV0bG+09kaQciCWmHBd4SASjRqTqNighHCfctrB1651t87AwKt1TkhIGRaL4UUxqGPo4wYuFnZhrUyKMXUm6LxEUDYNHFzRX/fnKIxwnPG/r3hf0J2v4vxOXVWIEDVvT+MxFZxDNFave2aM6lI4jvrTsx/Id65/P8v4hlg6O8NC8Fvpq4rzykUflD2+9oKpfWlU1HEfIULkHeWdTPTubJjqXRuIqWHxGsbOhUV66Z/eUYysH+lk8NDx0iLfMGqpqOI5Y84a9S0/p693l4dITi1NnFYnbHt///Tlq7eMzice/uXj3I/PmGyFHuosyRbm0fyj9g7NP86WMlEdQAMojKCooISgAJQRFBSUEBaA6lJ42/GjFV848uf+Je5enu7E1g72xJrlt7qJTXrrtvzbPxv1Vq+FpwItfsjX8zT98qthQyo4fk8DueCvLct0a8uZjLkRVNTwNeM0jf9g8WQRQnjS/ONfHn5tP2zQbz1BCeBpgas6M6xsFEs3WT52NZyghPA3oNBtnWDkFaa2GTGNE3JXc+I5jfYYSwnHOsLg6XiMKwkOjj/nYmLjo9DKfdu8UFhwo8ucVK79wrM9RrYbjnN8tOnk0G5aM0sg+VrGPVVPOx4olhHPsAb/yCMc5D7ct0Vdv9SgSY2wlZjoaJh0NARKLMM/e/fgxP0d5hOOMc9/afa70vDuFVv6OnmMbnGhvo44B+o05fO2SZ7N1UStCSk7a28Urb9tEvDQoBsWbX9ggv/6bo32u8gjHEee/tVsg5Z26po2n4fBigiTDPMaZ/GbtGWxd1AqU8yg8tmQet5+8HKMU54Hk6l8fy7OVEI4jhOPeL8TUOSgPLF/GrfPPwSHE3rnTc2LsnVuHS4S9Tc3iq8t+ddRz2pQQjiM85KkHT0WSQvDHNSeRbU6zIN097T2LR7pxMTlv7362tSWPekNRJYTjCE/Tp01JW9m3j8/+5Ytc3Hcvb9r0R5qyo+Pn5qSHufKRewGwvQTP23Wv2Cw+/dajebYSwnHC+W/t1oWYXh7/9tAfidkWAC3ZXl656S+s7NzJSx75Ozf9/AvoubHM7YLlnQXms+sru8W1rz3S548POomNzgXAc4BzKbdTcsABYBFwPuUWho0nI4BZ2V8CvClt2PJRQeUWopLnR4AuBJ4sp32h8r8nJ6UiqhzTqVwzcQs8Ca6EsS+MlBPnhJC4Xvk9riznNzF1cD2J5ZZ/l5UMVq7QEZ4EzaXoFvFkAY88MQMstx9XFoBdwA2Us6icAvxObkz1iQ2jlwFNwC/lxlTxSD/o8DsH15nwIiHl/RGYJ+BTAlJ65c+M2jZzZSX336SBwG/94pMsH+pkOBznqpdeQ1eycfzcqu5uPvDruzAqZVBPJxHSZKknTwKHGCDG0sR9cZ18+7sOZZ/gs3Yt8BCw+Ej/uCNi7I8TkwpTVIQyJpCDHePkwGns+sPFk2NCYVwompj6XNsDUysLaLhY/n8Gy4E8EK+8toEXy42pw9pwS7xrKByT8v4aKU/WmEiydfBforsui1xv2t/5X7f/nPXbb+dzZ72Ym0573rT7X33LPZy1q2P8dZxhkvTTzfIZngJAyzr59r6DD2rAp/FDBGNJoMYY+10I0LVyIYmDfiZzJCKAifvBxP0Pfm5IL5dMpnQoEUD504xPem0CPxQbRg+rWtWkvHpMBGM3m+kvcTVtRrF/+awr+NUJZ7O1acGM9+9JTV1BnaOWMIVDPAWAv8500AAuONQ7nhGMeYYjoxZoBTqf6kITnnNYihECTUq8g4SQjUS45tn/jua5M77tpEpKvkk3YoC2J3vSqpkOasADh2PnPy1CgHHEMXMaOKw9+Gy483BlNtMcIQMQnoen6dMufv5DT7C0b/jgu5B78hyce2c6qAEbgGl1xqzzZDOhnmqW1FgccaTvGcOTE68nHy+5ZQ9aE5qoOmbGmvS7B7xBbkzN/BU9CE+I/5cRYu+hU3NO2Ovo0wUpgKTnIWT5b2jKDBMr5FjV0cer7plplppExz7UU6DcIJj+HCklYqOjAVcCLwROoyzELNBFudWwnLG041IayEqq4cofgKwcGW8xTPqrJ36fSE8smP7BHyoYnNxCGGuBjLUyKjF2ORm6V/7RKMcEjgeOlJiawPPKLQtEOV+mJiSW62JLGw2HmAklN03JKwD7gHdTdv2nAj8H9gMvo9xq+I7cmBrlCIm8c/D5BrxESPlAGBZp8G6tHKoKD4g5Di2eFIca/Fnas4/tDS2MRMsxwakde3nXbx8cbzFM+sCIkKmkEU9QaTVI4G7g4nXy7TNuO6DmLB4nnP/W7pDjScuYwTPpjk3LSA+PzZka07/zz3/njB0TztzAwiCPhXH1qfL9XzyS56sOpeOEO77aWhJy5j2B1x7Yw/bGedOODzZBM50k6cckR7hmPxbaD45UBKCEcFxxqMLoTNRxcu/0GG9dzxPMYxvLeJQ2sZ14xpGnyg++5miereYjHEcIRD9SthwcKw2FYjz7wA5iJYvRaILHmhfw4u33cfnuhydCMs1hyF3c8KQNxydBCeF4QtfmStdzhZgInJf29bAzmeLi9kc5q3MHALamYXpTNxLZHW/j9NF3H9yWPGxU1XAcccdXWz008fyxpqKQkksfepT1O+4dFwGA6XlYxMaboqMiSTLNh47l2arVcJzzlRO+J5c6B7hsz6NTjueJsTW8kt89aymvvecuucT5n2P6UiuPcJxjFsntr2mZdjxLiqLVwEXbdpKJx475OUoIxzlfe/YlLaGsTUdkzvixPHH2say87G1kEMMpHbNbV8Hicc4D32zLPWj0e13uidoAC9HwyJBibK/JsJuhVDI3HOtzlBCeBuSNhIi4Eos4bmXfKYEkhEueqLfc/t//d6zPUFXD04Adja0FIcoSMPEwcTErm4/lCN0/G89QQnga8OdTVq8oSQcpxnamq2xALDKsYuic2XiGEsLTgJ/8/uTOP6xb80HPzFPem9ohF7HkHNlzoiG/OSvtf9WPoACUR1BUUEJQAEoIigpKCApAdSg9rRAbnRXANsozOLfJDcbqWbu3ajU8PRAbnS3AiTOcMuUGY8YJqUeCqhqePswkAoAjXoc5E0oIT390sdHRn/qyJ0cJ4Z8D66kveXKUEJ4GiI3OU60AVh7hGcJhLa87FpQQnh6oXd6e6YiNji99PUoIxz+H5Q3ERuenx/IQJYTjn5cc5nUvPZaHKCEc/xzTN/1wUUI4jhEbnUW+PUuNNRyfVHoLbY6sxeDJDcZR9SnMfkQq1htAlHKOwtdRzjoSAhqBOhjPmzJmsFc55lHO7dgBpCrXC6AAJIAM8Bjl/EX9lDO6pICvASXK6X+6kDdLxHoxGxteBcyRigCOwcM/uUcQ69cA/wucRbkwJeX0cv9MSMpdtBrlBKNvQ97856CMERud7wJHnDn1IG6UG4z/PKLnHlIIYn0c2AM0H6NRTzdc4GTkzVv9frDY6LwI+PUs3e5UucF45HAvfjJX8lyeeSKAcpX18oCe/blZvNd3juTiJxNC9knO/bMT1N+emcV7HVHmtycTwl+Ah4/Nlqclo8B3A3r2scYGkzncjijgyYQgb3aBS4CPAJspR/N7KEf2/yyMJdx+lHJW9h8BpyBvPjivrT/GbDA2A/9yjLfxgIvkBmPoSN7kbz+CWG9SbnUUgHMoR+s7KQdoOvLm0UnXTs5fLSrCfMYgNjo2R9G8lxuMoxqpVB1KxzFio1PiyJrro3KDUXs0z1JdzMc3NUdy8dGKAJQQjmvkBsPiSbJrzyZKCMc/DX48RAnh+Kf01JcAcFS7u42hhHD8c1hCkBuMrx3LQ5QQjnPkBsP24zlKCE8Pjnlt41OhhPA0QG4wqj70r4Twz8ExL4RVQvjnIP7Ulzw5SghPf9Jyg3HEG1cejBLC04erZzgm5QYjNRs3V4NOTzPERueTwBXAf8gNxt9n677/H6FCdkfbDKp9AAAAAElFTkSuQmCC" id="image15413c5aca" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature98_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-39" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-38" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagea34aa6696f" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pdd2e2c4ee2">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
