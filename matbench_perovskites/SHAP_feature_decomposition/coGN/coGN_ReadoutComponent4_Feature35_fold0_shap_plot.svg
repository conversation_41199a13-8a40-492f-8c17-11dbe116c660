<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="784.682281pt" height="679.5765pt" viewBox="0 0 784.682281 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T04:52:24.799868</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 784.682281 679.5765 
L 784.682281 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 415.323281 638.149 
L 521.875281 638.149 
L 521.875281 27.789 
L 415.323281 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 485.161858 638.149 
L 485.161858 27.789 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 415.323281 609.084238 
L 521.875281 609.084238 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 415.323281 580.019476 
L 521.875281 580.019476 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 415.323281 550.954714 
L 521.875281 550.954714 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 415.323281 521.889952 
L 521.875281 521.889952 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 415.323281 492.82519 
L 521.875281 492.82519 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 415.323281 463.760429 
L 521.875281 463.760429 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 415.323281 434.695667 
L 521.875281 434.695667 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 415.323281 405.630905 
L 521.875281 405.630905 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 415.323281 376.566143 
L 521.875281 376.566143 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 415.323281 347.501381 
L 521.875281 347.501381 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 415.323281 318.436619 
L 521.875281 318.436619 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 415.323281 289.371857 
L 521.875281 289.371857 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 415.323281 260.307095 
L 521.875281 260.307095 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 415.323281 231.242333 
L 521.875281 231.242333 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 415.323281 202.177571 
L 521.875281 202.177571 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 415.323281 173.11281 
L 521.875281 173.11281 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 415.323281 144.048048 
L 521.875281 144.048048 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 415.323281 114.983286 
L 521.875281 114.983286 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 415.323281 85.918524 
L 521.875281 85.918524 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 415.323281 56.853762 
L 521.875281 56.853762 
" clip-path="url(#p7ae3398a29)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m0260a0838d" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m0260a0838d" x="439.96611" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(431.857907 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m0260a0838d" x="485.161858" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(481.662483 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(345.917875 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=1_88e+00 -->
      <g style="fill: #333333" transform="translate(46.273125 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-31" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-38" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-38" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2b" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2530.761719"/>
       <use xlink:href="#DejaVuSans-30" x="2594.384766"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_50 -->
      <g style="fill: #333333" transform="translate(202.502656 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_range_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(58.149844 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-47" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-53" x="1957.962891"/>
       <use xlink:href="#DejaVuSans-76" x="2021.439453"/>
       <use xlink:href="#DejaVuSans-6f" x="2080.619141"/>
       <use xlink:href="#DejaVuSans-6c" x="2141.800781"/>
       <use xlink:href="#DejaVuSans-75" x="2169.583984"/>
       <use xlink:href="#DejaVuSans-6d" x="2232.962891"/>
       <use xlink:href="#DejaVuSans-65" x="2330.375"/>
       <use xlink:href="#DejaVuSans-5f" x="2391.898438"/>
       <use xlink:href="#DejaVuSans-70" x="2441.898438"/>
       <use xlink:href="#DejaVuSans-61" x="2505.375"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_mode_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(25.054687 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-64" x="2084.476562"/>
       <use xlink:href="#DejaVuSans-65" x="2147.953125"/>
       <use xlink:href="#DejaVuSans-6c" x="2209.476562"/>
       <use xlink:href="#DejaVuSans-65" x="2237.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2298.783203"/>
       <use xlink:href="#DejaVuSans-76" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-4e" x="2419.486328"/>
       <use xlink:href="#DejaVuSans-75" x="2494.291016"/>
       <use xlink:href="#DejaVuSans-6d" x="2557.669922"/>
       <use xlink:href="#DejaVuSans-62" x="2655.082031"/>
       <use xlink:href="#DejaVuSans-65" x="2718.558594"/>
       <use xlink:href="#DejaVuSans-72" x="2780.082031"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CrystalNNFingerprint_std_dev_pentagonal_pyramidal_CN_6 -->
      <g style="fill: #333333" transform="translate(7.2 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-70" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1595.044922"/>
       <use xlink:href="#DejaVuSans-6e" x="1656.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1719.947266"/>
       <use xlink:href="#DejaVuSans-61" x="1759.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1820.435547"/>
       <use xlink:href="#DejaVuSans-6f" x="1883.912109"/>
       <use xlink:href="#DejaVuSans-6e" x="1945.09375"/>
       <use xlink:href="#DejaVuSans-61" x="2008.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="2069.751953"/>
       <use xlink:href="#DejaVuSans-5f" x="2097.535156"/>
       <use xlink:href="#DejaVuSans-70" x="2147.535156"/>
       <use xlink:href="#DejaVuSans-79" x="2211.011719"/>
       <use xlink:href="#DejaVuSans-72" x="2270.191406"/>
       <use xlink:href="#DejaVuSans-61" x="2311.304688"/>
       <use xlink:href="#DejaVuSans-6d" x="2372.583984"/>
       <use xlink:href="#DejaVuSans-69" x="2469.996094"/>
       <use xlink:href="#DejaVuSans-64" x="2497.779297"/>
       <use xlink:href="#DejaVuSans-61" x="2561.255859"/>
       <use xlink:href="#DejaVuSans-6c" x="2622.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="2650.318359"/>
       <use xlink:href="#DejaVuSans-43" x="2700.318359"/>
       <use xlink:href="#DejaVuSans-4e" x="2770.142578"/>
       <use xlink:href="#DejaVuSans-5f" x="2844.947266"/>
       <use xlink:href="#DejaVuSans-36" x="2894.947266"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(140.8075 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(79.583594 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CrystalNNFingerprint_mean_wt_CN_4 -->
      <g style="fill: #333333" transform="translate(150.614375 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-34" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CrystalNNFingerprint_std_dev_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(83.063125 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6f" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-63" x="1592.75"/>
       <use xlink:href="#DejaVuSans-74" x="1647.730469"/>
       <use xlink:href="#DejaVuSans-61" x="1686.939453"/>
       <use xlink:href="#DejaVuSans-68" x="1748.21875"/>
       <use xlink:href="#DejaVuSans-65" x="1811.597656"/>
       <use xlink:href="#DejaVuSans-64" x="1873.121094"/>
       <use xlink:href="#DejaVuSans-72" x="1936.597656"/>
       <use xlink:href="#DejaVuSans-61" x="1977.710938"/>
       <use xlink:href="#DejaVuSans-6c" x="2038.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2066.773438"/>
       <use xlink:href="#DejaVuSans-43" x="2116.773438"/>
       <use xlink:href="#DejaVuSans-4e" x="2186.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2261.402344"/>
       <use xlink:href="#DejaVuSans-36" x="2311.402344"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_1_0_1_0 -->
      <g style="fill: #333333" transform="translate(73.441094 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-5f" x="2335.466797"/>
       <use xlink:href="#DejaVuSans-30" x="2385.466797"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(94.129375 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(64.326875 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(125.006406 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(140.8075 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(79.583594 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_2 -->
      <g style="fill: #333333" transform="translate(43.183594 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-32" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(52.263281 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(81.811875 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-69" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-6e" x="2035.347656"/>
       <use xlink:href="#DejaVuSans-69" x="2098.726562"/>
       <use xlink:href="#DejaVuSans-6d" x="2126.509766"/>
       <use xlink:href="#DejaVuSans-75" x="2223.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="2287.300781"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(269.88125 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(83.057031 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 415.323281 638.149 
L 521.875281 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagee2c9adb705" transform="scale(1 -1) translate(0 -578.16)" x="417.6" y="-43.2" width="101.52" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature35_fold0 -->
    <g transform="translate(159.716281 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 528.534781 638.149 
L 536.164281 638.149 
L 536.164281 27.789 
L 528.534781 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image62bd456d71" transform="scale(1 -1) translate(0 -609.84)" x="528.48" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(539.664281 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(539.664281 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(574.065219 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p7ae3398a29">
   <rect x="415.323281" y="27.789" width="106.552" height="610.36"/>
  </clipPath>
 </defs>
</svg>
