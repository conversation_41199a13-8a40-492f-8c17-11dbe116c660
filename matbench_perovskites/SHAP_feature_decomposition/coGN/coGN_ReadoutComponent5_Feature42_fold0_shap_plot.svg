<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T08:47:32.354008</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 463.695964 638.149 
L 463.695964 27.789 
" clip-path="url(#pebc933189d)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#pebc933189d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="maecca4a23a" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#maecca4a23a" x="463.695964" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(460.196589 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#maecca4a23a" x="520.330964" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(516.831589 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_31 -->
      <g style="fill: #333333" transform="translate(213.278437 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- BondOrientationParameter_mean_BOOP_Q_l=4 -->
      <g style="fill: #333333" transform="translate(95.335938 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-6d" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-65" x="1488.859375"/>
       <use xlink:href="#DejaVuSans-61" x="1550.382812"/>
       <use xlink:href="#DejaVuSans-6e" x="1611.662109"/>
       <use xlink:href="#DejaVuSans-5f" x="1675.041016"/>
       <use xlink:href="#DejaVuSans-42" x="1725.041016"/>
       <use xlink:href="#DejaVuSans-4f" x="1791.894531"/>
       <use xlink:href="#DejaVuSans-4f" x="1870.605469"/>
       <use xlink:href="#DejaVuSans-50" x="1949.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.619141"/>
       <use xlink:href="#DejaVuSans-51" x="2059.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="2138.330078"/>
       <use xlink:href="#DejaVuSans-6c" x="2188.330078"/>
       <use xlink:href="#DejaVuSans-3d" x="2216.113281"/>
       <use xlink:href="#DejaVuSans-34" x="2299.902344"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_129 -->
      <g style="fill: #333333" transform="translate(205.007187 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-39" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(102.165 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_maximum_CovalentRadius -->
      <g style="fill: #333333" transform="translate(29.212656 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-43" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-6f" x="2163.382812"/>
       <use xlink:href="#DejaVuSans-76" x="2224.564453"/>
       <use xlink:href="#DejaVuSans-61" x="2283.744141"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.023438"/>
       <use xlink:href="#DejaVuSans-65" x="2372.806641"/>
       <use xlink:href="#DejaVuSans-6e" x="2434.330078"/>
       <use xlink:href="#DejaVuSans-74" x="2497.708984"/>
       <use xlink:href="#DejaVuSans-52" x="2536.917969"/>
       <use xlink:href="#DejaVuSans-61" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-64" x="2665.429688"/>
       <use xlink:href="#DejaVuSans-69" x="2728.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2756.689453"/>
       <use xlink:href="#DejaVuSans-73" x="2820.068359"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(151.583281 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=1_23e+00 -->
      <g style="fill: #333333" transform="translate(99.345625 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-31" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-32" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-33" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(210.808437 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(151.583281 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- VoronoiFingerprint_std_dev_Voro_vol_mean -->
      <g style="fill: #333333" transform="translate(121.882344 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-76" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-6f" x="1736.763672"/>
       <use xlink:href="#DejaVuSans-6c" x="1797.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1825.728516"/>
       <use xlink:href="#DejaVuSans-6d" x="1875.728516"/>
       <use xlink:href="#DejaVuSans-65" x="1973.140625"/>
       <use xlink:href="#DejaVuSans-61" x="2034.664062"/>
       <use xlink:href="#DejaVuSans-6e" x="2095.943359"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(90.359375 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=0_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(16.423906 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-30" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(205.007187 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(213.278437 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(104.22875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(282.905625 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(161.390156 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(119.806406 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imaged5908cfb8b" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature42_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagec9b0ea99f3" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pebc933189d">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
