<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="768.781844pt" height="679.5765pt" viewBox="0 0 768.781844 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T19:53:31.413658</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 768.781844 679.5765 
L 768.781844 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 378.374844 638.149 
L 514.806844 638.149 
L 514.806844 27.789 
L 378.374844 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 446.434066 638.149 
L 446.434066 27.789 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 378.374844 609.084238 
L 514.806844 609.084238 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 378.374844 580.019476 
L 514.806844 580.019476 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 378.374844 550.954714 
L 514.806844 550.954714 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 378.374844 521.889952 
L 514.806844 521.889952 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 378.374844 492.82519 
L 514.806844 492.82519 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 378.374844 463.760429 
L 514.806844 463.760429 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 378.374844 434.695667 
L 514.806844 434.695667 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 378.374844 405.630905 
L 514.806844 405.630905 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 378.374844 376.566143 
L 514.806844 376.566143 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 378.374844 347.501381 
L 514.806844 347.501381 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 378.374844 318.436619 
L 514.806844 318.436619 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 378.374844 289.371857 
L 514.806844 289.371857 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 378.374844 260.307095 
L 514.806844 260.307095 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 378.374844 231.242333 
L 514.806844 231.242333 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 378.374844 202.177571 
L 514.806844 202.177571 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 378.374844 173.11281 
L 514.806844 173.11281 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 378.374844 144.048048 
L 514.806844 144.048048 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 378.374844 114.983286 
L 514.806844 114.983286 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 378.374844 85.918524 
L 514.806844 85.918524 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 378.374844 56.853762 
L 514.806844 56.853762 
" clip-path="url(#pab0854fe6a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m12588ca14e" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m12588ca14e" x="404.340714" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(396.232511 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m12588ca14e" x="446.434066" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(442.934691 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m12588ca14e" x="488.527417" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(485.028042 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(323.909438 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_avg_dev_Number -->
      <g style="fill: #333333" transform="translate(41.625625 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-75" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-6d" x="2146"/>
       <use xlink:href="#DejaVuSans-62" x="2243.412109"/>
       <use xlink:href="#DejaVuSans-65" x="2306.888672"/>
       <use xlink:href="#DejaVuSans-72" x="2368.412109"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(53.370313 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(103.859063 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_84 -->
      <g style="fill: #333333" transform="translate(165.554219 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- CrystalNNFingerprint_mean_trigonal_planar_CN_3 -->
      <g style="fill: #333333" transform="translate(31.436875 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-70" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6c" x="1924.878906"/>
       <use xlink:href="#DejaVuSans-61" x="1952.662109"/>
       <use xlink:href="#DejaVuSans-6e" x="2013.941406"/>
       <use xlink:href="#DejaVuSans-61" x="2077.320312"/>
       <use xlink:href="#DejaVuSans-72" x="2138.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="2179.712891"/>
       <use xlink:href="#DejaVuSans-43" x="2229.712891"/>
       <use xlink:href="#DejaVuSans-4e" x="2299.537109"/>
       <use xlink:href="#DejaVuSans-5f" x="2374.341797"/>
       <use xlink:href="#DejaVuSans-33" x="2424.341797"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(42.635156 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- CrystalNNFingerprint_mean_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(70.205312 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-4c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1480.160156"/>
       <use xlink:href="#DejaVuSans-73" x="1516.244141"/>
       <use xlink:href="#DejaVuSans-68" x="1568.34375"/>
       <use xlink:href="#DejaVuSans-61" x="1631.722656"/>
       <use xlink:href="#DejaVuSans-70" x="1693.001953"/>
       <use xlink:href="#DejaVuSans-65" x="1756.478516"/>
       <use xlink:href="#DejaVuSans-64" x="1818.001953"/>
       <use xlink:href="#DejaVuSans-5f" x="1881.478516"/>
       <use xlink:href="#DejaVuSans-43" x="1931.478516"/>
       <use xlink:href="#DejaVuSans-4e" x="2001.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="2076.107422"/>
       <use xlink:href="#DejaVuSans-32" x="2126.107422"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(103.859063 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_33 -->
      <g style="fill: #333333" transform="translate(165.554219 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(165.554219 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(88.057969 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_wt_CN_5 -->
      <g style="fill: #333333" transform="translate(113.665938 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-35" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(165.554219 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_maximum_GSbandgap -->
      <g style="fill: #333333" transform="translate(7.2 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-62" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-61" x="2298.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="2359.28125"/>
       <use xlink:href="#DejaVuSans-64" x="2422.660156"/>
       <use xlink:href="#DejaVuSans-67" x="2486.136719"/>
       <use xlink:href="#DejaVuSans-61" x="2549.613281"/>
       <use xlink:href="#DejaVuSans-70" x="2610.892578"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(42.635156 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(157.282969 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(56.504531 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(235.181406 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(113.665938 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(72.082188 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 378.374844 638.149 
L 514.806844 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image5479b5d00c" transform="scale(1 -1) translate(0 -578.16)" x="382.32" y="-43.2" width="128.88" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature101_fold0 -->
    <g transform="translate(131.599844 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-31" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 523.333844 638.149 
L 530.963344 638.149 
L 530.963344 27.789 
L 523.333844 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagea49d50ccc2" transform="scale(1 -1) translate(0 -609.84)" x="523.44" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(534.463344 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(534.463344 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(568.864281 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pab0854fe6a">
   <rect x="378.374844" y="27.789" width="136.432" height="610.36"/>
  </clipPath>
 </defs>
</svg>
