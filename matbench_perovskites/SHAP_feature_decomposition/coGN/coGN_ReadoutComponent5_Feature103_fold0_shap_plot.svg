<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="797.390062pt" height="679.5765pt" viewBox="0 0 797.390062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T20:06:56.181557</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 797.390062 679.5765 
L 797.390062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 468.060524 638.149 
L 468.060524 27.789 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#p959b4f1fb8)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m90864fc2f9" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m90864fc2f9" x="432.923793" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(424.815589 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m90864fc2f9" x="468.060524" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(464.561149 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m90864fc2f9" x="503.197255" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(499.69788 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(198.505156 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- OPSiteFingerprint_mean_q6_CN_12 -->
      <g style="fill: #333333" transform="translate(174.304844 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="923.511719"/>
       <use xlink:href="#DejaVuSans-65" x="1020.923828"/>
       <use xlink:href="#DejaVuSans-61" x="1082.447266"/>
       <use xlink:href="#DejaVuSans-6e" x="1143.726562"/>
       <use xlink:href="#DejaVuSans-5f" x="1207.105469"/>
       <use xlink:href="#DejaVuSans-71" x="1257.105469"/>
       <use xlink:href="#DejaVuSans-36" x="1320.582031"/>
       <use xlink:href="#DejaVuSans-5f" x="1384.205078"/>
       <use xlink:href="#DejaVuSans-43" x="1434.205078"/>
       <use xlink:href="#DejaVuSans-4e" x="1504.029297"/>
       <use xlink:href="#DejaVuSans-5f" x="1578.833984"/>
       <use xlink:href="#DejaVuSans-31" x="1628.833984"/>
       <use xlink:href="#DejaVuSans-32" x="1692.457031"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(147.689375 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(205.007187 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_tetrahedral_CN_4 -->
      <g style="fill: #333333" transform="translate(104.202344 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-74" x="1526.929688"/>
       <use xlink:href="#DejaVuSans-72" x="1566.138672"/>
       <use xlink:href="#DejaVuSans-61" x="1607.251953"/>
       <use xlink:href="#DejaVuSans-68" x="1668.53125"/>
       <use xlink:href="#DejaVuSans-65" x="1731.910156"/>
       <use xlink:href="#DejaVuSans-64" x="1793.433594"/>
       <use xlink:href="#DejaVuSans-72" x="1856.910156"/>
       <use xlink:href="#DejaVuSans-61" x="1898.023438"/>
       <use xlink:href="#DejaVuSans-6c" x="1959.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="1987.085938"/>
       <use xlink:href="#DejaVuSans-43" x="2037.085938"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.910156"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.714844"/>
       <use xlink:href="#DejaVuSans-34" x="2231.714844"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(183.356094 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(161.390156 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_130 -->
      <g style="fill: #333333" transform="translate(205.007187 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_122 -->
      <g style="fill: #333333" transform="translate(205.007187 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(205.007187 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(213.278437 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(90.359375 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(90.359375 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(118.191562 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(104.22875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(282.905625 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(119.806406 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(161.390156 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image205b1f3906" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature103_fold0 -->
    <g transform="translate(160.208063 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-33" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagebfc2a99e71" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p959b4f1fb8">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
