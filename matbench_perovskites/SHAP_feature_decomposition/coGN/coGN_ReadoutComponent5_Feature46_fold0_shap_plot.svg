<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="789.474531pt" height="679.5765pt" viewBox="0 0 789.474531 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T09:09:29.549293</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 789.474531 679.5765 
L 789.474531 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 423.139531 638.149 
L 523.643531 638.149 
L 523.643531 27.789 
L 423.139531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 464.934895 638.149 
L 464.934895 27.789 
" clip-path="url(#pc58922f507)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 423.139531 609.084238 
L 523.643531 609.084238 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 423.139531 580.019476 
L 523.643531 580.019476 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 423.139531 550.954714 
L 523.643531 550.954714 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 423.139531 521.889952 
L 523.643531 521.889952 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 423.139531 492.82519 
L 523.643531 492.82519 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 423.139531 463.760429 
L 523.643531 463.760429 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 423.139531 434.695667 
L 523.643531 434.695667 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 423.139531 405.630905 
L 523.643531 405.630905 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 423.139531 376.566143 
L 523.643531 376.566143 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 423.139531 347.501381 
L 523.643531 347.501381 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 423.139531 318.436619 
L 523.643531 318.436619 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 423.139531 289.371857 
L 523.643531 289.371857 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 423.139531 260.307095 
L 523.643531 260.307095 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 423.139531 231.242333 
L 523.643531 231.242333 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 423.139531 202.177571 
L 523.643531 202.177571 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 423.139531 173.11281 
L 523.643531 173.11281 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 423.139531 144.048048 
L 523.643531 144.048048 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 423.139531 114.983286 
L 523.643531 114.983286 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 423.139531 85.918524 
L 523.643531 85.918524 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 423.139531 56.853762 
L 523.643531 56.853762 
" clip-path="url(#pc58922f507)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m055e111f1b" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m055e111f1b" x="424.598904" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(416.490701 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m055e111f1b" x="464.934895" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(461.43552 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m055e111f1b" x="505.270886" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(501.771511 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.710125 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_avg_dev_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(49.407344 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-76" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-6f" x="2207.962891"/>
       <use xlink:href="#DejaVuSans-6c" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-75" x="2296.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2457.71875"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.242188"/>
       <use xlink:href="#DejaVuSans-70" x="2569.242188"/>
       <use xlink:href="#DejaVuSans-61" x="2632.71875"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- GaussianSymmFunc_std_dev_G2_20_0 -->
      <g style="fill: #333333" transform="translate(148.613594 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-32" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(115.232031 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(180.396562 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(148.62375 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mode_NUnfilled -->
      <g style="fill: #333333" transform="translate(96.962969 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_avg_dev_NpUnfilled -->
      <g style="fill: #333333" transform="translate(71.218906 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-55" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-6e" x="2219.291016"/>
       <use xlink:href="#DejaVuSans-66" x="2282.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2317.875"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2373.441406"/>
       <use xlink:href="#DejaVuSans-65" x="2401.224609"/>
       <use xlink:href="#DejaVuSans-64" x="2462.748047"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- VoronoiFingerprint_std_dev_Voro_vol_maximum -->
      <g style="fill: #333333" transform="translate(90.290312 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-76" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-6f" x="1736.763672"/>
       <use xlink:href="#DejaVuSans-6c" x="1797.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1825.728516"/>
       <use xlink:href="#DejaVuSans-6d" x="1875.728516"/>
       <use xlink:href="#DejaVuSans-61" x="1973.140625"/>
       <use xlink:href="#DejaVuSans-78" x="2034.419922"/>
       <use xlink:href="#DejaVuSans-69" x="2093.599609"/>
       <use xlink:href="#DejaVuSans-6d" x="2121.382812"/>
       <use xlink:href="#DejaVuSans-75" x="2218.794922"/>
       <use xlink:href="#DejaVuSans-6d" x="2282.173828"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(60.292812 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_31 -->
      <g style="fill: #333333" transform="translate(210.318906 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(148.613594 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(87.399844 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(202.047656 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(87.399844 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(210.318906 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(101.269219 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(279.946094 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(158.430625 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(116.846875 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 423.139531 638.149 
L 523.643531 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagea1fb41268a" transform="scale(1 -1) translate(0 -578.16)" x="425.52" y="-43.2" width="95.76" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature46_fold0 -->
    <g transform="translate(164.508531 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.925031 638.149 
L 537.554531 638.149 
L 537.554531 27.789 
L 529.925031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagef53bf28462" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(541.054531 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(541.054531 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.455469 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pc58922f507">
   <rect x="423.139531" y="27.789" width="100.504" height="610.36"/>
  </clipPath>
 </defs>
</svg>
