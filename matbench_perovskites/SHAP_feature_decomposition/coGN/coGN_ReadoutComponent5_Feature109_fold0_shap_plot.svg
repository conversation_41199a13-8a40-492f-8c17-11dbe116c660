<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="795.582531pt" height="679.5765pt" viewBox="0 0 795.582531 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T20:56:42.032842</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 795.582531 679.5765 
L 795.582531 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 423.139531 638.149 
L 523.643531 638.149 
L 523.643531 27.789 
L 423.139531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 461.730896 638.149 
L 461.730896 27.789 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 423.139531 609.084238 
L 523.643531 609.084238 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 423.139531 580.019476 
L 523.643531 580.019476 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 423.139531 550.954714 
L 523.643531 550.954714 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 423.139531 521.889952 
L 523.643531 521.889952 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 423.139531 492.82519 
L 523.643531 492.82519 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 423.139531 463.760429 
L 523.643531 463.760429 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 423.139531 434.695667 
L 523.643531 434.695667 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 423.139531 405.630905 
L 523.643531 405.630905 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 423.139531 376.566143 
L 523.643531 376.566143 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 423.139531 347.501381 
L 523.643531 347.501381 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 423.139531 318.436619 
L 523.643531 318.436619 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 423.139531 289.371857 
L 523.643531 289.371857 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 423.139531 260.307095 
L 523.643531 260.307095 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 423.139531 231.242333 
L 523.643531 231.242333 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 423.139531 202.177571 
L 523.643531 202.177571 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 423.139531 173.11281 
L 523.643531 173.11281 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 423.139531 144.048048 
L 523.643531 144.048048 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 423.139531 114.983286 
L 523.643531 114.983286 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 423.139531 85.918524 
L 523.643531 85.918524 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 423.139531 56.853762 
L 523.643531 56.853762 
" clip-path="url(#p24f4d78a8a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m9e97a6b6fa" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m9e97a6b6fa" x="429.971376" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(421.863173 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m9e97a6b6fa" x="461.730896" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(458.231521 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m9e97a6b6fa" x="493.490416" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(489.991041 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.710125 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- BondOrientationParameter_std_dev_BOOP_Q_l=1 -->
      <g style="fill: #333333" transform="translate(78.675625 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-73" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-74" x="1443.546875"/>
       <use xlink:href="#DejaVuSans-64" x="1482.755859"/>
       <use xlink:href="#DejaVuSans-5f" x="1546.232422"/>
       <use xlink:href="#DejaVuSans-64" x="1596.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1659.708984"/>
       <use xlink:href="#DejaVuSans-76" x="1721.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="1780.412109"/>
       <use xlink:href="#DejaVuSans-42" x="1830.412109"/>
       <use xlink:href="#DejaVuSans-4f" x="1897.265625"/>
       <use xlink:href="#DejaVuSans-4f" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-50" x="2054.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="2114.990234"/>
       <use xlink:href="#DejaVuSans-51" x="2164.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2243.701172"/>
       <use xlink:href="#DejaVuSans-6c" x="2293.701172"/>
       <use xlink:href="#DejaVuSans-3d" x="2321.484375"/>
       <use xlink:href="#DejaVuSans-31" x="2405.273438"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_1_0_1_0 -->
      <g style="fill: #333333" transform="translate(81.257344 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-5f" x="2335.466797"/>
       <use xlink:href="#DejaVuSans-30" x="2385.466797"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(148.62375 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(115.232031 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(80.597188 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_183 -->
      <g style="fill: #333333" transform="translate(202.047656 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-33" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(102.589531 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-38" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-30" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-30" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2d" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2157.714844"/>
       <use xlink:href="#DejaVuSans-31" x="2221.337891"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(202.047656 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- VoronoiFingerprint_std_dev_Voro_vol_sum -->
      <g style="fill: #333333" transform="translate(128.11625 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-76" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-6f" x="1736.763672"/>
       <use xlink:href="#DejaVuSans-6c" x="1797.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1825.728516"/>
       <use xlink:href="#DejaVuSans-73" x="1875.728516"/>
       <use xlink:href="#DejaVuSans-75" x="1927.828125"/>
       <use xlink:href="#DejaVuSans-6d" x="1991.207031"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(31.977188 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(20.9475 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(24.693125 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(148.613594 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(87.399844 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(210.318906 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(101.269219 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(279.946094 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(158.430625 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(116.846875 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 423.139531 638.149 
L 523.643531 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image17d8d0b2ac" transform="scale(1 -1) translate(0 -578.16)" x="425.52" y="-43.2" width="95.76" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature109_fold0 -->
    <g transform="translate(158.400531 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-39" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.925031 638.149 
L 537.554531 638.149 
L 537.554531 27.789 
L 529.925031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image914ed892a7" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(541.054531 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(541.054531 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.455469 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p24f4d78a8a">
   <rect x="423.139531" y="27.789" width="100.504" height="610.36"/>
  </clipPath>
 </defs>
</svg>
