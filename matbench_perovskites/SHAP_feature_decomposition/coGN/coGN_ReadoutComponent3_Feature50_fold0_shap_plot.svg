<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T16:06:53.900851</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 485.213615 638.149 
L 485.213615 27.789 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#p7d5e293b89)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m44b23c0048" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m44b23c0048" x="452.664299" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.25 -->
      <g style="fill: #333333" transform="translate(435.809377 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-32" x="179.199219"/>
       <use xlink:href="#DejaVuSans-35" x="242.822266"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m44b23c0048" x="485.213615" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(472.967521 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m44b23c0048" x="517.762931" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(505.516837 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(53.959375 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(106.288437 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_range_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(34.898125 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-64" x="2091.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2155.130859"/>
       <use xlink:href="#DejaVuSans-6c" x="2216.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2244.4375"/>
       <use xlink:href="#DejaVuSans-65" x="2305.960938"/>
       <use xlink:href="#DejaVuSans-76" x="2367.484375"/>
       <use xlink:href="#DejaVuSans-4e" x="2426.664062"/>
       <use xlink:href="#DejaVuSans-75" x="2501.46875"/>
       <use xlink:href="#DejaVuSans-6d" x="2564.847656"/>
       <use xlink:href="#DejaVuSans-62" x="2662.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2725.736328"/>
       <use xlink:href="#DejaVuSans-72" x="2787.259766"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(151.583281 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_101 -->
      <g style="fill: #333333" transform="translate(205.007187 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(102.165 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(210.808437 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- CrystalNNFingerprint_mean_wt_CN_5 -->
      <g style="fill: #333333" transform="translate(161.390156 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-35" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(75.102656 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(48.728906 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- Miedema_Miedema_deltaH_ss_min -->
      <g style="fill: #333333" transform="translate(176.421406 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-69" x="86.279297"/>
       <use xlink:href="#DejaVuSans-65" x="114.0625"/>
       <use xlink:href="#DejaVuSans-64" x="175.585938"/>
       <use xlink:href="#DejaVuSans-65" x="239.0625"/>
       <use xlink:href="#DejaVuSans-6d" x="300.585938"/>
       <use xlink:href="#DejaVuSans-61" x="397.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="459.277344"/>
       <use xlink:href="#DejaVuSans-4d" x="509.277344"/>
       <use xlink:href="#DejaVuSans-69" x="595.556641"/>
       <use xlink:href="#DejaVuSans-65" x="623.339844"/>
       <use xlink:href="#DejaVuSans-64" x="684.863281"/>
       <use xlink:href="#DejaVuSans-65" x="748.339844"/>
       <use xlink:href="#DejaVuSans-6d" x="809.863281"/>
       <use xlink:href="#DejaVuSans-61" x="907.275391"/>
       <use xlink:href="#DejaVuSans-5f" x="968.554688"/>
       <use xlink:href="#DejaVuSans-64" x="1018.554688"/>
       <use xlink:href="#DejaVuSans-65" x="1082.03125"/>
       <use xlink:href="#DejaVuSans-6c" x="1143.554688"/>
       <use xlink:href="#DejaVuSans-74" x="1171.337891"/>
       <use xlink:href="#DejaVuSans-61" x="1210.546875"/>
       <use xlink:href="#DejaVuSans-48" x="1271.826172"/>
       <use xlink:href="#DejaVuSans-5f" x="1347.021484"/>
       <use xlink:href="#DejaVuSans-73" x="1397.021484"/>
       <use xlink:href="#DejaVuSans-73" x="1449.121094"/>
       <use xlink:href="#DejaVuSans-5f" x="1501.220703"/>
       <use xlink:href="#DejaVuSans-6d" x="1551.220703"/>
       <use xlink:href="#DejaVuSans-69" x="1648.632812"/>
       <use xlink:href="#DejaVuSans-6e" x="1676.416016"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(47.794531 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(282.129687 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(83.556719 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(90.359375 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- GlobalSymmetryFeatures_crystal_system -->
      <g style="fill: #333333" transform="translate(134.205938 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-63" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-72" x="1372.376953"/>
       <use xlink:href="#DejaVuSans-79" x="1413.490234"/>
       <use xlink:href="#DejaVuSans-73" x="1472.669922"/>
       <use xlink:href="#DejaVuSans-74" x="1524.769531"/>
       <use xlink:href="#DejaVuSans-61" x="1563.978516"/>
       <use xlink:href="#DejaVuSans-6c" x="1625.257812"/>
       <use xlink:href="#DejaVuSans-5f" x="1653.041016"/>
       <use xlink:href="#DejaVuSans-73" x="1703.041016"/>
       <use xlink:href="#DejaVuSans-79" x="1755.140625"/>
       <use xlink:href="#DejaVuSans-73" x="1814.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1866.419922"/>
       <use xlink:href="#DejaVuSans-65" x="1905.628906"/>
       <use xlink:href="#DejaVuSans-6d" x="1967.152344"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(11.010625 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(61.607031 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imaged4295f04c7" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature50_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagecc5af53342" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p7d5e293b89">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
