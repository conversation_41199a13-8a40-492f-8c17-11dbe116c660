<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="785.946156pt" height="679.5765pt" viewBox="0 0 785.946156 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T04:02:44.452784</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 785.946156 679.5765 
L 785.946156 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
L 522.851156 27.789 
L 416.875156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 460.009955 638.149 
L 460.009955 27.789 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 416.875156 609.084238 
L 522.851156 609.084238 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 416.875156 580.019476 
L 522.851156 580.019476 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 416.875156 550.954714 
L 522.851156 550.954714 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 416.875156 521.889952 
L 522.851156 521.889952 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 416.875156 492.82519 
L 522.851156 492.82519 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 416.875156 463.760429 
L 522.851156 463.760429 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 416.875156 434.695667 
L 522.851156 434.695667 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 416.875156 405.630905 
L 522.851156 405.630905 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 416.875156 376.566143 
L 522.851156 376.566143 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 416.875156 347.501381 
L 522.851156 347.501381 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 416.875156 318.436619 
L 522.851156 318.436619 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 416.875156 289.371857 
L 522.851156 289.371857 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 416.875156 260.307095 
L 522.851156 260.307095 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 416.875156 231.242333 
L 522.851156 231.242333 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 416.875156 202.177571 
L 522.851156 202.177571 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 416.875156 173.11281 
L 522.851156 173.11281 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 416.875156 144.048048 
L 522.851156 144.048048 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 416.875156 114.983286 
L 522.851156 114.983286 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 416.875156 85.918524 
L 522.851156 85.918524 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 416.875156 56.853762 
L 522.851156 56.853762 
" clip-path="url(#p11aa5e49db)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m26acabd8c5" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m26acabd8c5" x="460.009955" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(456.51058 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m26acabd8c5" x="508.947912" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(505.448537 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(347.18175 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(83.36375 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-69" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-6e" x="2035.347656"/>
       <use xlink:href="#DejaVuSans-69" x="2098.726562"/>
       <use xlink:href="#DejaVuSans-6d" x="2126.509766"/>
       <use xlink:href="#DejaVuSans-75" x="2223.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="2287.300781"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- XRDPowderPattern_xrd_36 -->
      <g style="fill: #333333" transform="translate(220.503594 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-36" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- OPSiteFingerprint_mean_rectangular_see-saw-like_CN_4 -->
      <g style="fill: #333333" transform="translate(28.662187 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="923.511719"/>
       <use xlink:href="#DejaVuSans-65" x="1020.923828"/>
       <use xlink:href="#DejaVuSans-61" x="1082.447266"/>
       <use xlink:href="#DejaVuSans-6e" x="1143.726562"/>
       <use xlink:href="#DejaVuSans-5f" x="1207.105469"/>
       <use xlink:href="#DejaVuSans-72" x="1257.105469"/>
       <use xlink:href="#DejaVuSans-65" x="1295.96875"/>
       <use xlink:href="#DejaVuSans-63" x="1357.492188"/>
       <use xlink:href="#DejaVuSans-74" x="1412.472656"/>
       <use xlink:href="#DejaVuSans-61" x="1451.681641"/>
       <use xlink:href="#DejaVuSans-6e" x="1512.960938"/>
       <use xlink:href="#DejaVuSans-67" x="1576.339844"/>
       <use xlink:href="#DejaVuSans-75" x="1639.816406"/>
       <use xlink:href="#DejaVuSans-6c" x="1703.195312"/>
       <use xlink:href="#DejaVuSans-61" x="1730.978516"/>
       <use xlink:href="#DejaVuSans-72" x="1792.257812"/>
       <use xlink:href="#DejaVuSans-5f" x="1833.371094"/>
       <use xlink:href="#DejaVuSans-73" x="1883.371094"/>
       <use xlink:href="#DejaVuSans-65" x="1935.470703"/>
       <use xlink:href="#DejaVuSans-65" x="1996.994141"/>
       <use xlink:href="#DejaVuSans-2d" x="2058.517578"/>
       <use xlink:href="#DejaVuSans-73" x="2094.601562"/>
       <use xlink:href="#DejaVuSans-61" x="2146.701172"/>
       <use xlink:href="#DejaVuSans-77" x="2207.980469"/>
       <use xlink:href="#DejaVuSans-2d" x="2289.767578"/>
       <use xlink:href="#DejaVuSans-6c" x="2325.851562"/>
       <use xlink:href="#DejaVuSans-69" x="2353.634766"/>
       <use xlink:href="#DejaVuSans-6b" x="2381.417969"/>
       <use xlink:href="#DejaVuSans-65" x="2435.703125"/>
       <use xlink:href="#DejaVuSans-5f" x="2497.226562"/>
       <use xlink:href="#DejaVuSans-43" x="2547.226562"/>
       <use xlink:href="#DejaVuSans-4e" x="2617.050781"/>
       <use xlink:href="#DejaVuSans-5f" x="2691.855469"/>
       <use xlink:href="#DejaVuSans-34" x="2741.855469"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=4_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(7.2 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-34" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(90.700625 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(18.42875 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- XRDPowderPattern_xrd_40 -->
      <g style="fill: #333333" transform="translate(220.503594 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-34" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-30" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(97.064531 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(142.359375 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- AverageBondLength_std_dev_Average_bond_length -->
      <g style="fill: #333333" transform="translate(57.692812 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-73" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-74" x="1114.298828"/>
       <use xlink:href="#DejaVuSans-64" x="1153.507812"/>
       <use xlink:href="#DejaVuSans-5f" x="1216.984375"/>
       <use xlink:href="#DejaVuSans-64" x="1266.984375"/>
       <use xlink:href="#DejaVuSans-65" x="1330.460938"/>
       <use xlink:href="#DejaVuSans-76" x="1391.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1451.164062"/>
       <use xlink:href="#DejaVuSans-41" x="1501.164062"/>
       <use xlink:href="#DejaVuSans-76" x="1563.697266"/>
       <use xlink:href="#DejaVuSans-65" x="1622.876953"/>
       <use xlink:href="#DejaVuSans-72" x="1684.400391"/>
       <use xlink:href="#DejaVuSans-61" x="1725.513672"/>
       <use xlink:href="#DejaVuSans-67" x="1786.792969"/>
       <use xlink:href="#DejaVuSans-65" x="1850.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1911.792969"/>
       <use xlink:href="#DejaVuSans-62" x="1961.792969"/>
       <use xlink:href="#DejaVuSans-6f" x="2025.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="2086.451172"/>
       <use xlink:href="#DejaVuSans-64" x="2149.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="2213.306641"/>
       <use xlink:href="#DejaVuSans-6c" x="2263.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2291.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2352.613281"/>
       <use xlink:href="#DejaVuSans-67" x="2415.992188"/>
       <use xlink:href="#DejaVuSans-74" x="2479.46875"/>
       <use xlink:href="#DejaVuSans-68" x="2518.677734"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(93.875469 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(142.359375 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_76 -->
      <g style="fill: #333333" transform="translate(204.054531 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_avg_dev_MeltingT -->
      <g style="fill: #333333" transform="translate(76.382344 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-74" x="2183.402344"/>
       <use xlink:href="#DejaVuSans-69" x="2222.611328"/>
       <use xlink:href="#DejaVuSans-6e" x="2250.394531"/>
       <use xlink:href="#DejaVuSans-67" x="2313.773438"/>
       <use xlink:href="#DejaVuSans-54" x="2377.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(81.135469 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(84.608906 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(95.004844 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(273.681719 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(152.16625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(110.5825 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagef8ef4f8277" transform="scale(1 -1) translate(0 -578.16)" x="419.04" y="-43.2" width="101.52" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature13_fold0 -->
    <g transform="translate(160.980156 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-33" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.474656 638.149 
L 537.104156 638.149 
L 537.104156 27.789 
L 529.474656 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image309759bd62" transform="scale(1 -1) translate(0 -609.84)" x="529.2" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.604156 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.604156 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.005094 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p11aa5e49db">
   <rect x="416.875156" y="27.789" width="105.976" height="610.36"/>
  </clipPath>
 </defs>
</svg>
