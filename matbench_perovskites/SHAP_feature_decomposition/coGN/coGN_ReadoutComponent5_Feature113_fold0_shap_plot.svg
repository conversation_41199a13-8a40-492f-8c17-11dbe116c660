<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="792.054156pt" height="679.5765pt" viewBox="0 0 792.054156 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T21:44:32.704915</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 792.054156 679.5765 
L 792.054156 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
L 522.851156 27.789 
L 416.875156 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 463.241806 638.149 
L 463.241806 27.789 
" clip-path="url(#p8922073836)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 416.875156 609.084238 
L 522.851156 609.084238 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 416.875156 580.019476 
L 522.851156 580.019476 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 416.875156 550.954714 
L 522.851156 550.954714 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 416.875156 521.889952 
L 522.851156 521.889952 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 416.875156 492.82519 
L 522.851156 492.82519 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 416.875156 463.760429 
L 522.851156 463.760429 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 416.875156 434.695667 
L 522.851156 434.695667 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 416.875156 405.630905 
L 522.851156 405.630905 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 416.875156 376.566143 
L 522.851156 376.566143 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 416.875156 347.501381 
L 522.851156 347.501381 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 416.875156 318.436619 
L 522.851156 318.436619 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 416.875156 289.371857 
L 522.851156 289.371857 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 416.875156 260.307095 
L 522.851156 260.307095 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 416.875156 231.242333 
L 522.851156 231.242333 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 416.875156 202.177571 
L 522.851156 202.177571 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 416.875156 173.11281 
L 522.851156 173.11281 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 416.875156 144.048048 
L 522.851156 144.048048 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 416.875156 114.983286 
L 522.851156 114.983286 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 416.875156 85.918524 
L 522.851156 85.918524 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 416.875156 56.853762 
L 522.851156 56.853762 
" clip-path="url(#p8922073836)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m21479082cf" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m21479082cf" x="463.241806" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(459.742431 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m21479082cf" x="519.721805" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(516.22243 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(347.18175 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- XRDPowderPattern_xrd_21 -->
      <g style="fill: #333333" transform="translate(220.503594 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-31" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(81.135469 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mode_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(26.606562 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-64" x="2084.476562"/>
       <use xlink:href="#DejaVuSans-65" x="2147.953125"/>
       <use xlink:href="#DejaVuSans-6c" x="2209.476562"/>
       <use xlink:href="#DejaVuSans-65" x="2237.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2298.783203"/>
       <use xlink:href="#DejaVuSans-76" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-4e" x="2419.486328"/>
       <use xlink:href="#DejaVuSans-75" x="2494.291016"/>
       <use xlink:href="#DejaVuSans-6d" x="2557.669922"/>
       <use xlink:href="#DejaVuSans-62" x="2655.082031"/>
       <use xlink:href="#DejaVuSans-65" x="2718.558594"/>
       <use xlink:href="#DejaVuSans-72" x="2780.082031"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(142.359375 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- GaussianSymmFunc_std_dev_G2_4_0 -->
      <g style="fill: #333333" transform="translate(150.620469 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-34" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(75.236719 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(25.712812 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(195.783281 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(204.054531 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=2_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(7.2 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-32" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(108.967656 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(189.675312 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(97.064531 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(81.135469 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(142.359375 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(14.683125 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(95.004844 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(273.681719 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(152.16625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(110.5825 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 416.875156 638.149 
L 522.851156 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAI0AAAMjCAYAAACYoJOmAABaTklEQVR4nO2deXwdZb3/38/MnP1kT5s06d6yFFqgUBbZBEUEFJSKoF531KsXlys3Lj8XVAT1aq77xlUR9KKIUNywirIja9naQgvd1yTNfpKzzvL8/jhJmrXtpKc5mfPMm1deTZ6ZM+c7w2e+z/fZvo+QUqISl13x4rOWYSyLmNmf3nXXCR8rtj1eRKgkmrPfu03uLosjhcBwHBZ39ci//+Zordh2eQ1lHtjrr3wpkw4GOa+jm8ta2jmtO8He8jJx+eVr315s27yGMqJJRSPBU3sS7DMM1pTFMIFTevvIBI1fFds2r2EU24CpoqE/yX3VVdRYFjU5k82REBHbob43XWzTPIcyniadszkl0U+l7dAZDnJ0KkOVZRFOZottmudQxtP0R6Pi2RkV7ImEAXiyuoKzOrqxYqEiW+Y9lPE07WXhIcEM8nR1BXYwWCSLvIsyokmEx4ojp2nkhC6KYI6nUaZ6CmqgSUmlZTMzZ5IwdGzHoSGZ9EXjEmU8TUoIXr+rjZAj6dIEZabN63e3s7x1ny8alyjjacJZixeq43z7T3/i1dt30BUOc/NppxJJmsU2zXMo42lsBDf+4z7O3Lmb9vIK4qZJ08OPYAWF72lcooynsQ2deakMN7/u9eQCAQKmyXnr1zKnr7vYpnkOJTzNz46+W+ghjftPPIlcIACAGQhw/wknkdECRbbOeyjhaT5y+blfnSXB1kferq3r9EWjOOItsk3UUy2T80Lylp1FMtMzlLynEc2WsIPG57vKo1ijwhcHaGzvQUNSL1to0yq3F8VIj1HyosF2biAcIhU0eHjeTJyBYgmEu/uY19kFgACiZERWvH9JsUz1CqUvGtjDwDyzd/zlGTZrAbYEg5Rv6eBVj29HDGtxO0IDSBbDSC9R8qKRnwn+mFSaaDrHot09nLKpjVn7koRzDhLIDoR1DpBzgk5I3uzHNAdBiUCYcOhVGcnjt75xBTtnVaNJyRNL57Jiw26ueO5+zIRBGzPsWpmMFNtUL6DMHOEZTZ1ySV+OeX39VGZzpAMB9kbDWL093HvXCX4HnwvU8DRAmYAT9nXQ2N2DLiW2EJRXVtBr+pOw3FLyMc0gWV1jdlc3+oBn1aVkZk8vYct3Mm5RRjSzevvH3KwmJbFMVo36uYAoI5o9kchQH80gDrAr5g8juEUZ0aRCAe5b0DD0twTumzeL9lhstJZ8DoIygbCwbP65cDbP19fS2JdkV3mctC1Yuru12KZ5DmU8jTAdtL4sGaGzvaIckXNI2w59AcOPaVyijKdp6EtyTCBItW0Ple0JGERNf+aeW5QRTYVpy0rNHtG+bsiZZB17oo/4TIAy1VNdNjemTAhBWJ1HUDCUeWIV6RSaPdKrhLI5srbvadyijGh2xGI7Ttm0nepEPwHTYkZPgpM3bWNpb7Kq2LZ5DWUGLAHe+ea1zqk79wrDtskaBo/On51bddfx/mJulyglGoB3rVy/QiI/VZmzrv7hX5b3F9seL6KcaHwOH2ViGp/C4YvGxzW+aHxc44vGxzXKDCMA2OJKW0Nq+VVOEom4VZN3vLfYdnkNZTyNLa7YqyE0gY5AI/+vfE+x7fIiyohGIOoFo+cDa9hi5YPFsMfLKCMaEDiAREdi4AzcugkrimuX91BGNBIQGORvWSDQkehFtsqbKCMagSYGFt8O/NgIBNtFXJlnUCgUemASgY2AgR8bcPjQO68JH+SDPqNQRjTjL4lz2DmzwV8t5xJlRDOebATQ2Ns19aZ4HGVEk281jSSrGeyqrCmKPV5GIdHIMb5GCsHuqtqi2ONllBCNFG/T84HvSMK2yeIOf7GcW5QQjY3zn3KcW+0JRtgbjBXBIm+jhGgk9ifznXnasDLYXj6TtPQTALiltEe5xcoYcKuDaBjsB5Zo/GHxifx82TkkgmEcWyA+1esg6SWgzZVfL+srttnTndL1NGJlFOiU8JYAmnCERAL3HHUS7RXViJDOo3MWD5yLQFCJ5STE5/r9yPgglK5o4AYgNOhMDWmzp7yGFxrnseqEFRyTaOc/Xn4sf6YDaANtK8v+RlGs9RClXD0dM/yPtBHiY296J388/mQA/n7MMo5qb83vHpYbkaJm2RTa6ElK2dPclP8nL4htVbVDghlk04x6NEPkn8LgUh5NNE+hjZ6kdEUjV/0JuE3gINEJW+Nn8XQMHYRgoLv4cfmN8t9PoZWepHRFAyBXvRMwTHLtC7o7OGbf3vwd6wPeBQmGANv5EQHdkN+qOLO4BnuDUo5p8shVdkCsbEvpwRlbZtTlvQrk/9UFSIlsrvxocY30FqXtaYYwzrcMA8sY+47MTCWKYI+3UUI0OqJTOg7CGZvI8+h9LUWwyNsoIRrk72S5meWKF57Y30qSkoBpclT7nuLa5kFKP6YZQAC/uf0nLGvZye0nncncnk6+cu/vufTd/1Vs0zyHMqlGHPE2qQ2srBxsX6+tm8vHL30HD/5suT/l0wVqVE/kJ5LLgd9AQ6Ixt7uTi15co8ZbU0CUqZ4k+sBap/xfIKjI5ZjXutsXjUuUEQ0gh3XSDBW+edu6dJHs8SzKVE/juxNHqbemUCgjmvzif5v98pGAM7Dy0scNCr1oDvl3JD/BfLAdpaG/rohGeRJlPI2EJcO9zMB/UpO/e6yohnkQZUSjy9+97CBOkdhSInFw+jV5uzL3X0iU6dzzKRz+m+bjGl80Pq7xRePjGl80Pq5RqJ8GLHFFSiMaAZDkHF3e7ifdmwTKtJ5y4oq0Tll4ZPdvTmryNt/bukSZB2browUD/cG4P4QwCZQRTWu8YkxZ1MzhiKv8XCMuUUY0dX0djB7r1qQJ2OVFMcjDKBMIh50cAhNJiPxwpUk+n7BU5sUpFMqIJp872ERgDpVJBD1EM/52ue5QRjT5LFjGgKfRyHsamzj4SYxcopBr1pFEYWh/hBAQRic5NkL2OSBKiWZsAmoB6Mp420Kh2AOTgDXwr0FeNLY/sdwlCokmh8Aa2PVpcMlclDRB6XfUuEOh6skcEgwMtqbShNH88SeXKCMaMe4iFomNaU25MR5HmQFLR6yUYqDZDQ4SC4ng/ed/wo5aFolYhD7Hsu559SkbnWDwDmCNbDLuLbLZ05KSj2lEs2Xc8MOvvXCtHiFkBxhckttJNdtp5LETjtI3z54BQDhn6q9ft/XE1ScffeLAZx3gctlk/Kl4dzD9UKF6+vBjr77guICtI0ih0YcgQS17MZB87jePD+WsyQQD/OPExZzz4vbBFbwa4CduHIUKonltTTqFThaw+OdRy7jt5HPojIaoYy9z2vto7NjfKWzpGjP7UsM/HxTNVtlUGz2dKfnqCXglmk3RH9B549Wf5+FFxwEQzWX41c0/J7pdozcWGjpZk5JUcMRjkbLJ8IcahqGCp/n2kueetm459bwhwQCkgmG+eOlb+P2rl9AfzYtGOJLlW1t4cMn84Z//5ZRa6wFK3tPIJqNN8KWyax/885ie3811M6iek2HZjjYCtk19R6+8f8k8MuGADWwFPiKbjPun3urpjTJN7r8s+Zq89OpPjyh7/cvP88eff3tGSP6mo0hmeRIVqicA3rDxOT79wB8JWvn5NMt3b+XHd/2MAOb4+e99JkQZT+OId0iNDN2RGN2RGAu79g3uoDtLk7/3N7J0QcnHNPvRkASoTCepSieRaEjCQNLfj9AlColGDkzCcpA45OfXQAb0aFHt8h4KiUYb9u/g75IwJItkkGdRJhDOT74ajQQITrEhnkch0TgwYkN3CWSAQHdx7PEuClVPOnlvk2F/msYIkqwfCLtEIdFAfgXCYG2UF46AcqC3eDZ5D4WqpyyDc2n2r0rIkUX4gnGJQp5GR9A/0DcjEFhIcoTR+ottmddQRjQ2tm2ArpGfK5PvBw8j5G/Gbjfnc0CUqZ4C8i7DQZMOYRxCA8tzMz8qtl1eRJmxJ5/CoYyn8Skcvmh8XOOLxsc1vmh8XKNMk3uQt75p7bVx0/pgTyT05rvvOv7lYtvjRZRqPZ3/7q1OOBgQASlJaxrx9u7s3X9YFi62XV5Dmerpsje90FVhGCLiSGyhUWY7pGsqQ9ee+6ifCcslylRPXTWVlXY4yCvxCFJKpK5xfCLJszMq2gDf27hAGU9Tns6wPRQgnjEpy1qUp3JsiEUoz/lTI9yijGj6hUbQHhm/xbIWASxlnkGhUOaBdcXGTh8XwKwOf5DbLcqIplw6QoxqKYYdh2OS/rxytygTCAdNm9O72nnN2s2UpbK0VJfxwPKjWLp7R7FN8xzKeJp+y+Ktj64FobF3RiWzuvq4/F9rqU/7y7jdooynOWVvB/evOJatjflUabptc8FTG/jr0cezpMi2eQ1lPE1veWxIMAC2rvPYCYvYWRujRXxRmedQCJR5WFtnjt1rpS8W4bh9u8mS8TNduUAJ0Yiv9Pcv2902prymt49/W/84NrbfwecCJUSDpoVt6XDiKzvR7Pw88sr+JK999kUcIZFE1HgOBaJkR7nFl9KnIHgcXQRO2N7Khx55gcpUhmMTu9F1SUNPd34FlLCwpEk12/8elKsuKrbdXqAk3zDxpfRRaDyFrgWq+9I0/eNpKjM5arN9zO9rZ3HPbuIkCJNESEEIB4nxeilWfqPYtnuBkhQN8B40TQN40/OvDN1kzMwSIYmBiYZDAJMwSUDgEEVCU/FM9g6lKpqh3cAcsf8WbU2iMXJtnI6Dg4bE34zlUClV0fwKx5EAf1u2EGdAQslAZNyTTcJopBHw3aky0MuUciB8GoJ/oQvj2D0dvH3NBgK2w0W7n2ZJR8vQeS3hKoyMpJaN/4rIu84uosmeoWRFMxzxpX4rFDL0bNAgmsvy7489ykl7dvNU/RzKO20+9PSj9nx5ozJDKoeLGg/KsU3Nye8gl9IDfOf0V4MmwLK5+ZY/YJPxkwC4oFRjmhHIr1ZEFu/uBNOGhAkZG1IWwZTJUR37CGCZB7+KzyBKiAYg2pWkZl8fuuNwbFsn8zt7yRkGv11xAlFCYwemfCZEjeoJCEuHimSGT933FLWpDAAbZ1axrSZOrfxmrsjmeQplRNPQm+TVW/cMCQbg2H3dLOjpBE4rnmEeRJnqqSybY15vYkx5fcKfI+wWZUSjG5KIObYW0qS/w7JblBGN7ViyJx4jaO8XSczM0hXxhw/cooxoJJA1bFKxELlwAF2XHNW7m46wv52GW5QRTTxnc9txS+kwbGKZTrIixXdOO4P1M2b6HXsuUab19Ny8eVz+wiZuW7GcnkiYxt4+rnr+FVYvbvSDGpcoI5qy/kRP56zqqs/d9zQBy6EvGuTvpx6P1HJzi22b11BiwHKQC6/aKAOxCJUZk7ZYGK27x7r3zqX+pHKXKBPTANz7u2NFOt37le3C3CnTXSt8wUwOpTyNT2FQytP4FAZfND6u8UXj4xpfND6uUaafBmD5OzY+bsZiZySDBo4QLGjptB6863i/BeUSpVpPZ7x7i3z/Ey9S0Z8hEwpw5/Kj2OdY2578w0kLi22bl1Cmelp+xYvZ/3jweWZ0JQnmbMr7Mrz7Xy8iKsrnF9s2r6GMaE5IJLWy1Mj5NIbtcPKefUWyyLsoIxpd0xivIi43/UFutygjmvaATkdN2YiydDjAxsaaIlnkXZRpPVkBg19fcDJvfHojlf1J0qEQjx+/gM3l/rYIblHG03QYBgtbOijLprEDGkHHZMnOFuKWOq3HQqGMaEQowMnb9owom9nbx+xefzWCW5QRTX2in4AzNugt708VwRpvo4xoeg2DlrKRk8htIdhRHiuSRd5FGdGgaTw7r4F0KAhAztB5cXY9vaFQkQ3zHsq0nvbOrBDndvTx0NJj2Kvr9AcN5mRyVGfS4uCf9hmOMqKZkUyxLxZhV87iVVt3Yuoa/zxmDovwNeOWkh6wbBdXfzBO300WcWES5qHaM7AJDMnEFoLvnHcSb9reGvrsU+f5mSMOkZL1NJ3i/RtiyGMldegIdCAgbRyxfyaELiUfeOJFHpw/oxvwI+JDpGQD4Up6j5XEGJYdlpA9dl1cPGfSYEp/ba4LSlY0AGLU7TVm2seck4kEMPWSfgwFp2SflkRDYo8om5dpRYYcLF3DNDQS5SH6YiFkNle6gd0RoGRFs48FVwXoQJDPwSiBXy47g47Kcrpro/TURMlGAjw/eyZWItFaXGu9RcmKpkF+846g/J3I0dFh0SN/sex4Pv66i3loziwsIXCA52fW8NCiOWw599TGYtvrJUq6yT2cU9+xyVrTMEM3HMnMXI6MEHSFgpzV1cujN8/zO2tcULJN7tEEzRzHpDPMz1oY5KurvZZNQ4+/mbtbSrZ6Go0dCsijU7mht0QAjaZNNOdPqHGLMqKxhIYzTtO6L+wPWLpFGdEIaZEdJ3LZW+H367lFGdFolmR9KMjgJggOsDlokBF+DOwWZQLhTDBABng4FqbMcUgLjZwmOM62D/pZn5Eo42kCAYM3tuwj4Dj06jqWgHPbu6gy/Q1Y3KKMp7FNU85Ppfnolp20hYJUmRYx2+bx6spim+Y5lPE05X1ZdpaXEZCS2ZksMdumOxSksa2n2KZ5DmU8TZlAvFxTRXc4RHUmQ38gwN54jKN2+MNOblFGNDnT7Jy7r6tu58xq9sXyzeyK/jStlePvoOszMcpUT/f+eXm9wOTkzbuZ1dnL0u0tHNXSip42VxbbNq+hjKcB2FlVVdYTzSYM6YjtdWUYffqdf7tz2R+KbZfXUGaU26dwKFM9+RQOXzQ+rvFF4+MaXzQ+rlGq9QTwy8W/bq1O2jO7IyECRu4t//bKe+4utk1eQ6nW0z01Nzkn9+wSESeHg2BfqIKnGmbd+O6t7/tCsW3zEkpVTyf27hERJ79kW0NSn+1hUXvP54psludQpnq6d9bPjVPsLA8ceyJr5y4knklz4fpnqOnP+LOwXKJM9XT7nN+Ge2eVpzfMmT9UJqTkosef4vUt7/eF4wJlqqd5/XuNDbPnjSiTQvDwsqVFssi7KCOardX1DuPMB84E/U1Y3KKMaGZYnTKQHZu3qKKntwjWeBtlAuGkjNgLN++kOm0yf28H2aDBptl1dMSDxTbNcyjjaTqrIsGjWvYxf3c7feEg0oGTXtlFXV93sU3zHMqI5oNvvrwvaDq8OG8mO+oq2dxYzZZZVczuSBTbNM+hRPUkvpR4xDAddlVVUNWRJZSxsQ1BojKIsNXocigkanga0znDioWJJWzCGRsBGJakqiNLRlfivSkoajwxQ2jhpEkoO3JvBAFUpvqKY5OHUcPTSEEOic7Y7J4NZhtfPONef8DSBSUvGvHFfpucozk5BxkZuQQ3KE0qnD7KejuuK5J5nqSkqydxXd+zZGyNoA4VAapSFtJKogsTSxoEkHQwixky6XcLu6C0PY3QTkICQY1wxiRuZVjMdhbLXRzDNqrppp8y0rKK2+t+vrbY5nqF0haNMzCEr+tkNI0aOtHJB8MCqKIXUxOUy34ilvk/RbTUU5S2aHR9NiENMiY12QwhxqYV0QybXeXlvKnzI7cWwUJPUtKikV+OtGDo52PZsisWoSVeNuacPXVVWGmZKYJ5nqWkRQMgvxJ7kKAuZZ/Fj087m+ywzrwNM+bQUhXjiy++xc8C4IKSbj0NYTkSS/L47HncdsYF1Pd2kYjE6I5EOW3zumJb5znUEI2UDqCf39qOaQTYVVMH5N1sIlJVVNO8iBqiCYXCCNOOmBZ1O7up7ujHDOjsXlBDbyhebOs8R8nHNADyyxEH2+5ZtnYXM3f34khJIGNy9Nq9iP6xQws+B0YNTwM8cOstc3rTM/raGiNDmcvDKYvqLn8zd7coI5odlQ22CIxMdZ+JGuPNNfc5CEpUTwBBK6uZQX1MeaI8XARrvI0youmoKndyobGOtbvCD4Tdooxo5rR0yZfnNWAPq5764hFsv35yjTIxTVt5rSGFxgMrjqcykSQdChCxHWb09BTbNM+hjKfRy+zUjroaojmTmGVRlc7SGY/hR8LuUUY0H3jycmfJ7hbaKsrZ2FjPplkzMXWd5Zs2+9uwuESZ6gngjJfW/rAylfroKw31xLJZzl33krx813uVegaFQJlUI8N5JvL9UG2mPzdPfk69my8ASorG5/BQJqbxKRy+aHxc44vGxzVKtRxEsyWAy4D7ZJPRX2x7vIoygbBotrYB84cVWbLJ8BfJTQIlqifRbIUZKRgAQzRb/rKVSaCEaICOCcrfNaVWlAiqiCY0bqnjjFvsc2BUEc0ECDHjv1r89J4uUaX1NPRyhNMmCzv6MGzJtuoYXdVV5cU0zIuoIhoAYlmLc3d1UJ020YClvUnua6wG/CmfblBDNLYNuk7W0Fh99CwipsVZOzuZ1Z9lWUcfUFlsCz2FUjGNNTDVMx0weGReLbaAuOWve3KLUqIZTtbQ6QkH2V4e5ex3PPuGYtvjJdQQjRhnTqeUJASsnVVFIqr/vghWeRY1YprxEILlLftoLwth634yYTeo4WmcUeNrUoKU3HbCQk7d1cbMREqN51Ag1HhY2kD1JGVeQBKQ0FUe4/cnLCKA9JckuKDkRSOaLXGgZSrZgMH8/g5tQ+gz/5hCszxNyYsGOOgSlcZUF78968QLniq74d6pMMjrlLRoRLMVAQ7oaQAW9HQgheTo5NYLbm/4XUk/k0JQ6g/o4JOspMOFm9ZSZuYQ+fTCY1NL+IygpEUjm4yRO4AJAdqANAZ+6vt7ieZyvDSjkb2hGbvftveqscmGfUZQ0qIZYB6jp7QKMfTTWlHNl865jOv+8pfsC1UnH1scE72FEnOExbdMOVFcE0+lqOjtkbv/Z64KL1BBUONBSSnHeBtAd2y+cO/vcaTwp/C5QA3RDPCxh+/huNZdzOzr4fxX1rK2uYmVG9aQ0UKl724LiBpjLlKCpvGPo0/gE/9azZk7XuGElp1srp5J0LKIZHttmFlsKz2DGqLJDxyIjfVz+MhbPgRARaqfax++h48/+lcqsvZPi2uet1AjEP7vrI2uj6mKL1v3JA2JHn5y9xv8sScXqOFpJngxHli8lHQgxE+m2Byvo1QgPJq+cIwT9uwsthmeQw3RjO6jGZgaETJzfOCRp4pikpdRQzSjA7eBIYT3/esJblt+cukHdQVGlZhmXGHctfwkue+GajVenAKixgMzxn832sv9FPeTQQ3RCLFy3BaUprVNvTHeR4l+GgDRbDnko5khZJPh989MAjU8DSCbDA34IpAC/uQLZvIo42l8CocynsancPii8XGNLxof1/ii8XGNGj3CAzyu/6ii3Onp1gZGo0wse5n8klLPoBAo5WnKnN6eHmpFNzPoZAYZavS14rpcse3yGsq8ZU+KH0ccqgkgEeS7GRwMJOV+1nKXKORpUucbA4LRsdFw0ICcn6TRNcqIxsKaqWMTJ02ULDEyhMmiIfmb8Su/d9gFyojGwO4Kkxsx+BTARhc2jfZWP1ujC5QRTZpQbLybFVJga7rvaVygjGj+dvpJv+6NjN0iIWGEyUk/abkblGk91eRs7aU5DegaZIMBanr7ifelMLImFclUsc3zFMqIRncc0VsZH5pknoyE0WssXrPuSfZE6zihyPZ5CWWqp5aq+JhVCbahgw4Rx+/fc4MyommLxcaUadKhKmcxN+OvfXKDMqKZ0ZdCt0bmbDx23zaiTh85baygfCamJGOaqutTYsWOdrsmawoB9AQNzjdzzNneTseccvpCcSp6kkR26nRTTlegrNgme4qSFM2JO9vto/rTIqtrCKA6mUEKwbajZpEN55vd+yJBNAnaKzDL2iweET+bJRGfBvZW0/9HA2cB8OSx8tqeIt7KtKTk5gjXfq7bevXOTv2Zhip2VsYREo5u72Hl+i2U2SMTXglHcsa/XmGevYkWFiAYWrFLFUlEfhL6O4+V195dhFuZtpRcTBPWNX3jjDJ2VJUhhcDRBBvrqmiLR8ecm09u71BLK13kRxIE+YeSIggQBX66UXw7OGU34AFKTjRCOuyLjjNybRiM9qkz2ns5xl5HkDSGMbK32NqfTngmMOcImOpZSi6msTWdeCpLRzwyojxm2aQiEeZ07sGwLeZ07eXsHf8iQpZuarEsk+HvUGB/dvwWwG+TD6PkPE3KkVZjb5JYdn+HXWUqg+04OJpGa+VMztv2CK/bdj8RJ0uGOG3MoXrgUQzGNBFyAL3AB46V1/oJqYdRcoEwwOJP7nOWdCZEb0AjYNvMSOWIp01evXsLm+rnoTsOZ7y8lmN27MEkSkLTSDnlVRLxMZB7aui/V0cuAJ49Vl6bLPb9TDdKUjTiv01x9L5eu8K0hSahN6jLC1/aLpr+8WdsOx+7DGwAhS7SJGRYHie/UHJe90hRcjENgPxMQELtCBFc86bd0nRCo3bLEOQIo5deLX1EUeZpleUsJGPnWtkyhOnPwXKFMqIJmBZ7K6pGlEkgqQfYF/WTG7mhJKun8QjlcmxonE20L0eZncFGpzMYJUiKirQ/CcsNyogmnMpwzJa99NtRkuR7h50cLKSNbi1ykE/7DEeZ6olsxhYZDY39e4RpQA+VhMkW1zaPoYxozlr/4tWCsZlhTQx69IriGOVRlBFNFHvreD1SIZJ0aTF/vycXKCOafsJRoY1cEyew6SPO6zIfVya2KwTKiMZBLg5JC1OToFugW6RFgLjwF1e6RRnR5CKVt9XJTspkhpQTwXQCzJbtE+7Q4jMxyojmtakP9vQQp152cqLcxBK5HQGk8Ttp3KJUXZ4lEupAZiAodCyypHOnyq/6SxFcUpKj3D5HFmWqJ5/C4YvGxzW+aHxc44vGxzVKtZ72iWvLgvT2BHBEgkBqlvxffyLNJFCm9dQmrjkjjvW4QwSJQMPGJisr5E2+t3WJMg8sQu4xixAhEsTpQGCiERS7xUfOLLZtXkMZ0UjCooxuApgIIEo/AguBdV+xbfMayojGQRszrTxKP2k/+bRrlBGNHLOSG0DgYI9T7nMglGk9RcgM/GaTX4egARox/Hx7blHI0wBksXFIE8IhSz43hC8at5S8p8mJfxcpIo5FlF7K2Mc8JBo6JvNZT7b0H0HBKXlP049hdTOTHEHaWIAcuGWbADs4HgPd37rHJSUvGpuYqKST8ZyqRbD0H8ARoOR9s45FOb1Ixu5/oGET9mMa15S8aDQsJBCjn0Z2EcDCwCJNBBMdB3/1iltKXjQ2BiZhJDox0kPlMVKATdLv3HNNyVfpYmADwjyjO/h0/rzkHP3tb13vXPKGp66Zatu8Ssl7mvx+lRYGOQQSBw2TEKBhCY2nZjfw55MWiUX7en444+O7PtT+/TknAvzoxNXLY73pJ5BOUAC2bvzz/Vvf/Lri3s30oOQ9DVgYZId2yNVw8ilgybJ21iLO3NxOxtBZO2cGFYHQCaLZCgHEelNPSU0GpS5wdIGQ1gW/XPiH/1fUW5kmlLxoNGwxeqBSADo5QjJNojJONJtP3rmlvoqy/tT/fevsB2eDHOmFhUDY9rVTY/X0puRFYzDestu811nSvhVHF/RF8gnJDdsBxHogIcYd3xR+pk8UEI3Als6YSRH57HuWZmCJ7NDmYae+spvEl8u+8qlHz0tYAaN9xHVsiRTiqikye1pT8qKRCBw0bHTAAfZLaHtFI7e+aikB0+K8ddvk48fNG0rK1zuzvM4yAg8JhxxSdNuBwBvet+3yJ4tyE9OMkp8jnBAfdfqoFjH6iNGLNjCzxkbDwORv9UudW489M33HA2f7k8wPkZIXTae41umlTpTTThWdMKKqcuhDtyvlL0q+66GQlHz1lGVwy57BbHvD0ZD+MIJrSv4Ns9CQgMnYLZschC+ZSVDynia/W5wgTZQM+1O/SiBLjCx+phG3lLynMdhnJikPxsmQIY5DAA0biyAOBg79xTbRc5S8p5klfxqqYY9joqEhsQiRI4qDAThYWP5eTi4pedEAzJDf0SvoppXqoRgnS4AeIgP9Nz5uUEI0AB3UEKGfMnZRwVbCdNBHJcFx10P5HIiSj2n2I5nHS0ON7kr2AZLOcVpVPgdGGU9TS9uYXpoK2jH8OcKuUUY0AcbGuzYGDuMc8DkgyogmRdS2R9XGNlEWyJvGLlPwOSDKiKZOfs/opkGaxLAIk6aaTip8LzMJlBENQK38rtZF1UfamLFBx9Aa5Pf8KHgSlPwot0/hUcrT+BQGXzQ+rvFF4+MaJUVT9dnEbHFD1t/BfZIoFQgf8x97d+yurZibChoELZu5nX3mph/U+S0olyjjaU67pjWwbUbl3JSmQc4mJ2HzjPLAwo/s/d9i2+Y1lBFNwrSSJhAyberSOSKmDZZEBvUPFNs2r6HMKLela/qCvjQnd/UPrYBaVxmjNRgqtmmeQxnRVFsOc7r7h6ZcacCyniQi7uencYsyook4ElsInquK0RUOUpazOLG7n1kZf2qEW5QRTbcOL9ZU0hnON5Z6QwG6wgEWtPcW2TLvoUwgnAoYQ4IZURbxYxq3KCOakDX+HggR218u5xZlRPNfDz6uHdXeC1ISNm0CtkN1Jod0fNG4RZmY5rjt+7hm9dPcdsHJOLqOlJL67gQbayr84QSXKOFp7qj4wUvbambwz1ctwdHzjW4hBG3VFdSaFuJrWWVenkKghGjKpThGj0hMHCpTqRHHsobB/L1dfrvbBUq8YfFcShy/9WXO2vESQdtm7awGvnTh6+mJxumJBYlZtl9FuaBkR7n/VP7rhvrkpt2zxD6xuWwZS3taRhy/f+EiPvmGS8mFdGTEoCse+ieadi9wq2wy9hXHam9QktXTg+Lm8Ozczj0xxxA7KhZRNs4a/zN27QQgmLVJBXRmdiUvAL4JvCCarcaptdhblKRoeqqMdYuymynTkrSE68mGx9Y+Oyorh37XLYkZGer4qwc+MhV2epWSFA0ajRJBZ6QakDTmtpEI7R+YzOg63znzHCCf3CiCQ2Bkf039VJrrNUoyEA6mrDdnCf8dGcDUDKoyPXQT41OXXsr8nj7+tGgxrWVlCAEdlRHO7EiwPR6CsqFMWXcV0/7pTkmK5pLU++/9U5XoOLPn8dotNVW0huuoSZjcffwSksEgFTmLpCHIGgandPRi2ZLdecHsAb4hm4zVRb6FaU3Jtp4GWSfe15asWDRzYW+WS6/+ABXCQJP5jOUtkSCV6RwbK2O0Nlf5ze5DpDRjmmEsk7+sM9I4rfE4EWHQY+h0BAN0BgxqMybdYQPNzJX2m1NgSrJ6Gs3usnLbqqzUkoaOHNgHwRGCXkPHdgSOZd1XZBM9hRKiycZmxlYtX5aTozbxkULQpyNbf9zob/7lgpKvngCu2vE2c3dZZEx2PSkllh/JuEYJ0QDsjsRktzZSIV26ToUlfdm4RInqCSBm23JvKCT6NUlISrJCYAINWdsPgl2ijKfJaIJK2yYnBH2aRk4IZtg2mvRn7rlFGU9ja4Jl6QwZodGva1TYNoaEjdFAsU3zHMqIJqgJQhJC0qFi2DiTGVDmERQMZaqnieJd049oXKOMaBI6MqFro8o0ug1/bwS3KCOa+kz2zkcr4+wIBUjoGjtCAf5VEWdmOuenhXVJyQ9YDqe6qVt2B43BbbkJ2w7pb/pLWNyijKcB6GquEsd39bXWZ3PymL5U6vzWDqXuv1Ao5Wl8CoP/pvm4xheNj2t80fi4xheNj2uU6UMXzdZMpGwh/6JIhDhFNhnPFdsuL6KMaJCyjcGZe1IKpHwWxuxQ6HMIKFE9iW+ae0cW5LUi/tvvDZ4MSogGKesYNT8YIUD4m3JPBjVEIyaohsRoJfkcCmqIxg9dCooiovEpJIqIxh9fKySKiMavngqJGv00g4HwiBF9X0iTRQ3RQF4ww5OWC+nXWpNEkeqJ/YIZdDASEBLxpf72IlnkWdTxNIL8K7J/KAFsQcixa4pplhdRx9MMFwz532NmmmX7douvn/BzfzjBBeqIZpzO34bOFA/979d5fetO44eLf2luK/vPLyFWNhTBOk9R8qIRzdaE4e7OWJTfNF7Cye3rOSHTYdw351Vf7ie0xxFX/B2xsuSfzWQp6Qcjmq31IwqGN7lth6wluf7155IhyKltz5EJlfNs7TloOBcCV02ttd6hpEUDHJf/RwwEvhJyDmQdsICyMLtqyugIVROysggkPZHo4GcvLpLN055SF01P/p+BPhmHkX0zQlBpmVRnk2ypXIAUGmFzKCZ+eioN9RKl3uRuANJAvsk9TnRzYnsbL9fP4+kZp6A5OZbvexIHsUND/nxKLfUQJe1pZJORkU3G/mbTOCMHIhznwQWn0W848pwtD+6sdhL/piEXIFelp9BUT1HqnmY/QoAhYXiPjIA3rXsALZVzPr72QzpcXjTzvIQ6ogHQNAgOjjlJQPDzk8+R63+8yJ/26QK1RAODc4PzmpHwYl2d3xvskpKOaQ6J4LA9fXwOCeVFI/9fwJ8g4RI1RONIyeiUKgPVk4971IlpbECT++MZB3/y3iRRQzSD4hidZ9oXzaRQo3qCgfk0B/jb55BRw9NAvqmts3+kWwhw/BT3k0EN0eQTCw6s+h/hXvxQeBKoUT3pujm29SQHxeTjEjVEA/lJMoMakQNDCbpeUTyTvIsSopFNhoMQXx2qmoQATfxKNhn9xbXMm/h5hH1co4Sn8Sksvmh8XOOLxsc1vmh8XKNG594AF739pfcJR94kkM//9XdLTyu2PV5FmdbTWe940cYxtLayGHWJJFXpFK/e26F9as3r1HgABUQJT3PBuzYuSgRj2vpZtQBsqa2kMpWhNtFvosgzKCRKPLCdYePFzTUjM4r0RMPkQhF/QvkkUCIQbqupHHfz7VzA18xkUMLTrNjTxb5AiL5gEA2oSGboCxrMTfrr4SaDEp6mJpkhGQqRCwbIBAO0VZVRkbPIRELFNs2TKCGabRXlZAIjnWpLVRm5jFUki7yNEqLpiAXHlAkpCVj2OGf7HAwlRBO0LGb0JUeUndDSQUtdWZEs8jZKBMJRyxLzslmWt7bTHoswu7eP49u7eKVxRrFN8yRKiKYiY3HG1j1U9o9sLS1q6yqSRd5GieqpJRYklsmRDofoLYth6fn+mWDO4k8Vt/kLWVyihKdpSGXYuKCRgJa/XeFIGlraaNzdycLERgv8HebcoISn6S+PiUHBAEhNsK+2hkVtrTTQp7WJjzvtYuVNRTTRU5S8p7ns8rXp2tqxiw7MUICkCBGnlQCmyBH/kCNWfmiwrhr41wY2AX8EvopclRxzIQUpaU9z0od2PbosnQv3hsb205T1pLDMMK00IpCE6MciimDEal0dOBb4DHDrFJk97Slp0Rzf2nVWOhrmpB17qerpRTj5qTOGaXHs+l0ApBnKG4zOAZNiXY5YWX0EzfUMJV09WVr+nVjU3k3AdliyZTdVfSnKk2l6QhEAamkbOt9BP5BwHEbuGKUsJe1pNtVV3x1KZQBBfUcvS3a0Ut+VIJo1aUgkmM8OaugA8gsuLcZWY8O4FbmqdwrMnvaUtGievalx5bPxyJ495TFmdo39/50JlCEJIgmQo5IQidEJsjLAfcAngQ9PkdnTnpKungD+fufxs89/18vyVWUxahKpEcc6I+VkzHIshEwRmV0nb95bJDM9RcmLBuCY1k756JJ54qi9HegDE+kdIdheX0NtYmZumbwh5A9dHjpKiKYqmWX9vHqePXoOs7oSIKGtupz+QJBv/9t7w78stoEeQwnRtFeX8boNuwgnLXbWViM1QTBjUt6f4pf/t9hfwuISJURjBwPUtfUSNC0i6f1N6vbqeBGt8i4l3XoapEc3pBhnUaD0x7cnhRKiEVKSioxcxSIBKf1EjZNBieopYpo8ubCOzrIYp2xtpT8UIOTY1Cf88cfJoIRoOmJh7jjzOCxd5/dnHw9AWTrL9b99sLiGeRQlqqdEPDo0W2+QvkiIdYv9LbgngxKiifWPrYYClk2kCLaUAkqIpqonZZ2zZf8IgZCSizbsIG2Mu8Tb5yAoEdOc1NlXn3acrpP2dNAWjzC3u4/KrMkLsbC/xHISKJPU6IMXPmnNAj3kONhCsCMc4hd/Xu731EwCJaongJ/de7qhJ5KL+rLm7mQ691lfMJNHGU/jUziU8TQ+hcMXjY9rfNH4uMYXjY9rlOinGeSqi57+S13AeENtKk06YLCtLObc/vvj/XXcLlHK08zRxBsa+pMEHYeKbI4TOru1N7xlXabYdnkNZURz5XmP3l+THqkPTcIsKQ+42MlnLMqIJmfop9va2P483Xb8Tj6XKCMaw8zJrnB4RFnaMOgO+IOWblEmEDY1Q5vRk+D+2bN4pTxOzLQ4u7WdkOMvz3aLMqIxpM0TNVU09id57e4WOsMhVs+exXG9fcU2zXMoUz2Zmm7PyWZZ1t1LQErq0xnetXk7nYbf4naLMp4mYptiYSZLZzTCzqoKArbNws5uZmVyxTbNcygjmmDWYltNFU/PbWRwf+4ttdUcs7ulyJZ5D2Wqp+PaEqGNM2uHBAOQNQy2VY3Nx+dzYJQRjdQ1MZgZawRC432vf+qsqbfIuygjmnLpEM1kCVk2cxP9zEv0EbYs1s6oYsa+7vuLbZ+XUCKm6RCfeC1zzqLctlnS1o4xMFtxbqKfzKwZ9FaVKfEcCkVJPSwpVj4BnDaQAk3YhOhlJimqecOul2FLObvr60AIdkfD7I2GqUlnCEs/FYAbSmaOsBQrszCYaVGQIc5OlgIaGs5QbuDdNZW85QNvpSsWBkeC6bCoo4fNP230hXOIlFBMI4aNVut0MAeJPkIwALM7ezh30/Z8K0rXIKizpaaS+de03jXVFnuV0hCNWKkN5uTMC0RiEp7w9AUd3fv/0AQI0IV4/RG1sYQoDdHIVc5+uQAIovQM+3skjy2aM+yzEt1xsHT97CNsZclQGqIBQN4CgyKxqGUXMbpwEEPCsYXg5rNP4cmF+0VjmDbnbtkpd3yv9vkpNtizlEwgPIgUK28FLpAwSwJpdNnPfG1LZDa/e805mLEIG6vK6AwHqUnnWLqzDaMv7Xzn0bP9kctDpKSa3ABCrnoP7N9JJT7w85uFd1jVybTeEQ5xfFd+OoSwHRzLIZzoK6035whTQtXTgbGjUV6Y3YCeyRLvS2JksuyORclFgrTGw6mDX8FnkJLzNBMRylqUZTK8PKtuRHltT4LumdX+ljwuUMbTbIkGnCseeYaQuT+P8IKWdiqyOX76hxP9PDUuUMbTpEMBs622MnDdb+9h4+w64uksUgj+cfKSYpvmOZQRTX8gIO5bvphN8xrQyTfN91SUsSMaKrZpnkMZ0VT2pdGSKaxQkMG6aEY6w4uVsaLa5UWUiWleqS0nOLpPSgiO7k4UxyAPo4xoauT4SynLcn4M7BZlRJNC0xx75MI44Tiki2SPl1FGNLlgwFlfU4nI5QjmTILZLK2hAHvL/O173KJMIFwr9Mt2BoP3PTGngZBpYeoajqaRs7L+EIJLlPE0t/3t1PsTWn7AOxswcDQNRzpYlvU/xbbNa5TcKPfBWPbRFlvqhuYIZLwvedNTv5j/kWLb5DWUE43P4aNM9eRTOHzR+LjGF42Pa3zR+LhGmX4agE36Zy7YMLv+XitsCC1jynQ4NP/tL1+zs9h2eQ1lWk/bxZdn3n9CQ9tbNjxHzMyR1XUemH8M52/ZqcXk99R4CAVCGU/zyIKa1ss2rOXBhmWkjTCd8SCXbnmGv86ebb5VoedQCJR5WGYkwJ7QXFbs2EdfKERFNsutK87ltL0v+XGdS5QRzcL2JE/OmcOXL3wNLeVlLG1p45t/+Rt7y/xMWG5R5i2zhOSjl7+RlvIyANbPquPjl7+RuJUtsmXeQxnRPDu7EXPUhu6ba2uY3dNTHIM8jDKiyWpj09nHsxlMW5kaumAoI5oLdmzgspefG1H2pYf/RMA2/ea2S5R5zWqSSe644+esXnwSG2rrOX/7K6xo3cxeUe1nwHKJEqLR/1+3/VQ4ImQqzEWbX+aizS8DYBEhqFm+aFxS8tWT3tRh65GQ9oeTlgOj9aER9Csn15S8aKK6oZmhAH9ZugzIr6y0MAYy8UmEU1TzPEnJiyZZHgZN8PzsOTwy92j6qCZFJf1UkyZGLqhEDV1QSnrAsvGj+z67d3bF1wO2JGhaXPX0i2ysq2NeVxcffuwxjm1vwxQ9NMg9AAhwyDujB4HtwDnARuCPwO3IVQfOYyPeVgaYyNtHbpYprgwBIeQdJbGcs6RFU/vRfU4gHBDtNVGO2dWNo+3v3CvLZLjzVzcx29yANiKdo0ZeN+M9F7EO5InIVSMP5sVyM7ASyAI/AZqQt0vElV8BrgViwF+BdyPv6CrgbU45JVs91X60/aGwECIb1NEcOUIwAH3hMGvnxUcJBvJiEYwNmgHEMuDT4xy4AbiC/POMkBfJexFXXg5cRz6DmwDeAHznMG5rWlCyoonBOQYgNY0FrT1oztiItzI73laEBxINgHj3OIUXTVA2UbmnKVnR5CBrAWWpHNmgztkv7RhxfH5bF2fsenmcT+ZbVeNXTwBy4ziFWycom6jc05Rs0yGti2WGIzdVmTbJSIDFLR0c1dLJ9sZyFna1M7etizuPO5W3vfSkm8tK4KPjlH8JOJt8NQSwA/g+kAHeCxw7UJ4BPj+Z+5lOlHQgHPt4+/ejkcDHKjIW0azFzXfcynHde4aOP9E4lzP2vECEJDCiQhrvoTwDvAa5avztdcXb6oHLgRRwF/L2/nz5lRHyAXIV8AfkHbsP/86KS0mLZtbH2/WumXHLDOicvm079930wxHHJdAbgobM9/2hBBeUbEwD0PL9GbYlJVITLN+zZ8xxAWwvq5l6wzxOSYsGQEtaaJZDW7xi1CY+YKFRk/TTGrml5EVTlUp+1EhkeTFaxdrqeVjoQ+NPPzj1PLbU+Z7GLSUd0wzn3y552Ll76QnireteZH53Dw8tnM9Dixbw4z/8ho+s+4Af07igZJvcozm5ZSe/OXkFvzpl+f5CR7JgX7sab00BUUY0XbEyjmvbx5f/eT/LWltZ09jIdRe+Fg0/+bRblKme7qn/vjOnPy1mJvcPVO8pj9MdQF7Q8amSj+0KiTKe5oT2NgLOyNttTPRTLXJFssi7KPOGCWGPUyoxlXltCocyommpiKFjjigzyLFlZlWRLPIuyojmziVLCZAhSAqDLEFSSM1iZ0W5GkFdAVFGNCe/vGn9V157Af3hAAFydMQjvP+KKwnackWxbfMayrSeAK4/6bfWXaedoveFw1SkU3zi3nsz793x8Uix7fIaSonGpzAoUz35FA5fND6u8UXj4xpfND6uUao/VPxXTwBNPozkb7K56ivFtserKNN6Ep/s3Ck0fY7UNQQS6ThgGAH5jTJ/E0uXKONpRMCYIw0dhMgvNXAcyFlZQD/IR31GoYRotP/qWiiDQYIZE+HkZwqbAQNb1/yYbhIoIRppOceF7CyO3L9+Us9ZCN2f5TkZlBAN8GFHgi3AFAIhJUEJuq1GPFdo1HDPkgstAY7IexYpBFkx8RJ/nwOjhmjIC2UEgwGxj2vUEI1AZ5yuBd/TTA4lRBNEYEgYLhxDSoIavOfN//L7aVyihGhMQwhHQEhKwo5D1HaosR0ijsOH16zWP3zxfeNNIPaZACVEI4UgLCVVEqokVAARCfNMh09e+lHO27leiedQKNR4WAFDxAf6aDQpmWlZNFoWxkBH36plr+bKyx/7ebHN9ApqiEY66EBO1whrEJZgCYEtBJrj0B2KcdW6Ne8vtpleoeRFE/hk5yKA9ooInRURtlTFeb62jIyhgRDYmsainjakJsQl79oydn8fnzGU5Ci3aLYM4BOkMl8w+s1KKxpk9PtRlTE5OpHPTXPW1vX0BOO82DAXEwjAF5746awbp9xwj1CqnuYnOE4zOVlpmRLGaRslDY3BtvaFW9bwg7/+iFO3byMgBKYQN5z94ZbxEjL6UIKiEc1WOfBesjakLYK6BrmxqolYDjkBx7Tv5LVbniNqZ3jLuscQ5AfkLPA9zQSUnGjIz4/RBhN0OlJCziKY29+HF7Js6lNZhJTc9IfvYkhnoNwcnqRalcFc15ScaGST0Q38gaAOAQ1byw8W6Kkci7v7WdSdZHFPioAjee3WFwhb+fXdptB5bM5SbE3DBgz4VvHuYnpTqm/Tu9H1VkL6B2XKCgCkNY0OG+rsfFXVD5T1tbMvXEXaiPDP+afylxNPzIc/Uv5FwPXFM396U5Ktp+Fon+3+sMzJn+S3PBgYopQShCAKLMvkeOvaB9Bsh+dnL2m89ffL9xbTXi9QctXTaJxvVP2USACGz+wUAgHMHYhz9pbVkiOEL5hDo+RFA4Am8j/DiNsO5Y7DzL5usDU2xGueL45x3kMN0VgW2CO37unTNZK5JIu6WtAk3PKP85ZP8GmfUSgimvEDt22xCp5tPJpYpsvf/tQFSohGy9lSH60bKQlIhwtffkJ++ZEr/LVPLlBCNA6CiOMwKBwhJWEpyegan3/oLUo8g0JSqv00IzGwM1LXIraDhsQBsrqG6ctlUqghGgtpBQT9mj60WI6B9U8+7lHlXVuHEPllKwP/+kweRUQjv4ojR6xGQEp/3dMkUUI0huQZIF8vOTL/4ytm0ighGvP7tbsZJ0GE7jubSaGEaAACppnC0PLDCbqGrguExsxi2+VFSn6Uezih/+p+lZDyfgd2575dfVSx7fEqSonGpzAoUz35FA5fND6u8UXj4xpfND6uUWPsaRiPRb5tNWQSugbsjZTLR045Rf/UI6/2WwMuUKr19Ky40a4np+kDfXoS6CIkj5Of9z2uC5R6WNXCHhIM5FOPVJDzRy9dopRoBldSDkf3RxJco5RoLDF2Vqel1iMoCEo9MU3mcIbl9JSA5Xsa1yjVekoRI0OMCBk0HJJECJMptlmeQynRSDRi9FFDx8BftaQJF9ssz6GUaCrpIkyGDFF0JNV0kcLPmOYWpUQjkEhChIdSYxkE/ZjGNcoEwoHPdu/I6JExqe014M9lt/l9NS5QwtOEPtWx9diMnBuRY4NegcNx/Vt0wE93f4goIZp6iwUx20EjTZgUBkkcDDJUkSVIluwrwMJi2+kVlBBN2IHWcICr3v5O2uPlXLL5eb700N2UWXvYyukESC4oto1eouQGLEWzVUVf9nkMbQ6GRtg0xenb9vHQ7LoRi+Q++fADXLZ+E1kRQU86lKU3tZ8uv+1PND8ESioQFt8yX0vW6iIenEskIOoyWfGfT27k8braEYJZvquNFVuS7I000BmuYl9NDWZ0wYxXxOf8uOYQKCnRYNmrCOpDArnold3otiQ3uOZJSqKWzQUbd4z5aDIaIk2F/oL4rl9VHYSSEY1otjQcyod7lIZEamjfyqBlc0JPimU9KSrMsQ5FALqQ6NiXTpXNXqVkRCObDAfBnuHrtXdXxPJqCGrMTWaJDKRQWzevYcznZ/V34kiwMH49VTZ7lZIRDQBB40IylhwUzuqjZ5MKGoTIJ2YcZHtdDX86bSl9sQAz090s6thLNJNFJ5U4SX6iu0jWe4ZSbD1ppMyPIeX/YNtiVn9a9EXCYlbaojqd413PPcQbN6zB1HXaotUs3dPCDlFLTtoXrZDX/b3Y9nuBkhPNeBz90Ta5qyLG9Q/dy7X/umfEsV4i9BKVC+Q3S8vrHkGU6Nyrz+bkrH2muOCV58ccMwSkZPjHU2+Vd1Hi7dpq6L+ry+RY3Nk55pguHTRiHyuCWZ5FCdHs/knD258rC//HHqN+xEQICewNzGQJTxbLNE+iREwzyP2x/5XVdjfzsy0AbA810kucV2c+4k+NcIESMc0gjiOImIIe6gCI5xy6Qn7eabcoUT0NUkaWkLN/a0JDOlSMM8fG58AoJZqgHDt8EHbMIljibZQSTTowtjbuDwSLYIm3UUo0MSvFvkgcycDi/3CUiEwV2yzPoVQgHDFN4kaGHVW1gKAx3Uko61dPblFKNL3EWJTdy4xsAgAH2Ko3Mre4ZnkOpUQTthPmK/qcQJmWQpOShIwStxP+BmEuUapzD2CduKErhl0lkCQxskvlF/x1uS5RTjQ+h49SrSefwuCLxsc1vmh8XOOLxsc1SjW5RbNVhmX1gBDYkupk9gOdX6u4udh2eQ1lWk+i2QriONn8uqiB7U+lJNyb3pW+scLv33OBStVTekgwgoF/NTJlkTlFtstzqCQakd8xd+ivPJo/ac8t6ohmvGpY5MtFs+UrxwUKiWb84jdseAFSGT9bhAvUEQ1yXG/THYqwvGOvQs/h8FHoYQmwxg5ob47XcPa2TUWwx7so1E8jIWtTkUlx/X2/5/wtL7JhRiNfP/USttXUFts4T6GQaICQzh2/+A4XblkPwLLWXZy7bQPvveLDRTbMWyhUPUFDsmdIMIPU9/cyq6+Xr55xZ8NdC350TpFM8xRKeRpTathCoI8KiGuTSd60ZcOeqnSK20/4JevjNeYNj13mL1OYAKU8TXu0nN8sP3tE2aaaej79z0dY3NFOTTLJZeue48I9mwP/c9Ldby6OldMfT3ga0Wz9HbgA6AOqZJMxuQEzXfD+Kz/CmtmLeM3m9bxUN5vvnHMJv/zNbzl/8/4W1PJdO/jjUUtWodhLdahMywFL0WwFARN4H/CLcU75jWwy/s3VNb+elhjGiNSwg7zl+ef41W37U+1lNYM7lyyVf12w+Ki7/3zulvGud9mVLwSXJrLya387Tbk1MNNGNKLZOh14DPdvdwJ4p2wy/jzONS8FbtNyuTJHahAa37Ee09bK33/yI2YkkwCsq5nHGz94NZ+//VF+ff4yqpwe1iyYT28ogtadoSIo6ImHkICQIDWByHcdtqGJcuAu4GOyyeg9wP2GgG8B7wb6kfwBOBMpj9dtB1sXQYQACZolEcmcNLKW9aruBB2hoPFKLCJy8SCEdPYPwgKCncAF8tOBI9b5NC1EI5qtGNAFTDb4tIC4bDKyw645F9iKlHq4N00mGIDoBHs7SUlZJk3QsqlP9HD1Y4/wi7POItat84XfPsrbP3M5V657ml+eeTaVvWl6ysZZwDD0P23Ik/2fbDLeNZHBotn6OvDZ/Pfvt2PoWsM9opRgw9EtPVRmczxVWZ4XS2TU/ewXTqv8dGDWRN99uEyXOvs8Ji8YyMdmbxtV9kYGNsPNoYHpoDkTLHGS0BeK0BmL8+Ks2TRdfhVXPP8EG+fUEsuaLN/cyvq6BpCSvugBzBRi+FDFWw5i88GOj7yuADuksyMyINjgOClS5ND59eJbVtUhX98l00U0bQW4xujYI39Nma9CkLBiZyfh3MHHJh1NY3NNA7M6+wDoLI9QnUqCEOjOIXvm1oMcd33Pui2HciEznh2DniZffRyxRerTQjSyyVgDPHUYl9gsm4xHR5X9CdiEBpqef8A5KbD0cW55nIkRhuPwqTsf54ljGtncUEVYWuBIIr2ZCaZZDHiZfLUiga8cxOYbGLnHlDlUJUlGfoeUCNOhz4Hj+pIYjoSMNfF0D7hLfmp/VV1opkVMAyCarQDwbeAaxv3fOAYJdAC/Aj4rm4wxLmQgVvosOfP/BXKOXpXI0lEfx9FGCkezbRyxvyxkmvz05lt4YNFJ7GiM0R+L8MycecQypsw5iGjaxI4FsXWNUM4kFzCcVCjwdzTxIFAD3C2bjCcO4Z6XAm8nHwjfDrwGKZdWpnN1Ecs6KW3otRlND2A5/Zmcsy6esxILkplEtW3VbyyLntEeC0WdaCCMEBpSmmjaJgQ3yU8HfnAIz2/STBvRTIRotrKMjHckYMgmw9UabPHNnKP3ZMUxvX28tGDkDj2RZBZb0wg4DslgAITgY/94UH7/vtdNC0883Zj2nXuyyQgV6lrHJRJ0VUaHXVxS05uiM54vyw0U647N/LaWI+bevY46b5Km4YQD7Kkq218mBJqhEc2M7J87eXML165/d2SKLfQM097TFJLd5dExZR2xMK99/kX2VtYihcbRLXvk2557dCF8sggWegN1RCMlvfrYzj1hSxqS+/jHbScNBN8NwKlTa5vHUKd6AsbME5aScDLHX44/oXgmeRClPM3wrQoBEII4DhGnYLG2EqjlacYZ4bYCOg196SIY413UEs04VGRMusL+JD03qCWaUR2ZQkrqu5Lo2Z7DGcJQjmnfI1woxDdzDhlbEDZAEwjbJtqZIaUJnG+U+8tyXaBOIAz5Ea2ONOgCaUuSCAj7u7C4RZ3qSdM6CBsQC+RHrzQgakB4kvONFUYZ0cgmYyZSOkQNqI1ATQSiAWoy5thNun0OiDIxzSCi2QqRs66uT2d/1/LVirGbWvocFOVE43P4KFM9+RQOXzQ+rvFF4+Maz/fTDMwt3gh8UDYZ9xfbHhXwdCAsmi0LGN47J2WT4XvPI4xnH7BotuYzUjAAQjRb/p7JRxjPigaYaK2yP2R9hPF8TOMW0WyZ5D1Ur2wyjtjS1VLGy57GNaLZkuRfFAFUiuZx0n36HBQvi2ai6Qzjlg94mDHFotny59K4pBRFM1FzcKI5EKcUwBal8LJovNtX4HG8HAhPrWjEyhuBz40qDSJXKZc+zcuexlVMM4nrDDtjZRVjBQOg5Hpv39Mc2nW+OUG5QKz8BPCdgb8t5KqS7yfysqeZiCNRbbUc4Nh3GciSBwQQK0u+Ge9l0RSqejoUvubiXIFYueAI2DBt8LJoJvIoR8LTuBXi40fAhmmDl2OaiRCi2XoJ2EY+y/lgjHE4Ylrp8vw6xMrVyFUXI1aeC9wInMVI8UngHchVtx+GXUcWsbIM+DH5vqzHgWuQqzKlKBqAJQM/wzmcauuYSXzmIsTKPeRzl4yHAH6LWLkTueqxyZt2RNkKDG6GtQR4HTDXy9XTVNI+yc8dyvKY1ZO89pFFrHwH+wUzyBzEytf6ojk07juC1x4n/fm0oHGC8jm+aA6NL07yc/YhnPPTSV77SPMTYHT3gQn8upRFU8iM3aPjo0PBIu/ev8vE4mlDrvrEZI06oshV/cDlQCf5oH0fcDFylV2SgbBsMsYEvQNzZyYbDK8DTnRxfhdyVc3A759kMOujWCmAfwd+4YkxK7nqT4yNa0qyyX0kqHB5/mfGLZWrJNO3OjpkSrl6KiQT7ts0LnLVz4+QHdOCUhTNkegRXneAY8ODRQkcfQS+f1pRitXTkRh7+h3w3+MekauUy4rkZU8zdSnP5KodwHjrqdaPU1byeFk0U4tcFQFmkK+ObEBHrlpWXKOKg5erp6kc5R648qoOJp6grgyl6GncrlKY/v0l04xSFM1E4rhggnJ/ix6XlKJoxvU0ssl4AHh5eBFwjmzys3u6xcsxzURMKALZZBw7lYaUKl72NB0TlJf8xO5i41nRyCZjFuN7lZJfQlJsPCsagIGsVxZ58TiA5nYXXR/3eDp9mk9x8LSn8SkOvmh8XOOLxsc1vmh8XFPQzj3RbDUAO9k/qGcDi2WTsb2Q3+NTXArdI7xn1N86+eWxymz3V35df/dJ7T2VEcvm5ZoyesLBr/V8Of75YttVSArW5BbN1veBj01w+HHZZJxZkC+axpR9uT/93nXbwjFz/4qV1YtmsTsevmbFjj0/fNOGzaIrVsYrNdXORWufr3vHpvdM1Ks9rSmkp3nnAY6dUcDvmZaI/zb1V7X3hmOmjQP0hgOUZS3O2NOJ0d/xoy/+7Z9oAy/oy7MatVvPPLv9HR71wIUMhA8kQCGarVML+F3TES1s2ewsj/LDU4/mZ8uPYvW8av41q4Km+x4cEgzAMS17OLa9nc+85oG7i2jvpCmkp0kBZQc4/megvoDfN82QjS/VVvDI3JnM6+rhllt/z4LObmwgPM48r0Vtu+idNfMNU2/n4VNIT3Ow4KhONFvnFvD7pg3GV/q3ANvayiJYhs7ife08M7eRRxfNw8DEID3ifAlEsn1smhHz5NTRQgbCe4FZh3DqJ2ST8f2CfOk0QHwjNwvB3vwfYsyrc+r2Xdz9q++Q0OZQ099H1gjw5FHH0lER4SMrL+LyZ9a+a9Xtp/xfEUyfNMWYhPU9oGREgyPXIwB9fKf99Pw5/HHp2bRXLSCWSZMJBLH1vIN5x1OP8fjcY24FPCWaQlZPSu5oErasCj134NkYMSsBQDIcGRJM2Mxw499/S0846LkWVEFEI5qtOUzf5DxDiGZrl2i25LCfw46xwjlbzu9NETbtCaO6aK6LeV07R5S9atvjzO/bx7ueeUlcc8k9nlr7XajqaaObk0Wz9WZgFSP7Kf5HNhlNBbJnvO98Dpg9qvgh0WxphzO5vFfX5XwzQ0Mizdbq+MiDUoIQ3Hzq+fzllm+yoe5ouiNVLOzcxrr6OiRwyUtrufGCV78f+MBkbZhqClU9uV0GcjdjO7b+SzRbbQWyZzxOmqB83+Fc1HBsvTUSZHtlbJyjAgT87djlvPWd/0l7VEfILr5w4ZvpD4ZJBCO8avdznLr9ZSGaeqa9px6kIK2ncTYgPRyyssko+AMc2CBsPCa9WWrlV5KNlcnc7h3VURDjXEKy/9XQRr4jl6/dyOk7O6lNdnLexsd5YO6cjg+89PEZk7Fjqjns6kk0W++msEtVQ6LZygILgXnkc8OcAdwjm4zWCWxoABYAz8gmIyOarSXkF8e9e+AaB/KEkw5Ec5p2Qzxrg6aNH88I8r58nKZ4a3kFgi46Y7WsWn4Jp778fK34lpUCehFEyT9TA2gDHgX+CPwHUAn0AH8b+P0R2WTcAyCarQ8CrwL+CjxEvgvkSvJp+n8jm4xu0WxVAkvJp4SrB3408PtJA9fdDKyRTcaE+QIPy9OIZqsXKJ/0BdwzJu4RzdaN5DNP6eTzw20BTnN53aBsMlwvzxVfSbUtTKRndpaH6Q0aeXGIYRoUjPEwg+KpbE+wvKWTC3a1YkhJQsCNb5z0mG4XEOfAKzEkcCt5EUUPcr0dwMWyydgw3sFJxzSi2bqYqRUM5OOeoX0HBsazPsd+T1eDe8EAHO/2A+KGbBWaNjMZCdAbCowVDIzvw6SEZI4ePcADs+v504LZICVtFQcagTko1Rx86Y4A3svBBQN57/yDiQ4eTiD8n4fx2cPh9GG/F2r03P2YmJRnIgQZTRvrTQ5E2oLsfs//zIxqpIT1NVV5QU0fTp/owOGI5qbD+Ozh8Oyw358p0DVHTx47OEI8iZT0G/q4MQuQX4k1TAiV/X2QtUacEnAcHppTz5MN0y4GfnaiA5MWjWwyVsGokbgjz82yyXhlmA2PkXejg/9n+oGXJnHdiTaGnxD5hVAHtr3JHvQymgBnmHJyVt6r9JnMbG3nzlubean5k8zsH5nzMR0MsHrRHCKYY6u3Q6eP/KLBg/EHDi21yj5gwvzGh93kFs3Wdyh8VfUw8EHyLSiHvKv8vWwyxu1EFM3WYmAx8JhsMhKi2TodeCvwdvJxjgYEJvqy8fIOHwrihszvcOSVRAa2+rad/VWV6UAy///n6w/8js8+cQ8A2ytq+d6KC3m5qp5tVTOozqU5bc9mXo42sPrs0ywggyA0YLMkP+XkGfKtoXeRn37SC9xPvlX4APkAF+AL5Hd7+Sf51tZs4H1AK/Ad2WS0iGarnvyuKq8in9mrmXxM9yry4nuCfItswq0WC9VPcziJnUeTlk3GoQRrh4xotqJAcoLDk+6nETdmlyDlSwS1fD+NlCP7ZmwHkbG475YbOX/nSL3vi8Spu/anfPHhu3mw8Wje9fwT3R/c8LHqydgx1Uy3JSx2oQUzwIFc8oG2Gjwg8vOhDUgkthxZNUFeOIbGVa88xfk7N44JeWam+3nf8w/xjTMvQ3ccLt3YMdEGFtOOQonG7aL7/2Ns6PiUbDKOyFSNgT6YcYcLZJNxmP+zHBtt4DEOehlNDDXBbz/lHL5zziXctXRsY+Tap1ZjGjrJaDX18itTHR9OmkL9T/os8K1DPVk2Ge8iXz9PGbLJqBPNVh/5TrBB3nbYFxajquVxKunvn3UxTQ/9eUx5LJdlQXc7qaix67DtmEIKOXPvkOOayQae0xFxfdIkFNz/8g0OGwyjtq+XRZ1t/OvHX0Qf9rx/uvx87Gyca158n6eeRyFjGiU3NkfKkWM0o/pmkJKzX9rNk/OO5k3v+TQPL1jC2vq5/OC0Szi2uwVHF4fSVJ5WFDKGSHNoE7HuKOB3Fp2g4N9zyFsGnayQksr+LKahUZ3K8rYXtrDywad4paGWe447hXuOOwWAFVt3k9UcTm/tel0RzZ8UhfQ0hzLSfbNsMq4q4HcWnex18VuxHWcwrg9ZFt3xMP3hIDury7h/UQPr5tTz0uy6EZ9bs3A289r32j+7a8WDRTD7sCikaA52rYxsMq4u4PdNG+TnwjqO822QZIYPXgrBM3Nmcvep44+HnrV7+9QaWiAKKZqDzbo7u4DfNf0Q+mfGG+mWAh5bWI9wRvZKxLIZTt+2+4mpNLFQFFI0NQc4JmWTUajBxenKhH1Vc3raqcxk0RwHpCSWzfDT227h/NZ1r59KAwtFIQPh/wa+PsGxzgJ+z7REfjbg6F9LZ52AERrhbSQ0dHd+/Ot/vf37LVW1NPR2syXWKI/q678EedtEQxvTmoJm9zzAPNyIbDLG2y+ppBDNlgils33ZcDA/y9yRlPWlfpW4ofI9RTatoBS62z5IfmBw+IjypSoIBiC/FMYYtY6l9HJh+3mEfVwz3Ua5fTyALxof1/ii8XGNLxof1xxW6+m6i9cI4GZgJfkZ/a+5fvWKcVdB+pQOk249XXfxmjj5icij+e31q1e847Cs8pnWHI5oDrQkV79+9Qql910SzVYZUj6PlLMRQkMIB/i1bDI8k1JkIg4npjnQktzaw7iu5xHN1iakTCDEQjQtiBAG+V6+qweSKS0tto2Hw6REc93Fa957kFMemcx1SwHRbL0HKRcfZOHbOtFsfXeKTCo4k6qerrt4zaF86NzrV69QSjwDVVLCxUrJgGwyPDfd80g2ue89gteermx3ef5fj4QRR5ojKZrwdRevueYIXn9aMZAsqNrleuzXiWbrxCNj0ZHDtWiuu3jNq12cPmGOkxKka5Kfe140W+Ml7Ju2TMbTPOjiXE+t5zlMDudePbWFjyvRXHfxmre4/YLrLl5z6eDvaf1tZ+XElce4vcZ0RzRbFxxmQqKwaLa+XSh7jjSuWk/XXbxmBzDX7Zd87L4f3Vlr9l4B+ddRDvxo8BXkqi+7vd50QTRbl+DIv2i2LZxAQeazhQ+U4mO6cMie5rqL1yxmEoJBSmrM3isE+/33YNJLCV+yxEpPtiBEs/VRpLwHQaEEA1OfJGpSuKme1kz2SxLB+LjlAtDh4oxYedtkr10MRLMVwnF+MG5yxsO8tGi2/r2QFzwSHLB6uu7iNacAT3GYTfPavnY+9q+fTRgpSiCB4ewpW3DlcX2bViNXpQ7n+ybDLxavWgCcD4SAp6/evHLNLxavEldvXjnmAcW+1JcICcq6424TtR8S9uiUK6LZCsomIyeaLSGbDCmarSBwKvBJ4DJAx3aSIEMIIdC0jeQ3OBFAHfnZDJJ8C+/nwHfJ5yAG+Dz5jFkPATceSmpcIaXkuovXlAPPk0/gfER415O3Ud/XQUeomppsN+VW/4jjPYFyHCHIaGHiuV7+WX+e2R2pFeRzyW0Cvnr15pW/P9B3/GLxqouA/wUayK9DGlxfXojZ3RmAlnjEvuU1y2PVqSxPLz5CeYiGv8ijPdnol7ywng7y4loum4wXJjphUDSvAEcV+tuHU9fWSqQ3gxQaQjqc0vUCJ/a8iI1AH5XfKCcMdGlxy8J/G/5QJPDaqzevfGC86/9i8apFwCsc4Yllvz57GRHTYk91Oevm1R38A97kgCnltAEvc0QFo1k2oUQOObB/gBQaz1SfSFKPsL5iyZjzg9LCFgYzM+3DiwUHToT0PqZgJuKaxY1srqseK5jSWtUhRLM1YUJujXw+uiN6x4GcOSaekUJje3Q266qOG/czurTJamNqlf7xzh1gvAlhBSdg2WypH2c/tMJXE8Wmd6ID2vWrV6Q5woOLuVAQZ9RD1R2LBcmdVGe7x54vAmQx6A1VDi9OAj85wNfczBQ0Wc95aTtSK/mp1aZsMnZPdHDw7i8GfglMuPPG4SB1jc66arSBpFEBO8c57U8QdbKc3JWPt9JaiF4jTq8eJymC9h3zLt9JPvPmy+SD21Ov3rxy3A0eAK7evLKdfD7ce8lnsNhJPifuRg4tMfOB6AVeBF48ZevedVrOPHLVkZSDP5LRTdv9x0b+uL32gUlxkP0TDtbkjgJbyTfbJs0pO5/ljJ3PUJPsoidQToWZICBtHAQCyYbYQuamdiXj0qxGrsodzndNBaLZekrYzqlSK3g/DbjY72ogP/J55LOL30neG68AfkZ+9qRDfsORc8k3t78M3DJ6J72B62Rkk3FIU3QPeRjhuovX/B248JBOHsU1D99EXWps4ggJdOlxdsXnvuOknht+O5lrF4OBfpJMdaJfdJXFCi2cY4an8p+OuKmcL8VtwDzgDmtSnWM+KIFWUU4Q5zgvCQZANhk54Kyu8vF7ug+D5HQXDLgQzfWrV+SAr7i6er6bPWXAxyQDiS/JBxg2dMySiboy6/8mjFOmM7LJeBwIIkRboeIb2TQ648T0xO1I293k60U31CBXZTT44WS/dLoy0OVeL5qtWUi5FzicquqzhbLrSON6Yvl1F69xtXnG9atXlFwHxniIZksObqk8CVKyyfDM7L3JdDg0uDjXczPtD4PkJLtIJVBRWFOOLK5F43Kt9hVur+9h6iY54dPw2jKWI9m1+fz1q1f88Qhef1ohm4wk+c243PC9Q+0bmU5MVjSH8macPMlrexchjhrTi3tgmg5+yvRjsqKZdZDjF16/ekVJDfseCrLJ6JefCmiH2AT/gdeqpUEmJZrrV6/o4MAdfY9OzpwSweH9BxkjqpNNxseLZd7hcjipRm4Bxs2Pq0oz+2CIr2UvQBcfRNd+TX5M6AHZZDxUbLsOl8NKCXvdxWsS5HdvHc7516/23s4iPofOYecRHpj5907guetXr3i8IFb5TGv+P8cnWCqyrrT7AAAAAElFTkSuQmCC" id="image035c9a59ba" transform="scale(1 -1) translate(0 -578.16)" x="419.04" y="-43.2" width="101.52" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature113_fold0 -->
    <g transform="translate(154.872156 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-33" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.474656 638.149 
L 537.104156 638.149 
L 537.104156 27.789 
L 529.474656 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagee24c8588f7" transform="scale(1 -1) translate(0 -609.84)" x="529.2" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.604156 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.604156 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.005094 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p8922073836">
   <rect x="416.875156" y="27.789" width="105.976" height="610.36"/>
  </clipPath>
 </defs>
</svg>
