<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="766.351187pt" height="679.5765pt" viewBox="0 0 766.351187 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T08:41:28.670661</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 766.351187 679.5765 
L 766.351187 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 384.572187 638.149 
L 515.964188 638.149 
L 515.964188 27.789 
L 384.572187 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 443.434479 638.149 
L 443.434479 27.789 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 384.572187 609.084238 
L 515.964188 609.084238 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 384.572187 580.019476 
L 515.964188 580.019476 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 384.572187 550.954714 
L 515.964188 550.954714 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 384.572187 521.889952 
L 515.964188 521.889952 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 384.572187 492.82519 
L 515.964188 492.82519 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 384.572187 463.760429 
L 515.964188 463.760429 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 384.572187 434.695667 
L 515.964188 434.695667 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 384.572187 405.630905 
L 515.964188 405.630905 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 384.572187 376.566143 
L 515.964188 376.566143 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 384.572187 347.501381 
L 515.964188 347.501381 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 384.572187 318.436619 
L 515.964188 318.436619 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 384.572187 289.371857 
L 515.964188 289.371857 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 384.572187 260.307095 
L 515.964188 260.307095 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 384.572187 231.242333 
L 515.964188 231.242333 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 384.572187 202.177571 
L 515.964188 202.177571 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 384.572187 173.11281 
L 515.964188 173.11281 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 384.572187 144.048048 
L 515.964188 144.048048 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 384.572187 114.983286 
L 515.964188 114.983286 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 384.572187 85.918524 
L 515.964188 85.918524 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 384.572187 56.853762 
L 515.964188 56.853762 
" clip-path="url(#p0240ef691e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m4723b15cda" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m4723b15cda" x="392.038081" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(383.929878 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m4723b15cda" x="443.434479" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(439.935104 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m4723b15cda" x="494.830877" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(491.331502 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(327.586781 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(48.712656 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_24 -->
      <g style="fill: #333333" transform="translate(171.751562 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(110.056406 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mode_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- OPSiteFingerprint_mean_pentagonal_planar_CN_5 -->
      <g style="fill: #333333" transform="translate(36.114844 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="923.511719"/>
       <use xlink:href="#DejaVuSans-65" x="1020.923828"/>
       <use xlink:href="#DejaVuSans-61" x="1082.447266"/>
       <use xlink:href="#DejaVuSans-6e" x="1143.726562"/>
       <use xlink:href="#DejaVuSans-5f" x="1207.105469"/>
       <use xlink:href="#DejaVuSans-70" x="1257.105469"/>
       <use xlink:href="#DejaVuSans-65" x="1320.582031"/>
       <use xlink:href="#DejaVuSans-6e" x="1382.105469"/>
       <use xlink:href="#DejaVuSans-74" x="1445.484375"/>
       <use xlink:href="#DejaVuSans-61" x="1484.693359"/>
       <use xlink:href="#DejaVuSans-67" x="1545.972656"/>
       <use xlink:href="#DejaVuSans-6f" x="1609.449219"/>
       <use xlink:href="#DejaVuSans-6e" x="1670.630859"/>
       <use xlink:href="#DejaVuSans-61" x="1734.009766"/>
       <use xlink:href="#DejaVuSans-6c" x="1795.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.072266"/>
       <use xlink:href="#DejaVuSans-70" x="1873.072266"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.548828"/>
       <use xlink:href="#DejaVuSans-61" x="1964.332031"/>
       <use xlink:href="#DejaVuSans-6e" x="2025.611328"/>
       <use xlink:href="#DejaVuSans-61" x="2088.990234"/>
       <use xlink:href="#DejaVuSans-72" x="2150.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2191.382812"/>
       <use xlink:href="#DejaVuSans-43" x="2241.382812"/>
       <use xlink:href="#DejaVuSans-4e" x="2311.207031"/>
       <use xlink:href="#DejaVuSans-5f" x="2386.011719"/>
       <use xlink:href="#DejaVuSans-35" x="2436.011719"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(60.638125 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(42.029844 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(163.480312 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(163.480312 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(58.397656 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(171.751562 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.202031 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(76.664687 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- VoronoiFingerprint_std_dev_Voro_vol_sum -->
      <g style="fill: #333333" transform="translate(89.548906 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-76" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-6f" x="1736.763672"/>
       <use xlink:href="#DejaVuSans-6c" x="1797.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1825.728516"/>
       <use xlink:href="#DejaVuSans-73" x="1875.728516"/>
       <use xlink:href="#DejaVuSans-75" x="1927.828125"/>
       <use xlink:href="#DejaVuSans-6d" x="1991.207031"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- OPSiteFingerprint_std_dev_sgl_bd_CN_1 -->
      <g style="fill: #333333" transform="translate(102.23 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-73" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-67" x="1414.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1478.052734"/>
       <use xlink:href="#DejaVuSans-5f" x="1505.835938"/>
       <use xlink:href="#DejaVuSans-62" x="1555.835938"/>
       <use xlink:href="#DejaVuSans-64" x="1619.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1682.789062"/>
       <use xlink:href="#DejaVuSans-43" x="1732.789062"/>
       <use xlink:href="#DejaVuSans-4e" x="1802.613281"/>
       <use xlink:href="#DejaVuSans-5f" x="1877.417969"/>
       <use xlink:href="#DejaVuSans-31" x="1927.417969"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(48.8325 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(62.701875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(241.37875 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(119.863281 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(78.279531 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 384.572187 638.149 
L 515.964188 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image6deea58495" transform="scale(1 -1) translate(0 -578.16)" x="388.08" y="-43.2" width="124.56" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature41_fold0 -->
    <g transform="translate(141.385188 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 524.176187 638.149 
L 531.805688 638.149 
L 531.805688 27.789 
L 524.176187 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagee1666f18ed" transform="scale(1 -1) translate(0 -609.84)" x="524.16" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(535.305688 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(535.305688 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(569.706625 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p0240ef691e">
   <rect x="384.572187" y="27.789" width="131.392" height="610.36"/>
  </clipPath>
 </defs>
</svg>
