<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="750.871187pt" height="679.5765pt" viewBox="0 0 750.871187 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T07:46:39.842414</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 750.871187 679.5765 
L 750.871187 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 358.832187 638.149 
L 510.744187 638.149 
L 510.744187 27.789 
L 358.832187 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 428.440609 638.149 
L 428.440609 27.789 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 358.832187 609.084238 
L 510.744187 609.084238 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 358.832187 580.019476 
L 510.744187 580.019476 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 358.832187 550.954714 
L 510.744187 550.954714 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 358.832187 521.889952 
L 510.744187 521.889952 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 358.832187 492.82519 
L 510.744187 492.82519 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 358.832187 463.760429 
L 510.744187 463.760429 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 358.832187 434.695667 
L 510.744187 434.695667 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 358.832187 405.630905 
L 510.744187 405.630905 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 358.832187 376.566143 
L 510.744187 376.566143 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 358.832187 347.501381 
L 510.744187 347.501381 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 358.832187 318.436619 
L 510.744187 318.436619 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 358.832187 289.371857 
L 510.744187 289.371857 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 358.832187 260.307095 
L 510.744187 260.307095 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 358.832187 231.242333 
L 510.744187 231.242333 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 358.832187 202.177571 
L 510.744187 202.177571 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 358.832187 173.11281 
L 510.744187 173.11281 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 358.832187 144.048048 
L 510.744187 144.048048 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 358.832187 114.983286 
L 510.744187 114.983286 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 358.832187 85.918524 
L 510.744187 85.918524 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 358.832187 56.853762 
L 510.744187 56.853762 
" clip-path="url(#p9b317eeacc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m9a2fe9525b" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m9a2fe9525b" x="361.474883" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(353.366679 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m9a2fe9525b" x="428.440609" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(424.941234 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m9a2fe9525b" x="495.406335" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(491.90696 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(312.106781 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(7.2 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_167 -->
      <g style="fill: #333333" transform="translate(137.740312 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_40 -->
      <g style="fill: #333333" transform="translate(146.011562 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(131.238281 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- XRDPowderPattern_xrd_21 -->
      <g style="fill: #333333" transform="translate(162.460625 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-31" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_166 -->
      <g style="fill: #333333" transform="translate(137.740312 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-36" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(33.827656 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(143.541562 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(84.316406 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_79 -->
      <g style="fill: #333333" transform="translate(146.011562 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(84.316406 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(23.0925 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(146.011562 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(137.740312 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(23.0925 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(146.011562 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(36.961875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(215.63875 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(94.123281 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(52.539531 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 358.832187 638.149 
L 510.744187 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image99f264bfa8" transform="scale(1 -1) translate(0 -578.16)" x="363.6" y="-43.2" width="142.56" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature36_fold0 -->
    <g transform="translate(125.905187 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 520.238687 638.149 
L 527.868187 638.149 
L 527.868187 27.789 
L 520.238687 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagec6be50da1a" transform="scale(1 -1) translate(0 -609.84)" x="520.56" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(531.368187 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(531.368187 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(565.769125 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p9b317eeacc">
   <rect x="358.832187" y="27.789" width="151.912" height="610.36"/>
  </clipPath>
 </defs>
</svg>
