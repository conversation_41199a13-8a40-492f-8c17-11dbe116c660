<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.227719pt" height="679.5765pt" viewBox="0 0 788.227719 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T05:14:22.804569</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.227719 679.5765 
L 788.227719 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 421.136719 638.149 
L 523.152719 638.149 
L 523.152719 27.789 
L 421.136719 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 462.044923 638.149 
L 462.044923 27.789 
" clip-path="url(#peccbb56090)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 421.136719 609.084238 
L 523.152719 609.084238 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 421.136719 580.019476 
L 523.152719 580.019476 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 421.136719 550.954714 
L 523.152719 550.954714 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 421.136719 521.889952 
L 523.152719 521.889952 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 421.136719 492.82519 
L 523.152719 492.82519 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 421.136719 463.760429 
L 523.152719 463.760429 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 421.136719 434.695667 
L 523.152719 434.695667 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 421.136719 405.630905 
L 523.152719 405.630905 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 421.136719 376.566143 
L 523.152719 376.566143 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 421.136719 347.501381 
L 523.152719 347.501381 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 421.136719 318.436619 
L 523.152719 318.436619 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 421.136719 289.371857 
L 523.152719 289.371857 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 421.136719 260.307095 
L 523.152719 260.307095 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 421.136719 231.242333 
L 523.152719 231.242333 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 421.136719 202.177571 
L 523.152719 202.177571 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 421.136719 173.11281 
L 523.152719 173.11281 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 421.136719 144.048048 
L 523.152719 144.048048 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 421.136719 114.983286 
L 523.152719 114.983286 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 421.136719 85.918524 
L 523.152719 85.918524 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 421.136719 56.853762 
L 523.152719 56.853762 
" clip-path="url(#peccbb56090)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mf932cfbb40" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mf932cfbb40" x="462.044923" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(458.545548 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mf932cfbb40" x="507.94935" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(504.449975 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(349.463312 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(142.727031 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(97.202656 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- AtomicOrbitals_LUMO_element -->
      <g style="fill: #333333" transform="translate(197.444844 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-4c" x="788.679688"/>
       <use xlink:href="#DejaVuSans-55" x="839.392578"/>
       <use xlink:href="#DejaVuSans-4d" x="912.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="998.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1077.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1127.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1189.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1216.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1278.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1375.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1437.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1500.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(193.542812 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(205.846094 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(135.384062 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(85.397031 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(85.397031 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(146.620937 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- BondOrientationParameter_std_dev_BOOP_Q_l=1 -->
      <g style="fill: #333333" transform="translate(76.672812 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-73" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-74" x="1443.546875"/>
       <use xlink:href="#DejaVuSans-64" x="1482.755859"/>
       <use xlink:href="#DejaVuSans-5f" x="1546.232422"/>
       <use xlink:href="#DejaVuSans-64" x="1596.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1659.708984"/>
       <use xlink:href="#DejaVuSans-76" x="1721.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="1780.412109"/>
       <use xlink:href="#DejaVuSans-42" x="1830.412109"/>
       <use xlink:href="#DejaVuSans-4f" x="1897.265625"/>
       <use xlink:href="#DejaVuSans-4f" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-50" x="2054.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="2114.990234"/>
       <use xlink:href="#DejaVuSans-51" x="2164.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2243.701172"/>
       <use xlink:href="#DejaVuSans-6c" x="2293.701172"/>
       <use xlink:href="#DejaVuSans-3d" x="2321.484375"/>
       <use xlink:href="#DejaVuSans-31" x="2405.273438"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MaximumPackingEfficiency_max_packing_efficiency -->
      <g style="fill: #333333" transform="translate(59.340156 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-61" x="86.279297"/>
       <use xlink:href="#DejaVuSans-78" x="147.558594"/>
       <use xlink:href="#DejaVuSans-69" x="206.738281"/>
       <use xlink:href="#DejaVuSans-6d" x="234.521484"/>
       <use xlink:href="#DejaVuSans-75" x="331.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="395.3125"/>
       <use xlink:href="#DejaVuSans-50" x="492.724609"/>
       <use xlink:href="#DejaVuSans-61" x="548.527344"/>
       <use xlink:href="#DejaVuSans-63" x="609.806641"/>
       <use xlink:href="#DejaVuSans-6b" x="664.787109"/>
       <use xlink:href="#DejaVuSans-69" x="722.697266"/>
       <use xlink:href="#DejaVuSans-6e" x="750.480469"/>
       <use xlink:href="#DejaVuSans-67" x="813.859375"/>
       <use xlink:href="#DejaVuSans-45" x="877.335938"/>
       <use xlink:href="#DejaVuSans-66" x="940.519531"/>
       <use xlink:href="#DejaVuSans-66" x="975.724609"/>
       <use xlink:href="#DejaVuSans-69" x="1010.929688"/>
       <use xlink:href="#DejaVuSans-63" x="1038.712891"/>
       <use xlink:href="#DejaVuSans-69" x="1093.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1121.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="1183"/>
       <use xlink:href="#DejaVuSans-63" x="1246.378906"/>
       <use xlink:href="#DejaVuSans-79" x="1301.359375"/>
       <use xlink:href="#DejaVuSans-5f" x="1360.539062"/>
       <use xlink:href="#DejaVuSans-6d" x="1410.539062"/>
       <use xlink:href="#DejaVuSans-61" x="1507.951172"/>
       <use xlink:href="#DejaVuSans-78" x="1569.230469"/>
       <use xlink:href="#DejaVuSans-5f" x="1628.410156"/>
       <use xlink:href="#DejaVuSans-70" x="1678.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1741.886719"/>
       <use xlink:href="#DejaVuSans-63" x="1803.166016"/>
       <use xlink:href="#DejaVuSans-6b" x="1858.146484"/>
       <use xlink:href="#DejaVuSans-69" x="1916.056641"/>
       <use xlink:href="#DejaVuSans-6e" x="1943.839844"/>
       <use xlink:href="#DejaVuSans-67" x="2007.21875"/>
       <use xlink:href="#DejaVuSans-5f" x="2070.695312"/>
       <use xlink:href="#DejaVuSans-65" x="2120.695312"/>
       <use xlink:href="#DejaVuSans-66" x="2182.21875"/>
       <use xlink:href="#DejaVuSans-66" x="2217.423828"/>
       <use xlink:href="#DejaVuSans-69" x="2252.628906"/>
       <use xlink:href="#DejaVuSans-63" x="2280.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2335.392578"/>
       <use xlink:href="#DejaVuSans-65" x="2363.175781"/>
       <use xlink:href="#DejaVuSans-6e" x="2424.699219"/>
       <use xlink:href="#DejaVuSans-63" x="2488.078125"/>
       <use xlink:href="#DejaVuSans-79" x="2543.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- StructuralHeterogeneity_mean_neighbor_distance_variation -->
      <g style="fill: #333333" transform="translate(7.2 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-65" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-61" x="1417.427734"/>
       <use xlink:href="#DejaVuSans-6e" x="1478.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="1542.085938"/>
       <use xlink:href="#DejaVuSans-6e" x="1592.085938"/>
       <use xlink:href="#DejaVuSans-65" x="1655.464844"/>
       <use xlink:href="#DejaVuSans-69" x="1716.988281"/>
       <use xlink:href="#DejaVuSans-67" x="1744.771484"/>
       <use xlink:href="#DejaVuSans-68" x="1808.248047"/>
       <use xlink:href="#DejaVuSans-62" x="1871.626953"/>
       <use xlink:href="#DejaVuSans-6f" x="1935.103516"/>
       <use xlink:href="#DejaVuSans-72" x="1996.285156"/>
       <use xlink:href="#DejaVuSans-5f" x="2037.398438"/>
       <use xlink:href="#DejaVuSans-64" x="2087.398438"/>
       <use xlink:href="#DejaVuSans-69" x="2150.875"/>
       <use xlink:href="#DejaVuSans-73" x="2178.658203"/>
       <use xlink:href="#DejaVuSans-74" x="2230.757812"/>
       <use xlink:href="#DejaVuSans-61" x="2269.966797"/>
       <use xlink:href="#DejaVuSans-6e" x="2331.246094"/>
       <use xlink:href="#DejaVuSans-63" x="2394.625"/>
       <use xlink:href="#DejaVuSans-65" x="2449.605469"/>
       <use xlink:href="#DejaVuSans-5f" x="2511.128906"/>
       <use xlink:href="#DejaVuSans-76" x="2561.128906"/>
       <use xlink:href="#DejaVuSans-61" x="2620.308594"/>
       <use xlink:href="#DejaVuSans-72" x="2681.587891"/>
       <use xlink:href="#DejaVuSans-69" x="2722.701172"/>
       <use xlink:href="#DejaVuSans-61" x="2750.484375"/>
       <use xlink:href="#DejaVuSans-74" x="2811.763672"/>
       <use xlink:href="#DejaVuSans-69" x="2850.972656"/>
       <use xlink:href="#DejaVuSans-6f" x="2878.755859"/>
       <use xlink:href="#DejaVuSans-6e" x="2939.9375"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(208.316094 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(200.044844 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(69.504531 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(113.229219 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(99.266406 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(277.943281 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(156.427812 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(114.844063 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 421.136719 638.149 
L 523.152719 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAIcAAAMjCAYAAACPggNvAABayElEQVR4nO2dd3wc1bm/nzMzW7XqXbbccQeb3kwNEEghwSGdkEIqKTc3USCVJBCSXKJwk5tC6u8GSG6AEJNOCRB6Nc24417V+/aZOb8/VrIlr9aS1muN5mSez0dgnSn7zui773lPe4+QUqISNy3/xxoR8i9NW9L+8nMXGk7b42aESuL4/on32S/OnSVSwQBSSk7ZtI1UKBD+6qNnxZ22zY0o8826efl9c15aOFtMj8YQ8ThCwtYZDczfsnMA0J22z40oIw7TTG6Y1h8FQ0cCEqhIJNhbXy2cts2tKCMOy7SxNUF/MIAtBAJJOJmmJNrvtGmuRXPagEJR0h+jtyiMrWkgBFJoDAQD+FMpp01zLcqII14UADGyBhFANBR0xiAFUEYctrQZrd1VmoxOui2qoIw4itNpqju7R5SFYgmO3bHVIYvcjzIBaVozOOWlzURLIuytraC8L8qCrXvYOq3CadNcizri0DUqBxLM7Ohl8ba9B8qremJeUzZPlKlWNs2daWijRB2mroz+Jx1lxCEMH7saqrPKt0zPLvMYH8p8rep6unhh6Ww0KZm5p42Uz2Dd/BnUJHqcNs21KCOOPSUlojUQwDpuHs8uOwZNShbvWcuxLducNs21uHpUVjSbF2KaDxgpm9r9vTTKTl6ZMRuE4MrVj/OTe35Ft6+EylSmiZuJTDULeB74b+Tddzln/dTHteIQzeY8pHwNW1Kzv5dU0E9vSZBINEXAtKhOJvnQS/fzucf+goY9/EqGZAJchLz7n5NvvTtwc0D6LgBdShJBH/GQgUza9BsGHcEAG0pLeGTm6VjaYUfrr5wcU92Jm8URBbAFICVayso6YX1FGS/XLRnzHh6j42Zx/BaQUgqEKQmks8WhS8kr1ccNKxFwsC8kAdxy1K10Ma4Vh2wy2hFiFkKmeiuLqO+LIYbHT1Jyyr4OSnpi2AxJQgJaK/Bz4FTk3a84YLprcG1AeigfesvLdmVnn3ho0SzShsEZu1pYsW47Nek2Xr/3E14Xeh4o088xc2+rjPgR1/zjaXQrI/gQMXbXlTtsmXtxbbVyKKmIxuk71rJlXgMDkSAttWXopTECA8o84qSjjOfQYmk5bX8PK/ZvZqgfowSTytLdzhrmYpQRR2ggZu1muj6sg4s+SrC1pBpBlQMo43NLonFMfFnlLSFvsk++KCMOn64RjRzqCCW9xSFH7FEBZcRhJW3mp3cSJgaAgckM9lPidYLmjTIxRzhtUZnso5IBLHQ0LDQkdf09TpvmWpTxHKZhk9b9gEDHJrPmTaM46q2hzhdlxIEEn5U9vtIbKXbAGDVQRhy9ZcWyOxzJKt9d4bVW8kUZcfQ0zoj8ZfkpxH1+IDPE9uyc+bSHiuzDX+mRC2UG3gC+e/r95t7aWr2ht4eOSIRgKsmN95/pDbrliVLiAPj2mY9USGnv1NPm7V98/qKrnbbHzSgnDo/CoUzM4VF4PHF45MQTh0dO1BWHWOm1Uo4QpQLSluCnNZEyLE0GALBFCtuwi+pTP4g5bJorUWbgDYCUYYZlkhD7AUjIcqJmaACVPeRRRKmXFpIJUUwLCQKk0YnQSlhGveolT9TyHCR5nrMYoBSAavYzj/UO2+RelBLHTo4hBFTRho2gjxL208hspw1zKUqJQxAkRIr04GOVEqWPGoetci9KicOPSU+oiNbqUoriCeo7uvHJ7DkeHuNDGXE0r3hELKut5slli5BaJs6u7ermgudfctgy96JMayUh5ZtfWjjngDAAWivK2V3nJYzLF2XEYcOx0VD2MoTu4rAD1qiBMuL464yab4QS2ZOJp3W20yY+drEDJrkeZcRRVBTRz9j8CmXRLgD8ZorXb3icpZ2biWPe4bB5rkSZgDRkpvnOaadw35yFhEwLKW1m717NScToIMJMpw10Icp4jldLItw3ZyE+WyKEIGkYfO78lXT7i9EO3YjFY1y4Xhyl13b/pO4/O+3WSFjUp9LMT6aYk0qzIJGiLxjhL/NOJQ0BxEplvORk4Wpx1F/Ts9pniKtb60tEwKdRadkHEjAYQGMqzcL2XQQCRYEeGpLeHI+J4WpxFMeTJ3aVFoGA8ED2Xm5+oKW4gYpUL73UaUmCV02+le7F1eKwhEBqAiybeDp77VIklqArWIEmAkh0bIwLHTDTtbhaHJquSd2yQcKAEHQNizt10+L4TbuYtr8LS5P4iRFi4PMOmus6XB2kdQb9F5Z19j/YVVmMDBj0BDW+8vuHKYmnmNfSiSYlAwE//WaaWex/ALlqj9M2uwkl5pAWfan3hFTIeOFtL77GNX9+asSxlKZRZK/rP1b+qMQh81yLq6uVIaLfKX2xOGXJhq5+KmllAWtYwBoqaEOTkjBR938DHMDV1cpwqgcSlIl2ZnBw852ZbCUhNEwV3KMDKOE5ABLJFJdsW5NVXm504ce3Y/Itcj/KiKO0ref5YCq7OdsZKWYWfcc7YJLrUUYcl7W037m5aDb2sCS1NoINxXMQ8g6vWskDZWKOoKbd3qWVff8f085ift8OQLCxZBb9eDnB8kWJpuwQ/zftDhkP+Q8WSElZX5S3tV/pjankgTLVCsDJHeuJRBPopo2Rtqnu7+O07rVOm+ValKlWAGrMLt7a+hAtvmp80qQm3UFc8+aQ5otSnqMrUIFPmjSm9lOXbkdD0uX3NuPJF6XEsa2ygT7KkQgk0E8Z26oanDbLtShVrRy3fx0PLjqNyICJkJL+Ej/nbn7GabNci1Li2O+r7L10w8OlveEwICjdM8D64Kx4ldOGuRSlmrIAz1R877cB23qPFIiUrf3ttN5r3uy0TW5FOXF4FA6lAlKPwuKJwyMnnjg8cuKJwyMnSjVlxffSS7HsNYN7eEmEOEVe61/ttF1uRSnPUdnX+yq6JtB10DVR09/7vNM2uRllmrLihoEUwYBvxJppKQkMRM3EN8uydyP2GBNlPEdVPKZnLaYXgopUXJlnnGyUiTn6fAGCSZNFLX2E0xZJQ2NzbTG9gaDTprkWZb5VqVCA5Xt6KElZGBKK0jbH7e0lofvHvthjVJQRR2VfkoA9Mn7SJczsS3hTBPNEGXGkjMx2oYcS8ylTc046yoijPxSgtTgwsixg0FoSyHGFx1io87USsKm2mL6Qj9J4mphfZ29pCBRpqjuBOuKQUiAE+0tD7C8dlqw27eU+zxdlqhV0fXQv4YWjeaOOOExz1OLiWHSSDVEHZcShJ21GSzc6p61TfPKdGz3/kQfKxByhpCmKZC/Nf/sdb9rwMntKK2he8SZmt/ez8vE7u7krVo5c5UWnE8D1A2+i2XwXUv4+EEtx/63/zTnbDy5/tBH8vfZCFravxbI1FrDtbiFXvd1Bc12FCtXK77ElETPNih3rRhzQkMwc2EtLZAYGkh6t4nLESt0hO12HCuIAKYn7fQwEsvdbSWp+enylVNJDTCsBuGjS7XMpaohDEwRNyapjzx9RvKekmtdKZ1Ns9ZMggJbZ782bADROVAhIH0eIs3ymzV+WnsOOinpO2r2BjqIyHjzmFF73zKvMH9hINyUcY21NIFd1OG2wW3B9QAogms33F3cO/Oactn6MYY9TMdDDuesepWH9FnkWe/7kl3evdM5K96FEtSKbjFs1acsOn0FSCGwgoQnWVVTz0NzT0StmBD1hTBwVqhUABoqCJPrTtA3bHRIp2V4c4tzOz2VvqeAxJsqIwzIErzaWsaC1n1DSwtQF26qL6Z9Z5v560yGUEQdCYBk666eVjSy3vFHZfFEi5gAwUqOLIJhIT7Il6qCMOGyBHG3IPuHzOkTzRR1xGPqoo7LoyjzipKPOm/MG5QuOOuKQZM8EkxJU6OVzCHXEIciuVoQYvarxGBfKiEO35ahzSEX2Lhse40QZcVggR/MSctSlTh7jQRlxYBgRrEPchG2D0JY5Y5D7UUYc8hpfAtPaOxiEZn5Mq0t+0e9tm5AnSgzZexwdlPEcHoXHE4dHTjxxeOTEE4dHTtSZzzHI+y9/qHxbeU1LUSIRu+/2k71tmo4ApVorp3x4TWz1/IUhOThVsHQgxtkb1176lzvO+KvDprkSpaqVNXPmHRAGQG8kzM6Khj87aJKrUapaSfp8LGrdw9vXrCbm83HbiWfSXRb0Rt7yRClxXLxhDRfuaGdr9VKEbXPDg49wz/yZQJ3TprkSpaqVi7fuZmv1LACkpvHK9KW8fut2Z41yMUqJozNSk1Vm696mw/milDiq+7OXwVYMdDtgiRooE3PUfalHu6SqnI21JWyoKSNo2pyxYx8Pzp7GB5w2zqUoI45+M5bYVDGdZ2ZkqpZe4E+LZ3PyZm83jXxRRhy2r8jYML14RJnUBLGglxg/X5QRR0k8jRDZj1MZ63fAGjVQJiCdMZAQc/aMDEj9KZP3PP+cQxa5H2U8RzLoo6cowIrt7ewvCZESgoG0xfqKBqdNcy3KeI7OsJ8zOvqY2xtjxe5Ozt/VwbLeGEXJNGtCn3+L0/a5EVeOyoov9c9d0r7nGU3Kypm9HWk7VCe6SyK++b3xEedJKfnGX35EWayHBAHCdmJ/KfFjxfd+fyJwNRnP+UvZZHiDc6PgymqlfqBn07rq6TpAa6TaP7M3yqyBFJpls2jjHhr2dxMNB9g7q5SGWCtbQ3NZHN3APq2+/pm5x+wBAhxcXftG0Wy+XTYZdzv2QFMU14lDfKn/CiJlB/IqnNETJakL0jYc9/I25m9vA6C8J8r0/R1IWxC0YgDU2q3cGTl1tB0BPw544jgEN8YcWYK2NI2UgLk720cekBr79emUmANj3dON7+Go47qXIr9T/Ju6gZ4DS9ueKQ1jplP0hQwGAgZ/Pn4OP37dMh4/pgEJaNImPCiOVq2ORdFdSbK3g/vZ5D2Be3BrQLpkSfuex4Hymb2daXw1Ym9VqbFXCjoiB1Ncr3xhE1dteohT2zeSTAUIWomOCqLLxPd+fzwjA1KvShkFV4pjNMRX+iWHLJXVbBujxMeT3/3mJ07q/y/PO0wQ11UruQib2YnhbE0Dy8YTRn4oI47X7VxPcXJkP8eZuzdzxu5tDlnkfpQRR0dpibz3ru9z5u7NVMb6efuG57jzzz+htC/htGmuxXX9HLlYXzudRDDAE7+78UDZrSecTVE6fpirPA6HMuIwk2nrTR+81vjg6n9xXMsunpq5gN8dv4Llu3Y5bZprUUYcsxN2cG2lz7zlzItHlG+vzJ507DE+lIk5Xv1hvYUQGJbJBZvXsGLbxsz2XgFvmmC+KOM5AGZ1tfHgL77N3K7M+Mrz0+dwyQevAbzlCfmgjOcAuOH+PxwQBsDJe7bxn0/c66BF7kYpcZywd0dW2VnbNk2+IYqglDj6Rtk61OftqJE3Sokj1Kuzq7TywO87yqrZUDJTjcEjB1BKHCvfe1Uimq4iSZABEeG14Dw+/fbLr3TaLreizKjsEIHvJOpn7uvaHfXryb3fry1y2h43o5w4PAqHUtWKR2HxxOGRE08cHjnxxOGRE6XGVj70xtXBqKHHT9myn4Rf5/GFjfK+3y/2vgB5opQ4/IlU7A3rdlISTSA1wTEtXeLid9jWfXct9TaXzQOlxPG6NdvFzroyisrDSDIbNV3+7EYNljptmitRShw768ooNk0gsxBW08D0dqTOG6Xq45JU9iib9HakzhulPIdhWthCo2ggjQSixT68rarzRylxDAiDeTt70QZHBIq7Uzy1bJqzRrkYZXzuDnGjaGyJHhAGgM+yaWwbc4W9Rw6U8Rw6XWYoMZ3+kJ+nljRiC8GKtbsp7fPWreSLMuJIU6rtqinmG1eey9z9PUSDPm696Dg++Wcvm2C+KCMOCTxx3Ax+9NP7CaUyzdm20jC/vMTbkDpflBFHiG4ufX4DdbEYhrSJaT5qeuENz24F5jltnitRRhx+TOZ3daEPJu2J2Em6pcXiXdk7KXiMD2VaKymKDghjiBIrQVeZxgUXPnGTQ2a5Gld7jj7xsY+YBH5qYelQnHVcQ9JeWka0LPI5xMr1wE7gEeQqb27kOHCt5+gRV69PUP0LHdMoIS0q6ODQPHBRI8Ax+3rYPne6/lTj3P8FHgb+gVjp6i/FZOFKcVji8hkJKhdppNABHwkCpAjovSQ0A1No9PqCtAciWJpACI1zPv7NocsvBi51znr34E5x4D9JADopDKwDoyet9RbdYT+7iirpDBRjaRotlSGWbW3DNEY4i0WTb7X7cKV79ZN4SGJjESCFhZ84YHPKvm0Iezv7jAYSIoAMJ/jMZV9As6CuZ8Rebw87ZLqrcKXnQK7qLaL9RxYGSTQShACblB1hDwtJmDWE0xpzetuY1dvK7vIiNn//PwFiwFeQq5529gHcgTvFARTLX3ymii0BH6mbk/TEeyllH/NIEQIEMUrYwzy2VVRz9lPrU8WpxBygDrnq207b7hZcWa0Mocs7U2XweeDz+8QXpDxE62nCNLb2s7ytZwVylbf78ARxtTiGI0ZxghL4+D9e5MqWdz4/+Ra5H9dWK4fiJ4l5yKyvOD7ay7211PmijOcwsBgggAYY2KQwSGPQUpbdc+oxPpQRh4lPJoVfaBKSw8preqKO2eR2lKlWTAbM/nB2WkmZtbWKx3hRRhy18hf+1qIwqWFLEdpKw3QWeWkm80WZagXgufnT+dRFyzl+Wws9RUG2NFTwyVXPOG2Wa1FKHI8tn0Us5OfJJTMOlD1z7IzDXOFxOJSpVoBRU1lHg15663xRShxLdrZmlVX3eq2VfFGqWukLBHj/gy/xyLGz8ZsWZ63bySuN3q4J+aKU51jx8jb54HGzmdHeS3EswT+XzWHRa7u9rZryRLlUk5878eFUY9eAz9R01k8v6fjfR8+qdtomt6KcODwKh1LVikdh8cThkRNPHB458cThkROl+jkAznvnC+t2V1QvqhgYsGpSvbP/dufpe5y2ya0o1VpZ+NEddsgfFgFbYgMdPo1lu7csuWfV6eudts2NKFOtzPzkzlJ/ICMMyDxYTdpma1nNWmctcy/KiCPS2fN02JIIKQmZFtqgR0yGQ146wTxRJuZImbI6kkwyK5bAkGAK2BcMEPVbTpvmWpQRR73QKmZHEwzlKzYkNCSSzN31GjDHSdNcizLimNOfwF/soyQWx2+apHwGveEwPaV1TpvmWpQRR9pvUN3bh8+2AfBbFv60yRk71gEnOmucS1EmIA2b9gFhDOG3LBZ27XbIIvejjDh6/KPvjtAe9maf54sy4kigCV8yBrZJaayTcDLKjK5d3Hjm65w2zbUoIw4RDvJ8bRmptCSR8BFPS3YFLPrD3nLIfFEmIJ3W2cfctk5KowdznScCxbxnzfN4SWrzQxlxRMyECJgWe+szE4oru3oIJlMUpb0ppPmixMDbW96x9ucze6MfTZeWIrVMTWmk01zw4hMsbV9PRbTVqiAVQK7yuksngOvFoX29/6fHtXV/Yt5Aiqp0pikbSQzw+X/9gvq+NgBsBD2UMEAoNYOWKuSqfidtdguuDkjFTemgpmufeGV2HamA/0D5BZsePyAMyGQy9mlgGwG/BX9ywFRX4mpxkLY+ZPkNQLCt4mAGn4a+7JVvATtOqd1Lv1Z67uQZ6G7cLQ7Bi0P/XFddwuqGcqI+nXW1c7NOTWhBEiJIQCZik2qji3F9zFF2bafdGwkLQgcXTBcl4uz91icpSQ4gAIlgr1FHryhnSXr925CrVjlnsXtwfVO2t7pUL9vTYcWLi0TYEMzr3M/196+iJCmBEBLow0e/FZCL5PoVyFVPOW2zW3C95xjiqyv+Itv9VVy8ayMX73iSoJXCRvBy7UzqWruumCZ/8junbXQbyojjjPest7/+6FNiyb79CGx8JNhaUcvNrzuDP9x1ijdVMA/cHZAOI2yaLNm3HwCJRoowjV39LNi732HL3Isy4miMZRJMmkKwobKC3gP9HiHnjHI5rg9Ih+jyG/LuJYvEd08/hdZIhGA6zcdeeoXN06Y7bZprUcZz6Jbky+efS2skAkDC5+OHp5xEb8jLCZYvyoijLewnrmc/Tlx6sWi+KCOOcgv0UVpeS9vaHbBGDZQRRxyb+lQahgmk1LQGJ/t45IMyAWlDR7ecaUl2hcJsLi5ieizB2fv3c9fJx3Gx08a5FGU8x4sVkf2/OXERLdUlLEwl6a6M8N3zTua16kqnTXMtyoijytB/UBnrY21tBQ8cM50NNeUgBJes36BGF7ADKCOOR+5Y/v0f/OlOavp7ADAsk88/+jc2ldf3OGqYi1FmbAXgze946YdXrnn2Mw0DLSANbj92hfWz+85VJq6abJQSxxBXvm+bcdvtc0yn7XA7SorDozAoE3N4FB5PHB458cThkRPlxPFq5BsVT1bc0LPFuObLTtvidpQKSP8+43vW8j0dmiX9BImxuaZcrmj9qnJfgMlCGXH8quEXb1ve0Xf37aedzroZ1SzY28mHnnqcHeXCfFvLF7xJHXmgTAfRnP62u/7jivfw1OLMbpAPLZ/Dk4saufvX/zN6yh+PMVHG5e6umn5AGEO8MqeOV+u9rUPzRRlxSHFwB/tgKn2w3PCyLuSLMtXK0u2dXPDSFq54eC3zWrrZWV3KrRccy3Hbu5w2zbUoEZBqn23buTARn/HzX96PZg+bMyokZaKDJdaXvYmkeaBEtRKMhGbUxJIjhQEgRVZuUo/xo4Q40j6d9dOrgWwvaAe8mCNflBCHBNrLIpQzMr4opRef4SWMyxclAlLb0DDSJi0zDZbs3MoAEYqIUkI/iZjXzZEvSogjlDSJhQPYEvxGggZz4MCxmG5Q4aBtbkaJasWwASm5+bRzwT4Ydzw2Yw6/OvFMx+xyO0p4DmFlWiRPTZ/DHQtP47Rde+gPBrhhxQWUJQZArCxBrupz2EzXoYQ4rKRJIGWycsM6bnjzhewvL2VGRw83/Pl+Ltz1CGD0Ii4HDjRrJRg+5F1jN2XEO44FPgYEgVuRdz1+lB5jyqFEJ1jtZzqk5gehGwiguyhAPGBQlEyx/ttfY0ZyN5nvgQ0c7FpHrjp855h4x3LgaTLCYPAGb0Xe9dfCP8XUQ4mYwwzoVCRsKqMpKqIp5rT1U5RIEw36uW/ucjJ/U5vM4w7vQX37WMk7Ps1BYTB4g88X1PgpjBLiKDbtEQ8igMr+JEho7O0YLJXDjg4hx2rIREb7uDzNdB1KiEOzs6tGXUoueGUr5+x7aegsMgIZijsEyLvXjHHr345SdnvehroMJcQRG6Xb/Nidu7jzzmZCMs5BYaSHn3LOmDfOxBbvB14E1gNfAH545Ba7AyVaKzFdY1+xn/JoCoGkKxJgWVsUaZewPVjFnMTmKFileW2pIe+6Dbit8FZPfZTwHGbYT1dJkK31JWypL6WrOMhdy0/Ap5kgNZCrIt5eKxNHCXFoIrtFWpRK4bdTFKXio1zhMR6UEIetCYy0dbDFasPHnn4MDSiRnjjyRQlxxEMGpjbyUTZW1wOQUiOscgQlxCEkcEjV8tfFx/Lg7AVsKp7pjFEKoIQ4dCt7KqBu23z6De9ld7k3YJ8vSogj2D5gCjlSIMXRNOF0ihRezJEvSlTI/T+s9t0x5wfWg8uO0/66YDmaJZjR1c0N/7qbUDLt/pFFh1BCHAAn7d7DO7a/RFq7nf2RcuoGevDbFnfPPtlp01yLMuIYqiB9ts2Mvs4DxWWmN8E4X5SIOQDslCCNzsFRVwEIlu/f5aBV7kYZcTxSO2OzKfxkRJGZtxHX/aRNvxdz5Iky4vhoy6cXDcgIccKY6KQIkLKK+J+zz1On6pxklJgmOJw/1t0cPb2jJbTPX2SfFPu6J4wjQDlxeBQOZaoVj8LjicMjJ544PHLiicMjJ0pF8+IbsU0Y+nw0AZYE2zpffj38L6ftcivKiENcF/sKIWP+gXkdmgCLhxm5UMVjAqhTrejccOiEH3QNcV3/RmcMcj/qiEMbxUFICVLOmXxj1ECZagUhMmJIWZkcHT4NDB28HanzRh1xSAT9yUwgCpAAMvvYe+rIE3WqlbR1UBhDJNIgPHHkizriGGUxdWbdtFev5Is64tBHeRRNgKbOI0426rw5aYM+zEloIhNtHFrVeIwblQJSKA2CZWf+PSSOPm8Oab6oI46Qb1AUw5yhYPT+D49xoU61MpoGJKB76sgXdcRhSjBtiKUgns5UL7ZkVsc+tpZ8NOC0eW5EnWolYWX6OgbxRRMs7u7iwy/+S9T3dyXe9fq/2xvLqs1XZi44Xd5U+qKDlroGJTyHuKZvB+bIxD1pzeCyDU9w9rYdPFD1er736F+0zXVz/J995h8viGt6X3bGUnehhDgQzEQytI7pwM+PTryQ4/o2UpPuZr+czyktW7lz8WmUxqLLxDW9lc4aPfVRQxwwakDaGS7mxdqZ1KVaeXbaHN6+8QU6giUsa9sJcPpkm+g21BFHDmwh6DFK8dtJ/jF7CbWxbl6qmwXwhMOmTXlUEcfa0QqXte5iRlcfP1t+Dkt6XuPhWUu49LUX6A+Gn5Y3lfZMso2uQ5lFTeL6uCRlgWkjbMnc7jbO2r2dYMzijN1ruWvxcntdeU18W8OsY+VNpdudttcNqCOOG5Ny1HGUZEKevmOf8dTvFnvbRE4QVaqV3ANsmoYnjPxQRxzmKH9/WwKaGq7RAdQRhy0PCkQjM580bXvzOY4AdbrPbQkh4+CkHwPQbYgnHTXLzajztTK07NlgujcgeySoI47RhCCGUkB55IM6by6zgCm7TFOkre4A6ojDsjMB6ZAWbDm4mFp6zdg8USggRSIRmf4OmRmIy2yz4XmOPFFHHJqAaOpgYJoeXBZpeIua8kUdcUBGFKZ9sL/Dp4O01jtrlHtRJ+bQxYcREoIG+PXM/wF5Y8lyZw1zL8qIQ34j/P8w9GewbbBtQEp0sdBpu9yMMqOyHoVHGc/hUXg8cXjkxBOHR048cXjkRKl+jsZPteuff/JfcZ9uGz3Cv/Yrz688zmmb3IwyrZV3XfKCsSTZnn567lIANNvm0hcflx996b2ed8wTZV7cOXs2JYaEAWBrGv9ceor41kl/usRBs1yNMtVKKhTSGrq7ufSlF6jt62XttEb+evzxFMVjfwb8TtvnRpQRR8KGL/3tzxQnM9MCZ3d00NjVyarGuWrUmw6gTLUSiaUOCGOI43bvYlp3pzcqmyfKiKMskcoqE0AyEFbmGScbZV5cY2yv4JB5PQJJLOj3PEeeKBNzpA198F8jBVKUjk6+MYqgjOd4bNoiNKwR+Vtifo39ZdUOW+ZelBHHQ7Pmcc3F5zMQAFvY7Cgv4sL3f5CGjh6nTXMtyvSQLv3ADrlu2iFeQkrO2LWXJ3+7wIs78kAZz3FMe0tmeULKhKSZmWAs4NlFM6n+/C4vjXEeKCGOz51+X+jkts0ZQQw5wsGF1ZZPp6OsyueogS5FCXEEOvZ23Hzq68ja482SYFrenit54mpxdItrz+8VV++7sG9nuDMUyTrut0yQ8JaNL4gH5nxVRr7cJcU343L2p7f/2gFzXYdrxdEtrm0E/l5MV72UZQBUR0f2aVz3yGMcv38/RVaai7av55a//y8IwY7Kug+JbybeOflWuws3d4JdppEOCiQSP+gaf/79HTw8Zzb7iou5cOs2Ttu7Fyvko6UmE4i8a+0zXLnyk6AJyuLRayB4p8PPMKVxszi6JQIJVCa7QYCwNC5/aQsCCJFpoPQGgizZtwmAvkDwwMWW0NocsNlVuLZaAf4oMTbbaMwe2MrbXtpI0YBGigBJAvRQQkegiN+fsIw+fwiAb531VhAQSCboD4aucdb8qY+rO8G6xbUVYH/ipVL/txJiDo09I2OObZWlfPwDb2Bh104aOjv5vxPOpig2YAvDOKH/xvJXHDLbNbhaHENcvfS2x6/YEl1RnEyPKO8Kh/ivN5/B4wvqZP83K93sJR1BiRf207VXnqUdkqNFAl2REGXRBAO+sPu/AQ7g5oB0BJYu2FcaoSSeRApBe0kRcZ/BvnAIEvEHIeS0ia5DGXFEgwFqBvrZXlFJ1O8nnE4xu6uT8r4BKb9V8Xqn7XMjyohjd0UJM7u7Oba15UDZzpIyFnR0eVVKnigjjkRAsKWygqpoHN2WdIXCxIvSBC1r7Is9RkWJgBRgXVUZ53S9SKnoRPPHWBrbxIX71mB54sgbZTyHpSMNaYtZ8ZYR5aVW3CGL3I8ynmNNdT2dwZEtkrZQERvKyj3XkSfKiCMVCPHON72HDRWZqYLrKmt455veQ9rvrYTMF2WqlfndfdwzdxYnXPkfhNMpYj4/NSmT5ft7nDbNtSjjOQxD43X72qm1bHy6QWPa4l2vvkbANL1qJU+U8Rzh7q54pKg0fOKza0j4fFQMxFk/dzrdWnX2FDGPcaHEwNsQH3njarululoETRNTE1TtaY3/8uHTw07b5VaUqVYAfvn3k7Si7t5Z4e6+/+oMhTRPGEeGUp7Do7Ao5Tk8CosnDo+ceOLwyImS4nhe/KD+BXFzrdN2uB2lAtJ/Ft0yc2N92Y6dZVVIIZjX0cLito6ac6KfbXfaNjeiTCcYwNr6ih2zW9Is37oTgN3lEdbVVLWeo6iHPNoo9dJ8KT+V0YPZFhq7B4jqJd4i6jxRShzTugeyyhq6vZxg+aKUOGbEWrPKFvXucsASNVBKHHPtjZQztARWUs0+5libHbXJzSgVkJqaj6X2apIEEEj8pGjT65w2y7UoJQ5p+0ljkKaIFCEC9KNbSj3ipKLUm/Mj2cMyUsNWtxXT6aBF7kaZmEN8JzX/nnknjBAGQD8V/GDWb7/skFmuRhlx+GKxjYvbuon5fGyvqGBzVRXtRUWAoKMy8E2n7XMjylQr6aKwQNisr609kFWwLxTC0mEg4iXHzwdlPAfAbSeekpVucl9JGTWd2Z1jHmOjlDhaS0voD/roKD4Yd1hCY0lXr+c58sC1o7Li26kaYBtQBIAG4XSapGFg6Rrz93Xymfueo7Q/wX0nzuJ3pywD2IoQZ8qv+LO7Uj2ycKXnEN9ORYBWhoQBICAW8GPpmUfa3FDJjZetoLwnxvbSiqGz5iLlTnFjqnGybXYjrhQH8D8HNlWBzFMIMfIH2F9ahG7Dusaa4bFIALh60i12IW4Vx9wRv40WUQgBFvzp1Ln0FgUPPer1qY8Dt4rj6yN265LkFMjvVywlkMzaHPCPR80yhXClOOSX/Y8AfzwgCptDt3YDKUGDvmCYZS07Mr9n+JL8iv9vk2asi3Fta2U44tupP6KzckQfx+BjVff08Nvf/FpetP+LrvwiOIkSL0x+2f+2zD+G/QzyjQfu4Ylab1fqfFBCHADY0NDeN6LoDWteJZko48RdHZ448kCZsRUksjscEJc9vhENm3l7u+gsCnHDWy/ixtv/6rR1rkQlcbSGk+m6ize8xsnb9gGwob6S5xfXs7a0qMdZ49yJEgHpEKsqb5Ozu0ZWLfuLw7yh70Pe2EoeqBNzANO7skdfq/u9VJP5opQ40lr246Q1fZQzPcaDWuLQdcxhj2QhSPjVCasmG6XeXJndT4gEXZQjgAo60WTRmNd5jI5S4miw2ikixnQOTteIJb1ZYPmiVLVikJ1ydLQyj/GhlDj6tOyUo91GsQOWqIFS4rh/+rRoP5EDwyt9RHhoWq23zD5PlOoEA/jWcXf/tCIV+7itgSl8P/3sund/ymmb3Ipy4vAoHEpVKx6FxROHR048cXjkxBOHR06U6iH9+6JbSl8L+rttTQrdtqmLJr/8zs2f/o7TdrkVpVor1511p/zlCefREgoRNk0+sPYZztuyecblW/5jt9O2uRFlqpWbl/7quZ+deD4tocwi6phhcMuyM3mltm6Hs5a5F2XE8er0GSe0B0eubJNC8PK0Od4ssDxRRhzd/mKKkwl026YmnsA/uBN11YA3EyxflAlIK6QQNzx5L10lc5nV08n+SCmbSn1csGsjcLbT5rkSZcThNxNyZr/gyhf/SMAyMTWNJ+Yex6s1lU6b5lqUqVYMMyXO3vIyAcvM/G7bnL3lFWZ09zpsmXtRRhxL9+6WQTM9okyTkpP3bHTIIvejjDgeb2zEOiRZnAS6AprXWskTZcSxvaxaBGU3cnAVtUTip4/ucEidXr5JRpmA9D0bnscghkEcGx8CEw2bhqgXc+SLMp5jXl+bEEg6wyXsqKzF1iRgE0p7E4zzRYmxlYvf+Xzo5gd/F7vnxMtoK64CIYgkBvjIU79lRvce0tAbkKvKnLbTbSjhOTZV1Q/87pS30VZSfSBr4EAwwl0nXIoGGFCKWGkhViYOfyeP4SghDr+FiAZLD/w+t307p21fTdwXwhQ6g6tlNSCAWCkRKy9xxlJ34fqAVDSbb5sjbBGOR4kWlfChZ37Psn0bALCFQIxebf4dRb4YRxP3vyDbfp9PWrxYXsrr1z7G6rpaPvyWj/CTUy4kqefUvtf3MQ5c7znQtIfjPv9b+osN/nf5sdx97MkHDq1afDIP/ebbDhrnbtzvOeBHx7fuYUnHrhHCAHh47lKenT53tGv+MSmWuRzXi0M2GTKYTNp7I2WjHu8JjkjBYAOnIVe9cRJMcz3ur1aAVn/4s8IX+J8ZPZ3sKjs4RF/f182pOzcBmMhVPscMdClKdIIB3Hz67+QF21Zz9aUfYn11A0tbd/PTv/2Gfl8Zp+/5hheA5oHrq5UhhG3ZqxacxJN1c+nWQzzeMJ9fLzub9kiVGup3AGXEkZI2N664dETZD0+5iH3Z22l4jBNlxLG5tFKa+sjMgVJo7CgpzXGFx1goI44OIwS2PbJQSno0vzMGKYAy4qhKJQQpC+zBEENKSJm8VuZtypQvSjRlASzdJ7FsSKQzneODGkkbocNe55EbZTwHWkgwuDPkgf1WBIQdM8j9KOM5Qql+eWCXyGHimNXf7qRZrkYZcUyLdYFPh7R9cMxVF5SnvSS1+aKMOIil+rWAVmYbGpgSdAF+HV8yPfa1HqOiTMzx1TVXlV+yeR34DQj7IGCwtHUfF657+VqnbXMryoytADxa/fXP33fssuatNbU09HZz4Zq1+96494vTnLbLrSglDo/Coky14lF4PHF45MQTh0dOPHF45EQ5cTxS992bX6j8es/D9d//kNO2uB2lWisvVF1vL+neKfy2SdwI8ELlTPvslq9420PmiTKe496Gm3Ys69wq/HYm7VPITHJy+3btn3U3Xemwaa5FGXEUSWYc+jABO01POPhrRwxSAGXGVkr7o6Tx0UcVJn58JCmig5JozJt5nifKiMNKB2inEYvM8pQUQaJaEcXewFveKCMOqevZm4TaBj2Gt+lwvigTc+wqq0JgESRGmAECxAGbtuISp01zLcqI49X6ekLE8WGiY+MnjZ8kuytLvJgjT5SpVub2tJHw6dxy1lk8P7OR4/bu55OPP8miVm+aYL4oI47O4hBv+/D7eX7WTAAeXLiAexcvpLK7W1zusG1uRZlq5cF58w8IA5H52dBQx4tzZgvRbHp9HXmghDg+fv69/7OgvSUTW2giMwN98KfINAHe76iBLkWJauWiDVs+FY/4qR7op71kZOukq8hbuZIvSniO2d2dXLR9I+94YU3WMZEZV1TiOScb9780sVIzwlHx47PP4f2Pv0QolRpx2MysghOi2ZSi2fyVIza6FNeLw4Lehlgrt55yOn84ZQlx3yHZnUZus3GVaDY/Opn2uRkFxKFH+kJBkHDzReceKobRuHESzFIC14tDArrtoySZwtLG9TjmUTZJGVwvDh9W26vVi9HtcW+d8amjaY9KuF4cmlxV+/T0Bbz+5c1Zx87fkLW/27WyyfjjpBimAEr0c8T9UXnhYx3Cp9/L/516Erpt87GnHufMp9o5/4Y5xAN+KZsM138RJhslxBHTfVIPJcRZj3Ty1kfuwEeaLsp5cvZcEn4fZDIXe0wQJb5Ncc13RU+NRbTSpl1U0EItW6srufntp6HO3PrJR5mlCVvCX7Mr7XZxyqe+ThofO2tLQQh008QyjGtkk/E9p210G8qI4+7Zv7BX7N4kHqlbxi/OP4291RGKk1FCA2n5+G8WK+EhJxslYg6APy1eTvHeADV7Lb56+5MAdFcEue38hWqo3wGU+UYtbOnClx4Zd5Z3JTh7o7ddeb4oI46aeE9WmQTqYt2e58gTZcSxo9JPtCgwomzP9ApeavCyPuWLMuJIBEJ88/3nsvmYOjqqS3jpuBl89X3nUGx6XRz5okxAurC1lf89K8LVH76AoGkR9xk09HRT0+/tZZ8vyniOTfVVfP9Pf2Rxy37iPoPj9+ziiw/+k5aSYqdNcy3KeA5i8l8/Peus8z/43DMsamll9YxG/nbsIuZt61zptGluRZlOMIDvnHaH/eqcBrGlqpZl+3Yxc3ev9dVnL1fnCzDJKCUOgI9d+OgFOuZ3faZ25Q//dd56p+1xM8qJw6NwKBOQehQeTxweOfHE4ZETTxweOVGqmfea+GxCUBFIEUDDIkCPPVve5OUhzRNlWis94vOihSq7i8oDZWGilLAlOUf+xNuWOg+UqVZ6sJLdlI8oi1FEigZv1+E8UUYcAr+QozyOjVer5Isy4ggQRctKNikJ0eMljMsTZcRh4cfOehyB7nmOvFGmtWKi48ekjnaCpEhj0EolKfxqRNwOoIw4fFjMZO8B3+HHZDqtaPR51UqeKFOtCMxRKhWwCXieI0+UEYeJD0ZZ/Gjjyz7ZY1woI44UfkSWOGzSGF61kieujzk6xH9UGsj2KoSw8KNhIdEBicD2PMcR4Hpx+BAdBhKBxEcSC40kAguDFEWE6fFijjxxtTj6xafnGwiG6g1JpkfUwk+SACY+9oWrxHvfudH22xJ/b0/87gdO9zZgGSeuFoeN+XFtMBDVMIkTIU0IAQRJk9BtXq1dQHUyLp6eN53jdsnwZW952TqrvasEWAlEgHs+99T5LUP3vGXJ36+UGt9C0qVZ8k0f3/CmPQ49nuO4OiDVSTaCRZBefMRIM3LwNWhZBMw0S3e1cva67ewrC1OVSGi2afUAtwE/BV67+YyHTwX46dJ//DWQSNwaiiYaQ7HEMs02d/9s0d9Om/QHmyK4Whw2WrVBEoEc7DrPbpj4LYuyWAJb1/jEP1fTURLGsORwjxkBbgDQTfNNYliE4ktb2Lq45+g+xdTF1eLQEX6DzAZ/BiYGyRHHU5pOW1EJCEEknjl22mt7McystJRzAXQrO12lkLI8q/DfBFeLw8T3vDWsqVpCG36iWELQEYrwVOMxmLoOUtIXzJwXTJukAlnN23sBUn7fKAlsxfNHy/6pjqvFAdZOicAaHHnVMbFIs6msiidmHEN3qAikpCdSxIrNewFI6Tq6tP8PBncIhD8BXwYQiDcmg34pAVsI4uFgzyfWvfEsJ55sKuDqaYI94pMfLyJ+i0THRGMtS5AIyuimV4uwN1LDjnk19AhBJJmmIxJiU8jX9aeHV1TefMbDIcD3uafO7zv0vj9b/PfThJS7P7bhTXsdeKwpg6vF0S/+09BIp4NE6aWYbsqZy5YDx9upoZ8K+YmVl/5Qt+Vp/7hn+ekOmus6XC0OgA7xaTuEFBoJ/CTRD8lH2025LJc/dnn16Qyuf2lV8kdaikQ8SkBmhGEBKTKbI0iCJJw10MW4XhwA5fJXYQvTsjHJCMMC0kiSpAl4o7J5ooQ4AHQMxCFbqQwNxnnkhzLisEiPMp8DGOwk85g4yohDw8iauyGBJF5OsHxRRhwCkxi1pCjCRsPET4waLAJjX+wxKq4esh9OEgOdIGkahpVKTEx3t9UdRBnPESJN9qisIMKA11rJE2XEkSbAaLPPPfJHGXF0IRI+BkaU6cTpptRTTJ4oI45F8kfFnZThpwcf/fjpJYlOFP7DadvcijIBKYBJyLeLGSkDW5joUqPzkQXyez9y2i634vqBN4+jhzLVikfh8cThkRNPHB458cThkROlWis/W/JX8fLMBnNPealWFovLWR1d7/7W4+fc6bRdbkUpcdx37Hx7U1VF5pfyUrG7JHLHE8Z/Na4wr2121jJ3oky1cs2pD+7YUl5KX9hHbW8f56zbRiSa4tkFS773oLjVG1/JA2U8hwz6G3fVlfCJfzzPpc9tPlC+s6aYSvanwEvUMVGU8RyxgI/KgdgIYQDMaOvDwPTyTeaBMuLoKgpR39WPLaCjLELSl3GKAoGBEFvEtd902ETXoUy10tDRy7PzpvOTd57LQFEIX9rk7Bdf4/S1WxBoaISvA77utJ1uQhnP0V4UZnlrPwNFIQDSPoOHTl1Ef42ggj4ShB220H0oIw5NSFL+7JhzW20NflJU0u2AVe5GGXEkDQ0xyghzoF+Qxk+IOFvEV70m7QRQRhzVsSiRVBqGCSRgWYiQRjeVJAhRSZsyzzsZKBOQVvXG6K+1KIomeeuaBzh+7wY6IhVsLZ4PCPopZhfHyPOcNtRFuEocJ35092MaFD3/i8YTDz2W8Acoj8Z494urOHnPqwBM62tjidjCTpZj4aeYhFetTABXiOP4j+5+Xzrgv23ntAokgvn/2S71ZOr2DT+dduXQOb1+v1i8Zy8n7Vk74lpDmhTTSQ91NLJZh6wdezxy4IppgiddvU++MLsahMjEFJakvnOAimjMPm/jjjrp9+9u7OkLzN+1l0v3/w2bCDb+wYRQA7QznTQlNPJy8dNcrAHJ0+VHk8+In2tA2WnyY10Az4pblgvkxlPk1V7eBlwijorr+mV3OADxNH5TkjIyveGalEyLxvjYi5uYvX4fszrbWGatwxixfkWyk2kEgS6KEaRljNIBkH8RcDbQCPb+MKk6P5awEMQI/OtkefX5TjzrVMIV0Xuf34D2KGV9iQPCgExSN1sKpm/voKw3QbHsQM9a2CQIk0ajDx2BQBcgi0G8V0IjQJhUvR9LAOhIIiTOe0bccsXkPeHUZMqLQzSbJVbcBAllo+QJ7Qn4KO6JI4H20rJR72FhUEonJhoCnfCwxU8Sif+QMEQAOva//XqXKS8OoB8r4w1qYvGsg40DMeJFfqIlfjbXz2bAN/KRkgRI4iNEPxoSCaSGrbyXgDVK5mMbbW1W4b8ZU14cssmQQzFEh6ZzYmsn2mCcNG0gRiSdZl9jOf3FfhCCVcdcxNZIDX1EaKOWDsqZwXpsNDRsDPow8QOyB0BDECUwojJKYKRtxEcm+VGnHK4ISGs/2y57NIMUsKBvgON7+rANjaSuUbO/w56ZTmtpKajrOZhStLF/D6fuX01FeqjMYhPLmnuo3wV0gLxHwArgOOBxsM/QkR+2Ea9aaO8/Q37s3z4lkCvE0fjZlnQSw4jpmWA0bNtohm21fK/6QD/Nl85/Ss5o72Qosb1hmZyw6zVKYgka2YiNRoiYD7lqlBTWHqPhik6w3T+o89Vd07G9VFizBBpxaG37r+q64eeIeNw+dd86TZNgCo1UXxF9ZhV9wB6mUUwf1ey3Zjj0DG7EFeIAaLmpavbhjofTaeb17MNvm7RRxTaqhh0V9FPCifI/p76bnEJM+YB0vCQ0jQF/ZqJP/JBNeTzyQxlx7C4r5fmGRVhCUEpWrnsEkn/qv1HmeScD11QrY2EK2Flcw19mn8n0aDv2QBoR9Q32YEgq6OJY6xp7jNt4DEMZccyIJdBTFlEtxKbiGVAMmmmzcHcLlVYPlXQ4baLrUMbNxoJ+AumRrVTb0OgMFyOxaKMux5UeuVBGHMmh4fxD0E0AHR893jyOCaKMOMpiccJiZBL8UCxNJJ4kQqc8Rn5HmSp0slDmhUXiKWmHAix/bQe7wzUYKZuygTjTacPAWw2ZD8qIw9Q12W2HqDN7mdHVQRofftL0+wNEU/r3nLbPjShTrfRXFFcYSZPHZi6irbgMacDeSDm7w2F7rvzutU7b50ZcMfA2Xr541qOt03ujNb5UGsvQ6SgJW9c9db4y3nGyUUocHoVFmWrFo/B44vDIiScOj5x44vDIiXKR/NfPe+ICifiaL52+4mtPnLvbaXvcjFKtlWtf/5wdlrbQJaSFwDRN88aHz/SyCOaJMtXKV859fFPEsoQ+qHWflAQ1zfjWikcWO2uZe1FGHLbPd4wQIxcnCSFIGPorDpnkepQRhxhl1VpmCF8o84yTjTIvzpdKIg5ZS6ubFnKU9bUe40OZ1kpR2gRbYls2tibQLBtdSorT//YL1/JGGXFoaYu030cy4EdqGkhJMJEk3Jv0Uj3liTLVynN1VSIRCGSEASAEiUCAkmhcbPR/0xNIHighjm+d+Yg+3bZBO0QDmiAkU4i05aVxygMlxPFKVelVDa3dYNvoloVhmmi2jQT6gyE0DK8jLA+UiDliafPC6v2dDJRH8A21Tiybks4oPcWl2Lo6vcCTiRKeo8K0V26bN51wIomRSqObJkjJQGmIYzbuI2THRbf4wL99pp6JooQ4rKBfK7LSaFIiAM2W6KaFbWggdJKGhknxEqftdBuuF4f4bNsLXUWhrAfRpCTcF0e3bHwySYR+ECuzMh975MZVMYdoNhcDD2HLOgSQNEH3sW9wj5URSMm0HZ0E7CT1Zgd++pGwGrFyqKN9I/B65Kpdk/gIrsI1nkM0mxXAGqAObXDpY08ChGB9dQX7DxHItLY2FvRv5Yz4s4M7/2kIGD4CsxB4DbHSNe9gsnHTi/ksHFy65ktYYEqEoWNpGj9dvpC/z57Os3VVpGUX79jyD5akNhGWh+3i8AP/9lkDc+EmcYxwDUPpJ+WgK0gYBo/MqOfV0hKK22x2M5sBIoNnW0DO1BxFR8VaBXDNTDDRbDYAexisGcKJNLGEBSkbfBmHsnhfB7+6/V4C1pAQJLNZRxmtgBxtUN8GQshVqcl4BrfhGs8hm4x9wBuBOFISCxhUIglIC+yMGN75wsZhwgAQtDMdmwDmYCLaYV+FbuBcTxi5cVVrRTYZ98LwbR59GJ/viFf0p4JdwQChePbf2cKgh2n46KaY5G1Crnr/5FnsblzjOXJhfr8qVG+lCNg29y2amXU8bgQAE4MEnjAmhuvFAZA2dHtBZw9d02v4+7Hz6fAV0aWH2Di9ji31NZhomKS9vUMniBLiOGbL3qdCPh/H7O5gxau78aVtdAvq9/QiTZ0eSmWJvLPCaTvdhhLioKT4prjfx+kbstcwlXQn6NP9DhjlftQQR1H4/iLTxtKyH0dqgjrZ4Y72+hRDCXH87f/NSM3u6uXpxY1Yh6xdiRYZGHag2iHTXI2rmrKHY3FLi9xaXyfuvORETtiwm3AqTbzEIKlLWd92Y5fT9rkRZcQRGkgzf18bUhf0To8QtW1SUhDuGfCqlDxRRhydJUUs27CLWEmIVMAgGE/jS6TZPKfWadNcizLikFKiA8V9IzcJ1LwVb3mjREAKIPTscdeUoaMLl4wsTkGUEUdx/0D72nnTDrRW0rrGlpk1hAYSH3DWMvfimiH78fCtcx6zk7ouMCW6BoF02v7Sk+d5ua3zRBnPAfDVR8/Wpu/ruL0yHo027Ov8D08YR4ZSnsOjsCjlOTwKiycOj5x44vDIiScOj5wo00M6hPiv9O0Irjgw1dzmOXmt71RHjXIpSrVWRLOpIaXF8GF7KUGIBtlk7HfOMneiVrVi2ikOmc+BEGDae5wxyN2oJY5Rk5EeptzjsCgXc2BLsGRmXZwEdE8X+aKWOCSZGMM3zCFacsQyN4/xo1a1AiLLU6j2hJOIeq9utIBU82KOfFBLHIJR97PH00ZeqCWOXHjayAv1xHFoteKRN+qJ49BqRXqtlXxRSxyjOQ3Pk+SNEv0cnz7uD5tDVdXz6k5ZSktVmdPmKIM7B97ESq2X0Iv/nLnsuKcXnCrixRWk/EEAtpeGeWR2DfbQDgpSZtYs2PIy+SX/nxyz2YW4Thwx8b5qSartF6deRmdoGuUyyoaGuSPOeWZ6BetqSg8W2DLzY2g+2WSYk2yya3FdzGGQen71zCW80rCYM/a8QlekLOuc2oFRco9qAtJ2z1E3UCFcJw6JbNhTWk8kZdLYs49pPW1Z53SFDknWMhSUiuHJ5jzGwnXiEOhrp/W0kjBge1kVl615lLJY34HjocQA66tLDl4wNBiXuXrnpBrrclwXc6TEe30mduJ3x1+svVo1g889+1fqB/rYUDeLjmCY97/h7exrrBu5pZdlg42UX/K77svgJK4TxxBRccWHHi+d/vPnFy3Rt1TWi75AiEca59FTWgyRQ6oVaYMtwvKLvvjod/MYDdeKYzifueCfZXY62vmTN7xBoGkj533JzH/kF3xeb9gEUUIcQ4ibUjbaIVtEeuLIGyV6SA8wpPPh4ymeJPJGLXFAprNruDOUeALJE/Wi99FqSXVqzklFPXF4FIx/D3F41UpeqCUOQfYTCcgxsdRjDNQShy0zq5l0kXkyXWR6Sj1p5IVa4hDiYBUyfAaYV63khVri8CgoaolDkh613M69b6hHbpQSh/yiPzjq7HND8zljkbtRShwASC7EljIzd1RKJFfKJsMLSfNAqYE3j8KinufwKBieODxy4onDIyeeODxyotx8DtFsmhwU/etlk/FPJ+1xM0p5DtFsSkAn02EugAdEs/klZ61yL8qIQzSbL+Q4dOOkGqIQyogDOD5HuTfslicqicMTQYFRSRweBcYTh0dOPHF45ER9cUjJu1e+YL3pratvc9oUt6HMqOxgH8dILBuiaXxJi5BtsaS9RT71m0XqfyEKhNovypJgStK6Rp/Px3N108QZb3lyk9NmuQW1xeHXYVhKDksIYtV1xzhokauYcmMrotkMA/1k+i3SsskIjOOa7TkP6hoMm0I6vadDwNycp3scZEp5DtFsngZEydglAL9oNqVoNnN2cIlmcxEwK+dNzYPC8Fk2x3bEWHbVljWFslllppQ4gKdylI8+qzzDulwHiuNxfv+HH/M/99/GGbu28dnn16AJA7+ZPrboK72zhp/bvOIR/dirdy+57O2vej2tg0yp1sqoLY6R3CybjM8fco3NYbrOH//J11ixYxMpzccdi9/GLaeeTDzg44on1vDnY2fTEo7gCxtsqS0nresYlsWsth621FagpawuG1Ejrwtag5/1PuDrQB3wR+DTssnoEzelXwd8H1gIPFTZn3gu7je+FvcbejBlUt0V3b+3JLzH0rTjEAj8ml8IOHF/N8d0DWALwfqKYtZWlxBIm/asjr5HN06vukRe60sewes8YqaM5xDNZt/YZ/E50Wxecuilh7tgfe10APx2GoMWnmmo5ZWZtdxz8kLeu3oT5WaKjfWVpHUdAFPX2VJXQeVADNuvV/gs+7lB+04BbiUTsBQBVwI/Fjela4C/AsuAAPCGzkjgG7GgT5eaIB4w2FUWqbc07WQggMRPymZpex9LO/oJ2JKQZXNiey+ze2Mk/H5t47Sq82a19tw63nd3tJgy4gCKx3nep4b+IZpN/bBnSsk529YDmZbKmzc/x/SeTrDhpVn1aLakOp5AjrK7U0U0DkKQ9unLB0tXki3EtwFvAEKHXn9g/Yw9ijOUUBzPriln9cUO/Ls7Ejz0SzDpTKXWynhz8AzfI3aMlWySpxvm88GLP8mOshrO3L2R7z58F1dc+RlKYwkQkNZG/34M/W2FlEN/xdZRTmvJUX6QHLs2yFGK48ZBrQfSVu9h7zsJTCXPcf04zkkDXxv6ZXCxUs44RbPgqyvezY6yGgCebFzIbUvPRQBvenEzXUVhKvujRJKpEddFEim21laClART6abB4luB4U1mCXwTeIBDAml/2jq4+bEm0A+N63TB1spirGECSWoa6yozyXV12yaSSDk+g81NAenjwLtlk7H3kGtyBqShgSRlXdmpRy/bsBYLH4/MrZGdkXJrXluX0VZbTm/IT3E8xc6SIqTQ4kUJ86MD34r8dthnVQDvJxOQrpJNxrMA4qZ0CHgvmYD04dntA0/0+/VbLU07L5xM9S9t7fna/XMaksBJGMKPId6FECUl0UR6XsdAkaULuaUskqzoj+2uGYjvsTXt+hd/3vjIRN7d0WCqiSMJ+Ec5dI9sMlbmuKYfiIx2LBBLUtExUhwBM01d33759G9PmEpec0oypV7QYG/ooWo1cwlj8JrRA1lbkvT7SPlGxqyL23fjs/S3HKmt/w5MKc9xJBxaJRmmiSl0sCXBeBrDtEkGDJZu3yZf/M2SKfWlmKqo9JJGiMM0jMFOeEEi7GegJEhaExy3b7sa34ZJQCVxZCMEBzSTMvnCw3/ib4tPLHPSJDcxlfo5jgrfvvf3nP3aOqZFB7ji4g+t7vhBfb/TNrkFlWKOnE1a2WR4g2l5oFK14gmgwKgkDjVc4BRCJXF4nqPAqCQOjwKjkji8XKMFRiVx/DxHuTWpViiEMk1ZOJDVZ/hgipRNhkpfgElFqRcnmwwDCAKfBSo8YRwZSnkOj8LifbM8cuKJwyMnnjg8cqKkOESz+aBoNt/ptB1uR6mAVDSbcTKtlSG8puwRoNqLCx7yuxDNptcJlidTerKPaDbbgarBXxNAONfGOqLZ/O1o5aj3BZg0pqw4Rpm8EyQzfpJr9PXUo27UvxlT8lslmk2DHCIQzeavc1ymTvA0RZiS4gB+dZhjH8hRPmW9oFuZquJYfJhjuTyEN2RfYKaqOEoPcyxXzOG1SgrMVHXFW4H5OY7l8hyF3ztWrEwO3tcGAshV/1YCnKqe43CizeU5CicOsbILsVKSWdQtyMwRMRErf1ywz3ABU1UchyOXzYXJnyVWfhgoz3H0kwX5DJcwVcUxWhqGA4hm87+P4mf/4ije21VMVXGMlTzu06OUTc7SBLHy32YJxFQVx5vHOH74RHFHF8dzdU0WU661IprNqrHPAtFsJsi0IrrINGMbj6ZdwygeDFbHoo1MxiETeIZMOspjyLTC+oAHgVuRq1I573C0ECurgI+SeWd/Qa66F7GyEfgIUAHciVz1+JQbsj9M6qe8mdBC6vH94QvFX5GrLp3EzwOxshR4CZg9rPRbwMc5OMgpgfdNxWql8P0V40Ws/Pgkf+KbESuXTvJnvpuRwgD4PAeFAZn47UtTURxOBnyvc+AzczWbJ/PzRtuZonwqisPJeu4/J/nzdgJPT/Jn/pHsjQYeJvu93zEVxfH2CZ5fODHJVXsKer+DpIEXgH1ktgwZAO4FLkauMo/C5+VGrtoMvAVYTSb78q/IpOm+Alg7aOPNwJenXEAK49o9AcAnm4wDL1Y0mxuBBaOdOMGA9LC7MAA2cpU+eG41mT/21UAcueon4/4cFzDlmrKDPAGsOMxxe7gwBpmsZzk4T1Wuah/8V/MkffakMhWrFYDuMY6P1oM6OSOmctXhNgZSiqkqjuyE5cOQTcZoEXehek3/VKD7uJ6pKo7DTfY5ujO+5KqVeLPNgKkrjq7DHMv1hyvcs8hVGpnWxHCSBwLRfxOmakA6cJhjuVoShf1Wy1VvKOj9XMhU9Rz5BJdTVeiuZaqKI5/xhn+reGAymKriuPYwx/bmKPfEUWCmpDhkk5Fr82FkkzEjx6F/mxlak8WUFMcglWS3TA4392HyJ80ozpQN4mST0cXExHsCEBulfOoNHrmEqew5JoRsMuKMHnd8eLJtUYUpOSp7JIhmswsoI+MxGmSTcfhNgT1yopw4PAqHMtWKR+HxxOGRE08cHjnxxOGRk6PSzyGazQCZuZVDQ9w2UCybjNH6ITymKEerEyxxyO8aGbF4XdwuouBNWdFs/gD4jxyHl8gmY31BP/AoYIoPiw+s/Pi3d5eXbn1szsxfyy/7D7ykHuNTkUenz+gvSRj4LJs/LF3M70877ba271S830mbjwZHQxwpci9p/B/ZZOQSzpTgA5c9ee/vjz/+4pSugyXRpKQ0Gf9E17crfgbwcsm1dl1/QAx3gXcsn89n3/OuKvkFo9Mhs48KRyMgPdxUukOrmynFc9OvN24/6cSLU0Ef+DQI6pSaaXr9oVsANpXfUN0TmSYOrRvftHErlW1drzhg8lGloOIQzWbxGPeMFPLzCs173/aB7bZvpLa7I0GWdPUivhmNPjFtxjRbz04A0BYp542bXqufLDsni0J7jq+OcbynwJ9XUPaUlzVkFQrBzuIQhi7CG6ZVx56bOxdTCNpKSumKFAPw4pzZJP3qzT0udGtl5hjHvwx8pcCfWTDm7e+0186ZpjG84pDQFw4iNDh572Zre808brn4zUgtIwZfOsWi1pfpDdQoN0hVaM8x5lS9wbzmU5KgaWaeYOjPLAd/NIHUBB+97H1fLU5aB4QBkPb5KU2kmNvZq1wzvdDiaB/7FE4q8GcWDCNtZnpiJCNFMkifL/SBzkh11nU7Kho5bcdG5TxHob/Fp4zjnKM6v0I0m8cAGxjMEyabjLbxXttZFM4uHGrqSyBgkBolII36Q/zkdZfrV+Rl8dSl0J7jtHGcs1I0mw+JZvOEAn82otncDWwm05z2A62i2fz7eK/vCIetrE7cofhjUCMl8V6EfbD2tICnZ8ylxrSZ/ekd8s3vf+GsI3mGqUTBxCGazbGC0SGagfOBF0SzKUWz+dZC2QBMH6Vs3CvXusvCh30fAiiJRwmk0/hTKQKpFJFkkopEmlcqipnXEuOZ6obHJmr0VKWQnuPiPK+7RzSbxxfQjixEsxkaz3nvevQVIyvQGKpWdIGU0FVUmUmGLjO9pwDFiRSaJXl4wUw6iko49rPblOjzKGTM8bMjuPZF0Wx+QjYZPxsUSppMHs+FZHJlDgDPk8mC94JsMhKD1ZINnAdcxchseIcyZifELnF99earrhQAJ+3vYlFnP7aANTWlvFo9tOhfEEn20xcqA6A1FGRTeSlRn48UAlsT+NMWa2sbtoub0oKMs9EQ2AgRBf4P2DL4cwIwA7gTeIVMvBYlk7N0CbCMTM7SJ4B/DnsXBhlvGAQeA26VTUZh8r4fQkHGVkSzaVKY/BiSg5X+8H8Pp4tMcpe5E7hvQDYZh13XsiZ4/c/P/OLnPjqvL84Ze0cOkdw/q5atFREWtu7mn7/+Pr898V20FEV4YMY05LA+kQG/TlvAIFUx6KiyOtrHhU22R8/1LobOf7dsMu7K58MOxxFXK6LZfAeFS5wicvx7OBVMTBiQSYZ2WDZU1b2nvr+bed3ZC/yP6e4HCRdueZXpffv5zBM/I07/CGEAlCZNFrX14Eub+QoDRv+bHO5mGvD/RLNZ8H6WQsQcXyjAPY42Z451QmtRWeiD/3qJhJ79ShKGDkheaMjkdg2lE1TERk+BXmVamMak9/MVcRTymRZCHLcV4B5Hm8fHOsG2RFdFr4WvPzoi/0NaE6ypKQUheGrmAmz8SIpZueHFg8HqIH4p6Qv6MexJ39ApzlEYtzpiccgm40dM7gr3KJlcmRPhn2OdsGhP34094SBvfHYjd8+p5+XKEl6sLePOhY10hQYT/ApBJg4UnLh/B2m/wJASXUpCto3UNXaVhklrWpZwJkA+F/6HbDIK/jcoiP+TTYY+ztyhh+PbwNeBM8i0VvZxMGLvIRO1zyKT8XcAOIvMpKJLgHeRWeWWq8k6i9ypGwAIJMUvd1dEfvBabTmdxUGeCvghNPL1lMbjjKj+9RTbaiuoiKUwNY32iJ+yaBwQdmZcZvCVCEAIc9D2Z4DtwMlkBirvBB4Czhl8rkcHn/tCMq2zh4C7gGPJ7LqgA5cDxYPH/ls2HZ1JRgWbCSaazXeTaarlwzmyyTjizqPDCHS5bDLGnIxz8gc2WJtmTNP6i0JgycyPT2Q8hi1p7Olly01fxBh0lN8+9w187fVvHXEPLZ7CLg4UqTCZupCR0+48rztdNhnPFNCOLMYjDIAd1VWyPxTIOHafBroE085UvpqgNVLE7064hAu2PMm0vh6++Og/kELy2+NPx9Q0uvUAEp3ubxa5XhhQQHHIJuMJ0TyuNN7/D3g9cIVsMh4p1OcP0k121D7uCc0B0x4clZUHh+t1AVqmKknpGvcsWspH3v5mjulo5dRd21i+bxen7t3OnQtPoDiepso0jyvUwzhNodtcG8n05B2Ob8om46oCfy4AssmoEM3mZcDdZP60Z8kmY9y7Eizdu4+9NeVgS4iZmf9DRhxhA4RGyp/A0nU21jawsbaBWzOfzAlbNvDCz49Tak5HocWxjrHFMdreHgVDNhn3kGenXCJsaNhAyj4oDMj8O2mhG2Dq+sE5H4OUJNNc8NpWC5RxGkDhh+wPl3l4iB0F/syCkdb8GSHYo8S1lk1VZ+9t52x5jfq+6GALBASSC17biz8+gZ0ZXEKhPcdYW3BJ2WRM2cTyrcEwumVjjfZnFoIvPHrf3bPjHVde/VQVL02vpzfgZ0F7D43d3VT17fdmgo3B42Ta67n4TYE/r6BsrauUvpQlLL8BtnWw6hCAJtnSMKf2E/ffx8n7d1Lffz5txVXM6N7D2Rte5MenneZ5jjG4kcMvT5jaq/o1rS+tU4YtwdAG55Jm+jpE0pQ+a6AraKZp7N3LVc/dfuCyVt80fFZCOc9R0D+WbDKG9nrNxVg7TTuLLpb5xLBmLIAmqOntJ2RJ/1OzF2zq00e2lCWCtAgxIIJrJ93eo8zR+CYfbtQpV4LZKYH8UmBXKJrcXJ5MDHZ8wfyOTpJ+/43R75aYq386d91D05cTowITPymK6GEmIbuPH77pLWOO/LqNozG2fC+5k8n+6Ch8XkHp/V7FAvGtpG/Ztn1/SPoMa+PcunfJa3wHguh/LjnmEqFZ9561cyc+26IvbHHHsWf+VDYZUSftPhocjVX2ghxVy4Q24pvifPrSB0L7i6uCd//uhLG2HHMtRyXVpGg268iMgg5VWxIokU3G4fZR8ZhieHlIPXIytZuWHo7iicMjJ544PHLiicMjJ3n1c1x3yepPAjeQWTH/tuvvPWnKZwj0mDgTaq1cd8nqvzP6wuTnr7/3pPGkX/BwEeMWx3WXrJ5LZo1nLmZef+9JuwpilcsY7Pi7HLiAzEy3iS6dmJJMpFrZMMbx+4FFR2DLlEM0m0EyyyKGZq9J4GLZZDww7JwTgdXDLvuoaDZtFNgIaCIB6VhCWnjdJavrjsSYKUiMkdMaBVLeL5rNWcPKVpONBrSIZtPViVzG5Tmuu2T1hxlf3vIXgGlHZNEUQTSblzPaMwsBUm4X30mG8OlFY9zmEQq3yHzSGa/n+MU4z1MiaYloNnWk/EPOJY1CgGAtmRwiuZHS1V0F4zV+vKOp4rpLVn8+X2OmEGsyGTEO89i6NpdxvD/RbI4nT9qU5Ggo+7+Owj0nm0Vjfh3Gk38jc07O3bWnOmOK47pLVk9sDoaVdrUrBcAu6FC1a+ewjOcP2T+xOxri8Rnf/gVi5Zq09vb7ECvHm2Vw6iClPIIUCsowHnGMFZFnYWvaR4BjfdJ6/faixh3bw5/ePHHTnEE0m8UIobv4C18wDiuO6y5Z/dOJ31Jy8r41B36bHd1Nn6/kmLbgR86b+L0cYS+aKKg2RLPpyp7jsTzHJyZ6w9r+dsLpkXvuLOvbQNqSD71W/LkFE72fA2T2hMk/4dtoNA5uiugqRh1bGQxCLwLum9DNbItPPvkraqLZiWZS6HT5Skjr/nMb47c8mq/Bh+PX81b5AO2qLSuTv563KgjMIbO6uQLYdtWWlYd9HtFslld093d1l0aQWsGrlR6gcqz0TIO7SpSSyZh4HxmxpsksUm8lk1LiN8CnyORf9ZPpuQUhJPBZ4I6J5HzPaYuUkusuWX0C8DDjWwg9KqFUjA8+/3/U9ee26dHKU9kdmU7SCP0F+B5wHZk/4H3ALjL7sRQDJpnUSH7gVeDLV21ZuQ7g1/NWHQs8CNTka+tobCuP8PTCmZy+ZS9z2nt4dk4Dv7xoym7wkJuMSIaXPA5cNLjgbEIY112yupxMrqrs7QAmQPVAB1WjeIzhzEzsY0vpPJDyUoS4hIMLrz95yKl+YKgKmg2c9ut5q2aTqQafI5O1raD85vwTuPrBF6jrzSw/8U1+RsDCkF0dngX8AXjzRG+lAW/hCIThMzOJgTsiVWytmHXYcwNWcrjxY63IH04NmWxAQ2mdC8resghS1w4IA2B2e++RZAScalySz0UGmZHHvAmlE3ziqf9HaaJ3zG/bjsgRrYYcYGKCmhB94SCmJjAGc3OURxME0iZJ/1H7yMlkXPm4DkUD/gLknaowrfuoinWNKQwJrC9ZMJhvy2KCn/kSmZjoAaAlX1tzMa1ngPrufh5YdjBrdn/QT9I3ZXccmyg/zueioYC0FvgjmRygEwvTpUS30tQMtPPul+6hLDn6QvrWYDX3151HWbKL9nDdh4G/kYm455JZX7uVTJC6hEzit0fIVCGvAj+5asvKXoBfz1tVDvyezKyrfIfDLQ4u+NYBrcdvcM9pS0RPUZC5bd08vHQ2A6FJaX3ag7YcuYvKDkaTwDdkk/HdfG6Xc5rgdZesXgJMKK3AVx+4Cb+d7cEsBJ1GKTaa/PvMN/iv2rIyLzd3tBE3JEqLB+I9/VWRQvdzQCaZ7OcmZE9mo6JzyewU8atDpx+KZvNTwPVkWngS+DPwLtlkFCSaPuwc0usuWW0zAU/ymcd+RlWsK6tcAjt9VelZqV8cUYtoMhDXxyUh42iIY8xtPaYaY/WQLp7IzR4+5mzsUbQUF35mpTvGtVuS42jiaKyaf9FtwoAxxHH9vSdtnMjN1tYv5pbTP8CAL3QgOU5M+NkTrP8QcpU7Og40cQ5Jq6DNWNlknFiwm00i4xmV3T6RG7aW1HHnssvOETDdgjlh+w4xP/bf/5unfZOO/HLgVXRhj6taUacfZFTG01abx+FTOR2KvOr5yx+Dy49K2qBJQRNq/9XHyZie4/p7T5rYPh5CPJm3NVMFIbpGTVQLI73FWN4lc+6UbJmNh6Mxpe/so3DPyUUTX8C2x6o2xutdagtgkSOMVxzjrVZS1997kutdsmwybkXTUlijPEpm3QpkFpIf/lmFQDYZ2W17lzBecXxknOfdka8hUw5NvBN9lGojIwxLNhlf53B72WbOOyr7vU4WE1lIPWaH2PX3nqTUxEtxU9pCEyO/QLaU8hrfgTLRbKbI3fVdIpuMiU3QnkJMJOaIj3H87iMxZEqiiTC2XJ8ZLJRg2evQxKGNsDqyq91O2WQINwsDJrbK/mxGXzQ8xFHZYMdJBrcBXzLGOV2AIZpNDdCn8q4QE2WiyVs+Cvx8lEPfvv7ek75SMKs8pgR55SEdnIA8lML6H9ffe5Iy3xaPg/x/GikT/aVaTMAAAAAASUVORK5CYII=" id="imagea064613268" transform="scale(1 -1) translate(0 -578.16)" x="423.36" y="-43.2" width="97.2" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature20_fold0 -->
    <g transform="translate(163.261719 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-32" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.528719 638.149 
L 537.158219 638.149 
L 537.158219 27.789 
L 529.528719 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image8ac44ebf1f" transform="scale(1 -1) translate(0 -609.84)" x="529.2" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.658219 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.658219 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.059156 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="peccbb56090">
   <rect x="421.136719" y="27.789" width="102.016" height="610.36"/>
  </clipPath>
 </defs>
</svg>
