<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="800.729906pt" height="679.5765pt" viewBox="0 0 800.729906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T21:22:32.344461</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 800.729906 679.5765 
L 800.729906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
L 525.658906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 498.714345 638.149 
L 498.714345 27.789 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.658906 609.084238 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.658906 580.019476 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.658906 550.954714 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.658906 521.889952 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.658906 492.82519 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.658906 463.760429 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.658906 434.695667 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.658906 405.630905 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.658906 376.566143 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.658906 347.501381 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.658906 318.436619 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.658906 289.371857 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.658906 260.307095 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.658906 231.242333 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.658906 202.177571 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.658906 173.11281 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.658906 144.048048 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.658906 114.983286 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.658906 85.918524 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.658906 56.853762 
" clip-path="url(#p906b6432ab)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m45a24c06ae" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m45a24c06ae" x="447.709591" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(439.601388 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m45a24c06ae" x="498.714345" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(495.21497 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8575 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(218.598281 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(53.114375 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(287.449531 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_4_0_1_0 -->
      <g style="fill: #333333" transform="translate(89.536719 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-34" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-5f" x="2335.466797"/>
       <use xlink:href="#DejaVuSans-30" x="2385.466797"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(218.598281 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mode_MeltingT -->
      <g style="fill: #333333" transform="translate(108.417188 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(156.903125 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_17 -->
      <g style="fill: #333333" transform="translate(218.598281 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_39 -->
      <g style="fill: #333333" transform="translate(218.598281 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(95.679219 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_76 -->
      <g style="fill: #333333" transform="translate(218.598281 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(218.598281 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(59.279219 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- AtomicPackingEfficiency_dist_from_5_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-35" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(218.598281 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(285.976875 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(285.469063 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(88.876563 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(99.152656 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagedbb13fd225" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature117_fold0 -->
    <g transform="translate(163.547906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-37" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.548906 638.149 
L 539.178406 638.149 
L 539.178406 27.789 
L 531.548906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imageaefa625a78" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.678406 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.678406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.079344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p906b6432ab">
   <rect x="431.418906" y="27.789" width="94.24" height="610.36"/>
  </clipPath>
 </defs>
</svg>
