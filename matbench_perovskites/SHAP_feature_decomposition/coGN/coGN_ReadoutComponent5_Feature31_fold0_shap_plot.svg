<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="778.965406pt" height="679.5765pt" viewBox="0 0 778.965406 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T06:58:04.765853</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 778.965406 679.5765 
L 778.965406 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
L 520.118406 27.789 
L 405.646406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 459.125879 638.149 
L 459.125879 27.789 
" clip-path="url(#p096d064786)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 405.646406 609.084238 
L 520.118406 609.084238 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 405.646406 580.019476 
L 520.118406 580.019476 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 405.646406 550.954714 
L 520.118406 550.954714 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 405.646406 521.889952 
L 520.118406 521.889952 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 405.646406 492.82519 
L 520.118406 492.82519 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 405.646406 463.760429 
L 520.118406 463.760429 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 405.646406 434.695667 
L 520.118406 434.695667 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 405.646406 405.630905 
L 520.118406 405.630905 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 405.646406 376.566143 
L 520.118406 376.566143 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 405.646406 347.501381 
L 520.118406 347.501381 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 405.646406 318.436619 
L 520.118406 318.436619 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 405.646406 289.371857 
L 520.118406 289.371857 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 405.646406 260.307095 
L 520.118406 260.307095 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 405.646406 231.242333 
L 520.118406 231.242333 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 405.646406 202.177571 
L 520.118406 202.177571 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 405.646406 173.11281 
L 520.118406 173.11281 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 405.646406 144.048048 
L 520.118406 144.048048 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 405.646406 114.983286 
L 520.118406 114.983286 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 405.646406 85.918524 
L 520.118406 85.918524 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 405.646406 56.853762 
L 520.118406 56.853762 
" clip-path="url(#p096d064786)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m9007a42748" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m9007a42748" x="417.489339" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(409.381136 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m9007a42748" x="459.125879" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(455.626504 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m9007a42748" x="500.762419" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(497.263044 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(340.201 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_range_Column -->
      <g style="fill: #333333" transform="translate(87.895469 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-6c" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-75" x="2039.261719"/>
       <use xlink:href="#DejaVuSans-6d" x="2102.640625"/>
       <use xlink:href="#DejaVuSans-6e" x="2200.052734"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(28.27625 584.958461) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- XRDPowderPattern_xrd_21 -->
      <g style="fill: #333333" transform="translate(209.274844 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-31" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- VoronoiFingerprint_mean_Voro_vol_std_dev -->
      <g style="fill: #333333" transform="translate(101.429688 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-76" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-6f" x="1631.392578"/>
       <use xlink:href="#DejaVuSans-6c" x="1692.574219"/>
       <use xlink:href="#DejaVuSans-5f" x="1720.357422"/>
       <use xlink:href="#DejaVuSans-73" x="1770.357422"/>
       <use xlink:href="#DejaVuSans-74" x="1822.457031"/>
       <use xlink:href="#DejaVuSans-64" x="1861.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1925.142578"/>
       <use xlink:href="#DejaVuSans-64" x="1975.142578"/>
       <use xlink:href="#DejaVuSans-65" x="2038.619141"/>
       <use xlink:href="#DejaVuSans-76" x="2100.142578"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(41.154375 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(64.007969 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(192.825781 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(217.477031 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(184.554531 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(69.786875 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(69.906719 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_mean_CovalentRadius -->
      <g style="fill: #333333" transform="translate(37.3925 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-76" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-61" x="2063.480469"/>
       <use xlink:href="#DejaVuSans-6c" x="2124.759766"/>
       <use xlink:href="#DejaVuSans-65" x="2152.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="2214.066406"/>
       <use xlink:href="#DejaVuSans-74" x="2277.445312"/>
       <use xlink:href="#DejaVuSans-52" x="2316.654297"/>
       <use xlink:href="#DejaVuSans-61" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-64" x="2445.166016"/>
       <use xlink:href="#DejaVuSans-69" x="2508.642578"/>
       <use xlink:href="#DejaVuSans-75" x="2536.425781"/>
       <use xlink:href="#DejaVuSans-73" x="2599.804688"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(192.825781 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(184.554531 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(131.130625 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(83.776094 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(262.452969 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(140.9375 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(99.35375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagea2ee8a9a91" transform="scale(1 -1) translate(0 -578.16)" x="408.24" y="-43.2" width="108.72" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature31_fold0 -->
    <g transform="translate(153.999406 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.272906 638.149 
L 534.902406 638.149 
L 534.902406 27.789 
L 527.272906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image312c66b588" transform="scale(1 -1) translate(0 -609.84)" x="527.04" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(538.402406 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(538.402406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(572.803344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p096d064786">
   <rect x="405.646406" y="27.789" width="114.472" height="610.36"/>
  </clipPath>
 </defs>
</svg>
