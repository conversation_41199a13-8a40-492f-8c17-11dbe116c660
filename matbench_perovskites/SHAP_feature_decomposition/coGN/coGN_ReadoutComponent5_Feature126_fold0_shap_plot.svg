<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="797.390062pt" height="679.5765pt" viewBox="0 0 797.390062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T03:16:44.537651</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 797.390062 679.5765 
L 797.390062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 463.450706 638.149 
L 463.450706 27.789 
" clip-path="url(#p283791acec)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#p283791acec)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m7307c4b01c" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m7307c4b01c" x="463.450706" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(459.951331 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m7307c4b01c" x="504.486972" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(500.987597 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_mean_NdValence -->
      <g style="fill: #333333" transform="translate(90.239531 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-64" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(118.191562 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- OPSiteFingerprint_mean_sgl_bd_CN_1 -->
      <g style="fill: #333333" transform="translate(157.457656 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="923.511719"/>
       <use xlink:href="#DejaVuSans-65" x="1020.923828"/>
       <use xlink:href="#DejaVuSans-61" x="1082.447266"/>
       <use xlink:href="#DejaVuSans-6e" x="1143.726562"/>
       <use xlink:href="#DejaVuSans-5f" x="1207.105469"/>
       <use xlink:href="#DejaVuSans-73" x="1257.105469"/>
       <use xlink:href="#DejaVuSans-67" x="1309.205078"/>
       <use xlink:href="#DejaVuSans-6c" x="1372.681641"/>
       <use xlink:href="#DejaVuSans-5f" x="1400.464844"/>
       <use xlink:href="#DejaVuSans-62" x="1450.464844"/>
       <use xlink:href="#DejaVuSans-64" x="1513.941406"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.417969"/>
       <use xlink:href="#DejaVuSans-43" x="1627.417969"/>
       <use xlink:href="#DejaVuSans-4e" x="1697.242188"/>
       <use xlink:href="#DejaVuSans-5f" x="1772.046875"/>
       <use xlink:href="#DejaVuSans-31" x="1822.046875"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- XRDPowderPattern_xrd_39 -->
      <g style="fill: #333333" transform="translate(229.7275 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-39" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_avg_dev_NpUnfilled -->
      <g style="fill: #333333" transform="translate(74.178437 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-55" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-6e" x="2219.291016"/>
       <use xlink:href="#DejaVuSans-66" x="2282.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2317.875"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2373.441406"/>
       <use xlink:href="#DejaVuSans-65" x="2401.224609"/>
       <use xlink:href="#DejaVuSans-64" x="2462.748047"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_mean_trigonal_planar_CN_3 -->
      <g style="fill: #333333" transform="translate(79.161094 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-70" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6c" x="1924.878906"/>
       <use xlink:href="#DejaVuSans-61" x="1952.662109"/>
       <use xlink:href="#DejaVuSans-6e" x="2013.941406"/>
       <use xlink:href="#DejaVuSans-61" x="2077.320312"/>
       <use xlink:href="#DejaVuSans-72" x="2138.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="2179.712891"/>
       <use xlink:href="#DejaVuSans-43" x="2229.712891"/>
       <use xlink:href="#DejaVuSans-4e" x="2299.537109"/>
       <use xlink:href="#DejaVuSans-5f" x="2374.341797"/>
       <use xlink:href="#DejaVuSans-33" x="2424.341797"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(237.929688 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(90.239531 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(47.794531 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(205.007187 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_176 -->
      <g style="fill: #333333" transform="translate(205.007187 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-36" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(205.007187 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(213.278437 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(90.359375 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(104.22875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(282.905625 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(161.390156 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(119.806406 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image2639c6394a" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature126_fold0 -->
    <g transform="translate(160.208063 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-36" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image75a03dc3f5" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p283791acec">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
