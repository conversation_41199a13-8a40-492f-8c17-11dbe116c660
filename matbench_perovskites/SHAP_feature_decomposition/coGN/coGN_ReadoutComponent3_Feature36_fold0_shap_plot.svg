<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:34:45.738612</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 460.171801 638.149 
L 460.171801 27.789 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#pa0cd643afd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mb12d3f104e" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb12d3f104e" x="460.171801" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(451.425082 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mb12d3f104e" x="512.839756" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(504.093037 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- GeneralizedRDF_mean_Gaussian_center=3_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(30.124687 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-33" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(205.007187 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- TMetalFraction_transition_metal_fraction -->
      <g style="fill: #333333" transform="translate(138.170937 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-54"/>
       <use xlink:href="#DejaVuSans-4d" x="61.083984"/>
       <use xlink:href="#DejaVuSans-65" x="147.363281"/>
       <use xlink:href="#DejaVuSans-74" x="208.886719"/>
       <use xlink:href="#DejaVuSans-61" x="248.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="309.375"/>
       <use xlink:href="#DejaVuSans-46" x="337.158203"/>
       <use xlink:href="#DejaVuSans-72" x="387.427734"/>
       <use xlink:href="#DejaVuSans-61" x="428.541016"/>
       <use xlink:href="#DejaVuSans-63" x="489.820312"/>
       <use xlink:href="#DejaVuSans-74" x="544.800781"/>
       <use xlink:href="#DejaVuSans-69" x="584.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="611.792969"/>
       <use xlink:href="#DejaVuSans-6e" x="672.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="736.353516"/>
       <use xlink:href="#DejaVuSans-74" x="786.353516"/>
       <use xlink:href="#DejaVuSans-72" x="825.5625"/>
       <use xlink:href="#DejaVuSans-61" x="866.675781"/>
       <use xlink:href="#DejaVuSans-6e" x="927.955078"/>
       <use xlink:href="#DejaVuSans-73" x="991.333984"/>
       <use xlink:href="#DejaVuSans-69" x="1043.433594"/>
       <use xlink:href="#DejaVuSans-74" x="1071.216797"/>
       <use xlink:href="#DejaVuSans-69" x="1110.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="1138.208984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.390625"/>
       <use xlink:href="#DejaVuSans-5f" x="1262.769531"/>
       <use xlink:href="#DejaVuSans-6d" x="1312.769531"/>
       <use xlink:href="#DejaVuSans-65" x="1410.181641"/>
       <use xlink:href="#DejaVuSans-74" x="1471.705078"/>
       <use xlink:href="#DejaVuSans-61" x="1510.914062"/>
       <use xlink:href="#DejaVuSans-6c" x="1572.193359"/>
       <use xlink:href="#DejaVuSans-5f" x="1599.976562"/>
       <use xlink:href="#DejaVuSans-66" x="1649.976562"/>
       <use xlink:href="#DejaVuSans-72" x="1685.181641"/>
       <use xlink:href="#DejaVuSans-61" x="1726.294922"/>
       <use xlink:href="#DejaVuSans-63" x="1787.574219"/>
       <use xlink:href="#DejaVuSans-74" x="1842.554688"/>
       <use xlink:href="#DejaVuSans-69" x="1881.763672"/>
       <use xlink:href="#DejaVuSans-6f" x="1909.546875"/>
       <use xlink:href="#DejaVuSans-6e" x="1970.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_hexagonal_planar_CN_6 -->
      <g style="fill: #333333" transform="translate(47.84125 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-68" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1594.947266"/>
       <use xlink:href="#DejaVuSans-78" x="1654.720703"/>
       <use xlink:href="#DejaVuSans-61" x="1713.900391"/>
       <use xlink:href="#DejaVuSans-67" x="1775.179688"/>
       <use xlink:href="#DejaVuSans-6f" x="1838.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="1899.837891"/>
       <use xlink:href="#DejaVuSans-61" x="1963.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2024.496094"/>
       <use xlink:href="#DejaVuSans-5f" x="2052.279297"/>
       <use xlink:href="#DejaVuSans-70" x="2102.279297"/>
       <use xlink:href="#DejaVuSans-6c" x="2165.755859"/>
       <use xlink:href="#DejaVuSans-61" x="2193.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="2254.818359"/>
       <use xlink:href="#DejaVuSans-61" x="2318.197266"/>
       <use xlink:href="#DejaVuSans-72" x="2379.476562"/>
       <use xlink:href="#DejaVuSans-5f" x="2420.589844"/>
       <use xlink:href="#DejaVuSans-43" x="2470.589844"/>
       <use xlink:href="#DejaVuSans-4e" x="2540.414062"/>
       <use xlink:href="#DejaVuSans-5f" x="2615.21875"/>
       <use xlink:href="#DejaVuSans-36" x="2665.21875"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(183.356094 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(74.466875 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_167 -->
      <g style="fill: #333333" transform="translate(205.007187 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- BondOrientationParameter_std_dev_BOOP_Q_l=2 -->
      <g style="fill: #333333" transform="translate(81.635156 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-73" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-74" x="1443.546875"/>
       <use xlink:href="#DejaVuSans-64" x="1482.755859"/>
       <use xlink:href="#DejaVuSans-5f" x="1546.232422"/>
       <use xlink:href="#DejaVuSans-64" x="1596.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1659.708984"/>
       <use xlink:href="#DejaVuSans-76" x="1721.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="1780.412109"/>
       <use xlink:href="#DejaVuSans-42" x="1830.412109"/>
       <use xlink:href="#DejaVuSans-4f" x="1897.265625"/>
       <use xlink:href="#DejaVuSans-4f" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-50" x="2054.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="2114.990234"/>
       <use xlink:href="#DejaVuSans-51" x="2164.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2243.701172"/>
       <use xlink:href="#DejaVuSans-6c" x="2293.701172"/>
       <use xlink:href="#DejaVuSans-3d" x="2321.484375"/>
       <use xlink:href="#DejaVuSans-32" x="2405.273438"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(151.583281 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(213.278437 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(151.583281 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(90.359375 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_trigonal_planar_CN_3 -->
      <g style="fill: #333333" transform="translate(79.161094 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-70" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6c" x="1924.878906"/>
       <use xlink:href="#DejaVuSans-61" x="1952.662109"/>
       <use xlink:href="#DejaVuSans-6e" x="2013.941406"/>
       <use xlink:href="#DejaVuSans-61" x="2077.320312"/>
       <use xlink:href="#DejaVuSans-72" x="2138.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="2179.712891"/>
       <use xlink:href="#DejaVuSans-43" x="2229.712891"/>
       <use xlink:href="#DejaVuSans-4e" x="2299.537109"/>
       <use xlink:href="#DejaVuSans-5f" x="2374.341797"/>
       <use xlink:href="#DejaVuSans-33" x="2424.341797"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_1_0_1_0 -->
      <g style="fill: #333333" transform="translate(84.216875 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-5f" x="2335.466797"/>
       <use xlink:href="#DejaVuSans-30" x="2385.466797"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(48.728906 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(102.165 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(213.278437 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(34.936719 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(90.359375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image3d4398ff5a" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature36_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image3e04b39b5d" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pa0cd643afd">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
