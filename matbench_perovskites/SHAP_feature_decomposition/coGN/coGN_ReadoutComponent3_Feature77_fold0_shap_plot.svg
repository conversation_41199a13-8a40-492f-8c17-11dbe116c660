<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:36:42.972919</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 452.767371 638.149 
L 452.767371 27.789 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#pcfeddcc878)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="maa8fac1e6c" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#maa8fac1e6c" x="452.767371" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(449.267996 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#maa8fac1e6c" x="512.910364" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(509.410989 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(147.772656 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_157 -->
      <g style="fill: #333333" transform="translate(201.196562 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(234.119062 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_range_CovalentRadius -->
      <g style="fill: #333333" transform="translate(53.100156 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-76" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2070.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2131.9375"/>
       <use xlink:href="#DejaVuSans-65" x="2159.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="2221.244141"/>
       <use xlink:href="#DejaVuSans-74" x="2284.623047"/>
       <use xlink:href="#DejaVuSans-52" x="2323.832031"/>
       <use xlink:href="#DejaVuSans-61" x="2391.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2452.34375"/>
       <use xlink:href="#DejaVuSans-69" x="2515.820312"/>
       <use xlink:href="#DejaVuSans-75" x="2543.603516"/>
       <use xlink:href="#DejaVuSans-73" x="2606.982422"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- BondOrientationParameter_std_dev_BOOP_Q_l=1 -->
      <g style="fill: #333333" transform="translate(77.824531 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-73" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-74" x="1443.546875"/>
       <use xlink:href="#DejaVuSans-64" x="1482.755859"/>
       <use xlink:href="#DejaVuSans-5f" x="1546.232422"/>
       <use xlink:href="#DejaVuSans-64" x="1596.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1659.708984"/>
       <use xlink:href="#DejaVuSans-76" x="1721.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="1780.412109"/>
       <use xlink:href="#DejaVuSans-42" x="1830.412109"/>
       <use xlink:href="#DejaVuSans-4f" x="1897.265625"/>
       <use xlink:href="#DejaVuSans-4f" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-50" x="2054.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="2114.990234"/>
       <use xlink:href="#DejaVuSans-51" x="2164.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2243.701172"/>
       <use xlink:href="#DejaVuSans-6c" x="2293.701172"/>
       <use xlink:href="#DejaVuSans-3d" x="2321.484375"/>
       <use xlink:href="#DejaVuSans-31" x="2405.273438"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_std_dev_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(90.028281 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6f" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-63" x="1592.75"/>
       <use xlink:href="#DejaVuSans-74" x="1647.730469"/>
       <use xlink:href="#DejaVuSans-61" x="1686.939453"/>
       <use xlink:href="#DejaVuSans-68" x="1748.21875"/>
       <use xlink:href="#DejaVuSans-65" x="1811.597656"/>
       <use xlink:href="#DejaVuSans-64" x="1873.121094"/>
       <use xlink:href="#DejaVuSans-72" x="1936.597656"/>
       <use xlink:href="#DejaVuSans-61" x="1977.710938"/>
       <use xlink:href="#DejaVuSans-6c" x="2038.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2066.773438"/>
       <use xlink:href="#DejaVuSans-43" x="2116.773438"/>
       <use xlink:href="#DejaVuSans-4e" x="2186.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2261.402344"/>
       <use xlink:href="#DejaVuSans-36" x="2311.402344"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(86.54875 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(62.252969 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(79.746094 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(143.87875 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- AverageBondLength_std_dev_Average_bond_length -->
      <g style="fill: #333333" transform="translate(63.106094 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-73" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-74" x="1114.298828"/>
       <use xlink:href="#DejaVuSans-64" x="1153.507812"/>
       <use xlink:href="#DejaVuSans-5f" x="1216.984375"/>
       <use xlink:href="#DejaVuSans-64" x="1266.984375"/>
       <use xlink:href="#DejaVuSans-65" x="1330.460938"/>
       <use xlink:href="#DejaVuSans-76" x="1391.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1451.164062"/>
       <use xlink:href="#DejaVuSans-41" x="1501.164062"/>
       <use xlink:href="#DejaVuSans-76" x="1563.697266"/>
       <use xlink:href="#DejaVuSans-65" x="1622.876953"/>
       <use xlink:href="#DejaVuSans-72" x="1684.400391"/>
       <use xlink:href="#DejaVuSans-61" x="1725.513672"/>
       <use xlink:href="#DejaVuSans-67" x="1786.792969"/>
       <use xlink:href="#DejaVuSans-65" x="1850.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1911.792969"/>
       <use xlink:href="#DejaVuSans-62" x="1961.792969"/>
       <use xlink:href="#DejaVuSans-6f" x="2025.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="2086.451172"/>
       <use xlink:href="#DejaVuSans-64" x="2149.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="2213.306641"/>
       <use xlink:href="#DejaVuSans-6c" x="2263.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2291.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2352.613281"/>
       <use xlink:href="#DejaVuSans-67" x="2415.992188"/>
       <use xlink:href="#DejaVuSans-74" x="2479.46875"/>
       <use xlink:href="#DejaVuSans-68" x="2518.677734"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(209.467812 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(20.096406 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(147.772656 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(206.997812 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_avg_dev_NpValence -->
      <g style="fill: #333333" transform="translate(68.935781 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-56" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-61" x="2206.755859"/>
       <use xlink:href="#DejaVuSans-6c" x="2268.035156"/>
       <use xlink:href="#DejaVuSans-65" x="2295.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="2357.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2420.720703"/>
       <use xlink:href="#DejaVuSans-65" x="2475.701172"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(209.467812 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image265d4c3efd" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature77_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-37" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-37" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagef73dc20c7e" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pcfeddcc878">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
