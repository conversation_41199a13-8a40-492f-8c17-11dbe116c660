<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.621906pt" height="679.5765pt" viewBox="0 0 794.621906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T16:20:48.494782</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.621906 679.5765 
L 794.621906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
L 525.658906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 468.557761 638.149 
L 468.557761 27.789 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.658906 609.084238 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.658906 580.019476 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.658906 550.954714 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.658906 521.889952 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.658906 492.82519 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.658906 463.760429 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.658906 434.695667 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.658906 405.630905 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.658906 376.566143 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.658906 347.501381 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.658906 318.436619 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.658906 289.371857 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.658906 260.307095 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.658906 231.242333 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.658906 202.177571 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.658906 173.11281 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.658906 144.048048 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.658906 114.983286 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.658906 85.918524 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.658906 56.853762 
" clip-path="url(#pe5d4dec834)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m23cb807ff7" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m23cb807ff7" x="468.557761" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(456.311667 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m23cb807ff7" x="521.456928" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(509.210835 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8575 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_45 -->
      <g style="fill: #333333" transform="translate(218.598281 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(285.976875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(218.598281 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_59 -->
      <g style="fill: #333333" transform="translate(218.598281 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_avg_dev_MeltingT -->
      <g style="fill: #333333" transform="translate(90.926094 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-74" x="2183.402344"/>
       <use xlink:href="#DejaVuSans-69" x="2222.611328"/>
       <use xlink:href="#DejaVuSans-6e" x="2250.394531"/>
       <use xlink:href="#DejaVuSans-67" x="2313.773438"/>
       <use xlink:href="#DejaVuSans-54" x="2377.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(156.903125 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_maximum_NpUnfilled -->
      <g style="fill: #333333" transform="translate(68.358906 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-70" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(107.484844 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(287.449531 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(216.128281 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(40.256563 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(109.548594 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=4_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(21.74375 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-34" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(79.786719 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(106.414375 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(95.679219 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- AtomicPackingEfficiency_dist_from_3_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-33" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(288.225469 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.658906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAH0AAAMjCAYAAACI7B8/AACQX0lEQVR4nOydd5xcVfn/3+e26Tuzvaf3kFBCDQiCgAVFQQRFQcXGV+xGsSAqdo1dUb/qT7+KIgixYAFs9J4ACWmkbrLZzfbZ6TO3nN8fM9s3BUx25mbn/XptJnPbPPd+7jnnOe05QkqJW7nhvEfe0BXw/FY37d6b7z61sdj2uAXhVtE/fOHjZkig6Y4DQLfPi5M1F/3wnlO3Ftm0kkcrtgEvhhsufNwbkY4WSGTQTBMpFLScxd5QYDOgFNu+UseVoivx+Fa/UPElUwjHAQRaLkeTqohi2+YGXCm6qap1vngK1bILWyTCcfCkM0W1yy24MiuMVld5DTM3ZpsAjExu8hPKjMGVomtCwWNnJ2yvzAwUwRr34TrRr75qp/A5Nkv7xzrpimPTmOnmhovWlcv1Q+A60X/16znSEYKwHOSszsdoSHUxM76H1+y5h75AFUvWb3bumfHDk4ttZynjqnr6697e5rFzZjpkW6I63senHvwVjfEeLKHw6KxTMTI5umUttqnK1/W+03Uv9FThKu89Jq1M1KPzdG090MzPFn2Njz21lppEDn82zYmJDZzfdx//qD6/nMUfBNeIfvVVO9VQLsd/ZtWhSMnCZJqWdJZYsJLKRA+mYvBEwwqSuo+m5O5im1vSuCYLfCyov3VnJMSMTJbz+wZZnkhTZTvsr6nimQWzsUU+cT8fmc/xg1u5P/DVcv3tALhC9NCqASVgi58LTafStIkMN8rkyRoGfeEQAI4QZDweKu2s/veGH6SKYW+pU1KO3IL3dRu7DU21sH9Xl7Fe7Vc0RUVSmbPwC4fegJeoonBmT4zxhfbiXXupiw5iahr9tZWEEknqevajDCrSn8ra1YlsKK0ZAUexn7FgVzDH+efKd0zL3GBKRRcfGXhHwLI+OtuWiwCRQRJyJPu8OtW2QxoImzY5RUEKgQQcwItke1OYtCIglmNlX4zWUa1vhmlyyqbnUSTUJAbo99ZgGho5r8Lc7XvIKV5U06YxGkcagq5wEFNV8WVNKqNpdMdBJf8cbAQSBQ1LejB/JRDvWi4/YE7ZQ5oCpsyREx8ZeCPws3m2HE6ljZbD2pCPuVmThCJozJpEVRVZKJ8F+fKnP+QhbaiQsQB4vDJELJGiPmsS11QuenY3Cgr+RBZ/N/jpAyDt00kGvXhSUJVK41EsttXUDl8/6ffgKAqtvbFhOxUkOSCHIQS81cBKA/8zJQ9pipjKMv2tYdsZFlxxHBKqIGdo+G0HRUokDAsyhACsoW1q/tNWBM9VBPhXbYQ2XbKwZzf1yS4augcKZ+TxpU08aQcpIJTLEvN6J1w/7dWHncAhVPJ99BYqwFuOzO2XDlMpetIa9WwloMj8pyQvlQqISYobxc6LgKaAMWKykJLPPfxHrtj+Zy7b/mdsOTHjqkwnEBJsoaAVBlyMRjhy0t+E4dfnmOu6m0rRv5dUlJHOUEXB70iCGZOoR0O3HSQQcBxGS2AKcLI2VYnCsw8ahDVYGktxTl+cdbNOY3vNLCRQbfdM+FFNWAigL+gnlM5gmNaY/RWJzBinUAIWCiDRsQC+fmRuv3SYakfuNNVxrlto2pdJgS8FVNo2PYaOoit4czZBy0ZKSVtNkKTPIDdUlsdNvI4Dlsk5XQP4FQWfZVGdzRFOZTn+iZ2Eomksr4KjCjRpM9/cwbORhThCxVKgoTuGV9rEAl6ymkook8GTsQAFFZBIHJR8Ny1WTMP+0PHyA7+Ysgc0RZRUlW2IZe/rFm06d2WC3lcqhqZ4UjlImsy1bJ5ujYAFVz65meP6o6gIZm3uItI/Ohd2OJ1H2RmuZ39yZkfWUO96bfLt1wL8puW2mbYiOq/ec/m0rK5BiYo+Ga+5cquT9PnEhoZKTEPh7O17OW9bOwCqadOwe5BQNI1fpmhVd9AXCZJ0vNvP6fvE/CKbXnK4RvSXXNs+qzaZ3dWaSDFgqLQMJqlMJFALTp6tKkhVcPa2h+jP1ZAT7H1dz7Uzimx2SeIa0QFOf0+HPLW3HxuBKgTeXA5vNoetKGQMnSvXrmHFvg3c23geF3a8r9zTdgBc0fY+RMJx5LqaKrScCYU29mhFiHgwgGkYmJqOg0BPThxKVWYEV4n+3E9blJRtm56cWRj6PIIvl2ZZx2Y2VC8hbMbdk30VAVeJDjDXcry2z0Mgk8GfzqDaNpWJQc7Y/hR3t55LLumjhqSn2HaWMq4ZRDFETTotBODL5tBtG5L57VvqloBl8fod7yyX5YfAdSn9R7ctspmkORVAccq5+uHgOtEh316e09QJ23Wz7MAdDq7L3gGsVKovnExXO6qKVBUUx0E1LQaCoWKb5gpcmdI9Uv5dURU0JIZpojoOaCqqi9ociokrRe9oabo66TGAfG8dhVE2wrImL+zLjMGVov/q13OkZVq7kkZeeFNVSXh0ArYdLLJprsBVzbDjuXnZX0TcY9yiCf790Scv/Hmx7XELrha9zIvDldl7mf+OsujTkLLo05BjQvR5nxxwZSNTsXC1Izfro91WW0PlcHtsdSzp9H4hMrF9tswYXJvSz3jnru+3NVSqCMHQX19FQPHdMLCl2LaVOq4VfW9t5DrGzUxBCKTPu6A4FrkH14qe1CfPxQO5Y2qu4VHBtaIbB+pTn5D8y4zHtaLXpCfvO0949Cm2xH24UvSouNqZ09MxaYp2hHtrI1OF60SPizc7YRLi0//+DcFMesJ+3SqLfihcJ7qPjBDA6ft20nnTu1m2bzeMamuQ5UDQh8R1LVnqqInMH3rtW9kfbuA1W/eBhMdaq+n3GkW0zh24TvTRPDzrJN60oW14fvnsaJK/zmsAysPeD4brRDfxYJDFQmNGLMPvls2k1++hOZbi3F1dnL2nF6gutpkljeva3reKL0gNm3ZvMxd87q2Y6ohbEsiaXPnsbp5sqqQ+laWmL/G3W+5celERzS1Jjqojt07crK4TN69bJ25+6Qs99xlx0yM7xKftDeITnaO3J6lkkDoeWjRjjOAASY/OfbPr2BMJcM+CZp6aU/eq0fs/9PqN5c4YjmJKXydutsjHDhpC5qPCIcDBT4II3UTolAM0CR9RWcGgUMgfZWJgF0qfQSrpp5UgvfTTikThmVkNvOO610743UAmx5zeOBtaqhGOwwXbOwlnLXZGAuhSojoOs3vj8te/W+S6msuR4qjc+Dpxs4+xggMIUEQ+ZpNGigg5/HgxRSNtRAqCDxklUdjCSrZwOlkqMMjRwi4UbEBwwu4uTt+6d8Jv+y2H0/YPcO62TnTboT6ZI62rrGzv47R9/ZzcGcXRVfHWyzdP20b6o/W2Dx76EIcmdhxwr5cMFfQCCv00U0sbKjYqI9GhvvOLe/j87/7DSzbuHt42M5ogYNrEfAZXrm9DSEldMjvmRmtTOQaCPtc5sUeKItx4voKlYqNx8MTmJUGMGgASVBKhD5WRYMC67fDqtdvYOqOeGdEki3tihAvhQ7O6itd2sJF47YlFmCqn77yIo5XSDxJ9eSgGq06C8EGP6qNp+PtQCpdIInQhFZto0McTx8+jVqhcsrmdRb0xMppKUlPp8XswbIecIujzT2ywiaRz7qq2HEGOiugnyfdGJt+Tjw2pFARsZz4m+V4xB4bb2hwEe1iEjTF83iBV5PDQyC7msAG1YT+fvepCnp3fwraqIDsrA5gCnmmsZM2SFl7Slg8k2BX0IRHsD3iQ5Pvh9/sNcqb916Nx727gaHrvc4DtjARrXQt8DuxbgUCAHprYLEPY33meJYsMBn84i313AWIfYdnFCiV/qo2XNlPDvM1LylND9g1+kqQ1nX/OO4HXvf2jOEr+3dVsB0tVOG97J8t6YuzzGyiZnKPomiIsy6mwUVSQg/DjW+5c+t6jcuMu4KiV6SfJ9+5k8pxkwnziUeObVIDmwt9kJMTbJIDPMvn9CacOCw5gqQpISfNgin6vjpRS3vb7peW6+ThcV1fVGQn0uLG+dcL+qlQW3bZlHCdxxy0LXHd/U4Hrqi2jC6Nzd2xkXcucMfvn98X5+e1LymIfBNc9nNEe/Y3/uINXbHk6/0VKAlmzPDDyMHCd6H5i7GMOWQwsReHi554kks6iSEnSo/NsQ2WxTSx5XJe9+4ijYXLnrIt483VvGrtTSvp85b70Q+G6lN6HN+0jwWB9Cs20xgyVKnN4uE70ZvkL/yDB8/+46CwphqY0DSEEgXErN5SZiOtEB6iWv/jX3uo6zEliyQWzZUfuULhSdICcNrnp5cz+0LhW9ISmDAf4H8JjWvR79bLuh8B13vsQvkzWCXoMVRGQ0DUimRxpVcUwcxL8xTavpHFtSt/dWKPbSKIeHVsR9Pi9ZFSB7TEqim1bqeNa0eUqTdbFMzNrY2kZzJpUJ9NSc+SC5Kf9yWLbVuq4bgh0mf8e16b0Mi+esujTkLLo05Cy6NMQ19bTh/jMirvf9PJ1O38tUVTL4J5zs9e+otg2lTqu895bPx3z1vTHf6nBE9f+5T8zT27v/YBeaHzNohJDz50j/6fcv3oQXCX6Ke9qe+iEvoEzm1JZ0qqgtrOX1z29gbRhkPB5Sfm8VPUM4rGlulR+ePrOZjgErirTL9jbcWZLKoeCIGBDSyoNCHw5i9rBODP397Bx4UwMMZAotq2ljGtEf+UVG3YbheAjGU0jo6qc+PwuRobVC0Bw4tZddFfX+4pnaenjGkeufiA5M63rxP0+soaBL5NFlRJJfoZrHonXztEhDzxdqoyLUvqi7hjdPi9ZXSeQTCEsi5SuFQQXhT8FASzq380W8bk1xbW4dHGF6Bde9fzrvQr4bcnirTtp7exmTlcP9y9fws76mjHHWhhEZAIdeUmRzC15XCF6S9/g7/Y2VqOlUnTUVqEpgkA8Q13HIHHhZX1DE1HNi4NA5KNdIMtjaA6IK0QXoHVqGrvqa6hOpdByFpH+JEqhuikRtEciDOJDJUtU9bLdP4td4qPfL7LpJUnJi37pFc/VB20Hj6HTXREg6vfgyUwc/GhrKraikAOa7T2cknkWTcj3Tb3FpU/Ji57NWJuj1ZUouo4UkrsXzyI3Wax3KVEcSRYfKmkizgA+KUFcUfL3ONWU/ANxKkOVOgIBzImm2FkT4UuvPoPd9ZHhYyTQEBuknn5qGCRDCAswsNnEvNwBLj1tKelm2Ddfst5sq6vSFsVHoj1HDY0nWqoY8Oic8Xw7F2zYxbkbn6fRGiCLgYOChkWAHgwEDoIUUjbLb5X8Cz5VFE108aWcUpHNzVraNbjIYzvPJ237/B5Dn2dI56VVmcyi/kggcFZvkgFNoTKWYl5XP1lDp7O+isbOPho6e+mPBOisCvO2xx4gGAd7VBQzHwlq2UOOCJBEYBEjJH1ELS+DGR92nwZRCTuALQokgYeBx8kHTqgAEsg13UV4PEeVooguvpQ7y2PafzyxN1bdnMywT9d4vDaCVAVIqEhnOKc3Rn+FF2MgxrX/WDfc2GqqKjkV5KjJDhXZNIs7esf9imQGG5EoGCTxkB5TiRMMTYxQyS/E7RQi4gx9DPNX4ErkmtgRfgxFY8qzPPGlnI6Ut8+LpapbkhksAY9XVyDVoZY1yElBV9jLwzNquOLRzWMU0G0bbdyLGjO8mMr4WxFEmYlBBg/pwpaRv5HvNkOt0YLRH8NcBHzuv7jlkqMY5dxihGisK6zB0qdpSHUkW9ZNm6yA3ZEAABWpiWu1TLZihxgVXw7ARxKNLB4Op8NNcohHcf5hXMQ1FEP0PUiZiev51BW0HRgVyM9SFUKWg8/Kzz59ZnbDhAtkjLH9RDWpQeY42/GRQMUkRJQm2vCQweJwFvIZyewPwNbDuIhrmHLR5aeNKELcsLkySFpVCDoOM2Kp4XnmUlVoTaSY05WPNPrDV5zMxhl1OALiXoM7VyziRy89ma2NNeR0lfpcH+f0rMVLmhnsZB6baWIPGhYSQYymCXKO/a4yNordhOVA+jnGsvdieu9LmmKpDzclMqf6srlkL2Lx3qpQ2HAkjQMx6pIpETQ0dtWF0UyLjKnQ7zWGl+AKZXJcuWUnDak0DbFe3vjMX8cs9QEWFgp9zEFigdZPtdU33DrvISNHBJZSgC1gD/B58onhJGAD8HvkmsOIdeseSrqefspV2xxFUYUpoauygo6IfyQIgZSc0t7DG57fTWP7AAv2tFHhDNJKG36SgI0AnuMUwvQSIPmpKvnDrxTzfkqFkh5Esamh1n/C4GB67cwGgoNpfKbFCfu6kargmdZ6dleFmLWtm7quOGlCpAnRTy2n8AA6+ZjxQfaToXZ+q/z69mLfT6lQ0qInvxHJnPieQcdWFaU+nuCdj28gVIg08br121n9qtPZUhuhris+fI6Jh14aaKCdHloIkq2ukZ/sL9Y9lCIl3zRZl8i+rzGW4sy2jmHBAcLpLBdu2MnNrzl5wjkZQvQxBxMPNfI7ZcHHUfKi3/ObBT86o62b+T3RCftq4yn2V4XIjVrLReDgAUx0YqjlzpZJKOnsfYguQ7X7/X61MpUZs31Tcw0N0QSOBpYUOIogZ+g8L1twwknO6/xwedLDJJR8SgfQpX0TSRtbKDiALQSPzG/h+foqKmMpcrpOym+Q8eo4ikJcDeBkXfE+F4WSrrKN5puz7pLVZBFAVlNJeg2qE2n+cPJCrrln/YTj56Z25pbYnymn9ElwRUoHWDu3Hsg3p3gtm+pEvhNlf1UQUx3biqZKG0fxV021jW7BNaKnqwPWtsaxw50HAl7m7otSE0uimTbCkSiWQ3Oqh+PMj5ZjzxwA1xR8lVIsf2jhrE39wQCtfVEGAj6emNfKR299iJw00LMSHRsPOYJEy5MXD4JrynSAT573sP14a5OSMXTC6SwXbNzCWc/sIoUPGxUfWcIkQM/OWpr7VFux7S1VXCU6wOfPuv+RykzmdGk5lj/WZ56zu8OPBAsVFZtBn8KpqVUTusrKjOA60cfzk4W3xmf1xoOhbJa+gFeu6N5T3yS/0FNsu0oZ14te5oXjGu+9zJGjLPo0pCz6NKQs+jTENY0z4/nMqf/+TDiTe7+KeNOH17/8X8W2x0240nv/wZK/OLmgR2hSktBU9FjW+djGV5XXVD1MXJe937ji3rgTMMTQLJegZTNYE1S+eOK9Hyiyaa7BdaJLXQvEDJ3/tDTwr9ZGoh6DulSGtsrgd4ptm1twXZne7fOKW45fSKowQ+aemc18YO1Gcko5dz9cXJfS75s3Y1hwyA+o+P2SudQkUu5zToqE60QfmuEymqSh4zPtSY4uMxmuE705O3GA6+LBOMlQeVmuw8VVojd9oFedk05zVlcvhm2j2w5ndPdx3r4uLtn6gJIU1zhR8Z5yvPdD4Kp6+mWveDynVoX15V39aDkTW1WRhfVW37juLmYP7MNEJYH6bLX86QnFtbZ0cU1K/8yp/9zSpGj67gofgf39NO7toXn3fsK9g0jHIZTJBx/ID5lSjy+yuSWNa0QPOs7CO1fM4/z1O6lK5ic9CKAimkAxbZRR05Rdc1NFwjXP59kZ9XSEA8zrnDg1zZfJ8dDsk+mlnkEqcdzX/DCllPzTOW5Vd+Cdf30y0b9iAQB7asLMHjevLeM1aBOttJJEw8JHEr/4lHRQsUEKxIJZ8qbyVOUCJe3Ive6KDV6ZtlJzBuJCDfrx4aBlczTt7cFTqJcPVgTYPbuJ5q4BVj65lYgTpZU9WHhxhkOLZBnAsDUMBXJZH2qoVn7dKurNFZGSEV2stk6uHEz/OZLMNfb5dGI+nYZUmgue30dzKovPHml8EY7DLp+HxqxNOjCycsecPZ28Ze1fkMOC57GQ9FJNhgAChyCDtLAFGw82AQSgEsfPAKCsV3DmAMHC6RuQa5ZPyUOYIkqiTBerrebKWOax5oF0Y9bQiBk6NdksH/zP0zRlLTzO2LkLUlEI2mKM4AC7W+rZp80kSQCBjYcYBnF6qCNDPkSZRCFOJf3MJEQfQfYjUHEIkyaMgrOcEcEBliEufe4oP4IppSREB66rSmRVgO6QAZbDGbv24xgGCIEUE4ex10cnzlrK51kCG0mENoJ0E6SbLN4JxyYIY+JFI4dKClAwOeDS60tf9J2VIKUiujImMJQE3bQQhaLHVMfGjcoKwYJN+zDGxX2f3d6NYdlUsw+lsMKDQOIhNeEHNWzkhPBh02OORKmI/oOBkMcBqE3kQBc82VCLN51BsW0cRSGnqDTu7qO6M4qZM1FNh1Mffp7mtl4qe+PM27yPs9flc2GDscELWtk2JqKkQZYgUXTSmHiw8SOR6Bww/OuWo3LXRaKUHLmVVYOZP0SS2bpen07Ma7CkvYcLd7YTQFA5kGbxhnb6qv10zKzF1DSWr92NkbNwBFTZSaplPsuvYxdhxk5ySRAiSj0WXgzShOjGQcfBj0CikiBAH6BsVXBagaEenC3INYun9GEcZUpG9MlY9MGORitr75tlIlZsb+estTtACJ46dR7xcABhO4RiadJ+DzV9g1zw+Hoc8qXzfDbhJwYI4lSSRjo6CAvNlgRq6+XXo0W+vaJR0o0zW77b1PmTeX8N/HnF3FRFLDUcOLAiliYeDiBVhVhlIXBwIoVdWAFCAANUMUAtAlNK1KUt8mubi3cnpUVJiw7wnu0Xpb2z71DaqqqG622zdnYRjQRIhgpVNtPiuOfbkYVAoRoKLfLr08MrexGUiiN3UN666zJpBgyyhW5UI2dxymPPk0qkeaAhwsyOLtThurxEY+JqTmVGKPmUPkS/ZW1++OS5i2s7+vFkLNYtbuGx2Q20+X3MnNFIY8cA8/f3IpBUUI4XeDBckdIBfvjIS5dEhWV++dWn8fiZi/GGA7y0P85lHb081VJHf2UYFZtKuvEQf7rY9pYyrhEdQNbV+Y7vTxCyR5plA47D3FQO1c7KZrY7QfrfUit/cFIRzSx5XJO9A/zylrn2q9+2e8L2sGUTyvZQLb9fHvx+GLgqpQMk1IkmJxVBVvWVboNDieE60RsGk+zxGMNt8fsMneXbOlHKYUEPG9eJPr+tm9pUlm1+H9v8Plr747zsqW1saqo59MllAJeV6QB+JydP275HvKwvjT+VpTqaYOfCBjqrwuXGmMPEdaI7GTMdb4j4E2EfRtZil9EIjkPM6/lBsW1zCyXd4XIgbjzjPunzaDiaimJa7PZ65E/uOdV1RVWxcOWDuunRl4p2ix/tRkk8UFv97rLgLwxXpvQy/x3lFDINKYs+DSmLPg0piz4NcV09HeA3M36vzd7XuU2TTuUn3vraUNbrVQAUxyISS86/69ZF5XlrB8F1Kf0B/durZg+0m5rfmfWv0xaFHcOr6A7oDqho9Ff4txXbxlLHdaIHdfMb8xO9zE1001UZmbBfCo0rL1l/4dRb5h5cJ3qVmcZDggra8JvxiQdIie2Y7556y9yD60QPWQkEMZ7jZE7Y0IcyanKjkJL63gHa/L5ZxbOw9HFVi1yb+LJaSZvVyyw0HBwE7f4q/rzieKJhL83dA4RTGT7/khPkwM1NrnuhpwpXee+DOM9YSiu645BFIyU81KZiXPHgWnbXV/LoSYsxlEGWpzLlbtaD4CrRbZWlmg29SogutRKAjE+nbWEtiZCXrCFIBHzEfBOnJpcZwVWi9/rCBBIpupUIkJ+Pvm15E1mfAUBdKoulCjq9RvGMdAGuKvcqsnH6RGg4SEEq5BkWfAhNSloGE4RvSLjqhZ5KXCW6x7RwpECV+bnmqjn5kqpVmSxNvdHvTqVtbsJdoisJegN+mp1eLE3gzZhUdY0NJJASAtW0qI+mlhTJzJLHVVlgvKJWaKpNu15NY6of2zQwO3tpa6nB1lQymkbC66HKcUikkzOLbW+pUnL19D9V/ladF911g19kPmtKTdTRjpc0GXzEqWZD9QJSqh/FcTC9Bn8/+3iyqsoTkRC7/R50R3JcPMnyjh6eDgWypqaqEdtuVz36uyrjqSbhyLVaMLDp97+YWVo3PoVMiej7xWdPAHmGTqrJz8Br00Kf26tW+6TU8NsxTAyyIiAaZCdeEoBgQK1mvyfM3NReupjNACFm0s5efx39Vi2qY7O3qZrHTl7AfU01PB8cG17s7K5edof89KsaKY+OB4nhSJbEU3htyX5dZXs4gBSCcCpDXSoDiio314ZQFQXbccCjgRAOtrNJlSzRBYoinUxK176Jpn5WrtJcuU77URd9v/js10F+zEucCnpp11pY71mCFAqabVOdTqAUojoFGWA+61ALkaH6acEhgCjMZ0lhsIMZYwID9lSHePvbzyddmLveFEuwvKuPtM/gsSUzCCZyzN8fZb/HYGHOGo5mIYF1VUF6C9W7hlQGI5ElIhzW11flL64J8Kgg5fB5+ZMlwnHiUlUb5CptYuiqEueoOnL7xWfnAqsEkhB92Khs8ixAivzPhnLZYcEBElTSS8so4/LRnQUgEeyitSC4RMfGwKKhb5CKXD4IwYqObj750Dpes62Ny9dv42N/f4xE0GCWaXJqPDVGOAHMToxEodrv97I0lSKKQnho9QhLguOMFRxACHSHEPC5I/e0po6j7b0vIh/+D4FDThiYYqRerTsT111JERr+v8PIEh1xAlhogMRPjnq6aWEvEeKsaO8G4DXP7x5zQ42DSU7d2UGf34OjTGyZNZyxuZzfdtjn9dCYGhWSTE7eomvnr+fKKdFH23t/FEhLFJ+NhldmCThJkko+OFBOVfFZY+PyBokC+ezXQRklYl6gGvpZxEYMsgA4CBZll/C3yHwimewEAyqTGSqFjeJI4sbYhpz9oxp2VCnRLJuanElXaHQzrmSyoIK67WCryprDfA4lxVFN6Q3y8/3AVRL6BqlH4nBCZj1+J18MpnQVRD61SyQaaSQOPTTQxpIxER1DJIkQYx47hwXP34Dkow/8CRTBhpa6CTZoqkY4axIyLSKZLIqUKI6D49js9WggJbrj8Mo9nTxUHaHethgYasbVBKgKODJfrkP+eNMmo6uPAT86Kg/uKDNV3rsBtApkNkDXKVnU4weVwAWK4/jT+GsVcjW28OtZ6RExgqKSJAY2MXzMYQe2CGGjInGokb14SEz4jdAX/heJwpWPbWR5ezdJQ6crUoEMBHAUhZSmsD0SYp/fi4MkoypYQEs8JTMO0lIUKixLPlfhd9Jej0DFwlBTaNptiuD/OTn7+pC05zhC3JMMeH8oV2n7jvqDO0qUXD0dwBTvVtuJtA+oVXVZoQm/lREeTFlPjzBQCIwLJLSjupXPveKdbKiPYKoKTX1xUjY80lCtf/3hdRJwPvbgOaV3o0WiJEU/EFvV62W1hKCM4iGBQJJS/Hz73LfQHcln7T26RlxTCUXjG3971wnHFdnkksRVbe9dnjq5wTOXFJUM0sQgTeScai56ah3ZRIrbm+vQ4nF2B7z0BDx7i21vqeIq0WNBnYidKsQJV5CFRprWWA/z4nFW9PSyYn8vNbEkywdiXyiutaWLq0Rvjg2ypzo4YbsjBAhYua8bxbKZNRDjm/ec9kgRTHQFrhI9lvPKU3p24oyrN29qnIkUCroj0dM5NBf5KcXAVV2ruq7gz+Ubc0xFpSNczZ7qerbUt+b3Z3MYlo06SetbmRFcldKzuvd7Q2lYd2w8tsmAL4jqOBipDMGBOI4iSHg9RbWz1HGV6OcmrvtwOzXDMeQaYgMsa9tOpGuAUDSBcCSxSIjnA97yYuoHwVXZO8AAEfqpoF70s37ubO4//nhmdHThzWSJVQTorK1GM63fFdvOUsZ1olcSZz/V7JHNePaBOE7y/Ox8mY6UhBMpan16VXGtLG1clb0DxPEwk06CpKhKJ3jT3x+gobcP3bapiiWYuaeDjFd/Z7HtLGVc1QwL8NUVf5lx5rN726rsJKaqsLWxnrTuwVYUVMfhyUUz5Q//dorrXuapxHWiD/Hv+p+21iQSgf931hm/jSQyJ6qOQ1tNOPnzc44PyVWaO29qinCt6GVePOVscBpSFn0aUhZ9GlIWfRriusaZ0Xiuj/7CFspbVYHTmsl9avu3a75ebJvcgCu996YP9Iouv247Hl0MN8QLCGYyA/GvVZVb4w6BK7P3Tr/W6XhHCQ4gIeH3VYovZ1sOeGIZwKWiqx693pudpCPNluDItqm3yF24TnTx2VSwNp7NT0Twqvk/QwVdLUxEka67p6nGdQ9oZk/0g6ZHJRPygKbk/3SRfwkMFdznokw5rhO9z+P5QjQ0bmSMEPlpR5oARaH6E4M/LY517sBVop/13v2qVBVhj586DPkUnrFBgUGFa6bcOBfhKtE3ehRTlRLGO3FSguXkhZcC26OXR0YeBNeIfvnrHn1Xg+mIuKJC0oT+NGStvNgpK++5AyBBmSwrKDNESbfIffol9zwzN9p5fHekmvUnncZWn3dEXFtCLFfw2kX+9VUUsJ2RacVlJqUkW+R+Pv8votbcba/sWScqsgnuXXgutxx/Hrc1N0w8WFXyfz41Hx8G8ilf2tJQEYFc2hkIhc+QHzeemNq7KF1KLnt/sG51OFGJ05zrEcFUhq+d+24enLeSunQOMdkLKgBdAa+WT/FCQEAHryFyusGAL6zg8Lj4uvmGKb+ZEqXkUvoPl98hRS7EjLYuBmoDbF/aRMbwo9o2f2uo4dnK8MjBgrzYAQPUccW4lOSDVBU+lXy4ovx5AuC78uP6h47+HZUeR130W1tu197UfrkF8Lum2wSgZD2qtHTdU92RiGQ89iLdn/qo5eXlNQP96oPzXyKWbOvkuVNmkvLmAw01RztIagGymoenK8PsDPrZWRmiynFoTuXYXROgNzQu3Pew6AASRjv8SuFv7Jw4CWwDngLWAOtQxD5GzhRylTY2QI5LOWqi39py+wrgJ8AKYD2OfBQpLz8uuTEyP7VNqLZDv1ZFxIqiY9IeamZ9/Vw8PQbrly2gs2Zs/JgT96zn+boFACQ0lbtbGwlJ0CUkDZX1LRFsxCgvnrymQ468Pe4+VfK+wFCMuNGf4xmpDDwPXCJXaZv+2+dTTI6K6Le23K4Du4Gm4Y1SMjexnVNjaw94XsLw89vZb2bP3BoyxtiUe8Le9eyomsuj9TX8o6kORxEoUjI3laU+Z9EVNNhWHZp40aHJjI4c20SrkG/CfeG0AbPdPOL2aDlyJzNacAAhaM0ePDZPMJciXqNRF+2bsC+cimM6zrDgkJ+XvsPvwQJihn5wi8ZL9OKr8jOBxS/25FLgaInewSRdH2nl4Mts5FQdxRI07eqjOjEi/Al71zNgRNgX9E8IAugIQUpVUJ0XkPAE/82d20DXiz67BDgqjTNvar+87daW238M/M/wRinTWwKLfM3ZfXicfFjP8WH5NtUtIGt4UbOC9/7nF8SDQfxmmqpUlPaKRp6vmYHiyDHCCymJenT6tFHl8/DOUZ/aqA2jy/oXzrfkKm1iVuQijmaL3HXAXcDpwDokD8X08JX/qTp3YbO157yc8CzoseuUBnu/CJsx2gKt6CJDc6odvxEjbMcJD44sttcS62ROdD8X7Y5w1+yWkcC+msoeo1BHt2TeQVNE/nPMKyVGyncYFQzwANbna3gmsBVFWEA78EO5Srv7SDycYnLURH9T++US+Hvhb4jvH+j42cBfl/5A1O3rdqR/8unlwnHICWckXuuwk+aAlg8UjBR5IZVCyh8tqiNH3oP89mVAA2CjiPvdGsr7hVJyjTM/WPKnJ05r33DKyfF1Y9JpXPej2yY/PuFcPvzKq0Y8b8vJi68IvI5DxtDyqVyI/MugFo6zpURTLGAXcKn8uL5xau+sdCi5Dpf3bXrtqT9dmGibmds1oy47MLzdb6b4wRmX8/vjVuY7WYbwannRpcSTsclAoQWOfGoXAnJ2Wt7g9VMGKMGUPsR/mr/4denRPra4Zyf7K+r48emX84sFszENbSRbV8TE5lfLxpvMkvEbzQjl3Qj+V37G2zH1d1C6lKzoQzxdc+OCf8476SWqmfjpH088WzzYVDOSZWtiohdu2njTJumvVpT71A9AyYs+moobYrIpnmZHJISlKPlUPj58WNZClQ7WTcGy6Aeg5LpWD0ZNNPqPmF9HNe28k2bZY9rKVduhOpnF1hX3vMlFwFWi7/zBjAtr+pJktcJQ56FRMwVsRRCwJdhyWlS9XiyuEh2gR1PSipSAnFieC0HMp4GqnF8U41yC60TPVVfMakpl8y3gk/gjg4qK/Izvvik3zEW4TvT+oLcnN+S9j19g17TLk5oOA9c9Inm9LrsNPZ/ETQdSZn4cfNqCnAOOfWuRTSx5XCc6wJx4YtFwo7ok3xTrSFAk8osVVxbVOBfgStF3fLf++ZZE8sZRq2+CBppkVrFschOuapw5EOKmrCJv9JSraYfJMSF6mReGK7P3Mv8dZdGnIWXRpyElN4jicLnm8k032kLMqbSca75z59KyE/cCcJ0j9+Y3bXmFpWt/b0jmV1bu8+qYpv3IbbcvPrPIprkG12Xvca/x98ZkNj+KGajJmGiGtrLYdrkJ14neHE8zPi+vSed4/dXb318Ug1yI60S3FTHBaEfAftV+VVEMciGuE70r6JuwTZHgtWR9EcxxJa7y3k951y7zuHQOgI6Qj7aIn0japDKVod+rloc4HyauEj0d9GsV/SnWNVby4KyR+et18TSh/sG5RTTNVbgqew9aNrYQPN5SPWZ7d8hHWFFd9QIXE9eI/vELnmju9+hkVUFOUyfstybZVmZyXCP6tvpw+9zBFAHLoTGWGrNPcSTV8XSRLHMfrhD94ss3bvnz8tlUpfOtcK/Y1smMaDK/tmomx8t2dFJdcPDKHBpXlIPxoG/BSR391KTzwQwqchaXbG4fDmqQURUy4+e0lTkgJS367a13VMc10TPnhPnCE88wXtbnq4LMHUjgsR208orph03Jir5O3KxYjZW9W5bOI+yAM0lMmb2RAFG/h1Pb+5DA88bb7252Uhd2hMJidrT3YDcngbXA6cg10+51KZro68TNHwE+AMxgXBS/nBDcv/I4OuurDhjbzQH2hv2EchantfcBNhW68vJAymR+tPdQPy/IR8CyEJe+GPMd8sENzgPeDBwHPAj8DLmm5AMMFkX0deLmHwHXHmj/lvmtI4LDpEGB1tdHiHkNZgzmPXkNlcFAJQ2pgQnHHgUUYC75WHlDxl0JnAlcNRUG/DdMufe+TtxsAO842DG7W2sPGf0p6jMIZk1O2ZcP9CSAusQhU/iRZryRb0Zc2jTpkSVEMVK6eqjfDSQzRMPBA+6XwMxokjP39KCPKuuD2eSRsvHFIoBAsY04FFOe0k+S700D9x3smBUbdxEeTBxwvwBmR5PojiRVCDjkMbOoTtF9sseRa7YV24hDUazGmdeSDzU2qdMTjqd42UPrOX7jTqr74zR0DzBn58SwMTbwzzkNtFX4wGyb6tW5LEZi5e0GfgW8bmpNeHGU7Bi578298//5dM/bTY/OjI4+HjplERmfZ8Jx982o4dnmahZ3DvDgd66TVY51uLEgJdADvBe4B/gYkEGu+cqRu4vSpGRFB/jm8rtvwaO/ORoJkQj6kMrEjGltQ4SHZtVx8p4envx+U7lZ7jAoadGH+OD5jzoifOBoUbsifraFA2z6Vl1Z9MPAFR0uAx4jJ5wDD22fHU2hHWR/mbG4QvRf/XWF138Qbx7gxM4paZQ5JnCF6AC7Avq3vMk0A56JVXxbgK+c0g8b14j+27tO/mgi4MOYpOMlqWt0+yd69mUmxzWiA+A4BMyJDTChnMWzIaOc1A8TV4ne6VHkZG1uWVUhaNrPTrlBLsVVoreaBNbXhdkfHFkLJqcI/j2nHoVDLBBTZpiSHUQxGd+5c2n6lA/t5/Y59cyMJvFaNrsjATKaypn7eqLFts8tuCqlA8yKppBCsLsyyJbaMBldo3UwRQB2Fts2t+A60bFsXrK7G8PKl+6N8TQrd3dT74hrimyZa3BV9g4QyJiPR1KZ097x1A5MVSFmaAzgWPf8ZmF5DPRh4rqU/ss7lpw+ZzDdus+n220BQ9q29ZF7bll4iOUXy4zGFR0uZY4srkvpZf57yqJPQ8qiT0PKok9DXFdlG893lv816Xg9/rjPIBhNICz7Fx/Z+Jpynf0guNp7/8HSu1Kx+irf0BgpW8C2oJczn2u78N07Lv1HUY0rYVydvfdVh30SGPB56Q36cYSCVyg8Oq/pnmLbVsq4OntP6RodDbVkDAMApdJhXlcPT9eGywMkD4JrU/qFl2y4d3f9iOAAjqKwrzKCxxF849R/fbiI5pU0rhV9dix5QU6fmFFldY157T0Meo3VRTDLFbhW9FjAR2b84rpATTTOaZvbSStOOYs/AK4t0x3HJtw/SHMsQ7Q6RNrnIRxLcfra5/MTHPdFi21iyeJa0Wf1xZjXNUAuaCD6e5FAZ2WEx0+Yz+Lt7ajlNXYPiGuzd0NKbL82EqwCaBqI4rFN7jttKRmPJr58xn3NRTWyRHGt6GmvjqNONP/sjZtxVIXuyggJx/pbEUwreVwp+pL3dl23qbke1Z44ILq1r59gOs0Di2dS359Y/pfQr2qKYGJJ40rR+3Xlew8tbELN5FBGTWea0duLKiUZTcfyqnz+Dedw5ylzuotoaklSkm3vUlw6M4X6HBgBiSKzhBXQUMghceQ1l72dTY2zxDZF44d/+ycndfUSyGYJ5HL8a8kS/nHSMjbVV7CxPkx95wDXPLWJQV27+If/PPOuYt9bKTAsuhSXXwnyHJD3A68C53kBt0l4OygngHDAyUjEyUAlONJB8QukEEgByCweYVKJSgaDQRLUYhEQKll0ktIgi8QWJhXYqDJAgvy5JhKFvLAgyCIYyroFoCBRyaHTzjyeaJ7BN85eSSIYpC6T5YLnd1MzGKcnGGDr4jn0e3Ueaq6iIZ3hlPYegrE0nV6DBxY0y+pk1NE0pX1vZe2PTEN/AnhIfuzyCuDkV1z5sZZ7lpx4BpI0QiQxlO/Ij+n7X/BDXW2dBujAI3KVVnLVCOHwFi9k2gV2df7BOwgYFb9FQeAUvnsYqeU5QHo4placBhx0KmhHFI5OUENfYaHjKtoI0QOAhY4kgIYF5BCMDGTNn6kCBiMRuwQSh20s5ZkZtbz10tezMJvDJwvlk5Qc39HFiVt389yMen50/gqsISdPShQpcYaiWBQmQCpIHATvePLfff97x09CChiWonDjOa/nK2dcnD9WFRDQPiGvN752WA9ztRUhH8rk1MKmzcAFcpW273DOnyoUyH0rLzgMCQ6jH/fQi6oxtlqvDH/P4SdDJUE6hwUHCNKLhxgAUZqG96jIguASGDtyWQz/llL4lt+SIUCIBD8/8WRqbQe/zL8aAhBCsL6pnt6qMI/PaRwRHECIEcEBFAFO/iXQpMPX/3pLtZJ/w9Achy/+5w5mDxTcAFtC1vmyWG3VHtbThFWMCA6wGPjcYZ47ZSjgnD303/HtlmL8oRPIy2jhRWCjThIsyiAfh91BL2Tho68sJ/zm6OuORsXGIEtOVfE4zsTzhGBXbSVzB1Mcv+8QQQQLlXtLVUkaY6fAKUhO7Gob2WA7CvkwoIfDisPcVlQUUP6V/68z4VGP/T5Z0ZR/eDppJCoWxoQjsoVYel5iKMPXsAtXV0a9CKN/c+IqDQY5VEwuf24Tg5MEHAJIez0s6YtRm8xMun/kVvJ2qLaNPzf2WFsInmqcPbJBVRxg/cEvOMzjk2x77DDPnTIUMD4mUfcPfR0SeuRz6AFbjA375gx/10njo484jdiFLF8CMerIEcBHlGp2DZ8pcMiiYaMi8UxI7+NfhKGtYTq5+vn7ufGf95IY19kipWR+NF+UnLSvj0DWnHAFISV6zgJVQbMtbKHw4Yvfut8WIgWQVTVWnX8le8KFqr0qwKN8WK7S+g7w/MbzTeD+Ud/XAZ8/zHOnjNHe+6vAORfEvcBrwN4i4C8S3gjKcfk80U5IlDOAZoljOahVAlsVICRIG0Ox8aKSRsfERiOHBws/Bhk0MhIQDoawUaSKjUQRAhMVuyC1BkgyBFGwMfIePvmXLG9rt6eOy9/8SRKGBxWQQqBZNhft2jN8Y1GvwROttRiZNLP642xsaaDD0GU41WslKiqea49U/cg0jPXAk/JjlweB41921Seq/73w+LORMo4QWTzqj+Qq7QUHsxGrreWALldpa1+8NEePkqynx8W7w5LURgUasyBVgqqCjQBMLPtDr3yXsrl1lnCEwFFVhJTM64+ytLcfdVTowJiuccKzm+kIVPDj80+Zu/3mpvLMVkpU9ENxwjv32LMSaaUpkSCSM/HYNj7LRpJ3zqSqEjV0fn7SAt7xz6dY/ei55b71UbiyGdZvO1cs3rGXlniSSCaLP2sibAfFdoabZU0ks7r6CTvO4Va3pg2uTOkA3zzpHqlrgqxhjHEDHSEwdQ0RTdJl6F3ffezchqIZWaK4dhDFpsZalnX1TaivK1JSHR2ksn2QRYr9mqIYV+K4MnsH6AoGqEimJmyXwM7mRv4zv0Veuv8tT069ZaWPa0X3myahwTSKPbbRKOX1kDMMNN21t3bUcW32nlUUNs6ox68KBkMBdMsmp+ukvB482RybqiuKbWLJ4trkoAjIeHVUKfHmTGLBACmfF9VxmN+2m8ZYouS6NEsF14req2s3z+7Kd9X6M1mauntp7OnFH4+xoaGe09t6zimyiSWLa6tsAKvOvU9WZ0wUTUdzJEldJaNAn67xk3tWlhtkDoBrUzqAtAQ3n3cKMUPDlDZb62u57dTlBEzLvW/yFOBaRw6goWcgG05kPF97+alUZCwShsqZz++lq7rm/GLbVsq4OnsHWL1gTXzz/Nbg3powC/f3EEqYn/vywy8tue7MUsL1opd54bi6TC/z4iiLPg0piz4NKYs+DXFllU2stj4PfIb8wLmlcpW2tcgmuQrXee9itWUzMYe6Ra7SriqGPW7EVdm7WG3NY3Kb3zLVtrgZV4kOPF9sA44F3CZ6mSNAWfRpiGtEF6utHzF+TmWZF4UrqmxitbUTmH3IA8scFm5J6WXBjyBuEb3MEaQs+jTkmBFdrLZCxbbBLRwzogOfKrYBbuFYEv2kYhvgFkq+yiZWWxPjiEzO5Avsikt1IM7YGGWTMdTzZJFPDGph27nINfcjLm0l/7zakGtcPZGipHvZxGprLS8sBX9UrtK+hbhUBb4FfODoWDbMWuSak4/ybxxxSj17f6FZ9jfFakslX74fbcEBViAufdMU/M4RpdRFfzF8n6ntav3RFP7WEeFYFH0b0D+Fvzc4hb91RDjWRJdylfZt4CtMFnby6PCSKfqdI0api/5CAvSbQDUAcs2fgZXAC44B9wKwgfORa/Yc8sgSo6S9dwCx2nqKw4uv+ku5Snv75Be5VCP/EtwBhMiHsx7eSz5X+D8gg1zzP4hL3wL8DLgbueZ1L9760qTkRQcQqw9rFurv5CrNdZ50MSj17P2FcGexDXALx4zocpV2R7FtcAvHjOhlDp+y6NOQsujTELeIXnJLYrgZV4guV2nryNevyxwBXCE6gFylJZi6ptVjGteIXubI4TbRD3cUTZmD4DbRfQfYXs72XwCuEr2wbum/J9lVXi77BeCKDpfJEKutDwMb5Srt3mLb4jZcK3qZF4+rsvcyR4ay6NOQsujTkGNKdLHaUq5+1VPlxfcOwTHjyK18+w7niYXNwlEUwokMr3roubbf/HvlrGLbVYocEyn99RetHXh06UxhaxpSUYhW+Ln3jKUzrz3n4WXFtq0UOSZE3zSrITJ+W2/Ezz+Pm/FsEcwpeY4J0SsTmYkbhWBuV7wcjWoSjgnRL3xyB0zimzw7r7y27mQcE6Lvra2YtMslGvLxnrMf+PXUW1TaHBOi/3XlwvySjKNo7Iuh2w5tkcBFRTKrZHG96B98ycNaV1Vwwva0R2fl5j3kVNUzyWnTGteLHojG2ifbHg14ufiJrWT9Xv9U21TquF709Qub6xCTOOlCIIATd++fcptKHdeLfsKuLirjKTy5iSOpnpndwM6G8viK8bhe9FDaEh/502Ms6OibsO9vJ89HTJYLTHNKNqRYn/iQnkXGwOPxkRQGOcAhQSUmihRkHm2W3zszYufAFHidiVG+/GmLZW09U298iVMyKT0lrqmJiqtzKXG1jIlrpU42Fybn9ZMRAbJIdGI0kCGCQ0DkqF25W1wvhQqOqnDOlj2o9ojwwpGcurmd289ewk+qf/FM8e6s9DjqvWxRcb0AXqOQu8zH/jdqZHVJPvyDhQYoCBQsAqQxGKQBuxDnTyVHE7uxMIhTTRYPctR7Oqh7eHjRQpKGzmNzm2ivrCBtaHREgkT9HixN49WPbeHK+9fTmuzDsRyEL+uckFzfEbJTO4Bl5GfOdJGPSHUSUAvciVyz9qg+mCIyFaJ/D3i/nw4Mkgc8TgIJauhm3pjtXhJ4MDExyGEgkMPCO8Cfjz+eb194Gifu3I8/a/LowhYGgl6iQR+XP7CRD9z1xPC1VJFDSsFAxMeF0b+iccDAjw7wZuSa3/03916qHFXRo+L6emCfwFIr2HXItTh6mE2M+rEG4hAkgYcEPhIIJBkCDFCHROE7Lz2Lpbt6qUxmAbCF4BuXrOSp+U3c9bnfDm8fQho5fFaWk5yHDmXPFuSaxS/wll3B0XbkagBVYB/W4isGqQnbNEy8JPATH97mI4lNH/3U09IdHyOsKiWXP7SR5u5BgpmJ1ThFOPic9OHYc8z21hxtR24TsMXBwDmMnwrRg5fY8HeBQwUDeEhPONZLihQe5ndMjBNYHU/R1J+kNzh+QoyDzKp0+eoPnLGPcMzGsDmqokfk1yTwWuDfCZqkg4KE4T8HBQd1+E/BoYlNNLKJCF3Usg8dZ4zzNkQODwJBODmxL/2BpTP50WtO5uPvuIDH5zUR9+kYShr0HKahUaO0oUzsl9sNtAEp8uHFPnxEH0YJMfVj5PIx3QwHfEmUuxX041VQBYoQqMJCBxQMEjgY5IggkOikUQrpUyLooYE+Imyqa6Y2lmbQ7yHhNXhwyQz+77wTyOkqAGdtaGPp7n3ymgcevQ6P+vNTM6smDxE+jSi5gZEx8f5IFnODxNesIYUA0oQw0fCRRuCQwY+JSgZ7+zuuuXrejbc/yJ1nLObnF04MGl01mORPX7xNOUu+u7RutIiUnOiT0SWu9+ew/qmizBeIlY3y69uG9p3+jp1y7fxmPKbNS5/djWHZ3L98Jv0VfhTHoa4vRufXasptsaMo2WbY0dTLr6XIh/mcwMvWbpO7GqrFD37wd2pieYfvmruf5SPXXsC2pkoueWwzLozZe1QpmWbYF8sp2zp4w/0bhwUHCGRNrv7HegJZiz21lUW0rjRxveg768NOc298wvaGgSRJn0HCWx44Mx7Xi2779bkZz8TbeGxxM0hJZTRuF8Gsksb1on/sude0GTg4OmQ1BUsR3HvSHH59/jKElPT5ja5i21hquMKROxSmrtFd4yPu03lwySz21oR55z/X8eScRmbvj1rFtq/UOCZEr+yPYmkKPsvhwvU7h7frlk3O4ZtFNK0kcX32DqA6kskGRxq2zc0PveR7RTCppDkmRP/dS49n0GdM2L47PHE8fJljRHTVtOXdy+YO96qZqsLTM+oxc9aWIptWkhwTZbrmOCs2zqxb968T5nD2c3vY3hjBk7XY9MOWY3IQxH/LMZHS/3XrkqcjsXTz+eu22ymPJue293Vunt1wTNzb0cAVHS5ljizl1DANKYs+DSmLPg0piz4NOSaqbEMEPp98IuXRTsGRsrY/8f3u1TUfLLZNpcgx472LL2dylZmU/uZnHiGUzbBm8clsr6rNWjcGvMW2rdQ4JkTXv5BuaUpE997/v59jVjQ/SzWpe3jlW6/nwTmLXyY/6ZlsYYBpyzFRpgfj8Z3vefzeYcEBAmaWz/x7DaF0+p9FNK0kOSZEn9eb1GcO9k7YPjvaQ+NgpjwSdhzHhOgbZtfzt4UnTtj+t3nLmZlKM/v6/klH0k5XXC/6cdfuvT+rafx2+Uq+fOqrSOgeLKFw+/yT+fTJr+WZukoyudyvim1nKeEKR06stjzAyYW/mcAauUp7CCD42biT9HmFnstiDuTQbQvNsUnrhVGwES9V6bTT99VqtVj2lxolXU8Xq60rgd8gx42MkfLDYrUlgUCFbQvdNkFRQYCpatjKKH11lRya63O0I0nJii5WWx1AY/7LOF8s/11g2ylNcTA1nebeGHWJBBdv3EllNsfWSAW3Hj+fqKGSFuWx76MpyRQgVltfYUjwgx4oUJ3CsHbhcPXTW6nO5lCAxdEYV23eQTidRDdtTn/P1iuPqtEuouREF6ut44BPHNbBikJPOAKqYEHPIOo4/2R+d5SLNjxFRtfw59K33Lr4N+WQoZSY6GK19SVgwws7KZ/1R30Ts/CkrnHP4hPxZrM8NHeBeHhuRXJdwycOtF7rtKFkvHex2vok8OUXfb5p8eF/r2PWwMi8tt8vn8dDi1qRysi7ffFTT5OWRvO9tx3f8V8Z7GKOmuhitTWLfJy2V5Avn2cUPvsBA1gPLCr8fy7w31WpbActY3Hivh6qUxkqM12c1b6RroowPzn9QroqIgCojs07/3MfjzfPaAvbZM7Y8/zf7jrutPUVWaem0jIH/jmvoTOnqffIVdoxOwfuiIsuVlshYA1w/hG98KGwJVj58CTveeIf/PjP/294V0cowunv/xJ7K/NLtn3q3j9z+/Jz2VUbYHZPjMqEiaMoqFJSnc2xpSFk76oKvU2u0m6Z0nuYIo5Gmf5JplpwKfOiCwFC8MkH/jxmd1M8ypueehBPJoM3m+VT/7mdEzr2Mq8rTjhl4xSyf1sI+gyDEzoGVaT8uVhtHZPt9kdD9LOPwjUPzrjMqiqVmHBIOJMmi05G1blz+enM7e8glJk4t9FWBDlFIZS1DOC4o2RxUTkaor8w7/socOsJY/tXbCH4/dLT8l8cyZqlp7Chfg72JHevSInhOKQM1QG2TTzC/RyNFrkvAa8i77gdfYZ8klFu4LbqRpIiSMoraA9XcdO5r+eZptmF46EzVIeQFbRXBQhkLKoT2eFrRUyLnVV+bEX5vlylTbLgm/s5Kt67WG0ZwMuBNwPnAGHyXrrCSGacK2yDfFDoF15+Sjkhaz9lzzZW7mznp2eeRUbXcCRgjhwkFDh5dzdRvyabEgmWte9NbGpu2W6qAZ+BM7ChMbKzO+T7plylPf2C7XEJpVRP3wHMecEnOmPtf/dj/+B/V144Zls4nkI1LQaCfo7f3cWS/Vu//Js/X/jp/8ZeN1MyogOI1dYGXqjzZDt54dV8AT27v4tdNWNj+TYODPL+/9zH3QtnIlXfpQ/8bOkfjpTNbqSkmmHlKm0Z8PUXdJKiQDTLax58hh/+/E5inolz0gWQ8mS55tkHzp7ugkOJiQ4gV2nXc7gRmAu51HuefYYf/OVeXv38DgKTrNqk21n+07Tod29d95EHj6StbqXkRAeQq7TLgMMKEBROJHnnw+uGv7/5sWfGXUzSEark4ROXv/kImuhqSlJ0ALlK0xmqJx/I7xCi9fwt6whnRqJFXnvfE3z5zrtp6Y8WzhMojkSu0g4jxPv0oGRFB5CrtAVylSYQ4tNAD/lqngk8BShyldb+TMtCGba7x5x32ZPP4TFtQICU6NbELH86U1Le+4vB+/n43tc9t67lJds7mL8/Rk8owA9etpLH5ubbhiLJDHYmGY19pb4cJLZAyY6RO1yyft/Mp5vm2X8+4WTSRmEghQAkeCyLBT1RFHPgMsYtCDSdKens/XCQH9Oc+mSCxd37Rm3Mf7x05352h4M8+pNl/yqOdaWJ60UH6PB6rM/88w685shKHa3RQSqSXaSVY7J39L/C9dk7QGd97XH3z1myZedX38dfFq+gNhkjkkpwwTWfZlFn2235pv8yQ7jekRtCfDntnNqxS7z8+WfZUV3PHctOp2GgX7Z9o+WYyM2OJMeO6F83dWw7jqJ4hJQEkslE/AtVoWLbVYocM6KXOXzKWd80pCz6NKQs+jSkLPo05JiopwOIL2SU9zzwyFZHKNVPzpm98Okfz+w59FnTk2PCe7/k9Q9f8bJte393y6kn0V0R5ILN29gViOy5947jZxbbtlLkmEjpLdHU797/5kuh0OT6vw21nLJ999QMwXYhx0RKn/vRDnnS7h5esmUvjiJ4ZH4Ld5yyiHA8fcbAlyOPFdu+UuOYcOQW7+tnTjzHwyuWcM/py6lC5eXP7SKYSl9QbNtKEdeLfuK7Nv97RjzD3Yua0WNdVPXt49bjZrCvppKUIqd+Xp0LcH323vjhzuzKzj7jC3fdRU0qBUBPMMCrr7uG3ZVh5PWect/qOFyf0le2bef8Tc9Rk0qRQ6OXCCR0PvGX+0BROO4DW79WbBtLDdeLvjNUYzYPRMmis5d6ooQYJMRxG6Os2N5Jr+b/cLFtLDVcL3pa8Sj/mTOHPlGBHHU7Arjyvg0s7UroYrUVKZqBJYjrRR+M+Lw/PHcl/1y4YMK+UDrLqr89zsw9neXWuVG4vnGmqzIspFfn7hXzWLmlfcy+e0+cS3fEYFlXVIXWIllYepSs6IUseRXwIeA2wAe8kbHz2J8PSIukYvCvE+ZQkcryhoc2odkOfz15HnectQSAix97puzBj6KkqmxitXU2cB+HG6BASgLpLIa0GQgExuxa2dZLRld5uiEC0mFxW8dvN948uzyfjSKLLlZbGvloFFuBlhdzjXAqwR/+bzVvuPoj9AUqAIikk2RUD8GcRU5RiPkMFrR3k/TrF+yrrvz3dJ/XdrTCj3wWuIF88WGRjwhzVLLYi597kj/93zdIazo3r3w5q15z9eQHTnafQgwA75KrtMObGn2McMS9d7Haugb4HCP+gsZREhygrRAQ0GeZPF97kMDRIj+ZcSjWXCGmbCVwh1htTW3cuyJztIIHThnPNs/i1uPPBCCSTh38YPOAufo7j6hRJc7R8N6nfF7wVVe8j/tnnYTmJFBsC0ed/Lb8iTQpz6RD4XOTbTxWORqi38Dhhg85EkiJLRR+fvJKpAqOqg5vH87SgUAsSXbymMMSuHmqzC0Fjnj2Lldpa4CrgSTgFD6PXuov+GeWJrD1UaIKQSSR5u3/eJrHv/MJ5rR3YoU9hdhzcuhl2AG8RK7SptVAi5Kop4vV1hfI5xAvHLsQQFAA6kR/8Sv/9w/6mxP8dulp6Jrh2fW1hmmVlU9GSbS9y1XaZ+QqTQB1HGaAIQBsh0CqEMlzkne3NppkX3WYPy09jVdsf+4zZcHzlERKPxBitbUU+H/AZ8kvEPAzxr6o3z9p1973r2sttOuoAGI4EkVFMoNm2YSSCXZ/u7ncFFugpEU/HC5948Pyn4uPI+7xDUeNHI1iW+ipnMzcVFESuVop4PoH0U3QecvTD3Lj3X+fuFNKHEUlK0Q5vNQoXC961qeZHsskkHZo6ovm48QWcq8T97QjUjnwe6qKa2Vp4frsfen/7Ml2VoeM0b1szdFBvvmHv/Jkawu7g8K8408vMw5yiWlHyfanHy4pVbHHd6vui4TRLIvfn7CMrCHfVCTTShbXi65b9qRVvOtfdxFJQ6X3KzXTqgftcHB9mT6zP37jhG5TCTtqqqhKDHyoKEaVOK4X/R+3H/cdHPKNM0N/5D+r09a0HixxIFwvOjD5AAkJ61tnrp16Y0qfY0L0qmRCjhFeSnAckp/1P1I8q0qXY0L0N2x64gRvNpdfz6Xw15SI/rvYdpUqrq+nj+aVV69d3RGuXLy+pfVSeb2eLbY9pcoxJXqZw+OYyN7LvDDKok9DyqJPQ8qiT0Nc0/b+vcV3bm0ZTC/AAa+T5eGWhpd9ad1F5WrZi8AVov+u9rfmLMvSPFkbf9rGUQQnaNF/cRRnzhzLlHz2fmfVLc6MWEybHY3SkI7TX6Vx93EzUJPwvUW3P1ts+9xISaf0n83/g5iXTYlQLj+IVQHm9MfQDY1/L21GE87y4lroTkpa9BndfXP8uYnd5YojWT4Y54+L5k7YZ4nLr5No3xeoQpLLaFjVQt55iElu04uSFr0/GLJxkvgSiTHbE34vXdURKuTYntOcuPR3GsoVCplCL6vitfAl9XLZP4aSLtOfrq86fXtVFYM+z/C2nooAu1vq0CwLS9eHt2fFG/dqKFcI8i+CABQcVDKY4rJ3T7XtpUxJix60zN98/qqX8qOXn0w8qGEbkkcWNZEwNPYH/FRGY5z83s5GU1zVqCNaBHJCkhY4SLRyAMFRlHT2/lxLg5jZF+Vzf/zP8LYrnthI1lDYX1VBWzCAf7B3m4OjaGNmpI6WXiKxvFNmtAso6ZTuz+XERc9snbDdk7NZtL+D09v24rfwq+AdKt3l0JymUWOndITXFpdlpsruUqekRe+t8KHb9oTtQ9m4MBUCpiMcbKGQQ6Aixs1kzJftoKB5HPGGc6bE8BKnZEW/s/LWH1y0bR/SGyCjjc66JXphunsGg9Z9g2jYgIrNgeY0OAgUHLj3aNvtBkpS9Lv9v1i6Z2H9dbGqIKGkSb8WImYYaJh4yKJhk9IMokqAtXNacNBx0MkQnsSVg6HgVhK1PNOFEnPkxI0p9dYf3Loj2VI50/RoBAYzzF3fSUbRaG9qJJxNUZ2Ok1V19gfCaGmbRKVCQglS4QziJ4ONgUoOUSgE8lW4fDmvIciJt14p0B/Q5c/aD2XPsUpRh0vdGbnN60tm1+tWdl60wi/uXzaLyzasZ3ukiWRDgKYdA4T7UkgBfU0+5Ki10ANmhhU9u/AUJrhopImwFwUHiYoopHg5yrUDgYkHGw/5F8FGkpUeEk5+VrsqQGxU4FTkbekpfhxTRtFE/4f4ZQXIqIYteiN+vnjZOVzw3HYuf2wTCnmREqpBQvOSDnoImkkGwn4AFMfhJR1bMORYJ8/PAH6iKIzdnhfeBiQOGlkqYbiK5+BhP0rhtZBoSFSpIMLI38aP6kMoEkUr03O6+jdHCKEhuePsZeQ0lcue2DxskAB6m8Lsn1eFZjg0xOLM2d9HfX+M3gqVuHdi8ZzFXxB3aMrL0LVG0rqChcHgqLMULEKjjnUQSOFgH7MRp4omumo784fkiPk81MWSaM6IUClDp7YzxpKN7dR3DpJBx2fmuHv5HD5wxau5b96cCdf0MchQM+zYOU5j2+hVcmO2yTGujSxU+8SKI3CbJUnRRLc09ZeQl+WkHR10hQNYo8ps6YBujWTTEgUTjV+ceTwAN11wHs82NjB0DY00PqLjfmVI+PHZ/VhURhffSsEB5Esv9t5KnaI6cn9X/69TdewGHzY/uehUIgMJ3vrIegSSHOqY5TkANCxe+umryOgjKXNBdw9XrnuGTzz2Rzwkxxw/lKGLcWHsLHyYhMm/Smk8DBT2KMj8725X5K3zj/gNlwhFrae/0n5ro6Oo1THF+MxVf32y94JntzgPzW/BUiQaEyecaji8av22Mdt2VVdx/vat2JPUPk2CZKjEwo+DwEHBxIuJB4eEI0lEIXOjRH2bjXq9hfEp0P3HsuBQgjNctotviK1a2G6x0iKNBxuVfKlr48Ukq6l8/2WncO/SOcS8Xr79x7t4+6Z7UNBQMFHIFXx1HxY+huroBt2AQJW3Tfu+9ZITfYgHjB/LKjOb96WRZDUNrzW2bL7+9S/j2qfW8sq2f6GgIlHJ171VbHQY1fNm0IeD6ejy95MGiJ1OlGQzLMAjK5d5/jVnBu1VFTwxu2mC4AAXbdzGP2bPw0ZHIY1KEp0UBnG89KMyumNNIlH6pu4OSpeSFf0T952ZGwx6aa+NcNtJS+j3T+wSb5vRzN3L5yIwsTDyXSoFBKCTACQqKQRZHCoWTd0dlC4lKzrA6Vv3SAnsrA7z5VeuJDuqt+3Py+fTX1mB15ZSyzejTkAg8dCHyiA5Kn7plT/tnzLjS5iS6nAZjxdbJg1dRDI5/nHSfJ5a2Mrxe/azuyZCT0WAdz29nXM2du9QUGIS66ShYNAjSBQkGYh55S/fXpy7KD1KWvQBr69/R1VFzY76ShCCgaCP+5bMHt7f6/Pypuc2LBAoio2wQKCO6lXL/yloyJ1FuoWSpKSz98igvaCjIkjM65mwz7BsDMviVTuvkkLeZmvkVuZb3IeDvwMaEhMNcfoUm17SlLTo/z5xdrK5P54P5D8uOlhlPIkzaiiVkLc/qmP6JBaSLJBFksFGJJF3lEORjKKkRf/8upflLlu7mYVdfZCxIW1B1mbptg4++ddHadnbO/YEeUdGQSgS7nUQvQLxak3eESyO9aVLyTbODPH3yC+lL5XjrhPn81xTDUs6e7li3RYkgvX11fZ79r6hpP2SUqTkH9j/O23J+g/9a93yq594bsz2DBo9jZVvLJJZrqaks3eA399z6vFPzKr/VFaM1NFtBFtnVTs3PHH+HUU0zbWUfPY+xP81/35ZyMyu81i2urWl4Zm/n75sxT/+t8kdxpcYrhG9zJGj5LP3MkeesujTkLLo05Cy6NOQkq+nHy5nXbVNO76rNxpSRCClGdIR4v0/uOvEHxbbrlLkmPDeX/7m58XKfXsc21uBpWkgJcJx2O/1/vbnf1j25mLbV2ocE9n7Se17ui1vmLjPR9TrJePxkPV4UJBXFtu2UuSYyN6DVrp6bfVMHq2tZF40RtC0qbMsKjPlRZQnw3Wi7xRfCXbgeSzuDcwNWumv3v/S0296snW+eKI2wrXPbMZn57tgs6rCQDBwiKtNT1xVpq8TX+98at6shu1z/eyqrWZj5Qyu/cvj8ruvXCnO2tfF0v4oiMKAKSkZ8HoY8Ghf+vFdJ99QVMNLDNek9I3ia29SDRpet/d+OtpnkJU+NPEQ11/2atGr68xIpokF/Gi2jS+bQwiBx7bZ0lD/aaAs+ihcI3oO4zceo5+HPStxRN7/VG2bL9x6PzdffBbJUBAH2FpdQZfPYE5vlJl9g6RV19zilOGiJ6LQrTYMCw5gqypxn5c6x0EqCvfMaWRLdQUATzdWUx9L8ootu4HW4phcorijyiYuFQkgqfgm7MqpKlJRiHr0YcGH6KoI8NDsFt58yfrrpshSV1CaKV1camxj6eM2zrJeX4Oq+c4jYFqkcw6ZcbpXpFN4M1kygcmDQgop6A55f1Bzbefven/cWJ7WRAl679vEJ3sUwjU6OeKqhi5VTEenlzA2CnbIIWb4SAW8ZCq8hNIZMorgmcVz+H8nzifuGQkSrNkOyzqizBocpMejURtPOKbHc+Fdtyz8VxFvseiUlOgxcfXLu1l0dzPb8JJvWOnVIjwYOh3NglAqS4vdz6bGGWxYOmv4PBt4rKWRrqCPXZUB4h4NI2cTGUwTsWzetHk7jqKQ0FViuobXNNlVFV7551sWPlqcOy0uRRe9R9wgEmTvDmBesNvfIhqsTlpz3WOOWR9YxHbfHJCSuQPd/Oe0E0iOys73BgP8tbmBPlVBk5IW2yboSJ4ydFTgot5+TuvsHp7y9I/mOoTlIHWNzTWB9vPbe2f++vbjps2y2y+qTO8RN6jA0PyiPiAC2P2EDB+ZgACPJF2RUnwXOXi2qDJ3ZlbK2kFfoLI1u6+myknOUpCKRBUBLKrI0aXXsNffzLK+jRN+rzm9n31OK0bOoV+EMNLmGNEfroqwb2hyoxBsEYLlpoUC2ELwSGUYr7RZ0dbB/pCfWbE4axbPxdIEs6LJloym2W+5bANPttSSQGYX9bUP7KmtSWyvrH94xe6d8TZvONAbCN1MxGuTk/vwqgaWE0JXelCVPiAEGHKVNmYgvlhtNQJRuUqbNCadWG0pQBX5KTkBoJ381JxquUrreDHaHA4vOKX3iBv+B/g+MGFyv4WCiUEOgywaPd4QcdWHQNJk7aXG6cZj+gnRTy9V7KcBAbSwh35fkIzqZ3FyCz45NkbMOnEiMVk5/N3UBH21IYSUDNSEWHXOSaS0seZU2Tb9an6b13G4IBHn0Rk19AZ9kLFQYlkcIUARnBDr5cJdm3hw5kx+ecfNLOjdjyMEvzrpbN5x2bU4aQt9IMlp+3fx0MKlgAKOzEvl1yyM4caANuBkoBZ4AKghP6HubrlKe9WYB7/aeg3wC6B61ObRczAHgVfLVdpDh6vN4fKCRO8RN8wEdjF+cih5a9P4sFHJ4GXA8NJvhMYcc0LqOaqcGH2E2cFIWJc69rOYTQggRRAf8eGIjyYqD3P28E/aiiDt00aaW4HfHz+bX588duq5KiV24ZiFyTRWjZ8d1aG8WH1jE15lOsFgUxV//cXXeMXzYxeAuvLKD3DriWdBNIOSyKIZCjl9XAy7sAEjkbGeAuqZ2DjwSblK+yqAWG3VAHuBQ8WhjwOVcpU2MSLDf8ELradfyAHWQykE4y7EiIHUJLF3Vamjk6GLhuFtAod5bBt1UYUojWTxkMXDNmPemCC/pq6MERzg1Zvaxnz3mxbvfnYLx/UO0JgzOW8gSmeoUNczJz6/AV8QRyqsbHt+wr4zhrb5NBwEucla+MbOs1vG5K1Bbxj1/3M4tOCQLzaOO4zjXhAvtEzffuBdQ9FX8w/AcCwy6tjZpkI6OGhjIkZoWBijQn55SZAhSLzwYtTl0qh00ElT4RoTfzmcyfHjP/6T3524GFUIzt63n5pMlsXRKM/ObMKr6tQmM7QZQVAnvue6bWGqgqebZ3HOzs1j9j3TNCv/HzufnSuOxBl/CWXMSxgFwkwUdecB/n8wHPLl/BHlhab0+4BJF8BTABULDRsFh0gujeaMLK/lc5J46SdFmKZR92FikCAw6joOETrxMoCfAQxtgKCnD69vEK+SotKKw7giKWymObOtg8t3tHHpjjZqMvlJqqqEpR09qJbN2W3dBHImaAr4Rt51ISWffeSPNMf6+fBr3kp3YKRV7+8LT+DXJ52dLxJiWU7q2YPHGtdHryv5a47wMeCr4x5PBvjg0Be5SnsauGWy5ziOn8lV2hFvUHoxjpwCXAe8j3z20wvoFkJJ4QmoyCqwhYPwWGgkNS8+EqLC7qfXqKTajGE4Cmm89FKDjcpstlNNPjLIkDVDaefR6uVsCY+EBJ012MOsgV7a9HqkEATNHEE7hwQeWjoHWx3r0GU8Blmfl1gwQFrX6Qx5qI4nub+hlp4KP6d29nLN2rs4sXMrPz39fB5uXUAwk6YrEmFt8xzZ3NNLXU+faIkNyEfmLIj3VVfuAVGBLb0o4gkM5acIcQngB74kV2nrAcRq6yXAR4B9wA1ylRYd8+BXWwI4H7iCfE1IAH8DmoGZwE/lKu3vL0icw6So9fQB8Y5rPaS+a+ExcgQRhSjtlYWAvXHNzx2tF4wtw6XkZXs3UW114KeXPWIhuwMz2VNfzV+Pn8vJnf1kvR5U2yHr2HRUVzC/NzbBD/j5qYswbJjbHyeVTSceX9x6yfyB6NonvztrgGOcora9V8qf/xj48dD3DvHhdTaeE4dET2j+CWIhBEnNQLWqMZF0i0q+9urzEB4V06OQSOX9A5P86JnOcDAv+ij2B3xoKcuuS5u+P/xq3qj6YeRo3GbJUVIdLk3y2ydtF5+QOTwYZKnN9uOxc2RH1QR02yKTDZChAmgGKVBtCxuNFfvGBinw2A5zBhIUIvhjqgrPVQWdQW9w9nM/at4ztXdXOhS9GXY8u8VXtCyxtkrMJh2TXd5adtY0EzMqCOQyzOyNYmTGpv691QH+deJCgoaG7oy9n8caqnh0Vj0XPf70zvO7t857x1PvLK0bLgIlJ/p4esW7Xt1P+A5VUT3IIAkZGW4LGKIr5GPLohai4RCMbpmTkqxl8fulcxI7vtsQogzggkEUNfKnf1kgV3t3i3nSljoaE1dZ3txSi61rhFJpPJksqmWhmSaBRIq0phKRSk0RTC9ZSl70IVRpYeHBIIc23Jgj8RKno6lq2OHz5EwCqQz+dBYtZ/JYayNrv1dXji41CteI7njMpyRg4kHHLjTSmuAfZGtNNfbQyOfCX05R2F8Zoq28ivYEXCO69Ikz0oV1nCQKAoGHBM95F2IJeLK5kZyqghBkNI1nmupRhMrLOru7im17qVHyjtxoHq5crfijZocj/PU5VcOWkj5/8JafvfyUt/iFwu7KMAHTIqcIztjbSc7v5bt/WF5O6uNwlegH4iPnP+LsqK0W8/qiqIBu2wxWBNkf9HHHr+eXRR9HSTXOvFhiPp80DVWkK4JYisCQgJSsr6k45LnTEdeU6QejV9fOb+2L8u8Z9XQGfWypDHH7ghZqosmJ9bsyx4bof1yz7D8ZKe44dXcH/aogpsDxbV32o031E8NHlzk2yvQyL4xjIqWXeWGURZ+GlEWfhpRFn4YcE/X0n827o2HA6/2WlKz++MZXryu2PaWO6733XzTdktSFxx9IWkhVoMqcfG3/1eUc7CC4+uGsWnH3n32W7m/sSFIxmCXcn8EXk+JvgZ+Vu1IPgqtFNwP+11T1j52ipNoSW/NNnF5TZhhXi378nm50e+IMY8U+olO/jjlcK/qT4sdiZucACf9YX9QRgDS5s/bXJxfHstLHtd57TDd8NbkYFbk+ogSI46U35Ocbl53Jqdv2sqij+zFcfH9HE9c+lB7D6Z1r5tdHryRJJUmaE/3sbQzhqM0s7Ox0bS52tHHtg2m0zQnxxTrDQY5v72FRfwyPaojbq38tnxXfPqUY9pUyrq2nP6Z8T9bJFAoQ8xpc+5bXMjNtsaCvMIVJShbv2Eu4e5Cgk6t4SfzaeFENLiFck713ihvOc1D+5ZCvjQWEl35vgL5ggJ+cu4LumkoueGbHyAlCsGNGI4sSKYyoOYiLc7UjjStSely8Y7dAnSnx4KDjINkVqmNd3UIQgpsuPRvL0Oj3e3CE4KSOPi7Yvg9FwokPbyOYTdFZX1Vz1Z5Ly8EDKfG3Py3ecaUp3iiDDMw0SGLix8ZAYpBTvfgzJvV9CS54dgf9Pg9ZXcPUVB6fUccjM+rxJzMkwl5i/gAV/Yne/2u5/YvFvqdSYMpTekx8VIAUEjlDIl4t0dbkEH8GdYUEqZL5no7yEZXB272kXm/jR5AlTtOYNzRKiN5RgZk2t9Twruteg60ILn18Cy97bicVqSyBnEV7Sw3SFiBsmtt62RcwzJWDO7wnxT41bWLHjeaIix4THzEkioDU06Atkkg0ElJHUSx0cvhw0LHRULAxyCBheJvAxkcfYGDjAwQacbJU4JAPASqBXbTijJrIKJDcs2I2KY+HOd3REYOkZG7nAI+etghHEVhSQbEtanqiNO3rcRBWbzRSOfNtuy7LHNEHUcIcUvQe8amPSJSPS0SFwNYFUgOFvJgmAomDgoNaiC9lIaAQSkgnPzNcYqEiUYe/a+QAuzAhUcHEC0hUbERhuwB0BvEyQIwWLPwAOAh2MjN/A4Vf9xSOf2pWC+a4mHL1Awm6GqrYPaMeXypLZ20lc3Z20rA/iqMqeNMZOmZGWNa/iZPb1uN3TEfBscG+T2B/BHgbcAH52HkPAd9ArtmBSzmo6D3iU5+VKJ8biQAzcqyHdEE+yOFFDme+EgWL/DnK8FnW8AvA8HFeEhiYJAkhAIPMmF8SOARoR6Jj4SFFzfA191GLBngwEYCNQhaddTNbyOgaUhEIJx+MrKE/zuaFM9nXUA1CEB5I4DMz6E7+1VRsh5qOAbpbI3zkkV+MslIiydkCZ3ygxF5gKXJNNy7kEI6ceP9YofJpWS2kKgAbdZTg+WPy2e7INgeFieHnBCYGohAlTiuIN/qXALLUkKUGmxAe0ngYwEcfNfTiHXWOioOORSibJRvwkgt4yQa92JrCjrlNZIIeKhMJvLkcg5VBcsZItGhHVUhEAghTkjR8Y6wQqBMiY5KPBOna5b8OKrqcJBToxGMOdImRXEEwWW6Sn4joDL9Ek+c4En3UNwUF0MiRYZIYA0LSVRMZiesmBKbXwCzEjlOAQDaLZtuY2tgmipxHQyoKUoy/nwPOinJt9+1BRRfI2yfb7qAOS6RiwTjBBBKD9PD2fLlrjTvGKYQCFXhI40zycJ1JzHPwkCU4vC+l6eysrGZ7VQ3dwRCmPq69SQgqewcJDSSGN+mWhSc3Nv6slrNQFZtQNjlmu8Sa7G1MAr+bZLsrOKjotfLL7xHYvwCZleCAJRWsQjluYBdCgCmYDM0MzztheY/cSxKDFF7iVNCNgonARsFCKZT9Cg4SgYleuEL+X5UsGhMHwOTwMEg9ChZx3eCp5hnsiVTRHq7k+epahDNRI9V2qBgYaYUN9ydo2teHKJTpRjpHTldZtm8TGSFwkEgcKTG3CZxXAr8H9gPd5GO9nYdc49pARUe0ytYrrp8hsN6jYvdL5NdVUChIqhbcvjQV2GioWOhkcNAwMVCxC69B/sgQvag42Og4aFB4MVIEkShI4LmaJjpDlWNscFSVdNAzHJnCSOfwpbJYqkL7vGa0nEmkJ4o/lUKqUnozKSfuC3/3Xdte/9Ej9iBKnCltnLHE22skcnmacJvA+SjYFwr4Rxrf1TqOP1/hY2+A2Pw0skuhIuwhjYpFPw3Y44rRZ+pa6A6Ex2xTLYeo10PQsVAciVoYWaNJSc5rkDRUMKXUHPsT7952yden7OZLiJJue4+K99xiUfFmFQcTT6EuP8KWynr2RMbGELp/8QyWdfahpS3q+xM4qsCXNPGkcmxcULNv8f6Bma/ue+u0Hk9V0qIDZMS1uokVswh604QLDTxgovCTM87kqXlNvGz9LjTH4aFFM/jzKQtRbYdrHnyWKx55klAsx65wPYG0ed5F6bf+p8i3UxKUvOhDRMV73y8xvmfiAQRbmMlfzlzCN1535qTHV6Qy/PVHP8Ybd9jsb3Su6r7qkNXP6UJJ97KNJiJv/n6l/I6ok18TdfKrwkLysvU7CaQnXy475veS0yVOysuuGfWLp9jcksY1oo/Hqzg8uKiVn/3oT5z6/MQ4+NXxJIF2jcGA79c3PnXhxCUbpjGuFV1V0nJnQzULOvv42U//xPvveQzNyvtnmm3zlv+sk5o0jAvi77q6yKaWHK4ZLjUeXbc+Wz8Qv8lBJYvKW/+5nose20ZbTZjVl5xBKGeyQl5nHvpK0w/XpnSZjnxxxbbO4QZgiaA6kWHxnl7e+PBzzOqJvaOoBpYwrhV9hfwf2TCQxC4sFeRA/v+OYO2sFt6x47JfFNvGUsW1ogP0hLyMDKNQkAi6wyGao9Fim1bSuFr0QCL5aNqr4AiwhGBLQzVrl9YxsyvqjsaHIuFq0S/OvGtln64ko9U6NTLB0oH9VMWT7GoMXFxs20oZ17TIHYzHje8saKusfLizKrD3uaWVp/70jpeVI0UehGNC9DIvDFdn72VeHGXRpyFl0achZdGnIa5tez8Ua/Vv/m+VlXmXSr4TxkL5+xx5w6uKbFZJcEx672v1r98fts2zAzI3vFa7g0IG+dvZ8qY3F9m8onNMZu9S0c/2yxxqYTS9ID8DxoO4sk980l9s+4rNMSn6I7ObC4OkZaE7Jv9/BYmDfV9xrSs+x5zoveKT6sxYd2EurF2YT5vvi8sJhSzaScW2sdgcc45cjxrYubArjRdzzButIMmhoeEccy/6C6Uool/9uufmSTP33EBF0Fi0Z7/dMjBoZw3tr9XJzBXv3HbJQdvNxVdyPhzncUx5HMgciGXyc75tQ/vjur+1JpOdkIUJwC9NskNTX6YxU+69f+WEv68N2tZJupOftablbITjIIWgo7KC5mhce+e2SyZMRqi8fuBC3bLvGTQMNCGxvRpZTYO0CUK5Qt7kvx1gg7ra8TmWqGRwwpRIC4UMKpXEZofkd3ZPwe2WJFOa1f16xh2RSC53ku5IhCPxxUzCsSzBlEkwZdLaE+WhJXOtT7z0kdbR54lPJ0VaqPf0eH3kFJWU0MhaAgwVJaiDkMMzSH1OjgfmzOKpxpYxv50fWaPhJ0cW+dmpuePSZEqz96ih9+hC8EBrI/fMaCZp6Jza3sXb1m3Bb9n4czZz9vXQXhvZJW5IVaKinLy7811GbeXnsx5P/hVVBFgSbAmmg6Mq1CdTQnwes+1z36r+/tkrBZrD4mfaMYVAk7Lgw+fTvUCgIN4GvH0q772UmNLs/ZoLnpSKkPx8ybwx28/e1cH/PLkRgJwqeOy4efxo5ZJ81l1YQpOQZyTYgJRgOTAUW0ZKSJvUJOJPX/vk2hM/8MjDhWgYoiB3fi68jYqHDLn827OwTn5lWo6Hn7Ls/QPnPPryvuoKHm1tnLDvyZa6kS8CFu/t4CP/fooFPQP5fFlRIGOBaecFFmJEcEH+u1cnh3riW554FhsdWQiL4qANB0EainghAQ89m0fbIMX0mfY0Zdl72qPdtbCrl/66GggFxuwLZ/LBBxwBXSE/1eksi/b1cM/sQtFuj2TnBHTwFt5Vh/yLkHUQtsPMwTgexyGHThYDDQsPOQQKstAGn8SDlzQqKGnxdqkRkxpSgIIj3oCDmgPdAGcfiDmavGXyeVMuZspSeiiT03sDAd7yxEaq0xlqbJsFOZMZpsnLd7ezdkErGY9K0MzX2Lr9XjbWVo29iFcFn5ZP2ULkU3naBkcihWBDUwPdRgW9VJHCi41GFh8ZfFhoCMCDA+gIvOiAQlg4hfg1+cYc01BJIbCbJWpWiksHpLgsK8Wlf0BcekxU9454Shc3JE8AFr/3vnWvXNLVf0XGa+h6zhR2dRjFkUQyOc4dGGR/VWT4nD2zW3D6oizeK/AUluKIeSaJ42OMy4HNsQEffTkTaRqATZBMoSkWQJDGj0ocBRudBB5ShZg3kG+Z96OSGD5HLYRIEWgRiY2E1zkoDuJS1LynkQEGgJ0gnicfn2c9cBawDbnmmRf9EI8yR1R0cUPyZ6rtvOPm2/6JpWv4LQslI7lrwSxO3dvNkq49PL50Nvsrx0aPMDWNylg+wE9OVegPePnzSQsm/sD4eDLj0l1lKoNuO6iFUfDjD3ZQMcgOu3hj0ZCFGDr5oFra8BECgcBkKCRi4Zd9hb8mkGeBck3BwEI14dL/h1xTkrNsjlj2Lm5Ingm84yXb26lIZVGlgyIlaxtredvaLRzX1Q9CsHVW86TnG5ZN0tDpDQf56mtewqDfhzI6/JwAcgVHDoYdutHtax2REKqSmVTy/M3mc5GhwAZjGeqYgYmR1A6nhiNhbGvfNYhLzzqME6ecI5nSlwEs7BpACoGvELKrOplFKTyz/nAwHyhQgjXq8QgpeWJ2C0/On4k/neRzf34Av2kRN3T+b8USNjbU5J+pDSQKcxIloAqkKMStFAKkpMqzm2S6mTQGfkZ8MIMMBrlCdEkNG5WhARb53rj0gSPGcTjRSiT5NDTmBVlOPqxoSXEkHbkHATY01aBISapQJhujlsuqG4iDlPgdB8NxEFKi2zaNfTFe/dRWZrd386r1z+MvOHOhnMnbn9qIMnoZLqvgxWctyOaduHxEYQkCfnHy6SziGRbwNA3soIpO6thDFZ2IYfEEEguFBIIUCvFCWLQhxot8OP7bUNDUMTxwGCdOOUdMdPnFwEbgY4/NacrsqQyhmg5ZTaM1FiNbiNgYTqY5efNuFMfB50gipkXQtKlNpdGRXPjsNrRx5XZXwJ8X1HFGsnYh8nX38X0nDnz3lAt5tmoeKgoecoSI4iOJholKjrGRLC0UcoU+99HYgMVQ4y0T9k+GAORQJOkMcD1yzXOHceKUc8Rb5MQNyQgw4x0Pr581py/2naTXqBzwecPL2ruE6kgeXDiD/c11eIRAtWxe8egGqgtOnC1gMOIdFvPeOa38YfHcURcH1KE6uhypto0SvzGR4KEf/JBZTtsE2/qpwygEK5bYVNA+Jjypg16IYp3PqoeCEsOYNCxF/i34F9APPANiD8hHgH3AEmAvcs3Ai3+KR5cjXmWTXwxEgSicsR74M8CtLbeLrTURZ0YsRsajkdQ0PLbNnI6eYcEBVAmejE3Wp5FTFP42f+a4izOSncNIylfJO3UKvPK5HZjOxBFRNiomPuQohy1OI0G6C5fOF0dD2bxEYiOlAr9XYJGA9yPXHE52vf4wjikqU9Ii96b2y+UHz34oV5HNGa965nkemZ9hw9wZiEiIWeEgjYMjcVt9aZNPveGl1EUT+a7T8ciRWtEwtoQKjVc/uYnj9neQw8cANVTSC+Tjw2epwEOOHOpw9MocOkkMy4/UBBZgSxvWgzoHxE80efvHjtpDKSJT1gybMYyLO+sr737CU8fGOTOwVJXBuip2nncyVz30LHN68rnh2tY6RNLkuYZqyNiHV1sq9L6tndkoT9m7X6hIumiln1oMsmTxUUsfChIDGxuVLJlci/y256jedIkyZc2wP/nnqfcMGB6ygQDWqBDqjqLwwKJ8Nr6vJoxXFbxq6x7mtfWMjdQsAF0dKdNHo+W7W8/YsvP2SzZsGt5s4iVJGAtjOGq0QBLHA/jqJl5oejClXauXX/zswLb66kjGMzaBKVISzuZosix8gwkGBYN/XnNcBEBcHw+jiZ9jqK/HZqRbdcjLVwX4VchYnZ1f+sbMLHouOS4WvIZJXSGrtxFkQDbLr07bsXJTeuONmWyVZ5J3zG/b1Ns2Rs4kZTrSVxEaXoZJfi00KL8UvGz4YCHyKd5QIKhBlRcsackvhpoa5E1mDqUQMtxBxULDxEeqMAg6XwHTSN901G+2hJnSkTPfvfdU+d4LnvzDnqrQJV2hIDlNozqVZtn+XqKaSkJR0nf+5YTJJyNIdBQZRwovmgDDyL+ylgM55/ihwzRMdDKkqBh29zL4yeLBIIdBGpvQrVNwuyVLUaY1fewlD3m96ewjKZ82T1rOdk3Vae3rv+79my9+9FDnis+kBMifoSlXYjvbseTp8svB4Xpfh/iMk8YvnHHvc74bxcZPlEb51WOii/TFcszNZdsvPr0nTkXrxJIrP47GIC1b5eenbXkOx+AMF4E4I1+mj32ZDXJoWIXBUtObY070evnFfR5MgoUBEwoOXtKEGcBEQyFXcr1eU80xN60JGF4MqJo+FBwcBHHyAzc0lHOLbF7ROeZSeh7nFyqSOEH6qaKX2kLlTbmlQX55Wi/lAceo6A3yK9dInIslSAULB1sm8d64WH78qmLbVgocc957mUNzTKb0MgenLPo0pCz6NKQs+jSk6PV0cWP668BVwF+Ba+VNvnIE56NMUbx3cVNWYDtPIzkeMWr4U2FCqbzJN/UL7ohLxR/mnPTNOxaseO/+YMQUOC/95+/PXDvldkwBxUnptjOIlKH8KNZhwcl/kbcBl061Sf+36IyuD1x4da3fytHnC3oCudxTFe/rWB37QdMxN05uylO6uDGtgLQPPIFASnmTf2p8DXFp5Msnvfy5mcloc0Uuzfl7NuOzTRKah/df8Bb+0bqM9p/MOOa6YYvgyMncQWeMKEKIz6aTBz7gyDDvHVs/cf4bVg189ayLm39+/Nmc2/48Pjs/Jj5o5TitYwcpQ6XxvW0nHG1bppopFV3cmO5DCBVPYZ65Rx0JKTKECqjCL27KvOFo2XHKlU9921LVr2ypbsKwHc7ct4OgmR1zzOu2Pc2AL0SPv+Jp8fHYMRVPdspEFzemNaAKY5TQisiPdRuNpuZHt9ryNvG59AlHw5ZBX/BDs+MDRA0fgx4fujOxD2Zom62ogLxFfDx23NGwpRhMXUoXNDE09dhyIFeIIQMjL4Fe+MxPUxJInhafTUeOtCmenGD1P35P9PvXcUbndm5ZegYZVYXh4ZPw0xNfCkED9OFHtOFI21EspjJ73zkcI8a087NSLCcfQEhI8CigFcyRcvTzf/JIGiE+Hqv4450/ZkXfdjTp8GjjXD7/0B/w2vnmAUso3Hz8S/n02a/Pz4zVlOEIGKH37TsmqnBHvcombsooOE4UIVQcOflUb8HYst0ePV2QeeLzGb/8rDc1fPhqK4Qla4EPAr+Tn9APOaByiOsf+2e0IdPDvbOWIiVcs+EB3rTl8eH9mnQ4o3MHjiPyM2VNB104KJZFwh88yfeR3gXpb9W4OhTZ0a+nO3IjmhIiJw9SSwNyTj7fcciLPvR/XYCqJMUXMp9DV94MYj7AcCB3yQfEV80c4JWf0A9a//z+8pv/JxycLU645rNsr8pPcKlJxbjp4T9Rn4oNH3di9x6EdJAin/OYUuElnTt5sGUB3lRiLdSEJv0Bl3DU6+niprQcM717fNwYyAtrMvJSKOQdPJu8UycB5ORTmobOsaUEekF8QX5C//7w7i9kIjjy4u/dfWfdU3Wzv/G7uYvIjZsY+cGn7uU7/xkZCv944xxOf8uNE35KlQ6KbbE4mqx99v/N7D303ZcmR1V08bn0PqBpzMah8hryWbqu5Mv28WZ41ZEyfojR05RhrEcy9GKN7LexbQUhRF10gCs2b+f7i46bGMgAWNC/n80//xQKkr2hKi6+5IM8Uz9umrQAXy6LxzIxcpbs+skM13ZWHTXRxWfTz6GIpRzs+rrIT/G3Jon0oCv51D4kkiNHhBXkBZ8QiWLUMVKyvDvKxVs3cfruddw+90R+tfjA8f1nDPbRkBxkbcOsQjVtInOjXXzmkT/xu0WnEVO9L3v4tlP+feCbK12OiOjipowGnIKUb0JyLRIddZRYk57E2Kge4xOgruQbb4ZyhvHvhYDh3xjNKOF9Zo6+z78Dn23yu4Wn8K+ZS/jlspdgKWMTqcdyqMrm6PZ5sJWDJGAB/lyWLT+7nu+deD4PNc5jY3VjYna890vrG+d+W37jbQ5gIdeU9Bi0/0p0cVPmGuBnSDkS22sogO9wKpR5ccZnzUJMTL2CvNiaGPHmhcg7duPNlDJ/3PjULsfWENq+eh0zBnsZ6ss74a038Wz9zOEoFiu7ennF3g4MRxLXNG6dN5OdFcERm8bYnP+48w/f5zeLT2fNwlOGjzGsHOe0beE3f/9fhJTx7664cN4XHrqq+/Cf5tTxosslcVN2NvBzYKzgjBJcyryISkFRMepv+HhG/m8UUrc6KlufTHAohBCj8DId+MV9zdUfo98XQAA7IzM5b18PiuOAEFRns7ymbR9GITcKWRbzo7GRFxAm5kDAzFgvbeGaEbsF5HSD+2Yv4X8ueCs1mWTojZsfK9nGnP/GGTlAvPRxT2lInMkYv10fZ85BV94o5BJDAaDsgvhDOYcCqLB+xmwuedvH2FrTypolr2FbZT0zEimaEklmxxITNH2ivppJKRx4+ZbH2R6pZ23j7Akvr6lq/GneiQAsHNhfJz4eK8keuv+mnr73kEcMZfUHQxXDgf8OG0HeARz/WzBSvx/V2PPAnCVcd8k7GcwFGfAavHbnXp6pibCtsmLMJUwhiBqTxKQVMHOwh1M6dxHXPbzp4msPaFpDMgpAVtWk/HpFSZbt/43ovwK+CRy4oWLIWRtqcx+TncsRwSH/acmR9vehYyZ7bAd6lEOBoybJvx6bOQd6M3xq7UYMx+GRxho6/H5+tHgenX4vXtth5f7uAzYgtYVraQvXjrNjKK5d/iOYzfDN+25DArcuOu337zyAmcXmRWfv8kZPFkQz8FBenNFNp6MehiNHNavKgvN2ADFNORIF8kBNtgc1atznKJKazqJoDMPJ5//L+qIAtIUC5FSVmKFz94xmztu9aeLJk/2OhNpkDGQ+qOGsgf3yD3d+27lg13PpXy5ZedM7N3zkihdo/ZRx5IMH3pRZii2fRME3aZk8ugo32qkb3u9AQBt2kCatrsHEnGM8hdhyAIrj0NzTg3/Q4Z2bdgwf8plTlpPRtZHULWFZ9x5U6RQaZ4ZyoXHXtm0u3raOM/bv+uUnX3rFdfJroRQu4oi3KskbvRvl531+BCdOWk6PfoAHzCEKVbahiJCTXUMVB7ZeYcS5sx28qTR33PodcjLN7lGrSoRMc+z1BWyoa+WZhlljX8Yhs2Sh5VBROLlr90WfePydb3eb4HC0m2E/n74AxL3DT21MipUjDpyE4aevAEF97IWGumE1ZWwKH2/6yL6ngG8FMrnKtKr8wFGEaNzXw6fvvZ+PXvwKju+LUZvOcG9rI6Y2eevbaDOHL+/Y6I6DYuW2p7/XOP+wHkIJcvQ7XD6btjAUNd9NeTgnUGiNU0YacLJ2PpijX1OHY8FOrL9/T35C/+CBLnvdFVv0nsSezB8WrlCW9Heyvq6VA2bfk1H4rarEIH03t5ZkVexwOfqdBqpYjumAV8u3oB0KST60d8LKx3a3HED2owgduBSbHJYcHUVEAh8+mOAAP7xtkXn7335cdcGuDZzXtpkxSo/OOQ6UBgTg2PQHw3849E2UNlMyBFp8Lt0MtOdDgB3OCYXP/Ctpyc/69AMf/ML45oofr/n94lMvebxp1Npwh5vSzaz8/+2de5QkVX3HP/dWVb/mtTuzC7sLu8vD5Y28BKNojEkMisZzHECIBsxDDIli5DgEFd1dUNcoo8KR6BHxaCSew/KYHGPAgHhiFNDo8owoqCywLPuenZ2dnulHVd1f/qiu6Z7XTs9ud9/Znv6eM9Onq29Vf7u+de/93df3kkiulM93vlIrPjbQkOFBWZ9+BSPXViV4jLiNb1hVSy4ffeyq3j3pjgN2s04PAS+5+nAXHBo6MVJ9tap0mqhOj9ryv5Ib0ttrTeXVO7eQKRYOnKhinp7nh6SKxUH5QufsvZCHARomutyQrq5po1Vs4F+UG9On14NLm58P/veOG/iHx3/I8pGhGdN98OEn+dDDT3LG1m2juZuXLqkHFxto7OwPxVeirKOmDq5En0fNsmg/luPqRaPvsQe7vnbGm+WaTQ/y5L+u5WM/+z5OOLH773UvbOdNm7eRCMbML+86tb1eXGzAxlq2X5B0zo12RzblqVK61MiO+upvlfXpq+vN5bIL73/sst9uOvvEvTt4+Kg13HbGmxlKdvHqbXu54NmXeOiEo7nnjJP0fB04OVhYWqqcF7xJnSLFMO6jH5Ib093TnVcfMr36/pUnb7jh9e+67herow0AtTEYIJPPBaNfWV6zlsN8gZ3JfaH8UZTDS92wvikPyihe21AuMmAu3PLZj21v77nihB27SRWLeGFIWz5v2k3QNPV4Jaxaiqm1uRGgXF8q1skNaWte7Oq6/WrV8J7XOMbs2Xzbq16wxaPesO4jF61Xx7HiPrFAYV30FhqPw3bCfgsHj5boCxAt0RcgrPvIxVDrx1IYeSNGftI9mj9tr+M9Lv1drYCjDpgXgZy6fv+oSiczSkEiMLSNFUn7IU4+//yLX60cA22hFrBevKt1ozerdCIjrsY4mnzSZW9XmpwIw5nM8W+44vmm8XqZL7AuOsKHxdHlWSsCohSFjiR5Izzd01m1y0QL1cFqna5uzGdKWxxP+gCy7UnwXIoj+aYa4ZoPsJrTk2OFH6AcpkxficOMpIPjatKfGDmh0dyaGVZF9/L+KbOl8dsSdBT9dzWCz0KBVdE7g2JA0Wc8a2vK696gtM5dgyZrh2Fzwqro+VRqzJHS5AmXaNWKo6IlSQAGlDEs2zl44x2r755lVUIL1cKq6MMaFvs+hKbChMBE9mKBgdDwhlcGeW7l8u7vnHdKcMfquw/rRQbzBVZFT4Ry3Ek795atwgIDhdKEiiCaOrWls41XDY/x0MnH8u9nvOrfbPJtFlgTXV2ffc3y0TwPL1tajtb90mK3Yggjedif4yXt4Ep0PJvw3m2HLYi6XIn6y5NEXR41c9VFPaiLkrb4HAqsdMNe8tcvqYe6O8x+x8FoDRkvqstDgSCEkYlz0p2EQ5hJcOSeYdnxlWUNnsH7Hs8Q5tV4cRS7LAAUKfUobVAycH1DeR0CrIi++sM7v7Mlk74cp2RCVDlJcqwYGfFWQis4op1UMSC5dzS/78tL0o3gKeo9xwvB7xUa8FGlIin6nwYUQoAiQCY0O8IHtNzz1kZwPBg0fgr0dXsfOHso+2eP9yyGlAdhGHm8xyiJrkQ4MjR0GGHMUew4ooPQcThqeIxcGN43eFP3O2rNTdR7z4XitwVzskIr0ChcwEfG12S5lBfAj58JmNJKaQU4CKEonL8AuVfJ3fNqB6r6L1X+dP77KN6BUlAIOG3HXt750jY+d+ZJSDpR8p6pKLFDA/vzrCkGLDXlBe1Zz+HpZV0oEY4ZyvJCW3oUx1kta5OD1ZO5aLGg/gOc86PiI+7wVwgKFTkZoMbzdGw65FNyJ664mAPj6aHSMiM6N8758QOtIFq+6ZbTSuk7FJEFR3E3qN+AfkDBscCvgW8iG2vaT1FfU4INhSeAM2O3ALV7lHdv2cayQoFbTz+BUFc4TmiAaAFEQgnn7Jlq9/XMER0MpxIcNTLGK4szkAsF1GJZmxyenUyvA3q/4GUmDjnEN98vfZ9Goj1FSp8VK9xJBEW8Sjq+RuyEIKhxjzNK51euklSlUkDGz4iuMflX5lET4+tfAK9DNk5nwnJQqFtQpPqDNEbOhMhCpH2kgAjkHc231hxDGJrI7L8QlNvmYz4YQTvTjcLA0n053CBkR0c6elASWgFVmjipvxfIlHNejChHqvFiuTJ3Fia5k5TlV/gozLiUavy86ZhL6Xyp+FzPkHbKGNh5wJ/O+vPmgPpFwiKZstODIhNEdeATR/Sw33PjNJHgOT/a3kMiJ6q8qxlJTBTHAG5gWDE0RhibG0Qvy6sjpFbEOW6Gz0uvsRj+DCml4vhMhjgzCTrTObN+vniWk+aEuoku13qDKMlFb4TRTAKFkK/0Wq+0FjOlB6C0m8Kz3e0MJVxCoobRiI7sSDLFkFSxFBcFAvCtKhndoqa1qqqMyKn4/EC2FAeCw0y3Vaq6/hQftX3Afx0EkRlR3zav65xKGPqIMJp0WYWQjFeHzmAM2OlHgjqFgAIh+7Um6ziEsT2YCEVHl9a+8VFZm3ymKi5y707gisjAJiD68hAoED1WcZ1N6Xi8kU9c50/3wIRM/REy5Z2MB4aVxX/sb1qZvggERUF+D+wG/ge4ANk4e8wyBzS0yaY+kU0syRXyY56jxoSJe7WU8K5tuzhmZISlI2O4ImQ9lx+vPppdbRkAAg1bU24x94XFNekNE3XpySBnA38oyJVlE7M43ArH3004r/J3oSuCtHJrXlCiMFsVejVy9/gpoi5tUwRpSA0i3214R4mVzhm9drQoSnnk/AkG/0or3vfiVk7ZObEVNpRM8M1Xn0jRUexLenzwty87t951cs2i2ekg6uLXCOanCpVSkyQupSinBaLOGoAQQUQjHnLXXD0vGwIrfe/iuctxNGQSkHQjI4KMi3QmebYjMyX94kKRgqvZ5zmgVFu9BQdQcs8mLQNpcO4TEkR/HlLK1ZWvoENgpRD8E0iPlo16vgoOtkT/VHIwmR17LjIKTEBPJuqdC4Tti6f6C494LiMJF+WHIp9ua6hDo5K73qHkTgXmiyAhMAryDdA+MKyQNUrucZV8d6uWjTcp2bi3kfwOBlbnvS+5Zo/s624jTLmRZ1ypZ+7iZzZz3rbdAARKcecpx/H00sVgTF4+29GQfvdmht0VLiKFRftyycGetglerPecehyPrDqSJdk8L3R3knUcyPvQnlx6gKu1UCXszpxJuNkzhkejSGiSm+T2jjZe7O4k6zpgQsh4V8raVGuuXA1gVXQt7PnZ0q6oyTqps0wbASW0FXwc+Lqsz9xui2ezwaroxaSrc/FIW0h5UEqD0sJpQ6N4SiHwlE2ezQa789798NEJfu9xJ5WB0HMZbkuiTcg5OwY32eLYjLAavb/9vb/rvP/YFcMkppndnHJwRAjzAbIu05oFW0NYzen3fXfN/qQJZUofpxeNs4da4WZz87aT43CF9VWrBdg0vn1HSkPaKc+Z80PO3zP8SbsMmw/zwpRArx8z4uhoWlq8dXZocEZ9E2zoaK1sqTGs53QAsz6j24dHf0IQCrnA6LFieNbWPTe3BK8P5kVOb6GxmBc5vYXGoiX6AkRL9AWIlugLEPPCPFD1B48DZ5XeCnCu9LmPWaTU1LAevav+YDcwk5n+mdLntgZbagyrxbvqD5LMLDjAE43ispBgu04fneVzpfqDOxvCZAHBtujV9LjN283nD1fYsx/pD34wh7SJ2VO1UC1s5vS5ODXMVg20MAfYLt6rxbxoWjYLrIiu+oNbDuKcP6gHl4UIWzn9YLbUfLTmLBYobIl+MHPelOoPWqtbaoCGi676g0NxWtpXKx41g+pdh+odLf1dbptONWjs+vT+4Hbgbw/xMh3S59pZ6aJ61wC/omw7NRuKwEpkYFf9SM0dDRNd9QfHAptrcKlQ+tzGRPOqdyXwYyJ7r1pMwy4AdyADV9bgWgeN+vvIRf3rPwLOr+FlfSAL/B/wd9LnPjs3Ur1dwBeBtxDlWgfooGzq1sh59rHllBA9FAHR79sFbAKGiKq1RcAGYBD4c+B04HHgR8jAnPox6usj1x9cAtxVty8oY5P0uedWlVL19gE31ZdOXREb4sQoAhcjA9+v9gL1Fr1A9fXfoeJ06XN/dcAUqjcFjNHYnNwIDAJHIQOFWVNSX/PAJI0THOADVaQ5heYTHKAHOK7axPXzketzC0R1U6OwsYo0z9WdhR3sA16sNnG92+kfqvP1Y7wgfe4js6aKAp4v1Z9OXTG5Pg6Bq5CBXLUXaET0vgR4DFhVw8tGDr6wE7hG+tx750aq91jgTsrFvUtk8dzoyL0SlU6Fhij2eBbYQdRSSQOfBp4n6us4C/g5cBcyMCdzo0a2088HHq7BpYz0ufVf7qR6HeBvgFupXWxigP8GLkAGrK3GbXSP3A85dEfjZdLn7qwFnzlD9X6EqH1fbbXoA28nakvX3fuuWjR+Z4f+IOTgY4nG5PJqoXo18APgj4mK5Q3ABmSgaJXXLLAxOcFlqrNutXhjLYkcMqLce4FtGnOFlXnvqj+I/aTmApE+93CZ6TOvYesmfuogzrmx5iwWKKytcFH9wZy+WPrcZuxJs4LDpbicV1tcHe6wKfq/zCFtd91YLEBYXcBYbRHfKtprC9vFezUDMlvqzmKBwbbos+7DIn3u6kYQWUiwKrr0ueWdcabHSY3ispAwH5YLJYARIN68xQAXSJ/7kD1KzQ3rThQtNB626/QWLKAl+gJES/QFiJboCxDzIXpHrRtbTjb/a0+pTpCfF/u7a7kapoVJsB69Ox8fWW+QdbQlIqP/QoC3v4BO6vb8Py9q2Y7UAdaLdyNmHYtS0Ua7Yz5ojd+VQhfNPtvcmhX2i3dHw2CuvAOjb8DVFB1tn1uTwmpOz1yzdxv5YMKWmwAEhjDpoj+ZXWaHWXPDqui+mOVTBDcGQgNGkFxxqx1mzQ1roic/smtzQlQkchxMBmFUt4cGcj6gHHXt0FpbHJsV1kTvKYTH5lV5ZyZMlLsnwAhdQbjeBr9mhrVgKeu55cnvRqYKXkJO69asmRrDWk4XFO3GsMYPOLJYLG/ANwkKuOiKzV5j2TU3rInuIhwXGgqOw87OtqhjJumOR/JdRR8WpUiHwk8XdQza4tmMsCJ65tqh2zJao4CX015UtAvRX8IB16FHAkh7dIWGhKbDBs9mRcO7YTPXDrf15ItZ42kCFLu8acIKBSklOL4h7YeszBf5bcrbmb1l6fxtt6ve34A6EUSBFpAdyL0rbNOaDg0X/ZgP7gx3dmV03nOjHF6cuo5BG8OiQsCKIKTdGIpKkTLCz5a2/6H5TMdPG0p4MtTFPcA28BKlDd8FjIoKzdgZbMr6zNgm7BvIwD82lO80aKjoiz+0687RdOJSP+mVo3VjphpqBIbTc3m6KiJ6AzyfdtmpnaG37cuedP83V9XehVFdnAAnAeZ1YNpBnQfmaiBTErXUkoh5xa+aiS5fhomLcuKHQVWcQwhqM0g3KAf0euTuObtjHwzqIvqj6rZVL3V33P30SUed56VchjJpHjx6GVsSHvl0MgraADIueBryAe5YQJsf0OWHjGrNyYUiRaXYnPIYcRzaw6gTZ7A9CUGI0hpJuQGesxlPr5M+d+JeL6r3KuCq0ruvIQNfR13mgnwG5BoIE2UBFJFoimhybhxgxJ/BRIHdiuMh07uWxFP6K49Pt0K78oGpXMxrZkgPQA74T1B/AiyOqhQgetIeITIhXENkX7IaOJ7IzuRW4Iaai/6ouk0VHf3c0yeuWJNb2sFYwuOe41axNZmke3SUF5cujjJM2o3+gLaRAscMjk64PanQ8HRbiqJTjjWVCBJKFOwZie5PmwcJFxz1lvEZtJEx73cmUbscEqcB10WCVP7uWMT4Rh/onnhMjH9nSjudaJUPUyUmlxQxwmmuMfm8OS/1X1eP6P2cRGjW7Fi2CIB9mQwvJ5MoYNjzyl2uidJXG6F7pDAln+z2nAmCA4hSkTa+KZeUUnoPlQ7MV0zD64ry8ck3vvLbZ8sE092yaoWcqZ9pJuEmf9fk8w/K2+F99RA9CxCWCDoSERNgKJUEP4yEj+9TaKa9ZeFMHXHj1WnFWar8vZUcpuFVhXv0bB2Ak2905dM327UOtVStSamcrbnor5cPPFt09AOnPbcVA3RnRzkmlwego+iXI/ZsMRJOKfYmvSk/Z3/Cm9o1GwutS332TunV0zngaxUpb2ZiJBWUjpU8YSf/7Mp6eTbRAyYGcoZy5D4dKtPOJNrkuKGS1+Q00503J9xUr0Au9cqi9uufOn7ZJ7Yd0aVzqQSPrDiCXdoh0IowDoLbPehIQbZIxjf0FHyUCEMJl5GEG5UKgYligLh0UJHmxtOGdOIVEvrHOPrz0uc+M4GE6j0PeD/RWbcjA7+Mjl92IciXIFwDpqR+LLZLWUhTcbxSsPg1tp2j4vh0ObuaetmpSBs/kNPGFkL01D0HfBl4D/BaIo85AbYD3ypd6GTgKSLb8jcRuUp/Bhl4sKFNtqOu3n3mkKefUGmPsbEAVnREzdyd2WgGjeeURtwot5BEcIxw4liOFxMejvAmjXpk3y09jfFhU5ecDeZ7IMsjkqjyg1DZNo/NmSdH63FroBI6BPk2cu/7URe3RQndPHJnQ8RoeOfMmVe+nHtqSVdK53xMVwrGgihHV8LRoBQdQcAJuTzdxYBNPe3s87yzzOc6n2wo4clQlyoI7gPeCuIDT4I+t9yGryyqDSAG+Ctwvgcqi9xl3U+u4UOr79y6M7O5LWWSgbAnW4xy+nTQipGExxNHtrFq+zBDrjsgtgUHkI0CXDjluLrkdAh/CSoBBKBPRe79XcP5VQErU6B7Pr7/nNxYYVPOcacfR3d09CcCXUmO3pPl5ZuXtMbVawQro2yDn+t8LO8dYIg8DtwArxhAGNqxBW1S2JtEEQdqk9vjWo1Xi1qErmyerat75uVo1eEKe3PLUy6MlqY/O2q8zU7SQfmGdGhY5gf4YpA+13rw00ywJ3o+LLnEqqhedzV0JtGe5sRt+3ECw25X44Sq5ZpQY9gTveBvTCecS3Oug9PpkQyERDEgvbfANhH8hAOikJQzv0yAmwBWFzD2XLNHhj2PE8cKJCpovOxq9qdcTCEsBDf3pKwRbFJYXeHSVvBv7/bDCYIDHBkYVCicniu05sbVAdaXKq++erd0T2qrG+DXXWnxN3RYX1XbjLB+U3d5zpSe6SFHo0LzgBVCCwDWRXeSzsoXEy55pfCBva5me5sXFD/f9Tbb3JoV1ov3GOqG0beg1VUofbV8MrXNNp9mxv8DmZNePq8PCgMAAAAASUVORK5CYII=" id="imageca514dd868" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature57_fold0 -->
    <g transform="translate(169.655906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-37" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.548906 638.149 
L 539.178406 638.149 
L 539.178406 27.789 
L 531.548906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image0d22100fd2" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.678406 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.678406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.079344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pe5d4dec834">
   <rect x="431.418906" y="27.789" width="94.24" height="610.36"/>
  </clipPath>
 </defs>
</svg>
