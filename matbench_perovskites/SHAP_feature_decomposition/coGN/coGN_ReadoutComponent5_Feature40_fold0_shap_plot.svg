<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="774.453344pt" height="679.5765pt" viewBox="0 0 774.453344 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T08:36:54.149966</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 774.453344 679.5765 
L 774.453344 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 398.362344 638.149 
L 518.378344 638.149 
L 518.378344 27.789 
L 398.362344 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 448.727946 638.149 
L 448.727946 27.789 
" clip-path="url(#pfd35664831)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 398.362344 609.084238 
L 518.378344 609.084238 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 398.362344 580.019476 
L 518.378344 580.019476 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 398.362344 550.954714 
L 518.378344 550.954714 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 398.362344 521.889952 
L 518.378344 521.889952 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 398.362344 492.82519 
L 518.378344 492.82519 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 398.362344 463.760429 
L 518.378344 463.760429 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 398.362344 434.695667 
L 518.378344 434.695667 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 398.362344 405.630905 
L 518.378344 405.630905 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 398.362344 376.566143 
L 518.378344 376.566143 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 398.362344 347.501381 
L 518.378344 347.501381 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 398.362344 318.436619 
L 518.378344 318.436619 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 398.362344 289.371857 
L 518.378344 289.371857 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 398.362344 260.307095 
L 518.378344 260.307095 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 398.362344 231.242333 
L 518.378344 231.242333 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 398.362344 202.177571 
L 518.378344 202.177571 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 398.362344 173.11281 
L 518.378344 173.11281 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 398.362344 144.048048 
L 518.378344 144.048048 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 398.362344 114.983286 
L 518.378344 114.983286 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 398.362344 85.918524 
L 518.378344 85.918524 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 398.362344 56.853762 
L 518.378344 56.853762 
" clip-path="url(#pfd35664831)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m8875694c07" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m8875694c07" x="448.727946" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(445.228571 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m8875694c07" x="515.161104" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(511.661729 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(335.688937 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- AverageBondAngle_std_dev_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(52.395312 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-73" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-74" x="1053.939453"/>
       <use xlink:href="#DejaVuSans-64" x="1093.148438"/>
       <use xlink:href="#DejaVuSans-5f" x="1156.625"/>
       <use xlink:href="#DejaVuSans-64" x="1206.625"/>
       <use xlink:href="#DejaVuSans-65" x="1270.101562"/>
       <use xlink:href="#DejaVuSans-76" x="1331.625"/>
       <use xlink:href="#DejaVuSans-5f" x="1390.804688"/>
       <use xlink:href="#DejaVuSans-41" x="1440.804688"/>
       <use xlink:href="#DejaVuSans-76" x="1503.337891"/>
       <use xlink:href="#DejaVuSans-65" x="1562.517578"/>
       <use xlink:href="#DejaVuSans-72" x="1624.041016"/>
       <use xlink:href="#DejaVuSans-61" x="1665.154297"/>
       <use xlink:href="#DejaVuSans-67" x="1726.433594"/>
       <use xlink:href="#DejaVuSans-65" x="1789.910156"/>
       <use xlink:href="#DejaVuSans-5f" x="1851.433594"/>
       <use xlink:href="#DejaVuSans-62" x="1901.433594"/>
       <use xlink:href="#DejaVuSans-6f" x="1964.910156"/>
       <use xlink:href="#DejaVuSans-6e" x="2026.091797"/>
       <use xlink:href="#DejaVuSans-64" x="2089.470703"/>
       <use xlink:href="#DejaVuSans-5f" x="2152.947266"/>
       <use xlink:href="#DejaVuSans-61" x="2202.947266"/>
       <use xlink:href="#DejaVuSans-6e" x="2264.226562"/>
       <use xlink:href="#DejaVuSans-67" x="2327.605469"/>
       <use xlink:href="#DejaVuSans-6c" x="2391.082031"/>
       <use xlink:href="#DejaVuSans-65" x="2418.865234"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_mean_NpUnfilled -->
      <g style="fill: #333333" transform="translate(63.934844 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-55" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2084.769531"/>
       <use xlink:href="#DejaVuSans-66" x="2148.148438"/>
       <use xlink:href="#DejaVuSans-69" x="2183.353516"/>
       <use xlink:href="#DejaVuSans-6c" x="2211.136719"/>
       <use xlink:href="#DejaVuSans-6c" x="2238.919922"/>
       <use xlink:href="#DejaVuSans-65" x="2266.703125"/>
       <use xlink:href="#DejaVuSans-64" x="2328.226562"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(7.2 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- BondFractions_O_-_S_bond_frac_ -->
      <g style="fill: #333333" transform="translate(162.919687 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-46" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="306.910156"/>
       <use xlink:href="#DejaVuSans-61" x="348.023438"/>
       <use xlink:href="#DejaVuSans-63" x="409.302734"/>
       <use xlink:href="#DejaVuSans-74" x="464.283203"/>
       <use xlink:href="#DejaVuSans-69" x="503.492188"/>
       <use xlink:href="#DejaVuSans-6f" x="531.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="592.457031"/>
       <use xlink:href="#DejaVuSans-73" x="655.835938"/>
       <use xlink:href="#DejaVuSans-5f" x="707.935547"/>
       <use xlink:href="#DejaVuSans-4f" x="757.935547"/>
       <use xlink:href="#DejaVuSans-5f" x="836.646484"/>
       <use xlink:href="#DejaVuSans-2d" x="886.646484"/>
       <use xlink:href="#DejaVuSans-5f" x="922.730469"/>
       <use xlink:href="#DejaVuSans-53" x="972.730469"/>
       <use xlink:href="#DejaVuSans-5f" x="1036.207031"/>
       <use xlink:href="#DejaVuSans-62" x="1086.207031"/>
       <use xlink:href="#DejaVuSans-6f" x="1149.683594"/>
       <use xlink:href="#DejaVuSans-6e" x="1210.865234"/>
       <use xlink:href="#DejaVuSans-64" x="1274.244141"/>
       <use xlink:href="#DejaVuSans-5f" x="1337.720703"/>
       <use xlink:href="#DejaVuSans-66" x="1387.720703"/>
       <use xlink:href="#DejaVuSans-72" x="1422.925781"/>
       <use xlink:href="#DejaVuSans-61" x="1464.039062"/>
       <use xlink:href="#DejaVuSans-63" x="1525.318359"/>
       <use xlink:href="#DejaVuSans-5f" x="1580.298828"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_minimum_NsValence -->
      <g style="fill: #333333" transform="translate(39.161719 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-73" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-56" x="2191.166016"/>
       <use xlink:href="#DejaVuSans-61" x="2251.824219"/>
       <use xlink:href="#DejaVuSans-6c" x="2313.103516"/>
       <use xlink:href="#DejaVuSans-65" x="2340.886719"/>
       <use xlink:href="#DejaVuSans-6e" x="2402.410156"/>
       <use xlink:href="#DejaVuSans-63" x="2465.789062"/>
       <use xlink:href="#DejaVuSans-65" x="2520.769531"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_137 -->
      <g style="fill: #333333" transform="translate(177.270469 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_maximum_NUnfilled -->
      <g style="fill: #333333" transform="translate(43.555312 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-55" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.556641"/>
       <use xlink:href="#DejaVuSans-66" x="2304.935547"/>
       <use xlink:href="#DejaVuSans-69" x="2340.140625"/>
       <use xlink:href="#DejaVuSans-6c" x="2367.923828"/>
       <use xlink:href="#DejaVuSans-6c" x="2395.707031"/>
       <use xlink:href="#DejaVuSans-65" x="2423.490234"/>
       <use xlink:href="#DejaVuSans-64" x="2485.013672"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(33.870312 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_73 -->
      <g style="fill: #333333" transform="translate(185.541719 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(177.270469 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(72.187812 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(185.541719 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(123.836406 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(90.454844 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(62.622656 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(35.302344 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(76.492031 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(255.168906 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(133.653437 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(92.069687 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 398.362344 638.149 
L 518.378344 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagec5c57deea3" transform="scale(1 -1) translate(0 -578.16)" x="401.76" y="-43.2" width="113.76" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature40_fold0 -->
    <g transform="translate(149.487344 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 525.879344 638.149 
L 533.508844 638.149 
L 533.508844 27.789 
L 525.879344 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image6d6e6c1785" transform="scale(1 -1) translate(0 -609.84)" x="525.6" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(537.008844 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(537.008844 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(571.409781 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pfd35664831">
   <rect x="398.362344" y="27.789" width="120.016" height="610.36"/>
  </clipPath>
 </defs>
</svg>
