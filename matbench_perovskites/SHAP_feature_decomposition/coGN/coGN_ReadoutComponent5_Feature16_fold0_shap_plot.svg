<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="770.862562pt" height="679.5765pt" viewBox="0 0 770.862562 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T04:40:46.003156</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 770.862562 679.5765 
L 770.862562 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 392.071563 638.149 
L 517.487562 638.149 
L 517.487562 27.789 
L 392.071563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 447.843436 638.149 
L 447.843436 27.789 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 392.071563 609.084238 
L 517.487562 609.084238 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 392.071563 580.019476 
L 517.487562 580.019476 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 392.071563 550.954714 
L 517.487562 550.954714 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 392.071563 521.889952 
L 517.487562 521.889952 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 392.071563 492.82519 
L 517.487562 492.82519 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 392.071563 463.760429 
L 517.487562 463.760429 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 392.071563 434.695667 
L 517.487562 434.695667 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 392.071563 405.630905 
L 517.487562 405.630905 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 392.071563 376.566143 
L 517.487562 376.566143 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 392.071563 347.501381 
L 517.487562 347.501381 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 392.071563 318.436619 
L 517.487562 318.436619 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 392.071563 289.371857 
L 517.487562 289.371857 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 392.071563 260.307095 
L 517.487562 260.307095 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 392.071563 231.242333 
L 517.487562 231.242333 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 392.071563 202.177571 
L 517.487562 202.177571 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 392.071563 173.11281 
L 517.487562 173.11281 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 392.071563 144.048048 
L 517.487562 144.048048 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 392.071563 114.983286 
L 517.487562 114.983286 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 392.071563 85.918524 
L 517.487562 85.918524 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 392.071563 56.853762 
L 517.487562 56.853762 
" clip-path="url(#p2c95ce8224)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m610c0c946b" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m610c0c946b" x="404.679926" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(396.571723 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m610c0c946b" x="447.843436" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(444.344061 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m610c0c946b" x="491.006946" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(487.507571 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(332.098156 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_mean_CovalentRadius -->
      <g style="fill: #333333" transform="translate(23.817656 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-76" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-61" x="2063.480469"/>
       <use xlink:href="#DejaVuSans-6c" x="2124.759766"/>
       <use xlink:href="#DejaVuSans-65" x="2152.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="2214.066406"/>
       <use xlink:href="#DejaVuSans-74" x="2277.445312"/>
       <use xlink:href="#DejaVuSans-52" x="2316.654297"/>
       <use xlink:href="#DejaVuSans-61" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-64" x="2445.166016"/>
       <use xlink:href="#DejaVuSans-69" x="2508.642578"/>
       <use xlink:href="#DejaVuSans-75" x="2536.425781"/>
       <use xlink:href="#DejaVuSans-73" x="2599.804688"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(56.331875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_water-like_CN_2 -->
      <g style="fill: #333333" transform="translate(79.912656 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-61" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-74" x="1569.263672"/>
       <use xlink:href="#DejaVuSans-65" x="1608.472656"/>
       <use xlink:href="#DejaVuSans-72" x="1669.996094"/>
       <use xlink:href="#DejaVuSans-2d" x="1704.734375"/>
       <use xlink:href="#DejaVuSans-6c" x="1740.818359"/>
       <use xlink:href="#DejaVuSans-69" x="1768.601562"/>
       <use xlink:href="#DejaVuSans-6b" x="1796.384766"/>
       <use xlink:href="#DejaVuSans-65" x="1850.669922"/>
       <use xlink:href="#DejaVuSans-5f" x="1912.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1962.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="2032.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="2106.822266"/>
       <use xlink:href="#DejaVuSans-32" x="2156.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(179.250938 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(56.212031 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_maximum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(7.2 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-76" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.705078"/>
       <use xlink:href="#DejaVuSans-6c" x="2354.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2382.669922"/>
       <use xlink:href="#DejaVuSans-6d" x="2446.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2543.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2604.984375"/>
       <use xlink:href="#DejaVuSans-70" x="2654.984375"/>
       <use xlink:href="#DejaVuSans-61" x="2718.460938"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_168 -->
      <g style="fill: #333333" transform="translate(170.979688 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-38" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(170.979688 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_99 -->
      <g style="fill: #333333" transform="translate(179.250938 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_11 -->
      <g style="fill: #333333" transform="translate(179.250938 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(68.1375 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(40.439375 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(170.979688 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(179.250938 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(56.331875 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(117.555781 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(70.20125 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(248.878125 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(127.362656 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(85.778906 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 392.071563 638.149 
L 517.487562 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image1beb1aabc0" transform="scale(1 -1) translate(0 -578.16)" x="395.28" y="-43.2" width="118.8" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature16_fold0 -->
    <g transform="translate(145.896563 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 525.326062 638.149 
L 532.955562 638.149 
L 532.955562 27.789 
L 525.326062 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imageb45cc06db8" transform="scale(1 -1) translate(0 -609.84)" x="525.6" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(536.455562 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(536.455562 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(570.8565 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p2c95ce8224">
   <rect x="392.071563" y="27.789" width="125.416" height="610.36"/>
  </clipPath>
 </defs>
</svg>
