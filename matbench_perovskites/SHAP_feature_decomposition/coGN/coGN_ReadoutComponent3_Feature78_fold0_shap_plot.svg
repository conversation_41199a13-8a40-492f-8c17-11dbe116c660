<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:38:46.265569</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 447.819469 638.149 
L 447.819469 27.789 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#pa5eb4195ff)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m8bd73a02a5" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m8bd73a02a5" x="447.819469" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(439.07275 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m8bd73a02a5" x="493.004445" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(484.257726 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(147.772656 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_range_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(22.907656 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-53" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-70" x="1943.949219"/>
       <use xlink:href="#DejaVuSans-61" x="2007.425781"/>
       <use xlink:href="#DejaVuSans-63" x="2068.705078"/>
       <use xlink:href="#DejaVuSans-65" x="2123.685547"/>
       <use xlink:href="#DejaVuSans-47" x="2185.208984"/>
       <use xlink:href="#DejaVuSans-72" x="2262.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="2301.5625"/>
       <use xlink:href="#DejaVuSans-75" x="2362.744141"/>
       <use xlink:href="#DejaVuSans-70" x="2426.123047"/>
       <use xlink:href="#DejaVuSans-4e" x="2489.599609"/>
       <use xlink:href="#DejaVuSans-75" x="2564.404297"/>
       <use xlink:href="#DejaVuSans-6d" x="2627.783203"/>
       <use xlink:href="#DejaVuSans-62" x="2725.195312"/>
       <use xlink:href="#DejaVuSans-65" x="2788.671875"/>
       <use xlink:href="#DejaVuSans-72" x="2850.195312"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementFraction_Ta -->
      <g style="fill: #333333" transform="translate(272.810313 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-54" x="863.208984"/>
       <use xlink:href="#DejaVuSans-61" x="907.792969"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(143.87875 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_maximum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(37.416875 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-76" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.705078"/>
       <use xlink:href="#DejaVuSans-6c" x="2354.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2382.669922"/>
       <use xlink:href="#DejaVuSans-6d" x="2446.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2543.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2604.984375"/>
       <use xlink:href="#DejaVuSans-70" x="2654.984375"/>
       <use xlink:href="#DejaVuSans-61" x="2718.460938"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mean_NpUnfilled -->
      <g style="fill: #333333" transform="translate(87.860937 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-55" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2084.769531"/>
       <use xlink:href="#DejaVuSans-66" x="2148.148438"/>
       <use xlink:href="#DejaVuSans-69" x="2183.353516"/>
       <use xlink:href="#DejaVuSans-6c" x="2211.136719"/>
       <use xlink:href="#DejaVuSans-6c" x="2238.919922"/>
       <use xlink:href="#DejaVuSans-65" x="2266.703125"/>
       <use xlink:href="#DejaVuSans-64" x="2328.226562"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(98.354375 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(206.997812 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(43.983906 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_71 -->
      <g style="fill: #333333" transform="translate(209.467812 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(157.579531 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(157.579531 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(86.54875 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- CrystalNNFingerprint_mean_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(114.118906 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-4c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1480.160156"/>
       <use xlink:href="#DejaVuSans-73" x="1516.244141"/>
       <use xlink:href="#DejaVuSans-68" x="1568.34375"/>
       <use xlink:href="#DejaVuSans-61" x="1631.722656"/>
       <use xlink:href="#DejaVuSans-70" x="1693.001953"/>
       <use xlink:href="#DejaVuSans-65" x="1756.478516"/>
       <use xlink:href="#DejaVuSans-64" x="1818.001953"/>
       <use xlink:href="#DejaVuSans-5f" x="1881.478516"/>
       <use xlink:href="#DejaVuSans-43" x="1931.478516"/>
       <use xlink:href="#DejaVuSans-4e" x="2001.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="2076.107422"/>
       <use xlink:href="#DejaVuSans-32" x="2126.107422"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(209.467812 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(147.772656 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(115.995781 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- MEGNet_OFMEncoded_v1_48 -->
      <g style="fill: #333333" transform="translate(209.467812 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageed9f75d05b" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature78_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-37" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-38" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image426faf0bcc" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pa5eb4195ff">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
