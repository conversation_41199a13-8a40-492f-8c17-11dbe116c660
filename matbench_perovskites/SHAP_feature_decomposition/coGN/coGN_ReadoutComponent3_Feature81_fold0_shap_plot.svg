<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="782.718125pt" height="679.5765pt" viewBox="0 0 782.718125 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:47:21.964907</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 782.718125 679.5765 
L 782.718125 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 412.063125 638.149 
L 521.207125 638.149 
L 521.207125 27.789 
L 412.063125 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 468.375594 638.149 
L 468.375594 27.789 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 412.063125 609.084238 
L 521.207125 609.084238 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 412.063125 580.019476 
L 521.207125 580.019476 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 412.063125 550.954714 
L 521.207125 550.954714 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 412.063125 521.889952 
L 521.207125 521.889952 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 412.063125 492.82519 
L 521.207125 492.82519 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 412.063125 463.760429 
L 521.207125 463.760429 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 412.063125 434.695667 
L 521.207125 434.695667 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 412.063125 405.630905 
L 521.207125 405.630905 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 412.063125 376.566143 
L 521.207125 376.566143 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 412.063125 347.501381 
L 521.207125 347.501381 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 412.063125 318.436619 
L 521.207125 318.436619 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 412.063125 289.371857 
L 521.207125 289.371857 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 412.063125 260.307095 
L 521.207125 260.307095 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 412.063125 231.242333 
L 521.207125 231.242333 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 412.063125 202.177571 
L 521.207125 202.177571 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 412.063125 173.11281 
L 521.207125 173.11281 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 412.063125 144.048048 
L 521.207125 144.048048 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 412.063125 114.983286 
L 521.207125 114.983286 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 412.063125 85.918524 
L 521.207125 85.918524 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 412.063125 56.853762 
L 521.207125 56.853762 
" clip-path="url(#p28ed7a1ff4)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m6a22b72a87" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m6a22b72a87" x="431.600043" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.25 -->
      <g style="fill: #333333" transform="translate(414.745121 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-32" x="179.199219"/>
       <use xlink:href="#DejaVuSans-35" x="242.822266"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m6a22b72a87" x="468.375594" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(456.129501 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m6a22b72a87" x="505.151146" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(492.905053 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(343.953719 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(88.129062 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_range_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(20.862188 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-64" x="2091.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2155.130859"/>
       <use xlink:href="#DejaVuSans-6c" x="2216.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2244.4375"/>
       <use xlink:href="#DejaVuSans-65" x="2305.960938"/>
       <use xlink:href="#DejaVuSans-76" x="2367.484375"/>
       <use xlink:href="#DejaVuSans-4e" x="2426.664062"/>
       <use xlink:href="#DejaVuSans-75" x="2501.46875"/>
       <use xlink:href="#DejaVuSans-6d" x="2564.847656"/>
       <use xlink:href="#DejaVuSans-62" x="2662.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2725.736328"/>
       <use xlink:href="#DejaVuSans-72" x="2787.259766"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_154 -->
      <g style="fill: #333333" transform="translate(190.97125 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-34" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(70.424688 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(184.863281 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(137.547344 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(49.003125 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- OPSiteFingerprint_std_dev_q2_CN_12 -->
      <g style="fill: #333333" transform="translate(146.568125 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-71" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-32" x="1425.953125"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-43" x="1539.576172"/>
       <use xlink:href="#DejaVuSans-4e" x="1609.400391"/>
       <use xlink:href="#DejaVuSans-5f" x="1684.205078"/>
       <use xlink:href="#DejaVuSans-31" x="1734.205078"/>
       <use xlink:href="#DejaVuSans-32" x="1797.828125"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(126.310469 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(33.758594 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(76.323438 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(137.547344 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(60.430938 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(52.027656 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- CrystalNNFingerprint_std_dev_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(7.2 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-72" x="1570.777344"/>
       <use xlink:href="#DejaVuSans-69" x="1611.890625"/>
       <use xlink:href="#DejaVuSans-67" x="1639.673828"/>
       <use xlink:href="#DejaVuSans-6f" x="1703.150391"/>
       <use xlink:href="#DejaVuSans-6e" x="1764.332031"/>
       <use xlink:href="#DejaVuSans-61" x="1827.710938"/>
       <use xlink:href="#DejaVuSans-6c" x="1888.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="1916.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="1966.773438"/>
       <use xlink:href="#DejaVuSans-6f" x="2030.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="2091.333984"/>
       <use xlink:href="#DejaVuSans-2d" x="2154.712891"/>
       <use xlink:href="#DejaVuSans-63" x="2190.796875"/>
       <use xlink:href="#DejaVuSans-6f" x="2245.777344"/>
       <use xlink:href="#DejaVuSans-70" x="2306.958984"/>
       <use xlink:href="#DejaVuSans-6c" x="2370.435547"/>
       <use xlink:href="#DejaVuSans-61" x="2398.21875"/>
       <use xlink:href="#DejaVuSans-6e" x="2459.498047"/>
       <use xlink:href="#DejaVuSans-61" x="2522.876953"/>
       <use xlink:href="#DejaVuSans-72" x="2584.15625"/>
       <use xlink:href="#DejaVuSans-5f" x="2625.269531"/>
       <use xlink:href="#DejaVuSans-43" x="2675.269531"/>
       <use xlink:href="#DejaVuSans-4e" x="2745.09375"/>
       <use xlink:href="#DejaVuSans-5f" x="2819.898438"/>
       <use xlink:href="#DejaVuSans-33" x="2869.898438"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_100 -->
      <g style="fill: #333333" transform="translate(190.97125 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(137.547344 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(147.354219 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(61.066719 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(105.770469 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 412.063125 638.149 
L 521.207125 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image570c5940d6" transform="scale(1 -1) translate(0 -578.16)" x="414.72" y="-43.2" width="103.68" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature81_fold0 -->
    <g transform="translate(157.752125 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-38" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 528.028625 638.149 
L 535.658125 638.149 
L 535.658125 27.789 
L 528.028625 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image9fc663c9a6" transform="scale(1 -1) translate(0 -609.84)" x="527.76" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(539.158125 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(539.158125 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(573.559062 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p28ed7a1ff4">
   <rect x="412.063125" y="27.789" width="109.144" height="610.36"/>
  </clipPath>
 </defs>
</svg>
