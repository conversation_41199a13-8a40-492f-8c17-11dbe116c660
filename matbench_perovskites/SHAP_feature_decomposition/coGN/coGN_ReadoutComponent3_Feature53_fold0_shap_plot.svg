<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.657906pt" height="679.5765pt" viewBox="0 0 794.657906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T16:12:59.763917</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.657906 679.5765 
L 794.657906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
L 525.730906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 461.020635 638.149 
L 461.020635 27.789 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.730906 609.084238 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.730906 580.019476 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.730906 550.954714 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.730906 521.889952 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.730906 492.82519 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.730906 463.760429 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.730906 434.695667 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.730906 405.630905 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.730906 376.566143 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.730906 347.501381 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.730906 318.436619 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.730906 289.371857 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.730906 260.307095 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.730906 231.242333 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.730906 202.177571 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.730906 173.11281 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.730906 144.048048 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.730906 114.983286 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.730906 85.918524 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.730906 56.853762 
" clip-path="url(#pedb3429cd1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m6af87878e1" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m6af87878e1" x="461.020635" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(457.52126 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m6af87878e1" x="509.165683" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(505.666308 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8935 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(131.965469 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_range_CovalentRadius -->
      <g style="fill: #333333" transform="translate(62.230625 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-76" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2070.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2131.9375"/>
       <use xlink:href="#DejaVuSans-65" x="2159.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="2221.244141"/>
       <use xlink:href="#DejaVuSans-74" x="2284.623047"/>
       <use xlink:href="#DejaVuSans-52" x="2323.832031"/>
       <use xlink:href="#DejaVuSans-61" x="2391.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2452.34375"/>
       <use xlink:href="#DejaVuSans-69" x="2515.820312"/>
       <use xlink:href="#DejaVuSans-75" x="2543.603516"/>
       <use xlink:href="#DejaVuSans-73" x="2606.982422"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(15.479375 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(105.244375 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_34 -->
      <g style="fill: #333333" transform="translate(218.598281 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(80.4225 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(95.679219 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(285.976875 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(218.598281 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_avg_dev_NpValence -->
      <g style="fill: #333333" transform="translate(78.06625 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-56" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-61" x="2206.755859"/>
       <use xlink:href="#DejaVuSans-6c" x="2268.035156"/>
       <use xlink:href="#DejaVuSans-65" x="2295.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="2357.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2420.720703"/>
       <use xlink:href="#DejaVuSans-65" x="2475.701172"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- OxidationStates_minimum_oxidation_state -->
      <g style="fill: #333333" transform="translate(129.980938 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-6d" x="848.779297"/>
       <use xlink:href="#DejaVuSans-69" x="946.191406"/>
       <use xlink:href="#DejaVuSans-6e" x="973.974609"/>
       <use xlink:href="#DejaVuSans-69" x="1037.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1065.136719"/>
       <use xlink:href="#DejaVuSans-75" x="1162.548828"/>
       <use xlink:href="#DejaVuSans-6d" x="1225.927734"/>
       <use xlink:href="#DejaVuSans-5f" x="1323.339844"/>
       <use xlink:href="#DejaVuSans-6f" x="1373.339844"/>
       <use xlink:href="#DejaVuSans-78" x="1431.396484"/>
       <use xlink:href="#DejaVuSans-69" x="1490.576172"/>
       <use xlink:href="#DejaVuSans-64" x="1518.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
       <use xlink:href="#DejaVuSans-69" x="1682.324219"/>
       <use xlink:href="#DejaVuSans-6f" x="1710.107422"/>
       <use xlink:href="#DejaVuSans-6e" x="1771.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1834.667969"/>
       <use xlink:href="#DejaVuSans-73" x="1884.667969"/>
       <use xlink:href="#DejaVuSans-74" x="1936.767578"/>
       <use xlink:href="#DejaVuSans-61" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-74" x="2037.255859"/>
       <use xlink:href="#DejaVuSans-65" x="2076.464844"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(125.12625 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(16.330469 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElectronegativityDiff_maximum_EN_difference -->
      <g style="fill: #333333" transform="translate(106.414375 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-78" x="1232.613281"/>
       <use xlink:href="#DejaVuSans-69" x="1291.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="1319.576172"/>
       <use xlink:href="#DejaVuSans-75" x="1416.988281"/>
       <use xlink:href="#DejaVuSans-6d" x="1480.367188"/>
       <use xlink:href="#DejaVuSans-5f" x="1577.779297"/>
       <use xlink:href="#DejaVuSans-45" x="1627.779297"/>
       <use xlink:href="#DejaVuSans-4e" x="1690.962891"/>
       <use xlink:href="#DejaVuSans-5f" x="1765.767578"/>
       <use xlink:href="#DejaVuSans-64" x="1815.767578"/>
       <use xlink:href="#DejaVuSans-69" x="1879.244141"/>
       <use xlink:href="#DejaVuSans-66" x="1907.027344"/>
       <use xlink:href="#DejaVuSans-66" x="1942.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1977.4375"/>
       <use xlink:href="#DejaVuSans-72" x="2038.960938"/>
       <use xlink:href="#DejaVuSans-65" x="2077.824219"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.347656"/>
       <use xlink:href="#DejaVuSans-63" x="2202.726562"/>
       <use xlink:href="#DejaVuSans-65" x="2257.707031"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(216.128281 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_range_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(40.217969 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6e" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-64" x="2091.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2155.130859"/>
       <use xlink:href="#DejaVuSans-6c" x="2216.654297"/>
       <use xlink:href="#DejaVuSans-65" x="2244.4375"/>
       <use xlink:href="#DejaVuSans-65" x="2305.960938"/>
       <use xlink:href="#DejaVuSans-76" x="2367.484375"/>
       <use xlink:href="#DejaVuSans-4e" x="2426.664062"/>
       <use xlink:href="#DejaVuSans-75" x="2501.46875"/>
       <use xlink:href="#DejaVuSans-6d" x="2564.847656"/>
       <use xlink:href="#DejaVuSans-62" x="2662.259766"/>
       <use xlink:href="#DejaVuSans-65" x="2725.736328"/>
       <use xlink:href="#DejaVuSans-72" x="2787.259766"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_range_Column -->
      <g style="fill: #333333" transform="translate(113.667969 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-6c" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-75" x="2039.261719"/>
       <use xlink:href="#DejaVuSans-6d" x="2102.640625"/>
       <use xlink:href="#DejaVuSans-6e" x="2200.052734"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(288.225469 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image76c8866013" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature53_fold0 -->
    <g transform="translate(169.691906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-33" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.625406 638.149 
L 539.254906 638.149 
L 539.254906 27.789 
L 531.625406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image1ec829265f" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.754906 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.754906 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.155844 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pedb3429cd1">
   <rect x="431.418906" y="27.789" width="94.312" height="610.36"/>
  </clipPath>
 </defs>
</svg>
