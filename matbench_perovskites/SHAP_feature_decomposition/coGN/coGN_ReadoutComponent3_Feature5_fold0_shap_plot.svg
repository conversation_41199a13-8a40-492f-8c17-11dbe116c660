<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="782.875437pt" height="679.5765pt" viewBox="0 0 782.875437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T16:26:58.184887</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 782.875437 679.5765 
L 782.875437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 485.34009 638.149 
L 485.34009 27.789 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#p3d20afd571)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mae8773433a" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mae8773433a" x="427.439881" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(414.084334 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mae8773433a" x="485.34009" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(476.593372 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_34 -->
      <g style="fill: #333333" transform="translate(209.467812 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=1_23e+00 -->
      <g style="fill: #333333" transform="translate(95.535 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-31" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-32" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-33" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_avg_dev_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(48.55625 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-76" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-6f" x="2207.962891"/>
       <use xlink:href="#DejaVuSans-6c" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-75" x="2296.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2457.71875"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.242188"/>
       <use xlink:href="#DejaVuSans-70" x="2569.242188"/>
       <use xlink:href="#DejaVuSans-61" x="2632.71875"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(147.772656 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- VoronoiFingerprint_mean_Voro_dist_std_dev -->
      <g style="fill: #333333" transform="translate(113.598906 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-73" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-74" x="1856.880859"/>
       <use xlink:href="#DejaVuSans-64" x="1896.089844"/>
       <use xlink:href="#DejaVuSans-5f" x="1959.566406"/>
       <use xlink:href="#DejaVuSans-64" x="2009.566406"/>
       <use xlink:href="#DejaVuSans-65" x="2073.042969"/>
       <use xlink:href="#DejaVuSans-76" x="2134.566406"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(31.126094 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_70 -->
      <g style="fill: #333333" transform="translate(209.467812 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_avg_dev_Electronegativity -->
      <g style="fill: #333333" transform="translate(27.425156 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-45" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6c" x="2071"/>
       <use xlink:href="#DejaVuSans-65" x="2098.783203"/>
       <use xlink:href="#DejaVuSans-63" x="2160.306641"/>
       <use xlink:href="#DejaVuSans-74" x="2215.287109"/>
       <use xlink:href="#DejaVuSans-72" x="2254.496094"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.359375"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.541016"/>
       <use xlink:href="#DejaVuSans-65" x="2417.919922"/>
       <use xlink:href="#DejaVuSans-67" x="2479.443359"/>
       <use xlink:href="#DejaVuSans-61" x="2542.919922"/>
       <use xlink:href="#DejaVuSans-74" x="2604.199219"/>
       <use xlink:href="#DejaVuSans-69" x="2643.408203"/>
       <use xlink:href="#DejaVuSans-76" x="2671.191406"/>
       <use xlink:href="#DejaVuSans-69" x="2730.371094"/>
       <use xlink:href="#DejaVuSans-74" x="2758.154297"/>
       <use xlink:href="#DejaVuSans-79" x="2797.363281"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_maximum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(37.416875 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-76" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.705078"/>
       <use xlink:href="#DejaVuSans-6c" x="2354.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2382.669922"/>
       <use xlink:href="#DejaVuSans-6d" x="2446.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2543.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2604.984375"/>
       <use xlink:href="#DejaVuSans-70" x="2654.984375"/>
       <use xlink:href="#DejaVuSans-61" x="2718.460938"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_120 -->
      <g style="fill: #333333" transform="translate(201.196562 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- GaussianSymmFunc_std_dev_G2_20_0 -->
      <g style="fill: #333333" transform="translate(147.7625 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-32" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(43.983906 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(101.094531 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(209.467812 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(206.997812 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- Stoichiometry_0-norm -->
      <g style="fill: #333333" transform="translate(254.689531 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-30" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(62.252969 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(86.54875 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image9bec4a36a9" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature5_fold0 -->
    <g transform="translate(170.125437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagee6fe980a64" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p3d20afd571">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
