<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="796.185906pt" height="679.5765pt" viewBox="0 0 796.185906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T16:29:05.712057</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 796.185906 679.5765 
L 796.185906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 434.278906 638.149 
L 525.926906 638.149 
L 525.926906 27.789 
L 434.278906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 487.53403 638.149 
L 487.53403 27.789 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 434.278906 609.084238 
L 525.926906 609.084238 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 434.278906 580.019476 
L 525.926906 580.019476 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 434.278906 550.954714 
L 525.926906 550.954714 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 434.278906 521.889952 
L 525.926906 521.889952 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 434.278906 492.82519 
L 525.926906 492.82519 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 434.278906 463.760429 
L 525.926906 463.760429 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 434.278906 434.695667 
L 525.926906 434.695667 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 434.278906 405.630905 
L 525.926906 405.630905 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 434.278906 376.566143 
L 525.926906 376.566143 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 434.278906 347.501381 
L 525.926906 347.501381 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 434.278906 318.436619 
L 525.926906 318.436619 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 434.278906 289.371857 
L 525.926906 289.371857 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 434.278906 260.307095 
L 525.926906 260.307095 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 434.278906 231.242333 
L 525.926906 231.242333 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 434.278906 202.177571 
L 525.926906 202.177571 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 434.278906 173.11281 
L 525.926906 173.11281 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 434.278906 144.048048 
L 525.926906 144.048048 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 434.278906 114.983286 
L 525.926906 114.983286 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 434.278906 85.918524 
L 525.926906 85.918524 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 434.278906 56.853762 
L 525.926906 56.853762 
" clip-path="url(#p46bb59b348)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mf1ac796c00" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mf1ac796c00" x="455.664879" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.25 -->
      <g style="fill: #333333" transform="translate(438.809957 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-32" x="179.199219"/>
       <use xlink:href="#DejaVuSans-35" x="242.822266"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mf1ac796c00" x="487.53403" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(475.287936 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mf1ac796c00" x="519.40318" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(507.157087 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(357.4215 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(218.988281 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- XRDPowderPattern_xrd_20 -->
      <g style="fill: #333333" transform="translate(237.907344 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-30" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_minimum_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(53.217969 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-47" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-53" x="2141.751953"/>
       <use xlink:href="#DejaVuSans-76" x="2205.228516"/>
       <use xlink:href="#DejaVuSans-6f" x="2264.408203"/>
       <use xlink:href="#DejaVuSans-6c" x="2325.589844"/>
       <use xlink:href="#DejaVuSans-75" x="2353.373047"/>
       <use xlink:href="#DejaVuSans-6d" x="2416.751953"/>
       <use xlink:href="#DejaVuSans-65" x="2514.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="2575.6875"/>
       <use xlink:href="#DejaVuSans-70" x="2625.6875"/>
       <use xlink:href="#DejaVuSans-61" x="2689.164062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_avg_dev_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(60.546719 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-76" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-6f" x="2207.962891"/>
       <use xlink:href="#DejaVuSans-6c" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-75" x="2296.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2457.71875"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.242188"/>
       <use xlink:href="#DejaVuSans-70" x="2569.242188"/>
       <use xlink:href="#DejaVuSans-61" x="2632.71875"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(110.344844 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- GeneralizedRDF_mean_Gaussian_center=8_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(38.304531 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-38" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_100 -->
      <g style="fill: #333333" transform="translate(213.187031 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_134 -->
      <g style="fill: #333333" transform="translate(213.187031 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-34" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(102.012656 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(56.90875 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(159.763125 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(55.974375 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_120 -->
      <g style="fill: #333333" transform="translate(213.187031 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_110 -->
      <g style="fill: #333333" transform="translate(213.187031 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(159.763125 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(159.763125 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(98.539219 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- XRDPowderPattern_xrd_27 -->
      <g style="fill: #333333" transform="translate(237.907344 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-37" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_maximum_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-70" x="2157.035156"/>
       <use xlink:href="#DejaVuSans-61" x="2220.511719"/>
       <use xlink:href="#DejaVuSans-63" x="2281.791016"/>
       <use xlink:href="#DejaVuSans-65" x="2336.771484"/>
       <use xlink:href="#DejaVuSans-47" x="2398.294922"/>
       <use xlink:href="#DejaVuSans-72" x="2475.785156"/>
       <use xlink:href="#DejaVuSans-6f" x="2514.648438"/>
       <use xlink:href="#DejaVuSans-75" x="2575.830078"/>
       <use xlink:href="#DejaVuSans-70" x="2639.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="2702.685547"/>
       <use xlink:href="#DejaVuSans-75" x="2777.490234"/>
       <use xlink:href="#DejaVuSans-6d" x="2840.869141"/>
       <use xlink:href="#DejaVuSans-62" x="2938.28125"/>
       <use xlink:href="#DejaVuSans-65" x="3001.757812"/>
       <use xlink:href="#DejaVuSans-72" x="3063.28125"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(98.539219 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 434.278906 638.149 
L 525.926906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageaa9852e612" transform="scale(1 -1) translate(0 -578.16)" x="436.32" y="-43.2" width="87.84" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature60_fold0 -->
    <g transform="translate(171.219906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.654906 638.149 
L 539.284406 638.149 
L 539.284406 27.789 
L 531.654906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagec20a463143" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(542.784406 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(542.784406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(577.185344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p46bb59b348">
   <rect x="434.278906" y="27.789" width="91.648" height="610.36"/>
  </clipPath>
 </defs>
</svg>
