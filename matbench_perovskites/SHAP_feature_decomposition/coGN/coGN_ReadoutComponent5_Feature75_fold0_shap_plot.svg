<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="797.236594pt" height="679.5765pt" viewBox="0 0 797.236594 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T11:36:07.155183</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 797.236594 679.5765 
L 797.236594 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
L 526.293594 27.789 
L 436.013594 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 470.848426 638.149 
L 470.848426 27.789 
" clip-path="url(#p4f14732524)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 436.013594 609.084238 
L 526.293594 609.084238 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 436.013594 580.019476 
L 526.293594 580.019476 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 436.013594 550.954714 
L 526.293594 550.954714 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 436.013594 521.889952 
L 526.293594 521.889952 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 436.013594 492.82519 
L 526.293594 492.82519 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 436.013594 463.760429 
L 526.293594 463.760429 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 436.013594 434.695667 
L 526.293594 434.695667 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 436.013594 405.630905 
L 526.293594 405.630905 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 436.013594 376.566143 
L 526.293594 376.566143 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 436.013594 347.501381 
L 526.293594 347.501381 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 436.013594 318.436619 
L 526.293594 318.436619 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 436.013594 289.371857 
L 526.293594 289.371857 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 436.013594 260.307095 
L 526.293594 260.307095 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 436.013594 231.242333 
L 526.293594 231.242333 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 436.013594 202.177571 
L 526.293594 202.177571 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 436.013594 173.11281 
L 526.293594 173.11281 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 436.013594 144.048048 
L 526.293594 144.048048 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 436.013594 114.983286 
L 526.293594 114.983286 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 436.013594 85.918524 
L 526.293594 85.918524 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 436.013594 56.853762 
L 526.293594 56.853762 
" clip-path="url(#p4f14732524)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m09d97f74a9" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m09d97f74a9" x="441.992824" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(433.88462 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m09d97f74a9" x="470.848426" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(467.349051 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m09d97f74a9" x="499.704028" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(496.204653 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(358.472188 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(214.921719 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_188 -->
      <g style="fill: #333333" transform="translate(214.921719 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-38" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_tetrahedral_CN_4 -->
      <g style="fill: #333333" transform="translate(100.416094 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1570.777344"/>
       <use xlink:href="#DejaVuSans-74" x="1632.300781"/>
       <use xlink:href="#DejaVuSans-72" x="1671.509766"/>
       <use xlink:href="#DejaVuSans-61" x="1712.623047"/>
       <use xlink:href="#DejaVuSans-68" x="1773.902344"/>
       <use xlink:href="#DejaVuSans-65" x="1837.28125"/>
       <use xlink:href="#DejaVuSans-64" x="1898.804688"/>
       <use xlink:href="#DejaVuSans-72" x="1962.28125"/>
       <use xlink:href="#DejaVuSans-61" x="2003.394531"/>
       <use xlink:href="#DejaVuSans-6c" x="2064.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2092.457031"/>
       <use xlink:href="#DejaVuSans-43" x="2142.457031"/>
       <use xlink:href="#DejaVuSans-4e" x="2212.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="2287.085938"/>
       <use xlink:href="#DejaVuSans-34" x="2337.085938"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- GaussianSymmFunc_std_dev_G2_20_0 -->
      <g style="fill: #333333" transform="translate(161.487656 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-32" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_61 -->
      <g style="fill: #333333" transform="translate(223.192969 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(75.978125 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(223.192969 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_maximum -->
      <g style="fill: #333333" transform="translate(98.691562 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-6d" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-61" x="2007.564453"/>
       <use xlink:href="#DejaVuSans-78" x="2068.84375"/>
       <use xlink:href="#DejaVuSans-69" x="2128.023438"/>
       <use xlink:href="#DejaVuSans-6d" x="2155.806641"/>
       <use xlink:href="#DejaVuSans-75" x="2253.21875"/>
       <use xlink:href="#DejaVuSans-6d" x="2316.597656"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(161.497812 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(84.381406 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(223.192969 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(100.273906 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(220.722969 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- BondFractions_O_-_S_bond_frac_ -->
      <g style="fill: #333333" transform="translate(200.570937 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-46" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="306.910156"/>
       <use xlink:href="#DejaVuSans-61" x="348.023438"/>
       <use xlink:href="#DejaVuSans-63" x="409.302734"/>
       <use xlink:href="#DejaVuSans-74" x="464.283203"/>
       <use xlink:href="#DejaVuSans-69" x="503.492188"/>
       <use xlink:href="#DejaVuSans-6f" x="531.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="592.457031"/>
       <use xlink:href="#DejaVuSans-73" x="655.835938"/>
       <use xlink:href="#DejaVuSans-5f" x="707.935547"/>
       <use xlink:href="#DejaVuSans-4f" x="757.935547"/>
       <use xlink:href="#DejaVuSans-5f" x="836.646484"/>
       <use xlink:href="#DejaVuSans-2d" x="886.646484"/>
       <use xlink:href="#DejaVuSans-5f" x="922.730469"/>
       <use xlink:href="#DejaVuSans-53" x="972.730469"/>
       <use xlink:href="#DejaVuSans-5f" x="1036.207031"/>
       <use xlink:href="#DejaVuSans-62" x="1086.207031"/>
       <use xlink:href="#DejaVuSans-6f" x="1149.683594"/>
       <use xlink:href="#DejaVuSans-6e" x="1210.865234"/>
       <use xlink:href="#DejaVuSans-64" x="1274.244141"/>
       <use xlink:href="#DejaVuSans-5f" x="1337.720703"/>
       <use xlink:href="#DejaVuSans-66" x="1387.720703"/>
       <use xlink:href="#DejaVuSans-72" x="1422.925781"/>
       <use xlink:href="#DejaVuSans-61" x="1464.039062"/>
       <use xlink:href="#DejaVuSans-63" x="1525.318359"/>
       <use xlink:href="#DejaVuSans-5f" x="1580.298828"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(11.794687 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- AtomicPackingEfficiency_mean_abs_simul__packing_efficiency -->
      <g style="fill: #333333" transform="translate(7.2 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-6d" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-65" x="1362.451172"/>
       <use xlink:href="#DejaVuSans-61" x="1423.974609"/>
       <use xlink:href="#DejaVuSans-6e" x="1485.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.632812"/>
       <use xlink:href="#DejaVuSans-61" x="1598.632812"/>
       <use xlink:href="#DejaVuSans-62" x="1659.912109"/>
       <use xlink:href="#DejaVuSans-73" x="1723.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1775.488281"/>
       <use xlink:href="#DejaVuSans-73" x="1825.488281"/>
       <use xlink:href="#DejaVuSans-69" x="1877.587891"/>
       <use xlink:href="#DejaVuSans-6d" x="1905.371094"/>
       <use xlink:href="#DejaVuSans-75" x="2002.783203"/>
       <use xlink:href="#DejaVuSans-6c" x="2066.162109"/>
       <use xlink:href="#DejaVuSans-5f" x="2093.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="2143.945312"/>
       <use xlink:href="#DejaVuSans-70" x="2193.945312"/>
       <use xlink:href="#DejaVuSans-61" x="2257.421875"/>
       <use xlink:href="#DejaVuSans-63" x="2318.701172"/>
       <use xlink:href="#DejaVuSans-6b" x="2373.681641"/>
       <use xlink:href="#DejaVuSans-69" x="2431.591797"/>
       <use xlink:href="#DejaVuSans-6e" x="2459.375"/>
       <use xlink:href="#DejaVuSans-67" x="2522.753906"/>
       <use xlink:href="#DejaVuSans-5f" x="2586.230469"/>
       <use xlink:href="#DejaVuSans-65" x="2636.230469"/>
       <use xlink:href="#DejaVuSans-66" x="2697.753906"/>
       <use xlink:href="#DejaVuSans-66" x="2732.958984"/>
       <use xlink:href="#DejaVuSans-69" x="2768.164062"/>
       <use xlink:href="#DejaVuSans-63" x="2795.947266"/>
       <use xlink:href="#DejaVuSans-69" x="2850.927734"/>
       <use xlink:href="#DejaVuSans-65" x="2878.710938"/>
       <use xlink:href="#DejaVuSans-6e" x="2940.234375"/>
       <use xlink:href="#DejaVuSans-63" x="3003.613281"/>
       <use xlink:href="#DejaVuSans-79" x="3058.59375"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(114.143281 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(292.820156 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(171.304687 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(129.720937 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image013e85e80c" transform="scale(1 -1) translate(0 -578.16)" x="437.76" y="-43.2" width="87.12" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature75_fold0 -->
    <g transform="translate(172.270594 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-37" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.936094 638.149 
L 539.565594 638.149 
L 539.565594 27.789 
L 531.936094 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagec0b8c4bd00" transform="scale(1 -1) translate(0 -609.84)" x="532.08" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(543.065594 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(543.065594 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(577.466531 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p4f14732524">
   <rect x="436.013594" y="27.789" width="90.28" height="610.36"/>
  </clipPath>
 </defs>
</svg>
