<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:45:13.943626</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 473.482466 638.149 
L 473.482466 27.789 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#p4c0a2d5a09)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m6570196326" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m6570196326" x="435.466482" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.25 -->
      <g style="fill: #333333" transform="translate(418.61156 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-32" x="179.199219"/>
       <use xlink:href="#DejaVuSans-35" x="242.822266"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m6570196326" x="473.482466" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.00 -->
      <g style="fill: #333333" transform="translate(461.236372 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m6570196326" x="511.49845" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.25 -->
      <g style="fill: #333333" transform="translate(499.252356 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- CrystalNNFingerprint_std_dev_bent_150_degrees_CN_2 -->
      <g style="fill: #333333" transform="translate(43.689375 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-62" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1595.044922"/>
       <use xlink:href="#DejaVuSans-6e" x="1656.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1719.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1759.15625"/>
       <use xlink:href="#DejaVuSans-31" x="1809.15625"/>
       <use xlink:href="#DejaVuSans-35" x="1872.779297"/>
       <use xlink:href="#DejaVuSans-30" x="1936.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="2000.025391"/>
       <use xlink:href="#DejaVuSans-64" x="2050.025391"/>
       <use xlink:href="#DejaVuSans-65" x="2113.501953"/>
       <use xlink:href="#DejaVuSans-67" x="2175.025391"/>
       <use xlink:href="#DejaVuSans-72" x="2238.501953"/>
       <use xlink:href="#DejaVuSans-65" x="2277.365234"/>
       <use xlink:href="#DejaVuSans-65" x="2338.888672"/>
       <use xlink:href="#DejaVuSans-73" x="2400.412109"/>
       <use xlink:href="#DejaVuSans-5f" x="2452.511719"/>
       <use xlink:href="#DejaVuSans-43" x="2502.511719"/>
       <use xlink:href="#DejaVuSans-4e" x="2572.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="2647.140625"/>
       <use xlink:href="#DejaVuSans-32" x="2697.140625"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(61.607031 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(198.899219 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(93.832812 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(213.278437 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=4_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(16.423906 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-34" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(151.583281 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(63.252344 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_maximum_Number -->
      <g style="fill: #333333" transform="translate(78.210469 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-75" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-6d" x="2231.742188"/>
       <use xlink:href="#DejaVuSans-62" x="2329.154297"/>
       <use xlink:href="#DejaVuSans-65" x="2392.630859"/>
       <use xlink:href="#DejaVuSans-72" x="2454.154297"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(48.728906 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(151.583281 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- GaussianSymmFunc_mean_G2_80_0 -->
      <g style="fill: #333333" transform="translate(165.273906 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-6d" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-65" x="1157.439453"/>
       <use xlink:href="#DejaVuSans-61" x="1218.962891"/>
       <use xlink:href="#DejaVuSans-6e" x="1280.242188"/>
       <use xlink:href="#DejaVuSans-5f" x="1343.621094"/>
       <use xlink:href="#DejaVuSans-47" x="1393.621094"/>
       <use xlink:href="#DejaVuSans-32" x="1471.111328"/>
       <use xlink:href="#DejaVuSans-5f" x="1534.734375"/>
       <use xlink:href="#DejaVuSans-38" x="1584.734375"/>
       <use xlink:href="#DejaVuSans-30" x="1648.357422"/>
       <use xlink:href="#DejaVuSans-5f" x="1711.980469"/>
       <use xlink:href="#DejaVuSans-30" x="1761.980469"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(47.794531 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(66.063594 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(90.359375 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(99.924531 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(210.808437 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(90.359375 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAIIAAAMjCAYAAABpq8grAACUKElEQVR4nOydd5wlVZm/n1Ph1s19O/d0T0+OMMyQo0RBUFAQzCwmzKi76yKK7rKKYQ3o+jOvOSBiQlSCAck5zAAzMDn2dI43h6o65/fHvZ17GNL07b7VD5/50F236vZ7qr71nvek9wilFF7ivec/8peOcOR8y7azN920LlRue2YKwktCeMPFT8tXtHUJrfR7ezTMs411sb/csCJeVsNmAEa5DZgu/v2c+688bSAl0LSRYy2JFPtDgUFAO/CV3sAzN2BQ17+EEJOOLx+KTz7oQTzjEeane4WQPnJ+k0QkjBKCcDpDwEmV27QZgWeEsKO6gVX9O1jfuhZDSRxdJxUKknGC5TZtRuAZIUgzxL7GZuqH4hiOS97vI+P3g+Yrt2kzAs8IwSeVFhnME0wNASCFYLCxhmxtrKx2zRQ8EywK1yGYyo78rilFVX8ce4oA0ot4Rgh6Lj/pmGE77A1YZbBm5uEZIfhwJx3L+0zCyblWA3hICP3VtQzWVDHcj+oYOjsXzOMVu7rKatdMwTNCiCpFX1Mtu5cvYN+iZnYvX4Ad9NNZX83Cy7d8p9z2lRtPCEF8Pi8UgFIoXcOxfOhSIZSku8ZPZ+v8D+nXpuXx79+8qMymlg1PDDqd/qYnbRGsMk7uG0SbUNx/efQGAnac17zt47THajFcR/XrvgcJBJahi/cDb0LwK3W1dVtZjJ8mKlII22Mf+N+6dOpf33vBu8X9C9Zw7v5uagoFLKVhTCjvys5NDOV9zO8p0BMN8ZMzjuLRZfMBAUqBAOSEezT8qwYYJada/F6F4hHgQhT/AbwOaAeuVVf77j1kBX4ZqDghxK23/Uu0kPvlOy94H79Yeyqv3b2fgq7zz/mNvHt7G0ap38Bv2wRsh7p9HZy5affI9bau8dYrLmFHU23xgFKgA/YB7pNPUFTLGJQaQhIbcyQHHK6u9u16mYr5slNxMYKj6dcI4N4Fq0ApogWbv7c2sTCdQxgGrq7j6jppvx9bU6zb1TnuetOVnP/k9tEDovSgtQN0PE1ulQLjRADgB978Ios0LVScEBRkAXTpghDsiYZQQrAimZni3Dw5c3Ive97Qp/7ml0b6pX7BoaTihBAp5D6kgDc/+zAoxZBV7Dl0pnij5yf6uGXd4nHHkn4ffzpm1eiBkTiBSTUAUKw2JrNjwu9dwA3PtwzloOJiBICdsfdfFMrnf/mt484NX3/EqRRMP0LTOL+jb+S5KRRvfeQPLBnYxS9WvppQ0qA3GuaGU9awt666eNLwgx8OFjVR/FmVPhOAPhIsKopv/Y/RxMdw1ZuBC4H9wP9TV/vapqPsL5aKFMJETrhwQ7JtYVPYL0xOHIijK8gYOinToCqdwXYHeOeTj/DTo47l8eZFffu/3lJfbpunG08IAeCYy7ergBmmxnbGHd/p92HYvSRCEbmnfp5Pfco3dfhX4XhmPoKlTPxSTjpen8vTLYIn7/7fhQ+VwawZQ8UFiweiypWE84VJx5cNJdj8oyWeFgF4yCPU5m10TSODIK9rCMDvughv1IwHxTNCiOXztAeDNOZyOFpRCCjF1nCg3KbNCDwjhLimUe+4OHqxAakAhKDGnRw3eBHPxAj7LR9yik6l2MQBJY/iGSEsHkrIqToG44ZnnOJz4hkh1AzF0Vx3eLgYKHYG9jBXNYCHYoSc32Jh3wAdVVGylg9NKeoTSYQ+9WCB1/CMEAZ8Js15m5Z4Ytxxz9yAg+CZqqG1kD5yqj6DXsPwZJfyRDwjhC/fedbGzX7LHRlJVIpBBI8vW2SW17KZgWcGnYb54un/CG+sqlmf9vm+/+ffH/H1ctszU/CcEOaYGs9UDXM8N3NCmAOYE8IcJbwrBHHxXGKEMXguWMyJN0oLtzgKDWQxPhtSv/1Mea0qP54SQlpcXAiCOdYVSEBTN3neO3iqavAjzIlPXABZcfEny2HPTMJTQhBTrFYSQBbx0em3ZmbhKSFMNeCsAKkbTdNty0zDU0I4EJo7N2/RM0IY0i8LTTXzQAC2PjcY7RkhSJkdgslrmiWCnqqa6TdohuEZIWhE9Cw+5JjlywrYEllGLDujV6xPC54Rgg9XBCigj8lskaGG2kweOeV6d2/hCSEkxcWdftKTHrefOLaKUpvt9rwSPCEEHzRO1Yeg4RKUSZJGbRmsmll4Qgh5olO+8QKIsZ/H5x3Jmst3JqfZrBmFJ9pNGWrIEaSByel22yMxbjrmGF7Rnwif9K4dMpDKy8aCLZri8Qf+9+7TTyuDuWWhsgedxMWn5OG+vawTg8Q4lvtHgkWJ4E8rjufjZ7+T/poQqaBJLFdgRfcgZzy9i8aBBNsbarj5qGI+pWi+gM+V9PotZYFwoZC2jH5XiAbDcUmbutQQf1eaeH3qS1G7nMV+MVSuEMTF5yu4JY/FPpaxj2UESdLCXjQknbRw6RWX0lsTIh71j14mFRduaacxnWP5zjbaggFuWbcSKGbSq8/m6A36aY8EsEfzJ1GdKzDo9+FzZa5g6EF1bXBW3diKjREk3CwRWOSpoxuADBG2s4atrGUo7CdvGiTC4/drUJpgTyyIbRjsXtTM8r6Bkc90IG0aJH3GqAgAhCDtMwnZLgVD94fz9oeno4wvJxUrBAF6hggCqKGPFWzEpAAo6ugiHG2jvSmCmmKFtF56l/OmST40fhwir2k42uTbZmsCY3Rl9dEva2GmgUoOFvf4yC2OE6OKIRawi1Z2IdHQkVyz5k1IffIDDdgOyweKPY0+26E3OCoEBdTmC2R8BnH/+HUxoYJLxtSLGeCFmHXrJSrXI6iblpgU5AC1dNFCDj8JYuilwei0b/IWProree3WToKOi+66RFMp/njY0uJQNWBIh5RhEHAktZk8mlSgFAHbQaFwhSBccH6a+lJ04/SW9qVTsUIA0NRNegO7PmCRYh9L2MMaChT3ebx0w/3o7vhlj2v3dOEfiEM2Szaf5+GaKtllGk4GmfNlU8+Qyb0zbhpnFODsWN4JNKTz1oJE9siqTL5FwEqpCT355ap3l6OsL5XKbTWMIS7eqzRMbIKE6MEgh0Tnr8vX8rmz38zO+jp8eYfg/p6endevaiy3veXAE0IYEB+QirCoZg/amHlKOaJkscgQokV93dPjDZUcLI5gkkbDHScCAIskaWoJlJqXXqaiY4RhBmk4Ryc75WcKnQCFyneLB8ETQligvvbPFD4lJxQ3Sw0WcTpoKJNlMwdPVA0AfrK46BSIIHDJE0GhEaGdofDKOY9QbgOmixzsNbCxiKPhEKKPKtrRgGWp3Z4OFMFDQqhTv148vAmLSQ6d4gChAgJMTtbtNTwjBACHqTcET5U6mbyMx4QwxUZeBEig5cpgzozCM8EigImNjQ+DAgJw0THJUY/6e7ltKzee8ggSHbMkAgAdF4EgCO8oq2EzAE8JIU7om+MXuAiy+JVQN8XLaNaMwBNjDWPpFh96h5/sTw2ksKEtpn6+oNw2zQQ8J4Q5psZTVcMcB2ZOCHMAc0KYo8ScEOYAPNah9Ljxjea9LbX7B+uqhJUrqHntfR86e+gD3y+3XTMBT7UafrvyBnXSzu0YrgQUW+Y3E5eR1Re1X7ql3LaVG89UDb+t/tHASTu2Y7iK4hikxqr9nSif82y5bZsJeEYIQT9VxqQ9HgWtQ4NlsWem4Rkh+JLOFKkyQMvNbfcHHhLCUDjK9sZ5OELQGawipxn0RqJsq55XbtNmBJ5pNbiWZEewiS2xRhDF8UcNsFynvIbNEDwjBCElIufS2JvFyrkU/Dr99X6ENicE8JAQYsm8FstIqu0sBi5OWseft+mcPzdNDTwUI2R8JtUyiw8XDfDhUuNmsDJz+3+CR4Twk+brv2TldHwTVj8bSsJU28N6EE8IQeWdj/bHolN+9uzieeLMSzeoY965YdM0mzWj8IQQfIFAQLMVOxaM35ZhT3M9dx2xknTVPPx67PBTLnvMswscPBEshtM2/mSeh05Zwb7mOhr64/RVR9lRV8WGhTEylo9w1ubsjZ2muM4R6krDc/VFxQ46DYhPRnZGG1vvW7b663VdqXP7WqtRuoYClBJkbcmvTj2cbc21mK7E1gQn7t7Cx/5xC0/VzJen794qfUrXt9XUsnJgt4sUhYJldS9ID3x3Xj79RI8v8MNINtXR4ORPB1YDCdRN+8tc7BdNRQqhV1z95SfrFn782YVLRDLkJ1iwUWMyoSng26cewd662MgxXbps+/K/smSgh92RehYlB4FiH0NbpIZ/e+Xb+OYdv6I5lcXRNEyZASCvm/jdkfyaTwHHom6adZ0TFRcjDIhPvn5vtO6qbfNaRTYSpGCY40QAxbFHIcave3U1ndtXHQXA4mQvAqd4HrAgOcAnH76VD5z3LgQFTCkAHwLwu/bY9BvrgO8cwuIdMioxRnhlbyBKwV9c55gNWFTliivaHF3HNXQUgkUDKUylYUrF/miAhN9kwWDfAb/0qJ59/HXJEYxuETa6PkIKDU2NyOGcl79Ih55KFMK2cCGP7hS9s9I0pFIoQ8e2fADYmsa8jEtdIQHAqt4kCTPNa7asP+CXDviDLB3sgZF1UmNHLcdVr7tetpJMIxVXNQA/WZLofqpuYBDddqhKZyjoJgVzNEFmfziIbYy+0RpweE8CvRQvPdWwYNyjVcBVp7+ZL9/9G8BfOlKMCyQCYzTOKgDvO2QlO4RUZLA4ID5p5HTzwvVNi6/d3Ny6ur4nKToXNuD6ig6wLRZlIBwad01eFyzse5JHm5fS1NbNfz1wmzpycL/oCEf4Z+sqtTTeQ0MqRdZnyaBbyAddJxDNpfAXMrdY0AMMANeibkpNf4lfOhUphIn8cPmflBP2M1ATRWmChGWxu378zm6mPUhS5Nhjhm7Y+JMjLi2TqWWjEmOESUhd49ind5KMBNk7v45mBM2d/Ty5vJWM5cMoZDht6+MMmFL95ZH3eU4E4BEhkE45hpRGTTxFTXzUc5+2fgtKT9FibeFzJ50vv3PHG6faI9QTeEIIQc39latp7zDk+PmJiYCfvqp6dexb3qer/62p/DryOajEVsMkCoHQe3r9/pEGnwIGQ0GeWrKQQkDH6yIAj3iEy7e83vldza/42yuPYWHnAEPRIN311aAUTQ8+WW7zZgSeEAJAPqhJoWna4t5uFm7qZTAS5v61q0F4wikeFM/cBcvN8ZqHnqChb5COcARlu7zuvkcIpTLlNm1G4BmPUND9iFya7fX1I8d6QyGqM7Oy/+dlxzNCSAR8SH186zBvmuR95gGu8BaeqRpidh4pJqdczplzQgAPCSE8FJdqgg4UYNr5stgz0/COENBC9ZkErigKQAowpUPSsO4rt20zAU8MOg1ze9X3Ho7p+RMK+EBTdAci9pva3u4rt10zAU8JYY4D45mqYY7nZk4IcwBzQpijxJwQ5gA81LM4kY+d/eiRVanUL/Om+cUv3nfqr8ttT7nxZKvhqhPucAarwrqmFK7QmN83qD6z/jxPe0fPCeEjZz3wuq5Y5E9/OWo5eUPHdF0uXr+NI7buKXz6mQun3v3LA3iuakjr2h9vOnolSAmuQkq4ee1ytLzr6Y4lz7nD/Q016I6LLK2HdDUNWxO0NU6dSMMreE4Ihu2MW+UEIDWN6oxnc2QAHhRCdSqLmBgXKUUm4NnwAPCgEHQlxVse34zuFuc0a1JyyeNbMBz7IFdWNp4KFp8wvxyvO/0VYt3mfZy0rY3ddTFaB5LUprNsnl8HrCq3iWXDM83HJ4zPP7mvbv66TMCHqyY3EPbVRalJZmIf2nqRJ/eA9EzVsK+mZd2idCfBXAGUQqLIGRqOJnAFOLrBturw58ptZ7nwjBAidpJnqpeQ8oVQorgw1lAKBChN0DIQRwT8Hym3neXCE0L4xcLfbUj5wyihIZTEUJMDQyElbY0NfPi0uz057lDxQvhF8w0PoNSROd3kiPhGLmm/mcWZvZPO05Ti7A2b6Q0F3lgGM8tOxQaLv1j4u+XY9lYMXdQWBjmt7wHyukV1YYi4GeUvLefiaqONJs1VaEDCMtgVC3/j64+c/e/ls376mfVC6BDXHBWl4/cKtzUlQtqm2hV6Z3D8riwt2f10BpqRQsfvZjmt9wF8ssCG6iPo9tdz7/LlPLF0AfOGEpz5zA76qsPcdthSViaSYBigcPNKXPnHXy37RnlKeeiZ1ULYLz7XWs3e3RZxPUeQu5pOYdBXPbJDy4EIOynq8n3sCS0CivnR/rF2Bc+2NlGVznLZ/evZvKyFO5Yt5Oy9HUjTxBGCHr91zo2/WnbHoS/Z9DOrYwQN5x1BBvU4NbiGKIrgeZAywiMiKH4PnLZ5F7oriYcC9IWDNKUy9AtBIuAHwFAKIeV3D0ExZgSzWgiMJj2EKfdwmxpNTt6sI2A7BArFPgZdK36tJSXjV0ep2X6/DsisLpjE+HmGareKAQwHqgulPRwPUt3lLN8k2QyEAqT8Fis7exF+k6RlElMQyRaXxDlCIDX9o4egGDOCWR0jAHSK/zo2QudNCndeWgS1jbUrtYnBIjASNyhgqCFKKJ7Bly9mZ83rOhtbm/A7NlEdBoIBHm1ppDGXwyw6HZlDXHvz9cs+O20Fm2ZmvRAOxC9af3Oxz7Z/7+i6kKUVz4ZToD6ZIusLkwxZFFAjGZVdIbh/VSvSVVQlhm78xsPnvrV81k8/FSuEYX7V8NO7XStwOkJjfk+KQH40g/5TC+rZsLSJ1Z193HzCala391A9kHS/+MS5nhqVhVkeIzwfLu151xmBQu6p1V1t40QAcMS+Xm5et5yPv+FMDt/TTdNAisFo8OoymVpWKl4IAG/sfueRfjWaB0EC8bCPvuogx+/pwNE0NrQ24rMdvnfXqV8tn6XlwzMu0CJHDgcHg46GCNlAMW5444Zt1KZzPD2/Hte2f1dmM8uGJzwCQLsV3TifNjYvqBkRwTBnbN/HsXs6iChxeZnMKzueEcKZqSvXtgWi6R0LIpM+0xRUZwv865bXJctg2ozAM0IAOD5zdRjHUClrvEfojgbZ0/D8uqcrFU8JASAnUV8781i2N1STNXWeaqnnujOPZSDs7c3CPRMsDhPK5Uj4fXz+VSeOO75m/VZgRXmMmgF4ziP0xCK856GNRHLF5qTuSi5+ahvLuvvLbFl58ZxHyFgmJ+7r5X9vupt91RHqU1mi+QJbG6rKbVpZ8ZxHKFi+b/zlqGWYUrK0P040X2B3XRXdQZ+nM29W/FjDVJz/hqfd7uqotrRniKGgRTCbV3+8+UjPvRRj8WThb/39Wn1xz9ClWdPoboinPux1EYBHPcIck/H8mzBHkTkhzAHMCWGOEnNCmAPwWIfSPvEFrdsIp4b8oYCmFFX5VPex9r83lduumYCnWg2P+r8md1e3CFWa0by7PsL6xfNAQioaPvL265c/VWYTy4ZnPMJW/epUxr9IrOnpQCH4zcnHsG1pKwFX0R30Eentf5JxC2a8hWdihLRZG1qS6SMqsySqA+xdNB/LLXrDxkyBVH0tZ7x923VlNrNseEIIj/m+eHs074687tubJ4cF9ZkCBSXeO72WzRw8IQQHztZKW4TndYNAbvL4UkbX8Dk5z6bh9USMoKQUDj521tbTVlWNqzRi8SRDVcX5ixLYEAsjssKzWTc9IQRHr9IGLZd9sRoAFvYPcvytd7NrfiN7Gmq59bCldFs+FuWyZba0fHhDCBjgLy6Fr8pkaR0splJcta+TVfs6WdjZS+H8M2hp6wFay2hp+aj4GOG383784fmFQREtLXeryuYmnbOiowcTeHJpi2ebjxXvEWKJzDdjZIil02zL5siO2QtaAgm/RbaUkFscJOVOJVPxPYubtc8oVw+TR5ITVSSFRZXIcssxq/jhcUfQGwpQncvT5LoMmDpHb2/bdNvN644ot93TTcVXDUr3sU9vZLGznaidQhoGvzvnRL5y5nEk60L4IgbhgE51rsCiZIquppo1F7352cm5dSqcihbCgLhCDmlRtIIkj0WILB0Lq9leU4Ub8ZHzmxQMnbZYiM0t1cxLO1y0fR+N2bx21ps27y+3/dNJ5cUI4mLLQfuKDR+pQhNNhTA5VvIsx6IhOXHzZu5Z1YzSF4y7bDDgQ9cF26urWDkY577WxpaGj/UqW9dVPmzmsn5/N9AA3AG8SX3CrKhZz5XlEcTFTQoGBPpH/UhhY7CblahSMSUa/VRx+eO3Tb5WKVCKroCFrkBzJL1WgJzlE9HBbADBIgRB4HUIMuLLdkUNX1eUEBTiqwKCLj4EkKCaiUV0MDm8LcHp27ePO3541yAdhkGVAikEPaX8ijkJ3X6LcL6UyFsACg3FDw59iaaPSqsa1ihAlJLnhUlMOkHDxSLPH37yQ750+rn8Yd3RnLptLyft2A8Kehpr2VNbTW9JCKhiQraqdIFxq6gFx0xDeaaNivIIwC0CcPGjgCBp5rGH4WScAsUi9mMgCTgO73vgYRp6Exy3pwtNFW9GU3c/ocyYTicBSgm6IxOGIRQ3TE+RpoeKEoJAfU6hPa6TxCaKRKcvWkOmymAZuzmKTTRSXOy6r7qBr19wCaul4om1K9kzf7TKN4ZHJwX4pEO1dHCMkvMsaqofwaentXCHmIoSAuqmglC/P85AtmYx7N3aYfQbdTiGwd5II0IUn6JE42cnvYqcVXL/QtDZVMdQJIQCdsYi1KczhF1bSUMkZMD3eeANKK5Hcbj6hFmnPmFW1EaRFd2z2CsuzzwWOy2Q14vTDFzgyQVVvGp7GwHHZfOiFu49chWuXky7Ob+9m1g6y1+OXM1ek+/u/k7LFWU0f1qpaCEA/DX6E5U1/SAEQTvPgsTguM8fXb2Uu48+DIDqeIqkZfJwbdXmjf83/7By2FsuKqtqmIL63NDIz7FsZtLnq/a2A2A6DvtCfjbURi7wmgjAA0Iw7SG5INGJq4M2RSp/R9dpHIjjuAonl+Xx/1twaxnMLDsVL4QQ7vyUBcf0PIvrFDOxD6OAbYua2VZdRZdlEiptE+xFKq1DaRJL1Rc6N9V9W52aflY4yqQgAvzlyCOI5Atsmd/E+oXNWLbDsnSGbdFwZQdMz0HFewSAxnS/iosGfBiEHZvj9u/l8SUtbJxXz/yeQU57ZgcB28GvvDsxpeI9AkDesZw9keW+mkSxx3BJXz/X3Po3MobJw61LAOisi+I3jTmPUMmkQkG6quomHc8Zo2MHwUyOx6v83dNp10zCE0KocZJf29nUSNoaHS9whGBX9ag4djfEaJKiouYYvBAqvkNpmDsWfF89tWAlrb19SCS7Q1Gahor9Ck8tbOTXx67miP7ka+74yaLby2xqWfCMELbq1/w8Wx19+96aJrAhkEtw/bFH8/CiBQjDoDqd5+EftHo2WvSMEAAeNb820OSkql2hMWRE0Wz4+mtewf762oJrWYG7/2+eZzsSPCUEgHsi//fzgJ1/O0KppD/43VcOvvfD5bZpJuA5IcwxNZ5oNcxxcOaEMAcwJ4Q5SswJYQ7AI2MNE3n3qx6vX7OrrUsaQhN5273+lccHNvxooV1uu8qJ51oNHzvzAf/i9q7svccexiOL5+G3Hd5075Ms6e58w7t3X/aHcttXLjxXNTS1dyX/fvI6fn/0ctqqw2xviPHFi0+nN1T1+3LbVk48J4T5mYy+oaV23DGlCf527KoyWTQz8JwQ6pNpYunJk1hD+YpapvCC8ZwQCEguvf/JcYcMx+VND29kn/iCZwedPNdqEMrmoie2EMw7/PaktSAEb793Aw2JNH9auqLvI1B78G+pPDwnBKMgaW8K0t7Swqp0MUPOo0etwdq4iTZf0LObP3quamgoxMVtq4+hIZnimK07iaYzIAQPr1pNVbYwVzV4hTtXreTShx6lqpRl9awNm/jDaSeyq7mRpYPJMltXPjznEVp7EyMiADCk5PSnniGazBDODpTRsvLinZ5F8Sat3R913XwTBTU+FMgZBgPRIDUDSb7xiuPX/ui+0zaWycqy4RmPsF8L2bFcjha1myU8wSI2UMN+wiToixn8/Pi1+ITDkd27PbmdT8XGCK642NXGCF0FawhmsiObd2hIaujiT0taueK1l5AxfeysjvHWBx/xZMA4o4SgxGWGUL8sZs8WFxuom5yRD8VbdbAjoBcUrgOiBjgFuBvcC4CvKwgICIBAQwN0imnQJK2Z1BR/URBQSdrDxariniULyPlMPv3JXre5oz/nd7gFv2nkNXHtie07enut5uQfX7Eqr640Kq4bckbECEpcdgrwXWAtONshbwpYBDwIxm0gPgUEVSm3XTH3CQgECgWokZ+KaICf0b26hs+ZnFn3phXruOSNHx29UoOAAemAj0DewZCSaMGlN+QjlkuRRScdDLlS1/5bXWl84eW/G+Wh7EJQ4rIg0AbUFB9YZoqt1gxGvbxN8QGPPUsBcsxvASaHPxJwJ1ylWPPez/Jsw+geDUIXnPPsHv5+wopxVy8YzLCvOkh9cojekgdBiLXqSqMiAsuZECy+AihurTLhQU1Glf5NPKsoDDXu94logDHiOxSKAorN9S3jzjpmbzeFMTkUhon7DYRSRRFIiskX4QPPae4sYiYIYUzy64PFaWNd/UTGHjvQOhVB0bv4AB8+FD+8+bc0pVKEHJuzt+3jqj8+xIOrJ+/iosti1aNLOdbMrQcxeNZQ9qoBQInLfgn8S/G33IS6XO8FvX70dxeFRIzTcLFqKJZk+PhwjDC2fOOrFInixvnnE8q4+HMOtqHxsTedwY4VTcNvfPEqpahN5+kLWRzRsZeNzQtBiF6gVV1pVMTC2ZkiBA24ADgG1KOQCQpYA9wD1iMgLwO+CCpWChjF8ANWKCWQoigADXAY/dmAcQHm5E3c7qo/nvZAIwBdkSBDQZO7jljOsv19bGutZ0dzDbpyqUsNEs7m1e6axoHemugvgE+pK43J+wLNUmaEEF5WxCXHKtTdQAg0REkIRQEFJp1+e+sr6DFq2VVXhdQUNQNJPrbtYs/1JcyEGOHlRf3hcaFuCkNIk8iEi5QSVCd+FbfG7+8Z94XYXddKX12UH77yaB5c0kosM1Rhb8bzY0Z1KL2cCPVLpcPIoEJ/6Ipf27p8y70LD+OY9v30BWt4ovFwpNCwXMmpO9rZUVtFTyS6qZx2l4vKqxqeg73m5dl75p3l753XhM9xxn32wKJmarMJvnPbKZ6rFqASq4bnYKH944ClGcSjoXHHFdCYTnLy+s2e29RrmIqtGg5EdSrFQFUEEERSaaSm0R+LEnHydAZqPOkNwINCqEsNEUjn6K+OMlAdRQFDlo/GoSGalXdXvXlOCD2apeI+n+gMBZEohBBoQsMKBKgfipfbvLLhOSHk/VX0K8VtNVVIIdCU4ohMjqUDCZ5tmf+v5bavXHhOCJ3RALcsaUGWupClEDwTsJhXG+b2O078dpnNKxueajUA3LFqPgVjvP4dTWN3Taw8Bs0QPCeEmnQOTU7uO1nT3l8Ga2YOnhPCBZuffc8lT+5g7Khkk53njKe3eLfJgMd6Fof5+uI/ppQyQ5ta65mXSNMwFJf/tvf1k2ejeAhPCmGYO60f+8/KX14xQ8kvBU8LYY5RPBcjzDE1c0KYA5gTwhwlPNezeNUrf/s6n6NdsaVp5Qd//5sjdpXbnpmCZ4LF11z2sHXlvbfmEIp/LD0Cv10g4a9VX/v7a+a8Ih4SwsfP/J1sq64XvznihJFjSwa6qYvbhUd+sXzy9GaP4Zmqwe+44jdrjh93bFdNI0oM+g5wiafwjFv80brTxy1aGaagz+kAPCQEDZ2WVHb8Mak4rH+wTBbNLDwjhIjjcG5bN4sTaTSlqM4VuGBfF0vifeU2bUbgCSG884I7VuYskydrotQkM6xp7+XoPZ2EBlNsqqtm/pWDk7eJ9RieEIKvENg4YOrst8AxNZYUbPSgxd9XteLYJtGh/v0H/5bKpuJbDeLqpPiETJn3/vKzHNnTRtoX4PbDzubRhcewKp7il8taaY0nPN98rHiPcNbufT3/9vCNHNnTBkCokOXiJ/9CfbIXSyoOiyfpjgTRPpv+Y5lNLSsVL4Q1Q521Tenx09Q1YGnfLoZ8JppSdDdEUZZ5kfhsurM8VpafihdCZzBCxpy8HP5nRxzBT9Ys5v75DfgKLmgCLLNJfNn+aRnMLDsVL4SULyjuXHbauLwp1576Rp6uXkpLX4qmwTQ4CqQqikHwTvGlgufmL1b0WMNP1/7fhf977Fk3X7R/gHnJPlqG9vFo80KuP+w41JheRkcTdDSGQR/zXkgFCgdN+NUnzIpfHDu7hSAu9gGfBd4LVEvQBgIRfrL2fJKylrZGi03zl5Ez4PiOXrbVRVnYn+L+5sZJX9XREMIx9dFuaAUoNZp5R1D0GGMZvnVjD4/vxp6YAm498HXgBnWlMaNu/GyvGm4HPklx1xVNA+qySf710ZvY3hRiU+tyUobGms3t/Km5kcerqnikoWbSl0gBrqYVH2KxeijdmVLyLSFK3y7GH5sogMljGRMPHA1cD/zPSy/6y8vsFYK4eBFw1lQfWapAhGLK3RM27eX3x69koCZCPmCxs7aKhH+0+0QBQ2ELpY95sMMPVS+dMNETDF84xSDW8+Qj4jpnRvVdzOYOpfBzfdiQSYNSPDuvBtccs2RBEwwFfGT8JqYryZtG8XmK4URtavQBCwHikHhwP2ACMyY13+z1COqmTcCeKT8CtlQ3siiVpicWmnyCVDSk84QcyfxEBr97oASdlGKEA4jhxcdXf1RXGlNlCS8bs1cIRV4BPMrou4wjNG5bdAqrdqSwhWJxenToOVqwOX9fJ+/atpcTuvtpSWTxuYrUmKpiXDtTqeI/qcY8dDX6b6pzn1scNvAd4N0vusSHiNndajgIH3nFTbcWmla85tYF8+iMBHjfM7uozo92EXQG/fxqzWJsnzamOih9qABXgRB/B/VTwEYX+xDiCXWl8RwuZHZS0UIA+NDrn1Ant23jp+uO5ISeyavb/u+oZQyE/WN8CkUPoGuausqs7JszhtleNRyUO5vqVbCQQompiyo1MeriFSCV4zURwOxuNTwvmgdT/Hb1sRzdsYue8EIasqOBeso0yJljbkFBSnWN3yyDmWWn4oWQtsxCjxm2WpODtEeC9AT91OQK6EpRMAxymihWBVK66hp/xd+PA1HxVUMyEm7sNAT3z1+G5UJLpkDUVehAIdvJEfv2oOUd1Ke9KwLwgBCe/fa8eH0yKXWZoosEP1g2nx8taeHWxjBX3/8HQo6BkSt4Kh6YiooXAkDO8n80lE3zSMtSpBBkDZ2OSA1Xn/Vm3vDMo8Rcd2m5bSw3nhDCoz9Z8p1Ba3KP9CMty1nbsZXWtLNn+q2aWXhCCABBZ7L3r8u73LXoGB7/buNc1VBuA6aL+XmHVUOju8HX5Au8srOP25YeUUarZg6eiZSzmuLCfZ2c3tVHTteYV+pP2B8KltmymYFnhOC4Nn3BEHWZHBrFTsQnqyMsGUpIaCi3eWXHM0I4uuPZ0/6y6rR7QyGLsCPZGQridwrYhukvt20zgYofdBqHuDjw+tf/x4ApNGtnqHb/E79YsaDcJs0UvCWEOQ6IZ1oNczw3c0KYA5gTwhwl5oQwB+Ch5uNEPnDOHX2v3Lettj6b5L4Fy9kca3nlDbeccGe57SoXnhTC5Wff/uQXH/hbbU0uA8ApbTv5wdGv+CecMLfvo5dYnEmvu2vRcr50ytkMBEK88dkNXPXgHRzxjuO7Nv58VVO57SsHnhRCTzTGNWeeOzKh9esnnUVeNxgK+urLbFrZ8GSwePvywyfNar5h7XE0TcjD6CU86RGk0hBKEbRdDKnIGhp5w0fQ9lx+jBE8KYSaXJaMYeArbftXVYCMTyNnVtwCpueNJ6uGnqB/RATDWLZE4sklDYBHhZAyJifi1hScvG+3Z5uPnhSCkJKJY645XaM71DgnBK8gvmz7V/cO0B/0YWsCBWRMnf6AjycWNpfbvLLhqWBRfNX5TU0i8aZ5UpEN+ciESlWEEGiupKrg3b1APSME8VUni1L+gViUvbEcWqqAO5zzzDKQPh0979maoXJnKImvOj8H3j7xeCybJ9KTpi04fqqi6RPUJLPUqdyxm7654InpsnOmMKOFIL7qrAX+BjRQrM77gV8BnweagNehOBq4hNHcd8/Jxc+2cVNdzeSMaIaGD0VNMklUSpUXrrKUHNrV1PDMRx/+6y2XbnjgyIcWLG/92ivO/9Cu6xZsfHlLWn5mrBDEV52/A+cc/NFOwQGKJJTiI49u56etTSTNCbWiJsAqHmtNpUgYOjnLIB8JgBCcvvMZbv3xl+iIVvOh1737zn/85NhXvgjLZiwzstUgvuo08DKLAEAJwYDf5JSB+PikV0pBwQHHBQFt1RECBYcV/X0j592z9HC+e/KrWN7fxWVP3neW+JK94kVYN2OZkUIALnpRInge3LuwgcXZHA2ZHLhy9B+AXcrSjqDWtukORWBMr/MT85cAsK5zL8CrD42F5WGmCuEPz/VmvxT2xUL87MjFpPzm1OnwzOI6KFsqWhJD4+7QiXu3A/B4URB/OTQWlocZKQT1caMfuPk5xPCSZJI1DTJVk/dwIGSCgOpcjv2REAXDHAkqX715Pe9/+B9smLeInx99+m3qk2ZF7Ss9Y4NFAPFVZylwG7C01GpoA34AfBuoBy5AsQT4CONFPb5iGc6wDsUf3NIvtgsZu/iwgyZ6zsZAsqC/XzUNxeXuxvqOzrrqJz5x3y23XrLx4TV3Ll2z9FPnvvWjhf8M7j50pS4PM1oILwXxVecy4OdM1aQUsLA9zlCVn3jQh1CKumSeZDLHQuFaW77RVJh2g8tMxQphIuKrTh+oWqTCciVr98fJmRoSQcBx0RUknQLP/KDVk92LMzJGOBSojxt1ID7rz2U5c1c3zzZF2dgc45nmKnbVhpDAiV0d5TazbHhGCADq48ZncsKKPtxcQ9oa7VAaCFl0Ryz8U6TX8QqeEgKAusafTPkmz0TqCfvYF50ipb9H8JwQAGLJzKRjZsFlayToWZfgSSFEBjLjO5KkIolGbSLtWSF4Zj7CWPrDfsg4xe7kYkZ2EAKh6Qe9tlLxpEc4qrttdGOO4dnMCvqCnnwvAI8KISgn7+fpt22WDHSVwZqZgSeF0OOLTjqWM02C0pN9SYBHhaDk1Cua4oaZmGZTZgyeFMKuWPT6lnhy3LGT9u7ln384qbpMJpUdz4w1TCT2ofZnT+7oWJ0OBqlPDHDPomWv6/32vIqaY/BC8KwQ5hiPJ6uGOSYzJ4Q5gDkhzFFiTghzAB4dawB4x/kPfvvE9r0fspVQO5sXBP7f7Sd7bnraWDzZavji0X9wbzzqGO3Z2mqqCjbH7e/itN3bb/vUE68/v9y2lQvPeYRvvOLvxo1HHqNtrK8FYMBv8felCzAd5zVlNq2seC5G6HfkH5+pqxl3TAlByu/tjVw85xHsnFwRsW0yhs5RXX1YjstTTXU0p1LlNq2seE4IUenELty6kyUDSeozxQSbl2zeiSFszntnTd1ff7akr8wmlgXPVQ2DplZ4/bYnqMkXcE0T1zTRhQCloRLJt5TbvnLhOSFkfZrcV7UQNA0JuEKgNA1NgS89+Oly21cuPFc1dIcjLEwU6Kuuors2hhSC6mSKJe2dnDyQ82RmdvCgR9Dy0u6sraKzvgapaSAEg9EI21pbMHXP3Y4RPOcRFmaTTam6Yh9CVhPsDPpxhWAp0N/iWYfgPSGkTCsYzOfpjIa5vb6afMkLbIoEec3OyQtfvILnfKHQBAO6xsZIcEQEAFIINtbVcNXxt6bLaF7ZqFiP4Iq3nqTQngH3abAlmIsFgqt8MV7znv9kV2hyxpSE30c2PMUHHqBihCDFGy6SyG+BrwVMIdARuICJwodAIpC0FgbRyaGMGDjjZzOv6+5jeZ831zbMPiGIixcqeBREA5iARCGQGAj8CLIIMhSLpgAbNWYfhp8d8So2Ns4vfZdbTJ8DhJTLqfu72NHaLLaHrlU75rXw5XPO+eTd31vw5ekuYjmYjTHCo0ADDA8SKRQGAhONPBo2oCNQpVSsGhoFBHnyVPGtk04f/SZTh6APIj7O6Ogm6CqkFqZXj3Dyzmf52F13fklc5yyf/iJOP7NLCOLiOqChaLYGOEh8aBQAHbABbYqkScVjEkHWnLxpR00qx1lb9hHKF7BNi8HqKENmmLVtu1nXMXD1IS3TDGF2CQFK27ANT6YRCFTp3Zc8V3EUoGHz1qfWjzten8pw6YYtGKoYL2gotje0oCtJ1meR8Rn7X/ZSzEBmlxDUTWngvmKmPYdijJBHEkCQRuJDoZichlGi0PGR5t8f+Cev3LEbkc0T7Utw2L5uGtJZXNPAMTSGwiEaewcJOxn+uWqd2l4X/dp0F7MczC4hFDkd+A7kbXAQGGhkUFgIcigMJKCQqNJ/Eg2Bhg4U/DYP1daj8i4Jw+Ce5kZ+uGpZ8Zs1jZbuLhrVEN8//bzua847vUldacTLWNZpo+LmLLri4u+A9kFVHFweiRgUAoHgT0uP5vWvu3zSdR97ajPzM1myblZ96tELZuML8pKYfc3Hg6Crm64Arhh7zBX/4gc7DYbWGawa3rdlEkJJFvZ2T4eZMw5PKF9X1+d09RtdU78SvVYTtbn8uM8XJNMsTiQ4efcmkj7zz2Uys6xUnEc4GFYho1bk82LF1h0c0TuEz3VZMdjO8V272B2r5QNb335RuW0sB57wCGNp84c7Tt7TxsntfUQKDpar2BttZkt1M986+bRym1c2PCeETfNazBVd3WhKMj/Zz9LBLgJ2ntuWr+Xp1vnlNq9seK5qWNXV7nZHolz25IPE8sX5B0f27qG7+mRO2bpTwcIyW1gePOcRotKRTfn4iAgAdKU4pW07diH13TKaVlY8J4T+YCQQsifv+Do/HqehoNrKYNKMwHNCsPKO/Yd1h086/vDChTQI3/8rg0kzAs8JIeC6PLB4CV981StJ+YojkXcvW8pPjj2RKx55tWc3h/ZcsNhVW7Xtwkc2N3313LP45hmn4rcdEgE///rnR8ttWlnxnEfoap53xuLeJB/42+Ms6onT2pvkP25+iGTArKxBlxdIxQ06PR9e/YYNexYm3YVH7u4kFfDzyNJm3v30nfqrd3xk6pSsHsCTQgD46qn3iAfn1X3U7zh/+fVN6ypqD8cXg2eFMMd4PBcjzDE1c0KYA5gTwhwlPNeP8HTs2kAW/2Hr56/e8cFNr/XEfMTng6eCxU3+/5b9Vp2Qyo8ChgI+uuurX/fBTa/zbHr+YTzjER6PfLGQsJpEQyKPoLjguS6VIeXz/5kDzGH0Ep6JERrSfWZ1yh73xHWlmB9PcsPiGydv8uQxPCOErIgi9cn7OhpKEdAKk/cI9hieEYIlFd3VVZOOZ/Dx9NIVdhlMmlF4Rgg5/Bj5AgnNj4vAQWNQC6LyGoft3Ond5EklPBMsJnxB5icG2Gs0MqSP7gpvGxqO8sZC1+fCOx4hYlKlUgTlmLknSlGoMuhoqvd81eAZj6CLPBqw2O2gU69BKUFUpMmlod2tOej1lY5nhFAd76dLr6UtWo9b2hU+VbDI1sBgNFIHdJbXwvLiiarhjrrvviorY/SEYiMiAEj6wiSdCC3dXZ7dAXYYT3gEx7VvDYk8bbqPiUuhmxJJ9oT96fn/2tcQdp1XCaH/evO36idvJ1/hVPRYw60tP/L7MzJbl8jSGY2S8gVAKYQCrVTsRalOLrzi7aAEPtfFh0LL2+6m78/3xEsyTEUL4Y7qb8rmuCv2R6MkrNC4z2L5QTTX5L5jVrGxtYkHGmvp8fsQCqocG1fayvEHoqnPhT2xtcusFYK4ckgH3nh43/7Tvn7Xr6Nn79vc6qIfs6HmyNA+fyvRTJL98xcwFA0xf2cPmgJNSupSKcKFAjoF/vf8U/jtSesQaYfMmNjBciVhXHx5l6CSBKRLn2Fi5WwcoMp10XSNLsMgBSzKFZiXtxn06bhCw6dpZHSNmO2Q0wR7LR9+KTkikWFfVYD+kB+lJAnDUKYtBQUHQ0pVhXp277yqHoSwgfUUq+77gLuBtwOtwJ/VlcYDL/v9nMVC+O3ioZ43Pvqrz1OXS6HQeTh2InsDxRXN2aCP3pZiFvb5u3vw5R2a4nGi+dEkGa6ACz/+NtrGeIulySwrkpli8j4BT9dF6AxZaINZZOlWaUpxejJNne2Q0DWaCs7I9XlNI6frmEqNxCIucH8kSG9DGBkYM6zhSLAVKAWZAlbBYZ6hsachPLG4g8DYgPaD6krj+y/l/k1kVrYaxJVDhwNv/OiGO6jLFT13WouxN9Ayck4uaI383F8fRSlFJD8+U4qu4JUbd478HnRcVpZEAGAoOKIvhZa1R0QAxQTemwJ+DKDWGR9X+qREHyMCKGaAXOQ440UAYGjFyFUI8JvkNQ1/Oo/hTppVP7FV85nJd+WlMSuFADQBNKUTIwfymo+x7QHDHn1A2bCftiUNSCGQAvqjIQYiQRSQCvhBL15XZTuTJiaYShEqTG5EZLThJF3jEUw9uUHoB5jyMHxYFH9whJhKCBOpF9c5L2swO1sj4weAnj8sP6bhLVuLS9ViziB+mSOnFVPzhhJZtKCiPxwDQFeSvlCQHUubcIxisf25Am01VRDQqepOURBiUvPS1gTpgAmp8TsGt5Sqg4lCcEv/Jr5haUeBK2HsLjFKFfOEAtguQin8GuR8B30sf1JXGs7BTnohzEqPoK6L5YDX/H7lcQ9/4tQ3uEOm39bIcWr/QwRlBqEUgUKOizbcxbmbH+S0net57VP3Em/SR0QAkPP7OHP7fl6zo5MlqQy+gkOHaYw8XFfAxrowWAb1QuKTEk0pFuXyHJHJstcyuasqTK9p4AAJXWPIZ6I0jZSuYYtiWtBdlsmz4QDLuuJUp/PFJqwr0XJOUQx5BzOTZ77rsKMpCkV9pYACcCfwb8AOIAf8Dnjvy31PZ22weEDEv+j3RY6d35TcvcXxVfsXF7oRQI8/xIOL1tETahh3et7Q2bi0lQHTYr/fosdnggDlNxjQNRXO2wrbdh1Nd6OOE/e56teupv1Q6lqVo3FKStcIu7LXkuqxxry7Jey6Tb0aR3aFQ/d2+33ZN3X0y98uajAWpvPOnq/GVN2/D2gZUyfzlaoR/y8+mRTqS5GyPojKE8IYHtH+59u6X13h110ac/vZ0rSUJxvXjTsnnEjwiQvPpiVXnMa2rzrIoE9HKzgX2NdGbi2P5dPPrKwani8nyKs/XLDFzl7NT5WT5PjO9bQk20c+b423EUzZ1Pf2Xdvv19WQKfClsk51ztG8JAKocI8wzN3VX/3difGn3mBQQCBJ+sIUNJP1devoFfVEyTdcuPftveW2s5zM1lbDC8IaCry1x1f3hgcbTyAd8VFVSNLvr8bVDMyCTcYf8twg00QqumoY5iT1YWdLYBW2sMgbfnqC9bha8R1wdR0jJSfv5uExPCEEgKwI4U9PnpFmFQrEq8Nz09nLbcB04ZpQNZAjFB/tZvblHA5v7ySYy3p+pZMnYgSAWGGQjL+Glr1JCr40ri5otIeIqByma7+svXSzEc8IwZ+z6a+D+t40jtCozSbxIdldX0s6HJiLEcptwLThmrTPa6C3JkjMTaNpsKe+lu3NjRy7bYt3N4Uu4RmPkNMs6uNpnl22mK2LF+CW1kEevesZBgJVczFCuQ2YLoSw1Zodu4TPdumpi2Kmc6zbvZ14lZ8L2t7pzf17xuAZISzP7zn66eBhG47Ysw1rj4OOTUd1DflBmTj41ZWPJ7qYh9llXR3eGFka9yGE0BQy4XzsNbkPfKPcds0EPCWEOQ6Md1oNczwnc0KYA5gTwhwl5oQwB+Ch5uNUiKvit2OK89A1kFLqKTvmfKM2WW67yoFnWw3iyqFdwOLi/HVRnE0c0tGy7nHuV2OPl9u+6ca7VYOUi0GMLCxBCEg5yIDpyT19PCsEMdV6JCHALnhy3METMcK/nn7rnw7v63jt6sE94unGxew36x/ilFNHTwibECxNUsoUEP+VDKnPRdLlsbY8VLwQPnb6nwZP794We3zBGv58zOmEC1ku3nj3SYpTQUoI+SA8ZjpC2IJ4NonHvGXFCyHqurFH56/htqVHMahrSFHNk2e8hYCUZHUd/JPT8uI3PVc9VLzqXRHh/mVHUqUUCxyXsFQkdJ2Rpa7uFK0mqRAfGzxqWg0tMxXtEc5765P3pQ87fCQs1IB61yUnQNc1gkqSSRfAb0BpmTtSQdoGJR8FPDO7uaKFEC64p6TFeC+vKcV826XelWQ0jYIt2a1AhkpxQsYuZjIRYoo6o3Kp6KohnEsJlEIBrYNdNCYHsRQElaLalTTYDq4AqWmQc4v/NK3oHYS3woSKFcL/HPezvsXpPnTpsHCwk1018xgMxcadYwIGUzx0XQNDF+LTac9UDRUphH+58N5vH9e3t/avy49jRfcedtYWcytN9Y6H3AMse9SAdH73ITNyhlFxQjjvPY/7VnftuuIHR57Fd/7yDQ7rL2XgFwJnjBIKArJCEHXc4jiDUkWlaBSrBleCIZrLUYZyMC2DTuI6JwIsAnYCpwGbgTgwH6gBMsBCikmy6oG1QJZiouwosAToAB4CgsAAsP9tT9xT/66H77igrar+9WvbdvgGqmu0BUMDGK6iNpPAUJJ3XXI1bbFSlhSl8Cl41u+j39BBCHQUwnHRKGZLc30GytCKrQcpwZUSpVIo/oCu1QF7EWIIqAX+SjEHYivQpb5S1X+Ib+Uh45ALQVznvB+4DpiYPFDyEjySLl3esv5+Pv/339KYHOLid1zJjb/6BlW5LAr47omv5/drzsDRjZEEWS7wZNhPdsKf1aTEkMVMNi7gDie8kgosoxhAui6a7SINg8kptIBivqOvqq9U/eeLLVM5OaRVg7jOWQh8l8kieMl/29V0fnXs6cT9IX591CtYNNBLVS4LwD+WHceN687G0Yut4+EaoddnkhWT/6wUAkfTKOg6rqYVn3NpeFoMp9bT9WLrQkqmjjbwAZ8WV8VPfynlKheHOkY45VD/jXuWrGZHbRMZ3+h4wWPzV015btLQDtgslFrpM238OWo4fgDQBJpUB2tanvZCyzATONRC2HSIv5/Deto5d9uT3LTmBPqCEQCaE31Tnht25UhyzXFMfLAHetBSoYYnsRyYjQe3euZxSIWgrjSeBr5xqL7/xD1bOaZtJ6fv2sw1d/yeM97/3+yN1fL6Z++ldWh0FdvwY2vM28RsF0wNDA1dg0W5/NRfXmJEE46LJiVKExwgRgC4GZiV2wtPV6thDXA4xZbCq4ENFN+cdcBRFNMVNwDLKLYi6ijGbRnAKv0rUGx1aMCg6dg9X7jthtjZW548tT9c5T9p9xah6xqbq5v55fGv5dL1f+eO5cdy8+GnopRClLKqIgQJXcMREHEkAtji95HWJrwTrouuFK4mhm9QDkNXoNIg8hS75++mGAM1AW3qK1UPH4r7Nx1U3JzFvuA7qq555buHztz9FEd27eCyN3163OeS0ay3GQHdpsGQoRePCVHsQ9AE5GzQBOpr1Z7oa664DqW6zM/j1X3tzz7WtITG1AArunaOOHIJpMc81rSuM2AaSDF+7qJw5fCE1tzE769UKk4IAF946G2HVw12F/71vPfz6q2P0KEJujWNdl1jUNNICIEBxU6liTguouAUU+hr2uQ9hCuUiqsaxvLW1z+knmxeghKC5JgYwUWw2HHYYpnEJ4hBSIny6SCVUl+uqsgXZSoqej5C3jCUFEIEFQSUwgHiGigUOQHzbJeErhWbhABKjbYM897Kr1XRinfR+11VbH5AUfW1UlEN7Nc0hFKszBWIOU5xs4zhbVpcBUIcdPeMSqKihfCn3x1dL5BkRbHt6QJpU2MoYpExDToMnW2mwZDQGPf+SwVK/Vd5rC4PFS0EgPO3P0FGCBKaoNenk1LgpgvopTpgytdeKtQ3av9nWg0tMxUvhKZEv9OUGkJTYDoS3ZWEcxlQiqR+gLGHCg6gD0TFC+GTD7/dPLJzZ2plXxvV6ThVhTzzC7bKadroQNNYioth31gea8tHRTcfnwtx5ZAaN5w8ZpRRfd0bvYljqXiPcEDMCUUf6V305pvhXSEolR5XeqVAKJDqPWWzqYx4tmoAEJ9KpDG0YHFbXgk553L1jdqflNuucuBpIcwxinerhjnGMSeEOYA5IcxRYk4IcwAeF8KZ79j1wYvfvHnHOW/f+epy21JuPNtqeOfFG92oQgtIyaBpkEC5N/5+TUXPz3guPOkR3v7aJ+6qd6UWKC1zq7YdahX6m9+w6d/Ka1n58KQQpD94ekET3FNdxVNBPzt9Bj7HRdnya+W2rVx40xVKhztiEd62bQ+a7WJbJoMhi7Az9aJGL+BJITxjmeKdT26jYWg0/7ZeX42KWJ4VgierBoVGw1ASKaAnEiRv6LT0DpL0B/nqUb977jVwFUrFewRxbeYvuFzA8Gp2TbRfEk+yr6aKm45eTdJvYTouZ27dzfKePrK648ldYSu6+Sg+l4thy0EMrbgK2lVQkHzqzvu5/bDVJAL+0ZOV4pTOHmrTGXqj4fm//O1h7eWzfPqp7KrBle0EDAgaYOnF/wd1ArjjRQAgBEfve5gPP/oLTtuzYf/bLt1WUx6jy0NlVw2a8E+aiWRq/OKYIzFdia2P/+yMfc+yZLCDJY/9AVzVDys8EzxWtkcYfoy2CxkH0g5kHLY3VLMgk0WMqRZfu+0x1vXsGfn99H3rp9fWMlMxHkF8yQ6i1G0o9SxKvR8hNBRQcEtpu4ZVIRCOJKwU87I5lNBosB0INiKFQCuJYzDomfWvQIUIQXwhdw+adhpSghCnF/MgUcygOsUKFiUlt8QiFErJMebnbRS1fPWE13F05w5Ob9vCva3HccL0FqOszNpWg7jOEcClQBcF9x8oikvZYTSzjVLFlsLw/6EokGS+mFBz5MsEuiaKGdWAo7o7OG9/P82Duz/p1wr/fM/j7674zb5mpRDEdU4epXwju7M5anSnNqmKngBKn8ni52NxJaSL/Uaxgs2xyRTNyTShTI6Ez+CB5kbO27OLL9z5fzxTdxiduYX0BUM0yt5UV2NL1RVPvqbiFsjOuqpBXOdcC/jG7c5mUHrYopRCt/gjSoCcIvmVXszAHsnbvLOti1g2R2woPhJbHtPdx+6IRlDZnNr1OLctDBPpkNh6ILyAPZ1A47QUdhqZja2GT099eDQYHFkHP5wPaSo0wbpEiqCUBDPZcaNNuoKVg0n8rg3AcV1PkQ74MF1FRkYaXo5CzDRmoxAmM1WC9WHnrU1R9QkwLJ23bdlQOjD5nJA7ulC+oPtGhKIqtGdhNgrhsklHpgpzho9p2mhVMSb7+vyhfnY0tqCAnN8/+XoDXIrpdu5qPZFgtkDGZxASib0vUzlmFLNOCOpK4wYgPrJoVUqmVMLwMkYhxrQmFCjQHJcj+/rZ2jif3dVVpIIB4tEwBcOgYBoMVUVoTbYzZES4fuVr6XIbeGphDXp0qP21nVcsmr7STh+zstUwjLjO0QBF1pZo+uhbL8TImw9A1oHCaKB/dOdujk4JOkMREALLcVg6MIRRuheNiR7e9vjv+MGRb1mTskJ7/ufOkyp+M9BZLYRhxLW57yL44Ejif18p74GUxfghNzmIOL+jF0tCXi9mVdOkZOFgHx968Dcs693Bz499I+959C0VGhFMZtZVDVOhrvF/CISGxmdAXYTEQSqw1WiCrAmkNY2IbRO2bSzXJeA4fOLOH7C6Zzt94Vqebj5segtRZmZdP8KBUP9lKeCzpV9NAPGZjIuDNnEmouFKlsRTSJ9JwHUJuC4XPv135id6ANhXNZ/sxNzMFU7FCGFqSsHicOxQqjoWx1PUJ1KkQkEMpTBdh7wepKAZdIcb+POK06lOpmZ/nfkCqGwhKOIIqgFGpqopOGXXHpTuw1eKj1zd4JHFx7KlYSXzBjr445KlXLZ1l2e2+oMKiREOSMRqHPEGwxt2uIo90SgNmcmbeyT9QVQ3JE1qrr73tAPsA1iZVLQQ1JWGjc7VCDE62mixwfH5aUhOFkIon6S/Xqftu/MHp9nUslPZVQOgrgl+CfjS2GOXX5RQ3cEmNCmLKfYAlOKw9mdo1xuSU3xNxVPxQpiKxsEBXM2kNp4ga/lQQmAVCuz0t/Cpxy6Iltu+clDRVcOB2FLXoKC4c3wolyeczWG4Lk/Nm++plsJYPCmElKGxvyoy7tje6hjtIb9nheDJqqEm0f71hxYc/h+LI2GiuTzxgJ8tkRDze9o9lZF9LBUx1vBiOPnNG52uunrdJwRpBHVDA86G61d5qu9gLJ4VAsC/nfX7Y9pDtZ80pXPlDbeeU5HzDJ4vnhbCHKN4MlicYzJzQpgDmBPCHCXmhDAH4NF+hE+d+sCawWhwY0QVB6IKKIYEr/7Zrcf+tcymlQ1PCmEw4NvYURvh3pZ6EqbJkkSaM3e03Q7ezarmuarhPW/bLpygj78sbmbI8iE1wY5YmL8vb+X9r12/stz2lQvPCeHHS5p5tr56dBvgEnujIXoQrymTWWXHc0JQnw8pX6Ew6XjQdrB1GSqDSTMCzwnht/N+I45q76EuOz6d4qu37sVK2ZEDXFbxeC5YfPNl53H5zjbesLuTrVVhkj6DRckMKxMZOhxnXbntKxeeE8Jnn9rCruoYIcfliMHRWWmuYWAqeW4ZTSsrnqsarvnbCaogIJTJ0NTbR0NfP/5cnkAuR+AAq6K8gOeEsPoj3aLg2vQqxe8WtnBrSyMqk8ZKpRkIejL7LuDRYehLLnpK3bR04cjvPtflop37SBo6t/1xrSc7lTznEX5Z/euPPNRYj+5K6lMZqtM5bE3jqfoaMpZnJyh5K1h8VnyuO1LTXL+2o5eLn9mFLhRKEygFXcEAOxY0IK5zNHWlUXFZ0w6GJ6qGHeILr/Qh78hhkBRhtjY1kAmaMLZ3USm2NNWyYX49r92wTVXliL1r70WJ8lk9vRwyIfSIa6p0Mu8TuO9xsGpAuD7ShkKvcfEhUQRIo9DIY+IjD+jY6CJCHwJJgSg2fnxkMEnhYFEgjI9B/GQAcLDIEMPGh8AlRw0OPmxMwCWDQYowAoEfG3RJbzRMe6yaYNoGCX87ehl9zXVEHIe0qZPU4a23ryfqJNUDR0XZVNdCzhXyhKE2d2dNQ/ofrYftDEg3f8bezUNvfOaRG9+66aFfoW6a1W/UIRFCj7hmiUHmMQdfjRqpfRQ+8mgoDDIE6S8N9SkU7mjWMkaHAF0CuATRyGBgj3x/higmKUxyADiY9LAaiU4BkxzF5FgJ/GSxRq4zcGhkEInGHqsOiU4m6OOus9biGvrIeZZt488VOOWuzeR8gks/8lqUADfoK+ZjkgriedAEug7fueWn973/8Q+f9rLfyGnkUAWLn9DIjxEB6LhopaRXAYbGjPfKcWO/Y3/WyAI2+hgRAARJkKZu5HcDm1DJi+RLD14CWcY3Bx0MclikND+S4oNvb64dJwKAvGGgC9i/uJ6qeIHXPbEdV9Pxp3MlwwQETShIXF3nGye++lTExce/gPsz4zhUQlgiJ8ShYjRBMmJcYsQDe6RiNrypt14TE47q5Md9mxpZDz8eicAVo8U23Mlx4fBV2WAxv+KivjjAeEuGl9krxc6aRoClByzILOBQCeGvOs64A+7InxI4Y9z1c80FUYDEh5xgZtGHjPcSOapQaPhKx3XUyM/DaEgCFAi7uRHBtOzvx8qOH4302zYCqOlN4mrwuxNWFf+uOcZzFEpi1jXO3fG0BO46YEFmAYdKCN/ME/mZSYri41QodBx0FJAtBXRA6fjwz2PfaHAJlT43cEuu3MUgRQ0RekbOi9NEjhgSDR9ZLHJouNSSIEQGUfQD1DOEhsKHi/SDqwsMx+GkhzYzv60bvWATyuUI5go07+mjYf8At5ywmLbaKurTCaSlF3M15oobgBAyOGHPtsK/PXz7+1A3dR2iezktHNLmY4+4JmQx0Jgn3KTwpfzEhUb+8DTauxXBKo1MRmLFBHZApxBTGLYia/nJRwUIRQiwNYUPgUMBsEoSsbGwyAiFD4UU4MNHggFalEAhioGj2h2Mqb7qOu349v0ih580ARx0Ej4//dWhkQkqtqnTPr+W9oCf1s4BdjRF1by+7U89vXLhxk01rc8sHuzZMb8QzwcKTvu3jz57gICe/cLffhP51H1/2oO6adZnV/FEPwLAZvE51w9algBtvgZ8BRdXg2zIZO/SBnobqggmMrRFQnLV7s6TP7z9wkfKbfN04hkhDPOT5hvd3tZGLVSq4wfrwihNkBMaPbbDj/55kifHGjzVxQzQ2pduDdu97RuPXshQyE/GNKhNZdjj6gzOi5XbvLLhOSGcU7i84ycL/8hd8+p5YM0CAGLJLO+/5xk2e8w7jsVzo48AN556+IgIAIYiAX59yirMZLaMVpUXTwphIBqYdKwrHGBlx+SUe17Bc1XDv7x9t6jRJ1cB9ekcQptqc0Bv4DmPcP0vFivDFdSkRqez+2yXlT0JdtaEry2jaWXFcx7hzHfvFsI0WNWdJDOQwdYFkZyDlIKcYXWU275y4TmPcNdPFqsF3QMABG2XqpyDBlT3xdHyueryWlc+PCcEgLOf2EpdPDXy+/zeQdbuaCMfCXk2WvRc1QDQEwnw6RvvYEdzHbqULO3s51unHckix/lluW0rF54UQltLtXrcdcVxuzop6Do3HbmcWievvv7nk+yDX12ZeG6sYZj3n/Nwsj6TDeuuJGmZ9tfvOc27q1vwsBDmGI8ng8U5JjMnhDmAOSHMUWJOCHMAHm0+iq8UtgHLUUqi62fnP/6ODQpns4Yb1pFHauqmneW2cbrxnEcQX85LhFiOpoGua1Yhf6cUzqAg2CSJhV2CO3LiDd3ltnO68YwQVn+4V7z+gqfvR9PE2MWveZ/F90+8CJcQEj8OMQThBsTFnpq76AkhnPOmba3rdu2Xi+KpU3z25JnnBiksevDRg04ahzAFjLeWwdSy4QkhzO8b2nvJY1u4+OFnKJjj1zm+69E7+fBDt6PhlJbaxBHkyOH3zJJ48IgQNF0Xi/riaFP0ou6obcQeEzMXl9MVkPgy02hi2fGEEIL54trGnlh40men7tmKOWadpoZCwyXI0Kxe1PpC8UTzcWNrA3esW8Y/jl4xPksKcFj3/knna7gMEa5vmC4DZwAVJ4T/W3izqZCpWN7xFRTygdULT1zgN3l01UKWdA+yoz5EW33VyPl3Lz2MSzfcj0SjuGZSkSXCluiSzzXAF8tYlGml4oQQcJzCov44PtvB0TWtasO2R3c21ZAND/Gq9dt49aNRrr7slWxvrgWlCMcVmzkeG4swg/jIYlLAn7Y81XysKCH8z6pb9xybSBPIl3IkSJdGJ0PaMlm4sx2ABX0JfvW/f6QrFuILl5zKWx7aRJAkrTyGhkIhGKKWkOutOSoVJQRNsCCWGr9aSVeKmlSWB1Yu4J7DFqO7knM27uTEHft51/p/EkwJ5rN1JK2PQBGjD5OCaBf/oapp+31Q/faN5SjPdFIxQvje/N9tXJy3haTYFNrTUM1AOMCq/b08sbSZ608/euTc7c11mI7D2zfdSA/L0CesaxGAjxzzaGcba9+wSlzsQ900eZOHCqIihHDUB7u0t5rWmqPa++mPBPjtKWt5akkLAD7bIT/FNd8/9wSW3bGJ43buwUVHH5PXSSHYyrEs5lka2U8af1cIaqapOGVh1k9VO+kDnetDg+mjLr37KX5+ztF0x8KgFCYw3IeYh0lb96AUdY7Dhx75LW946qFS1jeJRGM/y+mjlSp6WMQzDBACrEeDJPs1eGqIiBPCnu8nvV2DtzrobQbiMzpuvYs4C4zvG9gdoM0DOkum5FE3TKXJGcGsFcKpH+jUXHAF8MrHtvP3tYuIh8YsblUKC0r9hCAnCMFnO8SkZN2O3Xzowb8iCyE0FHmCuKWcTn5SHMYj7GcRnSxHw6GGLpppxyKHGpP1qZhFTkPDpRhpOAgEYrTPLgl8FXXD5w7hbXnRzNqqQcKgRvFBDwZ840UAIARSKXRgXjxFZzSE1EY7Uo/d3cHbnryf03sfxMBGAb20sp/Rjd4MCoCihd10sxiJwSCNLGB3qX0x+hLpOLgl6RWPGwgcxqQQjQDXIt62AXXDLYfkprwEZnMXc2T4Hd+8uHHKE9bt6UQpRTZoEZESU0pqUlnedt9THNbeyym9j41kdBVAA22EGRy5vprukc+qKC6CcjHJExwngmEE9kgiz/H54cZx3gsu6TQwa4UwNs5f2j2A5riIMdXcyVv3ccXtD9MST6FpGiZQJSWa32ReIsWjq2pH8jmPJUgxuWaABDV0jvl7o6OWGg5T5YdUaIgRyw7YH7Xr+ZVwepm1VYMO6xQ8LYD5g0kuu7/4lm9uqad5IMmqzuIbvLyznyciQQCCuQI+2+Gat51JZ1WAX9zkxyrlcx4mSIJFbCRGz0jfgotBgtrS50n85Ck+6FHhFaMFE53CSNRQrBTGCeIZ4Mcv+814GZi1wSLASR/o/ADwvWWdfXz0zw9gaxqmHN8n8M3XncKeplpe/8BGTtq8B0MqNi5o4Kp3nM0VD97ONf/83Ug/goOBMWYkUpbq+w6WMEAj1fQQo19liAiLnAqTREMKhYZEqJJwhARboP6hw3wQ+4CHgN3AH1E3jFfeDGFWC2GYEy7f1/Xxm+9vDBZsatNZtFKRdtVVYVsmjy9v5dK7N4y75h9rF/PpfzmL5vgAj37zalqSQ5O+N0sQizw20rXUH2at93w+VEThHvnxgqb/vStY8LvC/O15J7C0ox9b16hOZshZJmt2Ts5/cdLW/dQkMnTEavnp8Wfxn/+8adznEoGPPH3MI0b3B6erLOWiIoQAcHj3UNWt61Zk4uEgm5aPb0qeOmZ/x2H6wwFwFUe37eaCZzdS3FKgmH89j0WcavpZyDx2Kp/6zQ+npxTlY9a2GibyqvQ7s3lNFlL65CL9dd1S5JiYLWWZfOZtZ/CmLY/x2Dc/z9rOLhQBFEEU0M4ycgTVMp74Vkz9oGLu0XNRMR4BoDZbOHxHJre9L+gn6rooBL5khoRl8qvTj+LVj2/hu+cewz1rF5OxDO788c0TvkEnSxWDNBGhB1P99qPlKEc5qCi1f+GJs3as3t1x9xGb9xLoHmLN0ztpauvuL5gG5z28hbxh0BjPUJfMELAL1GVSk74jTn2ps2hGBveHjIpoNRyM1124Sf3r3x8lmiuOJDua4Kp3nMNn77mRM3ZtHXOmAjIkieKQW1+rbjimLAaXgYryCAfiqLbuEREAGFLx3n+s5x1vfQ/3L1pWOioR5NCQRBmimtz/K4+15aGiYoQDEcxPnnZWH0+zv6qakD2EoNiqGNsHKMHxxFtSwhNlfXLB5EGpew5fCEKQNq1J24BJBA6+hZMuqmA8IYRBy7fh+lPWEg9YFHSN245exjcvOAGASH5yRnaBQMfdMd12lhNPBIsAZ79xy33RnP2KpkSC711w/MhClx/95ntc/vj4DdpyhPCT9qNumrEzil5uPCOEYX7U+geVtnzccPoawrkC5z/9NP+26TcMJ2Z3MeilTjap7+oH+aqKwhNVw1j+98ITL12/pJHzNuwkms1z1Rte7W5lTWc3rXSzgD0syXlNBOBBjzDH1HjOI8wxNXNCmAOYE8IcJeaEMAfgkS5mcW3O/Nb1P98pDLPWr3H85Zve/Uy5bZppVLxH+GXrd3advmVbYbC+rnVpJh5c1D+w6cZF35z1m3q/3FR081Fcm9MX9fQ5v//tTzi8dzSHZrc/zH0Ll6z7ly3vf7qM5s0oKrpq+Ppvfne7GXDGiQCgMZeiKp1eT4WX/4VQ0VXD7mi49ZKnN035mSs9u9fnlFS0EJb29fkzdpQc43fpUSh2+8OVWye+CCraNdZl0+EeGhmgjmY6CZEhXZqpHHXcin4JXigVLYTaVKFWEqKAzh5G55k00kPMLsx5hDFU7Ftxxjue1FPBqPBPmI0scJnHdo7s3Fsmy2YmFesRzn1iT41rmsxnF4NEUJjU04efDH7ymIas2JfgxVCxN6Pb0rce0dsFCJaxhWxNnh8ecwx/Wn4Eaa0OTejiHefeWV9uO2cKFesR5lGojmZzbGcZf169lA+86pKR1Dknduzlp7f+jtVdbT8HXlNeS2cGFSmE39d+9/tvyO0lxhDr6OVnza8dlz/p4eaF/H7lEcRNva2MZs4oKq6LeZ+4WhTMglxmj+9N3BGcT8YMcevKFTzQvJC6ZIKIk3zwW3dfckqZTJ1RVJQQfrT8p8cu2D/02CvsTQTd4gTkISvAgNZIIKuhAQnL4oOvfx1Jn0Y3WnbPz1cHD5U94iu2CVQBQyjqgQUItgNx4FjAVleZ6w/V338hzCghPCa+bwFfAy4HTMABTDUS1KqRzEXDC1J0cjRoHWytmYeZ8uHPCQ5jI2FSfP7U87l52THc+tPrx/2dzkiYYz96BZc9fheXPfMYXzjlIkK2zSMrF9IfDKOjKOjmmL0dVPFnIcanThp76ybmzno+ud2fz62fsPxq3PHiZ3YpuYMYsU3hlhI8KmA/8GdgPjAADAHvAwJAD/AOdZX5j5kmhD8Br3t+ZxdFsZit5EOSLfJwgtliCswa+hhsKXDS5VfzqTv/yUfvf3TS1cd+5EPstwJs/vHVuIbOmg98mTN2buPuIw4bfeAT0SYclGr0YU48f+K5B0KqSZuJMPaZiAlfPvyZNuHY8HfIF/U8z5kxzcfHxPfnAa99IdcYFKhmgFuXHUsoW0yApxD008DvFp4KwHm7Nky6TuCyZKAbHMkjTUs4vLedYzt2cc/SFRzT9gL2/nyuZ/18X7CpvmOsMCZ+zcT1eRPPf3F8bMYIAUru7QUwfI8Cbm7kUgF0R0PM6y2ufl6a6MIiPeZsSZAU5219FpTiqN59AKR9FkIp1AuxYPgBTOsWH8/xx168d7dmjBCOUx9oAx58Ide4+BikhvO3ry8mZaa4OddDy1vw5QsYBYe7F6wkQIooA4QYoooBTArsD0X4xOO3s7ZvP7ctW8fm+vmcs+NZ1s9/AXt6Dd/4F3v/lZr62klVw0GuGT7/xXuG7860GCEG3AicQzFAlDCSLY+JwaKgGCzW6L0k/CaJbDM+qegJB3j/289jKGgRKWS585ffYMlgnOG7uqWmnp+tO4JT921hyKrh5+tOozaf4OHlS+kNR3F1DaVNeEcmxg2j+bhLn08ozMGeydjY4rkewYH+5mgVoUq3hTHB4thcnyngXqCBYrDoUry/BpAB/lVdZf5oRgnhpfKI/t8/2zZv2TseW7aAW1Yvp70qQsEorl575c7NnLpvF3titWypnse/bHqALs1wP7f+nS97p5r4qm2iWIbiRxSj9Z3AIiAGZIFBoNi9LbgOxa1o7EAIW11plOWBVJQQAJS4TN8brHE+c+aF3LDmOGxj/DLG6myGZ7/+LTqiJl878eR7fvW3s84oj6UzixkTI7xcCPVLt001bLhs4/34ncmTlS97+nEMCmyqbeGYzt2BKb7Ck1ScEABOzXz6aH8ySyyfYuHQwEh9euq+nXzyvn/SHfWzqq+PIcv/AtqKlU1FDjoBdLu16bZoLHT3z79HfSqF7gga0hlMbJ5qrKKpL01nTd2Xym3nTKEiPQJAOFvT3JBJsNdfTU1cEUlrZAmToJqquM29C1r54d/OnVvXUKJihfCqwrsTr31kJxvrmkv7LQ0jCNpwwr6dc6udxlCxQgB484YnqctOTpZVk80RzU3OuuplKloImUimNxkwJx1/aEEr7bHqMlg0c6loIXQFI7LWTREgObLXUsJv8PmzzySW8dbezwejYlsNAIbGvrds3NDoJzeyd1N1Dual+ukPWWW2bmZR0R4hEQ0/GcsXBTB29HZBfJCCplX0Xs8vlIoWwsfOff0H+wKhccdcIcjpBgR855fJrBlJxY01TOTHS79TeGXXfrMxk6IvEOLORUtxUyn57n3/7rlcis9FxQsB4JuH/+RN4Zzz67xhKL/MvOld2z9808Gv8haeEMIcB6eiY4Q5nj9zQpgDmBPCHCXmhDAHUOE9iwCXvv7pa55sqf9sT8SP4UhaBuKusPyRx77bPHk0ysNUtBD+49WPuem6Gs2UGvMTNi7QE4roC4biGaZ5NcJMp2Krhk+d8/B9BcvSUprGyv5BajJZdCDmKlwhWP7hzhPLbeNMomI9QsoyX6HnC5y3czRX0qaGOp6a18D+qgin7N7/RZh3VhlNnFFUpBDOesvm68PhMK/Y38lgOEjK78dfsFnZ28/O6iqWxRNIjdpy2zmTqEgh1Lvq0upMio7aGgai4ZHjQ6Egx+9tx6cU2wNWVRlNnHFUXIzwzvOfSM2LJ2lJZ+kfIwKArN+iYShJQdfQXdlaJhNnJBUnhK5IKDQ/k+WeZS1TNgtcw0BzFXbIrLiyvxQq7mZEbQdHwKbmGvoC43MwFzTB+qWtOJZJvKaaq064+8IymTnjqKgY4YoLNlzlhPzYpklVtsDdi+s4tn2IxnSOIb/J+voq4prGq9p81OZdGgfiNzPXnwBUmBA6gtaXl2fzaEqxbk83260A98XC0BgjICSBjI1r6WwJWVy6YSfzBrPcrX9L1pATHdURXGnSPNRvF1Sk+gT1wXS5yzOdVMR8hP8895FTh6S61xcM4FOKBIofrViMrY/WfA2FPOd39KFLRYeCD/xtA0K6HJ3ZiY88QZI4aBQwGaSGAuppg9CRK9XHFIASb9IVwtTUb3IHNGQWM+uFcM2rHjkskMk+0xONYhnFB39fdZQHm+rGnyhgXTrNeXu70TN5TnykuBn8vFQvq9lKD60U8COQBEkQJs0QVWQxcTHQgRR+qukhRJoMMRw0u4aBhfXq653TXOyXnVldNZzztm1nHJ9I3WWg8CsXqQSaUges9e1SFhRpjS56WSDa6VPNFPADoNBIE6OPWiLECZEhToR57CJAkiwNGICfJDl8ph/Z0S8+hkEWHwkUSir0pJ/sO3X1+5sP8S142ZhVQvjR8j+G+6ujXxqIBt8X9/sMK+gXVZpGPGChDAMhBEoIDktleaRe4o5Nf2PoHNk1AEAgXUzGqUmXnPLTQy0KgYWDn+LCl1qGCJHGoMA8tuMSxqZ+5IaZOBg4OAhitKOXrlOgZYhV5eGPlngzEg2d4t+TgEtQKYTQyCsDp3SNwCWAQkMnjVbKj6PAFSC14nPKAbsopsLpBR4DtirEMoVRrdDv08k1UszMYlFMkXMU8BDwGdRNz+n6Z03V8IOVNx8/FAk9pAI+TS/ZrFQpeZAormNKhYIUrGKTscNv8WhtFT1+H+gar97TzpFd/fTGohz1xG7CqRzzMv3Y0mKsCwmSx49NgAy+0sN1EBgUCDN55NpBAgXC9GFQXFerAJcIAoFGamSV1dhEocO/K0wUQUYTIoFGAo0XtufUxO+eQA6oQ910wAB4NnmEn6ViYS2ay48cEGOyiGlAOJNhwFfMmNqcy3NRew+a61ITT46cF0imMDMFNCnxSYk94fblMQhTzKMwjIGinSZWsHvSzU5QT5YgvSymlWfxk0AAGhnAHBEBTP2gFIFxnxTzYfkp5rl6/hykDewHfgFccqATZk2HklBqkW+KVDhj0RQY7uhbqZRCCUHeLMUEStHcPUhfUxDpUyMueDyKAJkpkqQJUgQYmwKtgI8sxew7EoN+FqBKt1TgAgdbeV+UzGQOyZKLo5/rw1kjBKlpT2fN53ZgEkgZBv0+s7jETQiUppEMB4vxghC4poEhJd21YfzkmZjbLm2aDIjouGMFDPL4GKQakwwGOdIE6aWBse+ijQ9FUXTFN/tgDlfBlFXAIUndcNtzfThrhKBJ+eaavngmZxRvrgQSuj6SbFIB6WCA++pi1BYmrHQWgkJJRAW/OXKsK1hFlCw+bEwcfCJPygrSZ1aRxSKPQZIgXRSboiGypKjBxkQhmeiQg+PiAQ2wkPgnpWQcMQsQpBkVw3CCxOc/i26q/OBT0A589LlOmDUxwnu3vX4vEPrm2tteua+h+lsFoa3YMq9OP+3ZXfQ0N2A5DmHbZnMsymkDcawJyak1V+LL5jFzxbWvPsehJptDRxEuRfVKgSFddCUp4CePQR4DE5s6Uli42FgUsPAxRIRBksQACBOnmg7ARiJwCKJh42LiEkRQQCOtBAZgKwFIhNBxHUig8AlAChxHofIumKB1C2RaK+Zk3AncQzFHYxZ4CugHwhKrWWE8rJNuoZjL0QTSwHHAQ6ibHjnY/Z01rYapOPfNW9YYSm1clCtguZK6TJpvrFnB4myOM3sGRs7LCFi9bR/mmBijKpNlbUfXpO/c76+iMZ/EVMW3VOASJIVO8Q2WgERgIPGRIUQ7LgFcTByEY5Df4sO+1SLzKdRNs2a72VktBICrznvU/0hddXaxqzh8z342NtTwq5WLmZfNsySVwRbwmq17mNczOO46TUqO3bcfa4w4bE3jA++4gNc+tY2zNu/AX3CADNVuFqPU2jfIYpJRApck4Rub1bfeNs1FPiTMmqrhQHzlr8fnPvza9c27LN/+mqqwtq67n4ZMjk21MUKOw+K8jdQnR+FS07j+hLW8/cGnMJUkY5r877knMRQKIlybVdmnXxVRP/rHc/3tyCEr1fQz6z3CWN7/msfk/uZG0RE2OWVPD5aCvGmiXJcjN+0YF9ql/T6CyRyLu7ucqHQM03VRSJV3xc0nqf+4uGyFKBOz3iOMpTWePrO7wb57R109a/oyNA8M0TAwhBKCrqZa6vqGMBwXx9AJ5Qo82lIn37vnksnZtjzIrGk+Ph/+84Ez7jm8rYuGwTQN8QT1iSS6UhhSElCSruZ6umuimI6LBDYsXzQ3k7lERQkBIOszqU7lqUpP0RZXirrBYndzXtN57GcLh6bXuplLxQnh6ZpYR2MuT96YIkCUElfX+Mea5dx41Iq+Mpg3Y6k4IdzxqxUtjm3zVGMdcsyglK1p+DI5nl4wjx1NdUSF6CmjmTOOihMCQFiq8/fFIvxlxRKeaajjyaZ6Hm+sBwVPLmxhYf8Q/QHTX247ZxIVKYQ/3LjqtqUd3SriOgyGgugSIo7DQ4ct48juXgZCfqTQ2stt50yiopqPY/EZ5pvTjv3btYnRcf2mwSFymkZdPkeNZnymfNbNPCqqQ2kil77uie3xqqplh8dTCIr7C+8J+0npJn/95dK59QxjqMiqYZhf/fmY5dF07tSnApbcEbDosExShpmut53Qwa/2FhXtEeZ4/lS0R5jj+TMnhDmAOSHMUWJOCHMAFdyP8GIYFO/zddN6p0XhxBxWt0P4qCPUv3miK3qu1QCsF9860iK1XscVCp0gWaqIk8VPnJrTV6lP3FtuGw81nhfCRvH/rAxmroCBhkKU5jEZ2CxhFwUs1ay+WPFVaMUX8GCk0Z8tYKCjSusMFAKFg0GCKDoFT/RAesIj/Lbx14c5Lht9UmlCKhRCGq5acFHy0vYHxPdcDaG5iJEp6zC6BqmBHvzI41vVfz9WvhIcejwRLNouz+DXyWvFx6w5UpNZd/9j4vsaKE2io1H0BsMIiiujHSxs4o9Q4d6zogsH8Kt5N16gmRpoox5eGhpCE+wLhob8OKPZCCYgUNQTJ0pG9Ip/+89pM7oMzHqPIK5ztGA6tytjmQvQS09biDwQVVcaBeHK/1ZTLZ7VIB3zRckUL5GISaujQyTRyGHgMEjwWuDzh7g4ZWNWegTxpfz/iP/J2tbn0gql3EzIvxBDFwgBxelpFq7MATiaWC6nKKVRkIQTBTL48ZW8goakmMVEUUM3y9hINfvxkyRNWPSKj3x4Oss5ncyqYFFc52g4Th5dN0oPHJ/tUDD0YQGMohQIsfpH37zpbk2IxlDGxfYXFeFPOwQSDgJJ1C3gIKiiuCC2dDEL2IwfmyGakJgMr1gWFIjRtcSvfrZ7moo9LcwajxD7TPIzOI47VgTAyHL3SQjB6U/tfjeSlD9jExksYKUlekHgy7hoSiFFMTKoJjtGBACCTpYwxLySCIBSLwP4SNC4Ky2uqKjt5meFEMR1jogHrf/GMCa/+TD1MaUo2Pl/0aUMGLakbUUN7ctr6V4UY/cRjaSjFkKBhU2utPx9vG8UyAkhlCgtbg8SJ0lkYI+4auPLWc5yMuOFIK5zzkNKifY8TFUKoRS1iTSxdI6Bptp5oazTkAv5yEZGd4dXmqBnQRWGK0kSIEWABAGGCI6IwT8ugcUo/dTQyzzq6cJEW7NffDibFpfP+n2mZ2yrQVznhIEESonnIwLTdvjQ3x5jWfcglusyGPTz2JolOKZhOBOScwMULB19QsYTB72UGMPFBSIMkGQ0cWcGHw4meSwyxIiSp4Dlb2PeLWFxFWHitp/CCr/66Z6XWPxpZ1qDRXGdowN1wMeBt1BM6VJNMSNImJcoTKvgcO3v7qI6XcySu686yrKOPkyl0dsSG3duIJln6ebeSUmzLAq4pe6lOnqppR+BRoJYKV5QBBmigTZsorgE6KQWizgNtONDkiOIS5AcAbKYxBgkQB4XoTL4XRu/K9ANg3S3TfB7LkbEIH9bnqqnQeRM4scqZFuLum7PS7kfL4RDIgRxnRMEfgi8iWn2Om+4/xle+8Q28n4dKQR1fRk0CYmaIPGaIKYtEa7LvL0Jwqn8JCEMh4SUxhwixDHRsRifl6mR7UToI8M8XAwitCEJjatrs4RpZyFpLFrZThibbloZIkyMJEFyGNjk0AiSR8MlTZg8odKsax0DB4scIZIZDRmU6Nj4cNGVjmtb5NuBawPqWz97KfftUD2kLwNlySTSHwzSvD9D3tIYrLWwshJNQV1HiprOFFLXMGyJ7siRvoNi4itFgAI1pJEIcpgEyRIih4OGjY+xybOKVUMfPhLYhEnQSJTUOFv8pIiRYIgWdnE4DfSyl3ksZx9+irmcFDp+XCzypTbJEEkkWSKYFFdtR4gjIAig4yLIUyAgJLrPxlxsYv80Kz6yLaC+9eCLvW+HSghvPETfC4CQirfc9QznPr4TV9f480kr+NMpKwFY2DUEgC8vqe4ffeMFxTyMQzXFeCE6UEBPF7BwkAhC5EeSaum4xIiP9DSaFKPqPKMBp4aim9WlPEo9JGmdJITh0UwfDjl8DFBDgPyICIZR6Eh09FJaPYss2VI+lgDZKbzWcCYnDaeU7Au4GJhxQugCGg/Rd3PRg1u57J+jLbf337qeobDFPesWUfAVi6TE5GykAtBtiWPpJGMmVs7BcTUkOgFGs7Oa2JO6m33kUThYZNCQRBhikAVksQBFljDumIcJYGOVsi5Z6EicCb0V4xn9RI6pYNwpGnZqpDTjyth9wK9+Hhyq5uNnOGjqvxfPaU/vO+Cx351+GImgD6EmG6CATMgk79NxTI2CT8MtZTmVB7kVAkUrm2hiFw3swUe6lF0VslQDkk4WkSWEi06OEEM00kXtSBXkJ0MOq5TBdRQNB31MvuYso5uSZQlgT3hfXUyK+d40fEUvthf46XMW4CAcEo+grjRuFtc5xwCfBJYCIYoZwxfwMuSXTQYnNweXdg0QyNtkLZPOmjCGSJCMmMQG86UsqBCvtpClPR1sQwNdG0lmnSBADcWlcTbmSJ09jJ/EOKno2IToJ0UDoKhjD92soJNFCBQ6DnkMNCQ+bIIkWMIzDFFPBwsoYBAhjY8CWTRyRBEIkoRRpdlSGq4ycVSSqq4IiZ8L1CkO5ioXw1SwxU92vYbcDfwsoL7V/1Lu6SGL6NWVxgbgzS/kGnGd8y7gfwAD161B04Rpu9jm+LGE35+6mqO2d6GPyboasQtc/NBm/nTCKmrzGQohnU0L6zhCduMaOvmAMSICAKVrpTynxW+w0RkghIVNlPTI90LRGwTpnWSvMRJT5OhkMdlib6QKMKiyhInSRwNx18Vos7H2pKm+xcL/jaPUf6gHxA+1peq9k3qsGl7IDXsZmdGDTuI650KUurn4y/ga/ys//gcr2/pBgGNqKF2wuaWWgs9g7d4e8obOn49eyusf2kKiNjgpYNBcRcveBJbt4sMd9/FCOjAm9CqG6CMyoRruZz69tGCQIcyAYxNpaFWfH5/QcZYwY3sWAdSVxp8AIa5zelGqbqwYtiysY0nf0LjzU34fx+7uImfq/OCco9lTHebV67f1I6hVQiCGRa8UjqmBLtBsNUVQOfnlkPiQGGg4KATx4r5PNLLtnGr1wzte3pJPPzNaCMOoK416/Us5JQ1jeHiZm05czbrd3bQMFKP9juowvzxjLX86cRX76qvIWCbBRHpT3rKeFUK8SRpaaSyiONag2xKjYDNVTJskSIyxe1xIAvTQR4wCQVSx90HVMXR4SP148/TchUPLrBACgDQMK5jJpTMBy0BKhsJ+/v3yV3HE3h5QsHFRA1LT6Bi+QCkyQf/X8rp+jWYZaFIW0/aXPitYJo7UiZBE4hvZZwEgTohmdlMgioZDiB50CvTRisQkQndmofpaRS2tn/Gjj8OoK41C+pqwiRDfQtOw8gWUgqcWNfLUkibk2IEppUAqMPQbzbzjV5pGIJnDSuexMnkCqTxWroAGGGjEiKPjlmYouehodNFMiHZq2I1FGgcLE0ktHZ+tNBHALBLCMOpK46PqSkPkLZ9hue7XFrX3KtN2RvZtGEZ33UfUlUYuIB0ZSGXRAF0qdLc0vSRfnJ6m42KgESaNiYuGQiLI4scY3bCLDNW0sjUyT33jM9Nc5GlhRrcaXgjBz2WO1aX8qyuEyAat16grjUcAftr6m6csyVrbN777QkhF/b4UTSQxkSMdS1B88DESVNFBiDj9zEcn3t+gvjdhM8nKYdbECAcj81/Bx4FJD0oZ+jtF1l0/HGQOE4zbgPq5C+/QJjhGARQwSdPAIK2Y5KilZ8UhLkJZmXVVwwvl3bvfsCFpkIj15/CnbayMQ6wnh563B1+t3v3OAsaUPtHARZa26kgjnvSpnwxMcVrFUPFCAHjf/jdX9VT7/tNVuEZBOq7Gx16ffUcNgElhqHjWqBwMHIJkSZeaiqvUfx9VDrunk4qJEV4sD4jvHx7E3iQojjr6yeHDJk2QAj4scqxUn6j4hbCe8AjPxSnqA8/kUZ2UVkDHidJDPVkCGDjopOLltnE68LwQAE5UH23WsA+T5GU1fYRJEiaJnyE7Q70n9nTwfNUwFSnxnlABv1Ojvp0/+NmVwZwQ5gDmqoY5SswJYQ5gTghzlJjVQugVnzZ7xacDBz9zjoMxK4PFNnFNg0GuO0ukNJ9wiCyhGxepz7213LbNVmalR3BRXRKdKrqJMIhFgTCZt+wSn3XKbdtsZdYJoUt84l3z2CN0HAZZwBDN9LGABDXU063vEtdOseHjHAdj1g1DB0j9pJ0VSCxGpyYLJCYDNGLgzu3e9iKYdR4hS5gAKaZa0KYwAEG7+PgHDtXfF1+yLfE/hSPF/xRe8kKdmcSsEsJecfUtBQKEmWpqgELDpYBJiPh3X86/++qLN95xyWs3qKVXtCukyiHEBqRyjM9mtr+cf6eczKqqwcE8P6VHiLkCkxw2w7WAwkcBiYGPPDruix42/vZRtxt/W9p605b6mjNsuN2wncWnpzLH/ez0I5G6NrzEGYSGC8u0z2Wvkv8V+MrLUsAyMquajzuMT6mtvtUsyHdSJwdQ+FGlxBY2FjYaJjmidFNNbwj1+8xBv3QMXzruH75b1qzIK8sHQuAiebqpimzAR2kla3F2dNYtzZLWoOBAyGeoq33uwb5/JjNrqoYucdVpQ1oDPmVjKZcALmFSREgTJo2OQ4gUTewmi480kRe8+mhDTSyn/BYIgRTwVGst2ZBVTN+rl3LxxQuQd8GWkHXABXJ2Tnw6dZv4dHrWTnOfNULw0/fXiJPmmNzT1KnBSfkL/CLN43XLuavmVNJWgCz+41/I93/0pH92a8HASJUyEDBxNcGiniRr9g2ytCuBkbGnWGuvIC8NNPFqfCIlPpmclXHDrKka8uJSJVAkaS2uUWC8J7aFzj8bjy7+ohQnDzxKwY5dtERd86eJ3/Xxsx76JKgvKE0XfQH/ox2RwAlJXSMdCBDRdVCK3oiFbktChdG/0+436bImL8nHEEVBaKJYXUig4MxXX4zMmv2nZ7wQlLj4sD5iT1aRMzUkQywtpcK0xzUgs/jYHF5ET7gKgEWZvaxIbFEx9ZMRr/f+89dfERsY+rYMBNClpD8aJhEKcPjudlp6+nm6sY5kLMJh7d2E8gV2NdbywMolI9ld80KwKRpgXNNVA/TSn3BlKZYoCiNqO5+JfyX22UN7h14eZrQQ+sTlf05o1a8NSJ0QGSJ0MMCyUiIJt+QVJCZZAgziorMhfAS7QwtZG99MY26IbitM2B6674tnvOmI1Z3J2P7qCH1hP4t7h2iOp7jlmJXcecQSQrk8x+zp4cKHN40T2I7GWv5+5OqR31O6xv6Aj7SuFx+4GLNeQlFablc6WQOEaFCfD01OrjDDmBFCeDLw+UDQSf13QRj9EZU+UTn5M2wtWiOVRp1KoREoJtBmDzZBkjRDaXFaDfvGVRPDy9NEKYdynxnhiZpV5AwfQyGLG044nPtXLABgdXcfmxeOpnp6zRPbuOTR8YubpYAfnH3yuLWVu6oCDJqlldml22e4kuqcjc+VFHSNQZ+OU7rGp4nv5L8QntGZ3Q95P8JfwtefI5BfNVXhsLBK6GFSIqlHRFyL4ggTv0yzRGbp9M9nwKhBUy51di9Ls50YSgNC+BgkSC8FQvhIUsN2coQZYP6kWEEAOhIJDJgRHqleAwgMR1EXz/HuB54m7fexYUETm1vH5yfR1OSUuznToDdikfUZBPIO0axDTi9tBOKM5ltoyIwulQm4EjMn6Qj4QNMoKK4Qn05fUaw6FIANDCK4AU37b/XFcOJlvu0vmEPaargldP1bhVJ/F0pbV+UmzFr6tV3B5aLXaqJgBkHXODr7LANmLQNGDaCQmk6P1cQT0cMwtQ5i7CZALxINo5TXSCCxSBMiUcpXMIqCUlcz7As0Tsq0YuVcTtjdwURa++K88pndkxoFtxy9gj2NUbqrg+yvCfL0omqyMT9YWvG7NQ1LMWm9lKHAp2DMHhLFWKIYT5hAA4p/Q6n14lMpkzJzqJuPnx/ePm+JvY3t/pXjPvTLHPVOP91G6c0cm35fs9hjLUHHQUNQTIJnkyVMD4sZZBmSMEmaiNNSWp5WFIEqLWidnAulOCKRsor3XbNdUIpgweGtDz9L0HZxDQ1XE0hN8OjSZv521PKRa52xuZzs0dhATpUd/kDHJx9bCpQ9qfehrhqiwz/oODjaeOEPJ6cIygwZPYCckHAtp41OPipmPqujg5VUkRhZsg6qFC5G8ZNBoGGQxcFPa66HDn/duJt/wyvWcMtxK9EcyevWb6Mhk6eroYGGeClDihAovZRQY8w+UEIq1JjfkaMewNY1MoZO0BmtpjK6hqNPeM8O3PFd9r0fDrVHuGH4h369kfrC+GRUWc1Pl1HPYbktiCnq53n2qAtXQB+tgBiJCwQuJnlMCriEcLBQCAwKmKSoKiQ5fGgvISeLqyu+96qjueW4oleShsaTi+eB6UdD0BeLTvr7zzbWgiPR8i7zBif0Vk94yH0h6/+3d+bxcRVXvv/WvX1vb2qp29q9SZZtLIwBYxtjA3EgEBww2RxIeHkwvM+EGZIXnGFeCAESIPDGkEAIJE5mYPLCTEImCQRM4LGZxSxmxytgvGDjVbaW1tpSL3er98ftllpqSZZtrTz9Ph/4yLfrVp1776lTp05V/Q6NPo2YptLo9RD19WLtMzx/2ZAyBTyVW3h4MaSKIOA6R/BncJxdnpkUpeopMw6hSgvdToKE9wPV+O04Z8Xe6/xNcwyqE1uZYhzorMvlMnGDOWaaJT07luAeZQ9hIeggRAOT6aCAsJFEavDEwmpePqWqm3x7i8M0B1xa3XdnTGXT1FIcAaaisLa6gtdPrHDDzR6Fw/l+gsksYm5vVvdOzx46PCrNXo0OXUv7A6JLOI9wp5VZVgPBIYQ4X96RN+LTyyEdGpZ1XG7iknPnEHQ/W/BgQFVMb8RsKZNO8u4A8YtO7WglKTwIED6Z6lxQkkg6KEKkJ+jtBNEwyI3xCVJE8NNMEyXoWER9fiqaosyqzc31rdoOSVVBBbaUh1lbVYLPMHGEcKl8s4YUqQgKW1JMbE1yMN9HwqOCdMDosdakpJ3DTM/XFHcYsZyrUNU/yZXBUbmDasSWoS9s/fs4EAeagYsz1zNewX5xU1mIfTVeUPwkyKeeeioQOEgEzYTx9SCsznAUmwQp5BAdaoBdxdPZUxCWloWY2BjjUGGos/z83TUcyvMxJekQS3M4J/W+HXhVSiIdBtJyUByHpBDUaR5S2fxNjuziljVtNNu5yLir4Nljf1PDg1ERUOoPNcp395oyr6KQOI2UYqEhcFCwyCPeSZXb9RQKCgZ+YiTw0aIGqiZb9+wB+I8pf06+cHq191CkgHm7apjS1EZc1/nohEpemVbKoVDWLjdBN4ugWzYz6mOYCLx2lz9jATsCXqTIHgbc9QrFNPfad0WmDeHrGTSMekUAaBQ3CUg4GgkM4aVIHqaB6djoaaWw0zT3FjomOm2ApImCjRPlz+dn1/W7qr9+MSSST3xUWSFiPj+euCE7/AHRFPTz1zkVWLr7ETEcsB00VRAxLMpjSXZGgkxv6siRb79Xo82TNgNpPkeSxtvyF4WLh+P9DAbGhCIANIprpCqS5MsYCpJaZtHT11XSGZfAIIUqy+S9A3KGfz37ydJXp00+9FFxRPmoLE3DJEFxJEUpAxVoiPhBUZjVEMuZBe71a25swqPiMS2JKS8z78p/5LgfehgxZvYjqIj7o56yzn0INrl7RyWCJAE0EijYewda9zUffanu0bmztOpoei+k7RAwLIpSBgKIBXUs3YOlKrT0mBYmFEG7T0OVUkZiiefPirarY00JYAxZBIBd2s2yytpFPVOIUUSARGeeAzsdX3BQCFFHC4XnTJErXz2a+m885/Uzn5sx5Y0tU0qQUuKLG+BIknk+1+QDSEkkYRI0LJJAk0/D0ZWl8pbA84P9vMOJMaUIDcoKR5ceUU85bjpOULCRaU/BRyKd2ibGZHnnMW1gPefqQ+WB1rb9piM9+4vD1IUDsjXPL5DgsWw008EGTMtBGjZoJOVPw2P+/OWY2sUsZKopSX6hRHSO05mwtBtQ8qJi0Ub4mNt45YGJh2FiN/uv/6g1ZWqaTtIiYdp0xop9qiTiz+ulmjGHMeMjAOSRWOCnFq1HcqwuSDzYaBhPDma7xsoCbySRnBkxjN/j154n4GmiQL9frgwp8jrPmN69nMGYGhoAUuIK2UwlMUJZdgHcxJxtgEobIbVaXpe7eDGOPjGmLAJAG+F3RHrlQcFG4CBwULHTS9CmHFeCo8eYUwSV4GIvLSg4KJ0ZEzP5UVuIES4eaRnHIsacIkyQP5VxCv6aRwMa8XQQKUketbLFmz+zWl5/XNnO/n/FmPMRMjggbpulE39KIJU4+Z+plD/O3X82jgFjzCrCOAYXY25oGMfQYFwRxgGMK8I40hhXhHEAY2ytYbggbkl8HfgLpHP5wDnydv+6kZVqaDE+a+gBcWuiEsmenB8U5suf+DeOgEjDgvGhoSckOzv/VkXXlnSHDSMk0bBgXBFy4XFTxIou4qz03+KWxOaRFW3oMGaHhkeq7yzcUDxlc8g0yv1m6rnvb/jOxUe+68gQt8QdPErvm1o86RPQjvTJn/g/VdldxqSzeM/pD8z/7VlfWb+jcCIAmm0tKznxZ84V2354XBZO/MxUUYXAkV3nHhVwzQFpAgwJkjbAezxtjTaMyaHh1fIZ72WUAMBUPXx36ZXiF/P/9b7jqtiRVUjcD+5IsNMnnj3p4+wexT2zoPRyyGqMY0xYBHF928TJrQ0HW3xBodsWleF04rWsAygx3U9lc/13gWuPuaGEVdDZ+yVp1o0efSVNtSduT5jyFv+I8xoMFsaERYjE22oOFhSLdq+fcCqBzzRzeQaE4MFTlhyfYgvmueRYPeqW0iXKyjCeuOQXHnF78lOTIXbUK4K4vm1+cyDE9KbDlMTb+CRczMfhUnTT4Ly9W6lu7Fp9nt4W5d9n3/PTY29M3IEqQM9sXcf9+KbjDhO2dP+WgKKAlJ+aKeWoV4SAmfrFra8/zicTSqkPFoCi0pAfwdC8VLVG2fa7H7N69Sou3L2ZP8w5GzzqD29d/B9/PqbGVApdf0B0kVpYvex6kzLDmCbEyuQpx/xwowijevp41QVPnuIzk1veL6tg3ZQ0xV2PIaFu1QqKE20IIO7RufKib3HFh2/ypd23HvW5BnFbQqKluZGSttvzPX1UoyngOK5TqXlUeYM2pvdJjlqL8O3zHnvm4erTtzw491zWTa52L/ZQgrl1+yhJKwFAwDL4zYt/pEkPIX4YG3A+haoVn9zovSHqxgVSjku6nekffamTEKCqoHnAsW1xR2rpUTzeqMOotAjadU2zZjTVbg9YBu+XTMVSVDq/iBAZijpueutJVr6+Ouf+J8rP5okZs3ZOtlpO35dXtawoYb53z7olu3qWEz+3PHprh2HoXoHZx3vQlW58SV1CZvWhjExSxuVN3jFJzD1kiiCub9OArwEx4BCwEphN5lASlAIaSB1HCtcTlwIUdMtg+ccb+MvsrFPlvYh5UsNBPvzPH3e7FvUFEbagXfcRzQuztWgm5R2NeGSMNytm8sysucyOHmLTpGlsi5QTF96uuhW68ycC6KKLSTWDrHOQ9BqEFADbgOXyRm37AF7XiGNIFEFc37YYWAsMLL9SN5Mvu+bwWZf6wo/eepJb3nwC3XEPHD08ayHf2PEuEnhq2lzm1e8jYBs8NH8JP7j4cgw1PcO0HEj0+ML+NK1uh9W1zuDgcip2ypq+ZqbvzfAtZj9DZorpyv2WvFE7c0DvYQQxVIqwH5jSfykJoqeL0o8s2crRo1hJRytzGg7yx6fvR5WSkkR7RpJu5X567pe58aI0nZMjob1HdsCgx7UK2YfYst9Pyu5q3yO6gk092FVy5IWz5Q3aG30/3Mhj0J1FcX2bQk8lGLD/numGffwEvRFWUh8s4PXJJ3Dvgi9kKUEuLnn/7a5/KCL36S0nVxczvT37t0zEURF9DA09IFl05EIji0FXBHlXvoPrExxrDX1fTv90cv0BLvxkC6FknBlNtfyvd59hcc3H3L1oGbvCLourIXInDXVpCn+3PpnbVMrp3TG00wGl7PiCmU2TdwRlEKN/L8NQrTVcAazprL/Xb9vl/Xe9yOwul3uTkA5/euJ+Ltv+LgCWEN04kO9ZsBSP447dB0JhQpZJSTwGgKmorDxvefcK1SzHMNNkygFvDyVypHstI2cm2pjxBfp4vPQjfShv1F7pvdDowVDOGgLAt3BnDe24s4Zy0jE5QAfp/u04eE1DpDyaOzeHzhccTCWwFQ9JXefLO9fzt8dXHbHtpOLl4emL2T+hhIntDYSTSQ4Gi7n7CxdQG47gKApSSTt5KdOlRhNZSqEr3WcGmWQc2TBsOoNPGcjM/wRAI/BleePo9g0yGLLVR3lXfhzI/mqPDvTeeZe9+72tpRW/tD0aHbq/s3fNq9s/oPs3hk4jHE9E11WEv+/gvbjGZz1y63lLH5M/y++m9eJuc6YvYexQPIi48KazuEEWnb77XbVeRlBPTyWQIKWFohTIG7Sjyi43GjAqA0oAZ3z97beSPt+i6a1RHq9eCEKw9JP3ee6v9xzhTsGL5fMoPhwsPFVe21um0Nw7bkn8PN9M/HOb1+9+8Z6936P07RQ6Mj2syOfkTd4LB9LeaMSoVQSAK77w9Dcfnr3ov0xN67S4v3rxIf7nprWoUuLQm7creLpsAcsO33j0aw23JGTnymNPp7HnMJApY9igCo+82TemmVNG7VoDwEPPLftTKN62JttMf++8K6i4+h4enHN2D+EFplB5a0I1W/MnDXgYyoHE9RGyO4gjuwJI3cpKUMXXxroSwChXBID/tnPDn0pbXM9fSBscm5rQBIpy4gWSw8ECnqo6+bHrd/zdpcfWmkx1fvBMqNlKO4susXaXgjgSDMeRN/tyFzvGIEa9Ivx67SV/aAroFMST7iRDKKimybpJJ+SUrfPnsfK9yy455sYkP+iMFXgVl1w7O8ZlOW50MWW5cQWPCB9zW6MMo14RAP73use/0aErmckmn63Zwar557N6ZhfN8muTT+DJaScf754Ab+e6QtyGvgy+O01Mypt9seNsb9RgVDuLORDLBXK1vPe038jvn//fkUJhSlsjXtvkk4ISlu7efPMzj5/zL8dc/e2Jr+LzrMaS7sYU6D3qLUDe6j9qZ3Q0Y0xYhE7I1RKgMRj54vc2vADAgfxCdkXKOH/vhxyPEgAgxBPd/ADoFtqm+9VPFcaWRcjCDUv+Mn9vqPiNdl3T44r24drHFg/K3kFxc9zBqwqMXkYZges32Jwob/OPiX0GA8WYVYShgrgl7qAIkbMZBUChHYd8ebv/U/fSxsQBl2GFEEkE/pxBU4L8iT/U6z2fAowtH2E4IKjqY0vE9cMsybBiXBF6QP7EX4strwKkuykFiSOvlbf57x5p2YYS4z7COIBxizCONMYVYRzAuCKMI41xRRgHMB5HGDKsWrDmQuDfgTrgkhXrl+4dWYn6x/isYQjwnQvfSU1u7dBDhptdXgImrL5u/dKvjaxkfWN8aBhEXH3xO/+04JoamecIPd8wOxcupRDosPzO09c8PtIy9oVxRRgk3LXw+QWm8Nw3s82gtLWt22+qlLT6dJJe/SurFqwZ8HH94cS4jzAIWLVgjZLyau9FLEl5TS1eK3dHy0cTCwm1JxGW9TFQNfxS9o9xizA42NLu0ynoSORYgwwmNbfz58UnoWjatH/6/Bu5KedHGMPiLLaIb76dT/KMhPDTqgRRpI0h9Japdt1pyNV7h1yAIcY1F77tzGhoFZkjXH3h8bkziMSTnPVxDdE8X+UdLy/ZN1wyHglDbhHi4tKOApJnKEBQJii3o+STZKLdELYRexrEVda6yL1jlsBy1YI1+duLI0KK/pUAYEpTDEUoKFLycTjvzWERcIAYUkWwxPLtXmQg+wUJQJcmtvBQp5dh6F410n44MZRyDCWiXu3NfMPCMwDDejicR1Vrgs0V5YQcOfH6JS/fN+QCDhBDMjTUev7xi1LKJw4GK8TJsQ/w4c6nHQQb8uezz1eJpItlJGB1oJkxJ6FHyr7ccGXDoAs0hPj6pVvklOYYlS3usJ9h8O05NZBAY8DHmjnTWF9RhgDm7q9j/p6a1vufWRQeVqF7waBbhP3eazaW2NEny5xGcXpsI15MLFTa1SA7/LPY669Ciu7Hx+KeIEFhKWXJA/Vv5N8pm9QrP0AsH5XTrGzctWDN237L7lQC6IeEDSiKJwGwVQVLVVg/rZxXqisLVlz4ztVDL23/GFxFEMt/MNE4dFrGacocEPdgk2d3MNGoQcjejx6Yis5p7R/QqhfxcuHn5zQpob6pT0YBVi5+8Z4Ov37GggO5Bqw/DT5v+z4K4kmCKTfj/Y7yQlpV8W/i59aIsr0P3tAglkclFELfveKd/IXs91d2/luVFjPjOyk2oqjSotiMYiNQkDRoxdSJiHGysTOAXD3qzhb+y1kvyfyUNXBWoDQMVeHuCxZy4uEo8/bX0Zjn551p5WiWzYzDjdtWP3LK7CER+Ag4PkUQy9Um/B+ESJ7ocUf9frE2ci6NelcO77Oa1zHRONxn+UY1wjb9VBRTyoTuM0xFbNCFuP1zbf+w5tiFPn7ctOSVnaXx1MxjMadxzcO/nnMa1760Hk/WievfnXUy0aCPouZYYvO/VQQGT9qB4egVQSz3HNAnVZUaNRcAqzzpHjwQbAjN45PADAACdgfLok/3Wi6mhtinzcCb9CKy1MsBTEUhrnmJ+gKYDlKqiuMzki0+6dRZQmlo9fnfsDXvb3EZSxKA79Lay457mFm1YE1gb0DfUGhY1XmWc9SWIIMOTWVH6QTmHew+pOwuKuCX5y/gu2s3EG5PYKsKTX5vYk59S2TF+qVDni3GVQSx/GrgO0BZ+vpeIAqcgpupxA+Een7upOLD7yQH1JCBh7XhczE8flKqj6AV46LGZ3PKdSgBnptwAVNjLaQUjdJEe68v3RIK24rKXFbWnlxGR1LufsivUrpKIuhFT5okgl4s3YOTRZ+XubMmP0B5W3zQnKx9E0Lcc8FCqg9F+ea72wgnDeKayh8Wzubz2/aSZ9p8UlSAz7RSpx1sOAjcu2L90t8MUvMIyVcvBx46mpskEFf8BJ2BTf8NofHShPM5pX0LZak66vQSNGlRZDZ0cyoTipfnI5/HUTQsRcNrmcxurO2z3gOhMNHA4B81sAVEywqQWUk7enKA9kRfv5uKQmOeD0VKNk0pZU9hAf/jrQ/x9ViPeGT+LNZXlPHjp98kP2V2Xm8M+Hh2zjQuf3cbAC/PmkJZawcn1jYBfGvF+qUPHutzZsMD/N3R3iSAFk8Yn5FEHcCw8Gr4s9hCZVLKZd3L+AWN2gQ2hubTokWImI3MiX2AioOhuIlRTFXFFgJ1mPdMGD6tmxLAkaOGAqgP+Yn5vFRGWztl3lka4YHPzu1W9oXZlXzx/d3YApr9XmrCIaqiLVQ1tHRTAoAJ8SSHCrronRfuOcyj82dlFOEKYNAU4ZjGz4jVykHvZCpSB/osI4GtwZNo0Sfgt7vzS9movB4+G0NxWXqbtULeK1hIdcc2Nue7x90doXAwFGFqW1POh7CEQrNvaHwqcZR65wh4aNFJbKhwR9aCeJJvv7aFSS3tVDa25pTfXRwGIKWq5KdMig5F+62/tK3r3ZmqisfunIIP2hRbAe7DJZgbMCQQcOIYwkuH4u+33Pagm2choQao00o6f4vqhZ1KkEFSDeBzUt1iDU3+IFsLy9lTUEh9II+4qtHoC7BzQgm2MjQxJz1l4jG6vxK7hybGta4V/E1TSjuVAKA14OPReS6Rh8/MnfmWt7jfL2DZ6Hb/lA5bJhVz6sH6zn+/NnMyn/n4ILjsDfcd+WkGBg9y9WuI5WcCVwEV6QZ2AIeB03FjAxZwPqBkOosEZiR3YaBhoqL1wiqhAOc1vsABfwWaYxK0Yuz2T6dZi+RYCLdSyQSzmTNa32FL6FQSqtvjTY+HdiEobW9lb34hKU3rdN4Ux0a3TFIeHZmhy+/NGezKxdTvC0lpimzP9wt/RwrDtKXh04SjKthAbUGQklicxoCPJ0+dzle27KakPcG+Cfk59exPX7MVQXEsTkPIfZaiWJwLPtrbrwzNXp1YwMv7k4qQjmRmQzMfF4epzQ+aJx2K7pnaHHsP+PWK9Uvf7reio8CxxxHE8gID6jWXyH5AWBf+DLXe8s5/++0OEmrX+DctvpsFMZetVgJNSoQtwcXotkUolcRrmxzIC5MSCgVmwpKIqG7ZawyP9re6gsgmoA2Ql9Ze1nJsD9U/Vi1Yc8nrU4sfjhaElDM/OUxJh+ssb5hayu/PnNOtbFVDC9e+5D6LLQTbyyYAMKuuqVv8oCcsIVh1zlxagj6m1TY9tOH+yqP24Y4Fxx9ZFMuvcuC3/dBpI4FWTwEvFOYmOZnbtpGU4mOC2Ui5cbhbHQfUSWwKnYbp8HyxlXqiON7x2InyurrjE/j48c8XvLmsPj/41KK97ozGEfCHRXPYWFEKQH4ixbdf3czkloEN4SlVQQqBkJL1U0p2/OXRU6uHTPg+MGghZkss/1CFk/pShnqtmFcnnJtz/XNNL1Jo5vJiWgieD53zt4vaVnx1UAQcZPz9lzZYcw9FuyUGrM0PEPPpTIu25vT6pEel1e+luD3eNb7ihpx/v2g2sw/Uydpw6MxXHpw5aOb+aDC4y9Bi+RwJH/RUBhsQCJ4tuoh41lAQtGJc2PgcIj0FNVFRcYhRQKOSl6yyf9W3JzrCuGvRS1Nr83z7Klt6D3hl49WZk/m/p87A8KgEUib/+OpmqprasISgza9zKM8vH3jmjBHdNji4jcvVH7ZTcLFDd5IhFVCQfKb5NcpSh/DaCSYma1jSsq6bErTj5QBlTpMnf02Vc3DY4+1Hg+vfPm//xLb4kiOVqwsFeGz+LAyPO8OJezV+df58Hjj7ZFr8OjtKIqyvLP+HIRf4CBiyPYsx8Y2DAcxJR5rgSSCFh3b88SL5+zGXGOvOM16QeT2mgA1BHwlNpaQ9yZtVE/nbvFxOyMvf3kogabK1LPLB6/9n+ojnjhwycxSSD08+rBTX9DVLNlBpVUPUaiUIrKaxqAQA0fzgwZ5dqbgjydSWDnyWTUVT7q7mQMpk9v46CtvjU0eDEsAQ71mcbD8weY868ZXebI4Hm5Ado8is3+iVqwuHUo6hRGVzrNLoJzYxPdrKwj1dCW0Ux+Grm3YCYvsdryzpOyw7zBiW7exbAz/63MzEthe0tOJZ4Jh4fhCQj/xiyBsfBty3YI3TC3d7N+ybEKI+FGBGfQvhRApDEcXXvXtB/7HlYcSwnHQ6Kb5yLVk7uLT0f58WqPBHC67o72VWNMXYPLmEqdFWDNg0mpQAxk86DRau7JkWrKedPRAJsbZ6Ki1+/Y7r1i+dN5zCDQTjx+IHCasWrKmOedRteZbdmSP0lVlTCMdTHC7I47UTJuNPmTTelj8qOZzHLcIgYcX6pdu9jrOkXVWoy/MjgDN31VCbH2DTlBJO3l/LDc+9s2yk5ewL4xZhCHDN0rfe2TitfGFRR5JwPMnUhhYUWHb7a+c8M9Ky9YVxRRgirFqwRm8MeM9t9ultv3z+zLdGWp4j4f8B73dOT/pkG9IAAAAASUVORK5CYII=" id="imagefa4f0e4124" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature80_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-38" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image87e04b0bee" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p4c0a2d5a09">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
