<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="766.768812pt" height="679.5765pt" viewBox="0 0 766.768812 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T08:21:04.823259</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 766.768812 679.5765 
L 766.768812 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 385.457812 638.149 
L 515.913812 638.149 
L 515.913812 27.789 
L 385.457812 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 442.286862 638.149 
L 442.286862 27.789 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 385.457812 609.084238 
L 515.913812 609.084238 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 385.457812 580.019476 
L 515.913812 580.019476 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 385.457812 550.954714 
L 515.913812 550.954714 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 385.457812 521.889952 
L 515.913812 521.889952 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 385.457812 492.82519 
L 515.913812 492.82519 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 385.457812 463.760429 
L 515.913812 463.760429 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 385.457812 434.695667 
L 515.913812 434.695667 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 385.457812 405.630905 
L 515.913812 405.630905 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 385.457812 376.566143 
L 515.913812 376.566143 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 385.457812 347.501381 
L 515.913812 347.501381 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 385.457812 318.436619 
L 515.913812 318.436619 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 385.457812 289.371857 
L 515.913812 289.371857 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 385.457812 260.307095 
L 515.913812 260.307095 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 385.457812 231.242333 
L 515.913812 231.242333 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 385.457812 202.177571 
L 515.913812 202.177571 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 385.457812 173.11281 
L 515.913812 173.11281 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 385.457812 144.048048 
L 515.913812 144.048048 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 385.457812 114.983286 
L 515.913812 114.983286 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 385.457812 85.918524 
L 515.913812 85.918524 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 385.457812 56.853762 
L 515.913812 56.853762 
" clip-path="url(#p288f0af03a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="ma4f3da2e62" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#ma4f3da2e62" x="442.286862" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(438.787487 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#ma4f3da2e62" x="509.501204" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(506.001829 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(328.004406 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_mode_MeltingT -->
      <g style="fill: #333333" transform="translate(62.456094 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- XRDPowderPattern_xrd_21 -->
      <g style="fill: #333333" transform="translate(189.08625 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-31" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_135 -->
      <g style="fill: #333333" transform="translate(164.365937 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_4 -->
      <g style="fill: #333333" transform="translate(107.048125 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-34" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(110.942031 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_46 -->
      <g style="fill: #333333" transform="translate(172.637187 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(164.365937 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(142.714844 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CrystalNNFingerprint_std_dev_hexagonal_planar_CN_6 -->
      <g style="fill: #333333" transform="translate(7.2 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-68" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1594.947266"/>
       <use xlink:href="#DejaVuSans-78" x="1654.720703"/>
       <use xlink:href="#DejaVuSans-61" x="1713.900391"/>
       <use xlink:href="#DejaVuSans-67" x="1775.179688"/>
       <use xlink:href="#DejaVuSans-6f" x="1838.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="1899.837891"/>
       <use xlink:href="#DejaVuSans-61" x="1963.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2024.496094"/>
       <use xlink:href="#DejaVuSans-5f" x="2052.279297"/>
       <use xlink:href="#DejaVuSans-70" x="2102.279297"/>
       <use xlink:href="#DejaVuSans-6c" x="2165.755859"/>
       <use xlink:href="#DejaVuSans-61" x="2193.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="2254.818359"/>
       <use xlink:href="#DejaVuSans-61" x="2318.197266"/>
       <use xlink:href="#DejaVuSans-72" x="2379.476562"/>
       <use xlink:href="#DejaVuSans-5f" x="2420.589844"/>
       <use xlink:href="#DejaVuSans-43" x="2470.589844"/>
       <use xlink:href="#DejaVuSans-4e" x="2540.414062"/>
       <use xlink:href="#DejaVuSans-5f" x="2615.21875"/>
       <use xlink:href="#DejaVuSans-36" x="2665.21875"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(86.004375 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(49.718125 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(164.365937 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(172.637187 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(172.637187 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- OPSiteFingerprint_std_dev_q2_CN_10 -->
      <g style="fill: #333333" transform="translate(119.962812 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-71" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-32" x="1425.953125"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-43" x="1539.576172"/>
       <use xlink:href="#DejaVuSans-4e" x="1609.400391"/>
       <use xlink:href="#DejaVuSans-5f" x="1684.205078"/>
       <use xlink:href="#DejaVuSans-31" x="1734.205078"/>
       <use xlink:href="#DejaVuSans-30" x="1797.828125"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(239.507969 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(63.5875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(242.264375 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(120.748906 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(79.165156 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 385.457812 638.149 
L 515.913812 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image05caf09e9c" transform="scale(1 -1) translate(0 -578.16)" x="388.8" y="-43.2" width="123.84" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature39_fold0 -->
    <g transform="translate(141.802813 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-39" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 524.067312 638.149 
L 531.696812 638.149 
L 531.696812 27.789 
L 524.067312 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image5c2271d380" transform="scale(1 -1) translate(0 -609.84)" x="524.16" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(535.196812 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(535.196812 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(569.59775 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p288f0af03a">
   <rect x="385.457812" y="27.789" width="130.456" height="610.36"/>
  </clipPath>
 </defs>
</svg>
