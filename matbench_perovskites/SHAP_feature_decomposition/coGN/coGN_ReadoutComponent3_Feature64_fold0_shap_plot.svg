<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:07:17.374009</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 456.366706 638.149 
L 456.366706 27.789 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#p011d44bb4d)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="maf21c104b1" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#maf21c104b1" x="422.308418" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.5 -->
      <g style="fill: #333333" transform="translate(408.952871 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-30" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#maf21c104b1" x="456.366706" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(447.619987 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#maf21c104b1" x="490.424995" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(481.678276 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(209.467812 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_avg_dev_NdUnfilled -->
      <g style="fill: #333333" transform="translate(70.367812 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-64" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-55" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-6e" x="2219.291016"/>
       <use xlink:href="#DejaVuSans-66" x="2282.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2317.875"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2373.441406"/>
       <use xlink:href="#DejaVuSans-65" x="2401.224609"/>
       <use xlink:href="#DejaVuSans-64" x="2462.748047"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- GeneralizedRDF_mean_Gaussian_center=1_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(26.314062 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-6d" x="853.369141"/>
       <use xlink:href="#DejaVuSans-65" x="950.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1012.304688"/>
       <use xlink:href="#DejaVuSans-6e" x="1073.583984"/>
       <use xlink:href="#DejaVuSans-5f" x="1136.962891"/>
       <use xlink:href="#DejaVuSans-47" x="1186.962891"/>
       <use xlink:href="#DejaVuSans-61" x="1264.453125"/>
       <use xlink:href="#DejaVuSans-75" x="1325.732422"/>
       <use xlink:href="#DejaVuSans-73" x="1389.111328"/>
       <use xlink:href="#DejaVuSans-73" x="1441.210938"/>
       <use xlink:href="#DejaVuSans-69" x="1493.310547"/>
       <use xlink:href="#DejaVuSans-61" x="1521.09375"/>
       <use xlink:href="#DejaVuSans-6e" x="1582.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1645.751953"/>
       <use xlink:href="#DejaVuSans-63" x="1695.751953"/>
       <use xlink:href="#DejaVuSans-65" x="1750.732422"/>
       <use xlink:href="#DejaVuSans-6e" x="1812.255859"/>
       <use xlink:href="#DejaVuSans-74" x="1875.634766"/>
       <use xlink:href="#DejaVuSans-65" x="1914.84375"/>
       <use xlink:href="#DejaVuSans-72" x="1976.367188"/>
       <use xlink:href="#DejaVuSans-3d" x="2017.480469"/>
       <use xlink:href="#DejaVuSans-31" x="2101.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2164.892578"/>
       <use xlink:href="#DejaVuSans-30" x="2214.892578"/>
       <use xlink:href="#DejaVuSans-5f" x="2278.515625"/>
       <use xlink:href="#DejaVuSans-77" x="2328.515625"/>
       <use xlink:href="#DejaVuSans-69" x="2410.302734"/>
       <use xlink:href="#DejaVuSans-64" x="2438.085938"/>
       <use xlink:href="#DejaVuSans-74" x="2501.5625"/>
       <use xlink:href="#DejaVuSans-68" x="2540.771484"/>
       <use xlink:href="#DejaVuSans-3d" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-31" x="2687.939453"/>
       <use xlink:href="#DejaVuSans-5f" x="2751.5625"/>
       <use xlink:href="#DejaVuSans-30" x="2801.5625"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_34 -->
      <g style="fill: #333333" transform="translate(209.467812 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=5_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(12.613281 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-35" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- VoronoiFingerprint_std_dev_Voro_area_minimum -->
      <g style="fill: #333333" transform="translate(83.52625 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-69" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-6e" x="2075.724609"/>
       <use xlink:href="#DejaVuSans-69" x="2139.103516"/>
       <use xlink:href="#DejaVuSans-6d" x="2166.886719"/>
       <use xlink:href="#DejaVuSans-75" x="2264.298828"/>
       <use xlink:href="#DejaVuSans-6d" x="2327.677734"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(43.983906 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- AverageBondLength_std_dev_Average_bond_length -->
      <g style="fill: #333333" transform="translate(63.106094 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-73" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-74" x="1114.298828"/>
       <use xlink:href="#DejaVuSans-64" x="1153.507812"/>
       <use xlink:href="#DejaVuSans-5f" x="1216.984375"/>
       <use xlink:href="#DejaVuSans-64" x="1266.984375"/>
       <use xlink:href="#DejaVuSans-65" x="1330.460938"/>
       <use xlink:href="#DejaVuSans-76" x="1391.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1451.164062"/>
       <use xlink:href="#DejaVuSans-41" x="1501.164062"/>
       <use xlink:href="#DejaVuSans-76" x="1563.697266"/>
       <use xlink:href="#DejaVuSans-65" x="1622.876953"/>
       <use xlink:href="#DejaVuSans-72" x="1684.400391"/>
       <use xlink:href="#DejaVuSans-61" x="1725.513672"/>
       <use xlink:href="#DejaVuSans-67" x="1786.792969"/>
       <use xlink:href="#DejaVuSans-65" x="1850.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1911.792969"/>
       <use xlink:href="#DejaVuSans-62" x="1961.792969"/>
       <use xlink:href="#DejaVuSans-6f" x="2025.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="2086.451172"/>
       <use xlink:href="#DejaVuSans-64" x="2149.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="2213.306641"/>
       <use xlink:href="#DejaVuSans-6c" x="2263.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2291.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2352.613281"/>
       <use xlink:href="#DejaVuSans-67" x="2415.992188"/>
       <use xlink:href="#DejaVuSans-74" x="2479.46875"/>
       <use xlink:href="#DejaVuSans-68" x="2518.677734"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_71 -->
      <g style="fill: #333333" transform="translate(209.467812 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(195.088594 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_mode_Number -->
      <g style="fill: #333333" transform="translate(103.030312 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-75" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6d" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-62" x="2108.890625"/>
       <use xlink:href="#DejaVuSans-65" x="2172.367188"/>
       <use xlink:href="#DejaVuSans-72" x="2233.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(201.196562 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(86.54875 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_8 -->
      <g style="fill: #333333" transform="translate(217.739062 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(62.252969 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(79.746094 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(147.772656 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAIYAAAMjCAYAAABgQGhRAABukklEQVR4nO2dd5gcV5W331up4/T05Kycg4OcEwabDCbYmGDyArtgwgesMXFh8ZIxecl5WcAEC7AXsDE2No5ylCXLsnIYaXLq3F1dVff7oydPywZrerq6Ve/zjDRdXVVzbvWvzz03nSuklFQK/++8O3YLRV2u5M2dX77vog3ltqeaEZUijP93wd3yrL09qEhyQuVvG7r48V/OFOW2q1qpCGG869w78qtTaW1/a5Q9zXU0x9Ocv/UQ97dHDn/n3osXl9u+akQrtwH/CPWqov3h9FXcunZKAw8taWHZwaGuMppV1SjlNuCpuPKFDzWEshluXzVTA9u6WhioCZTJqurH9cLoV+V/5FGw1bmmBnP5Mlh0YuB6YSQURVt+ZJCuwdiM48GsSVMqXSarqh/XC8Mv5ZbHW+v5yk9u4pT9vQCs6B3mE7+5ncFI2GuVlAjXB5/9IWNdf10tix/dy4++/QckkNVU7lvRidRcr+uKxfXC2NTX965nPTGMIiUTDWu/ZbO9uZ7GeKqstlUzrhfGULQ21DZyAInAQcFBoODQMZZkzC63ddWL633x+269WYyFDEw0cujk0chh0DaSZizoNVdLhas9xitfdl/XV7MDBESAFNEZ7510ZBif5QCbymJbteNqj/Gih7YcVJwITWZiznsCEKrXKCkVrhZGxBdSNNtGBRScGe9JIJrI8KaL7ruv2LX3ie+Knb7PBneLa11dRreyoINo4X8ffdsZY4nvqYrC0rEYkWyO0YDB1vZGYqrGn75zLQ25BAE7iwL89OTncMmjuxFABoP9RgsB0yanKxxprqFuLIupKxh5B59po1kOOjb1coxBXxRFCrKGRjyo0xRPUZM1KfgYeeMm+e6XLFjBK5AF+zaJDyWU02Op7zVZDhsGR1g8lqAua7JsNMlLHj9E0MnSkR3Ab4OCyo6mVShSYaKyeHBRK0fbajjYGaGnrQZFhd6WMOkaHdUCbAVLaGSEjzi1hHM2QdOiPpmlZSxNd2MtWUOnUAkplzws/vuahSp7JbJgwgia+Y/VWjYKUJfOzHhPk5I3PXw3jhPGIEaeGrYsXoQtpmKIHU0tqFIilaljQcvCVgTTaxlVOuizqp2AaePP24yG/NMPv3sei1d1LJgwHOiR45+pI+YGjZYiEEgkCiDZ0NvHz87ZNCkO6cw1NacqIAS29tRBqJAgZurF/OdLceKwYMLIfLn+B326hiVgKBya8V5aV/n5KeehKClyRNFJcOrRo2zo6eE1b3kVty9dxpruMR5pa5q8xgFG/AUPIKbFSbZQsJkplJyuktVV6lMTnkoCfKAExawaFjT4DPz7aOPGWKrfpylKSzJNJJdjzK9zsL6Gft3gFz/7BmvHhgiYeVQkaVHH/V1L+NzFL+Cq6+8mo6ls62xEEZKu4QSRtInjOERyNoF8jqBtomMRlGliSi2OUMkaGkmfSlMyhd9yAClBXLlJvus7C1bwCsTVU/t2+z8ufTkfr3rza3npPTtZ3zsy+V5OVXioq4VDraGrfnnzWV8qo5lViavb+N2GYgskHcPDfPF5Z3DPsjZShsae5ijfuvBkRprDeKIoDa7uEn9mYldkUHSkFifTjAUMfnzRqdRaNglV4aTD/axMxMttYtXiamGo8pfpP6/9qayzc+K8sSTGtFpPiQTRbefYF3scF66uSgD6om1sjzZgODNjIVPXOVBXUyarqh/XCyM6cLT3zCO9UKTvYzjgL3KFx3zgemHsaF/0u/U9vURSM3tL/WaezoGRY1zlcby4XhiDAb9m2Dan7zpA+9AYmmXTPBLnood2crihttzmVS2uDj4BHEX0/u6M03j9XffSMTIGjsBRBYOtDTTFMu7thKlwXC+MBkXp9tk2N557DvWJBGmfj6zPB8BpD+0ts3XVi+urkuHmlp+t6DmKkcszEolMiiI6nCSnu978isX1T/Y3P1ma3xtpZvmebpp6RwnH0rQfHqL10AD9DTVeVVIiXC8MgHTeSj24eimjQlB3ZJhRn0FPZyMZjfeV27ZqxdWDaNP57Ia/OI1ZSyRrAoTiafY01zhfvPcZarntqlYqRhgA/37unS9xFOWzGOrFX7nt7L5y21PNVJQwPBaOiogxPBYeTxgeRfGE4VGUihOGuNby1iUuABUTfPo/lT4zZ2hbJoffHQmqosqrNG+2TgmoGI+R07UtKIV1JAgBqgJ52yq3XdVKxQgDpUgNohY76DEfVI4wiuCponRUtDBUbzJwyagYYRQTgWp5SbhKRcUIY/YscQBfGew4UagYYTSkc3OO1We9BeulwvXCuF9cc++d9Z+TxSoNp4gX8ZgfXD3nc6v6ictzbeGzty7aQNo319SeSLAMVp0YuNpjdBvtv9rauoHQUIaOodic9y0vZXTJcPWTzTSHRGQkwcqBg3z2V3+gbXhqEbOQEqRk9dVDp5bRxKrF1cKQlsPa7qPYiSiRfh+/+NxmPnTDzYX3gNpMnn6f9kB5raxOXC2M5lictDN9tZnC2Q+PEMxmQQjSPg1pSVeXoVJxTfBpi7fowMuBlRLShwh/rkFpZ4QgICcTt9Wm8ii2IBrPYeRt0oYu9M9kbAv1PRjKe4GlwAhwrrxK81YkPU1cMexui7cowF+BZwE4CB7xr6c9O0KcCE2MouAQo4ajeg1vfvurEVKgjZue9Kn0Ngax9Tk6f7a8Srt1IctSLbjFDT+fcVEAWKj4pUmANK0Mo+IggCgJnljdhO4URGFTyN4Xytm0xDLF7vvzhTG/+nCLMBZNf2GjUmNliOlzE6Ocf3Df5O8qkFcEAlCtogNq9fNr5omDW4RxEzA56UYnjykU+oN1c07sD0dmvBZAXhOkgkVHTm6cXzNPHFwhDFX+8CDwGuAwgMCxLMdhUNSQVo3J83Kqyg/OPHfGtXlFkFZhpMZXSOE5xTDwphKbXrW4IvicwBZvEYBPlT/M2uItgc1Nz0qbAZ321Ai6Y/PRlzybXQ0t1OYsBJA0VDRhc6gh0iA/4hsZnyh8GrBNXqV5I2zHgauEMZtfdfxcZo2pKiKvKNy3qp2+epWb1mxAUSCUytqjn6lzTbO7WnBFVXIs6tOjM17rjsPrH7yf83v2Ywd0/FKS1FRvu+YS4OpvWiCXlTW5hEgaYSRQb8a48bSVfOniZwNgKgr5ayLejnklwNXCEJmElets0S84eifP/dcPs6+tccZ+JTndy4JQKlxdlZwnP2ks6Rnk9iXPYG/7TFEAs1shHvOIq4UBcE7y/WLl49t2Gvm5a4uCaS+8KBWuFwbA+fKadVLOXUUiimQL9pgfKkIYAPkiGfpSfi/GKBUVI4xiucRRKsf8SsN7sh5FqWhhCG/5QMmoGGHoOWtm81RKpCeMkuHqDq7p5BUcxXIU3ZFIwNQVMK1Bb6FiaagYjyE/ElC1vLU3pymYmiIDWfN/5SdCzeW2q1px9eiqR/moGI/hsbB4wvAoiicMj6J4wvAoSkU0V096V29seSwZiRu6zAnlHXf9YNF3y21TteP6Vslz37jPWZXKCUtVkYDPsthSF3rXlh8s+Wa5batmXF2VtH1gNLQ0nRP18QQrjhxl2dFeAtkcXZncN8ptW7Xj6qpEjSWuaYolqI8nANBwaBseYdivexMxSoyrPYZhW++IpNNzjjfHU2Ww5sTC1R4jYDtaRlEIpzP4sia2qpINGWQ1b4JOqXG1x6jPWzIaT1M7lsKfzVOTSNN2dITumhA7xEf/Vm77qhlXt0ouuvwx+83371QUCbWJDCvHjhAmQULUICWkDK3u9NzVY+W2sxpxdVViAEKCpSqscvawivEUCBKGaEGadcMUsiF4zDOuFsbhSJjBplpyUZ3O7m4OBdupz6bwO1ka6CeF32udlAhXC8Py+TjY2cyyvj5+sfQSpFBQHZuThw6yJnaAEHNzf3rMD64OPkP5PLrj4I9lkaJgqq2oPNK0jLhWi4W3bLVUuFoYG4bGSDkOyqy5nVIIhv01FN/2yGM+cLUwhBTiusWdZIr0W0RzSQ7Tyd3qZ7x1iiXAlcJ44RW7PvHM1+12djTWiLSh89t1y7HGFxw5gKPkaMr3spatdDox7aB4t5MWr/paea2uLlzXj/GKK3Y7S2MJkRfwUFMTGUXgE4VcGLplM6AINhzt4Re3fI2gnQXAxOAAq1jGTgTaBk3+ckeZi1HxuKpVcvFbuj99ajIlojmTvy7qYG0qQ1JTyWkqDbkcEkGLz+Bd27fg2E2kMfExhkGOJvrpZTEdHNiOSz1hJeGqB6hIeXUwZ+IICNoOfschryi0ZbIYjsTnOHRkstyx7mQEIDHI0ohEECbGEG0oFFkW7/FP4yphmIp4PKfpCAm6LCR0rbHm5sVI+cPTXinY+EkRIUgCd1WMlYurhNGRt84+XBsmpyoo44LQ5NyMv9GxLDGmsgY7CA6xgsXsxsK4fsEMrmJcF3xe8cYD6qhtm7GgT5G2ZEMyjSHl5Oa7iu3w3Nu20jgWZx27sRGMEaGZXVJFXu6X13nCmAdcJ4zprH17t7NrabM4b3cPr35wL0JKlh3oJ5IsJJSPEMMhK31oodXyw0WzzHs8PVzVKpmNUBSkqrKrrZ6Njx1Em9YDKoE4AerJ1XiimH9cFWPMpi6bk6rtMBgN8ZOLT5nxno2glpRcIz/mzfMrAa4WRlIoLB1IoNkOv33GOv7tnS/i3pUd2IpkBQdRyZbbxKrF1VWJikPrSILLHt1FWyKFBMLBPOvlPlQkEq8GKRWu9hgp3cfZvQO0JQq1hQBSvhBPtBT2vckScm/kXOG4WhiK4xDKzt3TfX99C+AwQvBTC2/ViYGrhZF1HGegZu423YejUY4Sdc6QH/5EGcw6IXC3MBQhb9i4jIRPnzw2FPLznXNOZZ38D28ScAlxdfAZMC15MBrmUy84m/W9w1iK4PG2BjQvh3jJcbXHiGZzm8la5HSNhxe1sK2zGUtRaE54rZFS42ph5OujH1o0koS0CbYDeZvIcJrGtLfdWalx9VgJwBn/dlTmNZW0quC3HfwOpK3skce+v6Sr3LZVM672GABxn3ZyJGtSl7cI2jaWY6U8UZQe13sMj/Lgeo/hUR48YXgUxROGR1E8YXgUxdU9nxOIz5kq6fwNQFJeE3pVue05EXB9q0R8PPkfIK6Z3BNNAjgvlNeE/1xOu6od93sMh2uwHXCcwoZ5mgKK+BPgLSwqIe6PMWyn8KMohawHpl1ui04IXO0xxNWxQjwR8RcybQnAkpDxRldLjauFgcrP8GmQtyEzviLNUAs/HiXF1VWJTwoVB8hYBXHk7YK3EIIz3rjfS8BVQlwtDNVQFXI2IMdjDKWQOSWVR4fIq67Y426PV8G4+sGaigBVgKoSMS1WJrJojkNCVdhWFyEzEssA+lPeyOOfxtXCcGwHQgYYCnFT5SFNBUeiI1mayDCqaa62v5JxdVXi+HTwqWBLSOVhfO1qHsHecIDO1GiZLaxeXC0MtPE+rLzD7IwojhCcNDrADV3Xzl144nHcuFsYE2I4hpVBwjTkbWPB7DmBcK0wxCeSm7Flofow1EIQOo06M09LxmRVf3eZLKxuXCsM8s5LkRIyFs0jGeqzeVTTQjEtanN5Xrb/CHomz7BYxGPhD3yr3OZWG64ZXRUfT+1AiHVoSqEKMe3JqkTLW1iWg2HbqFKS0TRe/8RuBpoauHDvIWrH0qR8PurHEiAKW1n0tjbaifqah/KG/pEvbd54a1kLV4G4Qhji46nbQVyIrgACTKsgCl0FRSDyFi/ef4Szjg6gOpInamvo01KcFTvKA8vOoz6R4sy9B8kIlebhQoeoBHauX4ajqQ5CXPylzRtvL18JKw93VCWKcmEhX7wAOR5XjIsCKTltcJTzu/vRHYkCrIslWJ20+eRtv2LRaC8jNSHSPqMwJD+OAJbu7wEhFODfylSyisUdwijGxMYCimDl8NxhkaXjCdrW9+0HCtWHo8wsjpjK2eXN3fgncYcwHOdeHFn4dIWY9BQASMlQ0D/nkvbEAAAH6tpoGYvhN02mZwSVwIFl7VCoK79X8jJUGa4QhrwmdC5SHi50ZI1XIxMTchzJPYtaGAr4Js+3hOBgUydxX5hn776fU/cdYszvp6V3GEcUtDDYGHUUeBQhXvilzRtvK1PRKhZXBJ/FEB9L2AQNBVuC5VCXznHS4ChrR+LUSolUFM458AAXP34Xj4c7fnHW2KdfW26bqwn3DkIJ5WaEeAFOYcj9NfsOErYVEAI5PjG4N9KMYdt4oph/XFGVFKXW9yKsqahhTzQy55RFI0cY8c1NxeRx/LhWGPIqTZKfmvh7R2cbaTE113Px8GEOR2q5ofO0udsTeBw3ro0xAMTHEhJNY3JNCdCUTPHs7iNkArWkpOQvP1nqNUVLgGs9BlCYh5GzZjRdB3WDe9o6Saoawp67ZYXH/ODe4BMK/Rl5pzABWBGTE3X6VJUu2yJhaN4ikxLhbmEYSmEdCUyKAiBnqOQNHUXTvMizRLi7KsnZDsEi2lUVomPxxMPfbPGytJUIdwtDEQ4SCGnjs8UF+FWwHG761dq57VePecPdwrCc15GxIG2BoRVGT3M23phY6XG1MOTnan81ORyftcB0mJyJ41FSXC0MoOAcpvVjoLjf5GrA/U/ZsT+JAqhK4QcJOG8rs1VVj6t7PicQH04IHHk7gjxCPFd+tsbr2SoxFSEMj4XH/VWJR1nwhOFRFE8YHkXxhOFRFHcPoo1zVLxjNIITleiASQplpE1+p6HcdlUzrvcYR8S7n2lQEx1iMX3qIkZYgg9//Zj4lzPKbVs143qPoSNvOxJsYX+kBVM1CFlpVo4IInl7S7QChF2puP7BxtR6dkU7MNVCGoyUFmR3fRsWvqe40uN4cL0wkr4AUsw0M60FiWs1ZbLoxMD1wgha2Tlj7KpjMRzwEumUEtcLo8PspyvdM3VASjbE91JnenuvlhLXB595NOpHLYKpXnKaRsg0abSSKMTLbVpV43phDNJGE4P4zDyYoJJjhAY0vGR9pcT1wvBjogJxCsFmEJ0Ghhn1EgKXFFcLY6929eIABntZRG68eeojx2KOEKbnKa72OB5cLYx+rWG/46thf0sLpqERSpm09iXot5poZ6Tc5lU1rhZG2hcSB9o7cNRC4yle68fSFIIHs6QxvKniJcTVzdWRcGRSFABJn85DK9qIRx1e9y/vZtn/6xkqo3lVjeum9l3X/islnIxdX28Nv9SnIR5YtAGA+5a189sz15Dy+1Btm5dsf4yjta1IO8/geO6MgJmXB0IB8kJlSTyFIgSHagIEMznCeRupqhg4MuTIgVG/URPN5rt7w4GXDf9XeFc5y+xGXCWM69p/5a/NxhKjwRrtpUdvx0eGzUufS5Igat7hyy85h21LWwonS8kFe3u4e3k7zkSGP9OG3LR5wlJOLT0wLbAchJSsyZmsS6a5Y0kjLfEsRwO+D45+quYLC1tad+OqqkQ17W8nAgGtK9VPkDQSnfV9h4ikTB5e3jYlCgAh2NbRyKqBROG1lDNFMX7OZAoFvbCPmhSC/YZOWlU4pS/GE80RmtO5z4hPm14f+zRcJQwFebqtaPjtwlplgcOwHgWgu7F2zvmxgEFTIjeeNPYpbi7E5MrGnKIQNzQiOQtbVQjZjgo0z19JKh9XCcMRys9U22JvpAMLDYlN2ilkOljXPTjn/PqUiT2ZKJa5S1qnVyWOnMxNXmPZtGRM+kM+fHmLIZ+eAo6WpFAViquEcfnga74QTGUO+WyLe2pPp5suZFZF2JJTDvbz0i27UMez6IRyeZYPpeiN+KY+fJ8yQxzKeDUipETJFfJ3BW2HFXmTuF9nW3OE1QMJZyxgPE9+1HBPsOUCXBV8TnB9wy8WN8T33dFosfiosphOp5s29Sh+mcNUVf644nRuWX8OCV2nuzYodctBt/KyJ+gnq2syL1TZlkg7liLsI5EQHSOp1ECNT+Z1za8LbqlB/FqR8mzdlnc83lj7B08Uc3GlMCbYKT7pjFAvzuVexDTnJpEMiiAvf8uHz737+8vuLaOJVYurqpLZpPFxGg/MEAWAQOCXeTxRlA5XC0OQtXSKZ1OyUNzr6qoAVwtjk/xPw8TH7LaoxGGY9vIYdYLgamEA5DGQ5AGLgkAsjtCBTrbMllU3rhdGhghHWY6FioNFFh9parC8zDolxfVPN0+ARnrQyKBg4SfBCnagO16IUUpcLwxJDj/JGcdU8tQydzssj/nD9cIIM1b0uEG+6HGP+cH1wjAJSJPQjGM2GjkCZbLoxMDVU/sAMtTgQ0OiomIi0chSywAN1JXbuCrG9cJwsJwRmtUwfgxM8ugkqEEoCS9zXwlxvTDaiBuHlHo74UQnj6U0jWVWt7fcvYS4PsYIyG87YWfgZY6akTkVLDUr662el4Xlt729SkqIq0dXPcqH6z2GR3nwhOFRFE8YHkXxhOFRFNc3Vyf4zIW/7txT33Qo5fcp/qxj/s/vzveaqyWkIjzGd0//fu0vNp7ZHfO1KS0xg6S/wbj0VQ95zakSUhHN1Zdffq+zJKGJjmRq8thA0M/OsG/XjZtPXVNG06qWiqhKUkY9HYkeFKew9lQKQXMqgwkry21btVIRwojmLFTbnlxLJKQE2yacy3s5MkpERQijNZlCSImRyaHmLaQQ5P0GAaQnjBJREcFnVi2IQjMthATFkRjpHAMhb05GqagIYQwEggjLRgKm3yAb8mPpGgnd4F+fd9eOcttXjVREVdIxOiLiAR+GpuFohTwXeb+PiOMwoumry2xeVeJ6j3F91w+dhkSG7YtaJkUxQWcqw6LYmPvb2xWIq4Xx8xU/e73phMXanjhNqbmZgAWwPNZXEV6v0nC1MGysn6iOg3Akm3YdmUqbNPG+opAwvJ7xUuBaYdyu/PfSpsOWIhxQbQdfNk/tUBwlbxUEIiVZw2Ak2lluU6sS1wojpwX2J6NBfCkbX8ImE9DGez4LnkIiCKTSaF6EURLKNlby25brVAsW28iTcn59y7vf8HyzLpHOPWtXt/2Gm24bS0VajWgyg+n4GG71k/Or9He1YBlTIYWat1BzOY74fJe+9e+P3KjiyDwqB6MN4qWjV1iPim9oDy7vsJFSecu+y2yAmwI/8Q20hM03HHyFJ6knYcGF8duW64K2YMRRhc8R4CgCW1G4c2UXPztrPVf9371ctL2biJMji04WjcGOAMlIkKH2xjn3O3/3nTSk+lFGWsjjRyJwAEcopi4dI2XoHGqMktO1I7WJbHMy5DMAAtn8XemgceEbDr7CW4ZQhAWvSmy41VGFz1FEIR20EKhS8szdh3nz3du4cEcPQcdCAaTm0KgkaEylj3m/rBFkw8gBOthNIdcOqIAuHQMgZOZZ3j8CyM5Ejd+QikAqgnTQON+Xs/5rIcpciSx8jCE4EyEo5qfO3nsUzZIoSBI+nYiTw3Ac2sbidPUPo+dmrldVbJslowcAiDCCoPiKAp9tE8zNXesqBW847vJUKQsvDHmM3ElALODDViCPQtqvozsFL68Ai0ZiLD7aj54zUfMWWs5E2DY7WzsAMIWBPEZxJJBX1TnHhfRyex6LBReGAq8Ujixk0JoW35iqwg+fcTKZsEYOjbyqzkiwlPZp9NdHCGSyhFJpgpks/pxJWivEHQNyCXMzwBYYjITI6WphuH7CDtuRjireNv8lrA7K0ir5Vct1FziCv0lFqI4C+xpq5U/P20hfJMxZ+7r5tzseFYYFtfksLclCboyDLfUc6Jyb1VlRsyzdf8QxUurNTZncKhvFl9R9jwrB44ZjhXvrak4eCQeTwGc7ehLPzAXU19uK0p3XlTe+su81hxa46BWDK6f2/TX03dV5xXgipKYY1aIETZORSJCB1pnr2yWQDBjcuHGVcu+3OtxXkArGlcIAuEn7Rr5Ry2mjoQhjegQJDLZGZgykbe9oYldDhDt+tMKbsDPPuLbn8/nWu/W9tbXOuSP30t9Sy6FFTTy+YjG9jfWMRMLcuXox3z97A32RcLlNrUpcKwyAkTptJI3OjhXt9HQ2oUpAVbB8BouTWdqSGVYN9pXbzKrE1UPWVz7x5qa764akqfnQbJuWsRjqeLa+YM7k8u27MJKjXs9lCXC1xwA44q+/3JaSQM6cFMUEHfEMeekNo5UC1wvjVb1v+a0tHSwxN760FI26rON5jBLgemEAWCAHIzVzdrdKaxqRXG5jWYyqclwdY0yQ8+lkDI39zY20xBIYtoWlKNzT1shtfznL2xqzBFSEx4hkMtywpo3DtQH6IyEO10XYvHEpNVkv0XypqAiPMerzIaXgryunbUVhO7z84d1e4FkiKkIYPZEQmA5oAhRR2BEx77A/EvKEUSIqoipR05k/aY4DliwIxJKsGIkTyeRPK7dt1Yprx0pmc8rbD+f7aiNa3KezOJZkSf/w2J+vW+dljS4RFSOMCVZ+aNjY87mGY0728ZgfKk4YHgtDRcQYHguPJwyPonjC8ChKxQhjj/Gh05/wfcTLhbFAuD743Oa/5uSojG3d3bYC1XHoGugmpoWDpyU/lim3bdWM63s+zYC+9eenvpy8rgPgX34S5z+6JUUFebtKxPXC2NG2llN27KUxnSAeCvLI6hXs61wmzi23YVWOq4XxP12/HGiRJhsGjgDQloyxaHiIOzZsKLNl1Y+r3XG2JtK0YqCPlOFjZ2s7O1vbcRSFYCLGu57zx9Fy21fNuNpjDDdESB02uGf5Kuzxtad7m1vY1xjif8/cFD1z/VfOfMOO991fZjOrEld7DF/eZHvHoklRANiqSkb3E6vx8/OTn3NvGc2ralwtjGA6w1gwOOd4XToDps1QKOitQCsRrq1KbvH9KChWNBUm5wDRdIrFI4M4QnCeOcZFh//OO15whSeMEuFaYai2tU43LfI+neb4GOft240ynm7FUhQak3ku2fs43s4UpcG1VUnCEN/LhHzUx5Ks7zkyKQoAzXE42NjFc/bt5bLLHvx1Gc2sWlwrjH5NWyNtBy1posq5a4osVeVQw2JWjsYuL4N5VY9rhRHQFF/TQJx0UGdL21JuX7Saw5H6yfd3dC1iV7SL3qZWTnl7b6SMplYlro0xuuIZcaTNj6UVTMyrGtubOzF1jb2LOvnL8mWIRArDF6RhbHgU2uYm2fJ42rhudPWvwR+dsXtp65ZA3hRYFtZ4H4YA9jZG+d2pq+mPhEgoCo7l8KzBUULJBPh8bKuNEAsa/zL2xfofl7cUlY+rqpI7/D9o27O87f6DKzvF8p5BLFVBKiAVGAz7+fLFZ7C7uY6Y38AxNDBUttWEGI1G+c8bb2RjIk0kZ/0o+KH4peUuS6XjKmFkNO2H3YtaqI2nGKgNIccTxCIEDy5uIz9rvxJUhWFd44lIiNtWreK8fXtpNm1Uhe+WpwTVg6uEIRENAEsOD5AqZHaexLCKJ3fFkQwjONTQSCQ7OXfH2yztOHGVMPy29e727iFyhkZtcuaC5TMO9xHKzlpOkrMAUKRk8egIjyxaTFoROJJvLpTN1YqrhPGszFvuX7y7+6dYFs0jSfT8lJfwWRbNA2NgWpC3IZMH0wYBp4yMMVgT4eGmJvqC+qOpz0Y+WL5SVAeua5VM8Df9+zKnGxxe2kDLYIx/ffkzGQiH5px3yd6d7Io2y92LWjX5XyEvu8484SqPMZ2Buhqp5SWbHu+mczCOKPKR67bFR++/jyY42RPF/OJaYZDP2wErP5kd/H13PDIjFziArgm+ev5zuOuny7cvvIHVjWuFoWfMXfa0pPHP3tPNL//nz1z26B4IatAcJB0OsivoP/ZmJh5PG9cKwwr639XTHGG6OFYMx6DWB7V+EIL2xCgP/3TF3MDD47hx7VhJw2j+zu4lYZIYxMI6dSLNDy88lRtPW0l9LMm7/noPPisDXFZuU6sS17ZKftH1azEaCTurd/bjkxZL6Z9zzl/WdPAvO9/szeIqAa6tSq7ofqWUwmHv+hZiQT9OkU1qhoIBd6q6CnCtMACEA8sG+ti1qZMHVs2cwnegKco3zzzvC2UyrepxtzCkRS6h8bwtD1OXTLKntZW0ptMdifK+F73AOfTtrg+V28ZqxbXBJ0DaUZyO7LDSo9dT05tDl3kSBPFncjz44+XexJwS4mphXLz7sDYSDTqtY4kZx/vD3ky+UuPqquRU+S7ZH4lwpDGCpQryquBgcx1DXuLXkuNqjwGwp6Hhi0v0+Ae6m+oQUmJYFvtr615VbruqHdf2Y0znjc9/uLYpnb5DSpk+2FB/0fW/X+9lly8xFSEMj4XH1TGGR/nwhOFRFE8YHkXxhOFRFNc3Vyf4XfMPz2tPJ+9UpS3CWZOUpv/5tNy/v7DcdlUrFdEq+eLJ/1cnFHukPWWhAkm/QSCbZPmR3veelX7/18ptXzVSEVXJmCqGVo2kOdDVxp7FHfS2NDEabUQIvlJu26qViqhKAroh9ixqp3NkiI6RIcaCYfa0dfDI8hXizHIbV6VUhDAWjcRpslNsOrB38tiqniPcu9hLs1QqKkIYK44eYXFuZr7XlvgYa7sPl8mi6qciYgyERHMc8opC3B+YzMbVWxN0f+RcobjeY2TFv7xkoP48cpEQv910KjevXERaV3nZ9h2sOTjoTQQuEa4WRkq8YauBODkd9nPXmhV898z12ErByX37vLN4ZuNB3lNmG6sV11YlY+KdS1WUkx1UzFqNR9obJ0Uxwf1LO8pkXfXjWmEomPtUJBoWZx7aj2bPXbMcNL3tV0uFa4WhYwoVmyT1tMZjvGLbYwTN/Ixz3nDvVv4c/YG3yr0EuDbG0LARCGwMVExe9eitbNq7h4dblnL3skWELItNBwYxkF4AWgJcKYwD4lOXdKAAEgUTP2n2sBZSfjbtH2LT/kFiIZ3upjq2tzdzcbkNrkJcNYhmitetk2gPpwj4DCQaaQQ+YtTTzRIAgqSpJYGKQ0z3c/XLX8Tempr8UEN9aP8XPt4GrAUeCMqvj5SzLJWOa2KMnHj9gxDYAT5fiCxBhlHQsPDjx2Ixh4gySh0xVAphRW0+y7/eex9vu+cxvSMRM7OqegC4CTiaFu95TVkLVOG4w2OIS/0m9RlQkdhoxLCpxSSEgz55mgSyBGDWAue7WpazdWknhNJ8+NZbJg6PAR1B+XUvscrTwC0e47SJD1tgk6IRBTlDFIX3QGVmvs+E30/bWILO0QT3LFk2/a0oMOOAxz+OW4TxIOMjIBKVLGEkEpjrzeSs3/+2diNZXedoXZjnP7Fj+qkjwF48nhbuEIbcnJOYd4GDQEXDxEZHZ+a6IgeBRCBwMNHoNprpC0fZ19LIbesX85b77rXGT00Abw3Kr3sLk54m7hAG4JP/c4EguQgyKcjLNAEKzdUcexvqSKk+8hgIBH2hWrIE8TsWXUcG+dCLz832BaKq37Y7gWdQiC1+V+YiVTTuCD5ncUR85KJahm81puXR+fOS05G2zsreEYxxv5D0+8hInXOzb/c6ueYZVwoDICdeL1V0JoJSE4NusRxlmrlj4SAi63B6/l2eMOYZ11Qlc9Fm5N0yMOmUB9CVDKauoIg8zclhHOl4YyUlwLXCMNHmDIKYmiAZ0AjaGfwyTyzo50zrPV5mnRLgyrESAIPsBgffDgWJAB5sWsPWxlUgBIrjcErPYe5atYiTym1oleJaj+GTP3s8j9wlEQwadWxtWl3Y7QhwFIWHOhez4uhAma2sXlwrDICw/OEaE/3y7ZEVcyJkgSCU9cKLUuFqYQCE5bd/m1UDMHvnAcsia/jd2aSqAlwvDChUHasG+hDjOzZrts1Jvd0I2/KaqSXCtcHndEYCGqf3DrB4bISkz0dtNoPmOOS8yVsloyKEkTF8mKqKz7bwpa3J4z3RxjJaVd1URFVyy6oueee6dTOO7W1rI6lqXoxRIirCY+xraak/PJQc+8FznsOS/n5GIhFiqp/zt259f7ltq1ZcO1Yym3977t3rc7q2vSOZF8F0mrP27fvts0fecXm57apWKkYYHgtLRcQYHguPJwyPonjC8CiKJwyPolREc/VbK398b3+w6aysIeXrdz0S3BD/eK7cNlU7rm+VXLvqN87SHlPUpUwsRfC3jZ0EGfzyf2x9zb+X27ZqxtVVyU+X//j8rv682JA6xEk8zgZnN5c+upO99Yu8jq0S4+qqxJ9I37I624deM8D+Gj9x3U9jvJ+TevvKbVrV42ph9OuasiIyxlte+kZuX7YegJVDvXzixpvKbFn14+qqpK+jQb95/fpJUQDsaWzj+tO8mZ6lxtUeI6v62NnRNuf4ox1dZbDmxMLVHuNASx0pYcw53hRLFDnbYz5xtcewbD+f+fWd9L6lhcFokK6xUeJagKtv/SuFxDkepcLV/Rg/6Pyl9Ksmzzu8CzGe2tEWNnmp88tl6776vn1vfF+5baxWXF2VoMDFh/eTx49JgDQ12DKA0BxasrF3ltu8asbVVUkmqGCjEidEDh0feSIIIlaaaCbpzQQuIa4WxpGGCN857Tz+eMpqTFXl/CcO8y93PYyjZTFVTxelxLUxxtc2/aLl0eYlfX9fOXOzmpfcvwu/HaPBGmHJ2N7LLjv4yc1lMrGqca0wLn3u37J2TYNvR/vMfozO4Tjv/NN9/PYZy7ln/SKO1NbROjJINBbLDTQ2nTNS17hVXuXNHj9eXCWMlHjbSQfCgfsfbTjV999nn8NwJIzj88045+zu3Zw+uIerL3k1ebVQEyqOjTNrZwLVsrh47+MP/PlHX90EYiJVggQ+qsj/+ewCFKeicVWrxMfo/Sm5yPfBl76QutEMSi5LNDOVptOw8rzngZt47z238Ilbfl9ItiPAUdXCSvhpP7auMxwMnwFCHT8NUfjvM454wwXlKWHl4JrgMyXeesbRaJOvJ1TH0WiElpoxPnXHzZw12M23zj6X9sQYl+x+iCWxIQBe+/A9fOwFr3jSey4bHeIYIer7gDvnuQhVhWuEIWAokM8RzWQRUhK2TE4d2kdjOs/JQ4d49bb7Zpyf8Pmf+p7HriYHj9/i6sY1VUlQ/uBASyp90FQdXrn1MU6K9zAaFOQVhYRew3AwNHmuLQTbW7sKEYOUxfLEAnDvomXYYo7PkCA+WapyVAuu8RgANoHlp488fEPifl6UDNewZvQob33uv3L1/XfytbNfiCGzBPMmWVWjPpNidX8Pi0YHqDEz/GntJkxVKwShUuLP5RJXPLrlX1VpfUCinDKejucw8GxF/rSnzEV1Pa5qlUzn1RfffPh7f/tN1+1Lu/iX57yJ4UANkVyar9+2mRfu303GkFy3ZqP8yMtfjyOEKXUjIj+geXthzROuFQbAXZFrZdA3wrfPfh53LF5DwLY588h+Xv/QXQgryQUDn/K6P0uEq6qS2WxZ1M6fT30x/bX1+ABH1bh38Soe7GznrTfcmvfanKXDNcFnMZKaQX+kbsYxAfTWNhC2Hc9blBBXC8NQDAL5/JzjlipICm/77lLiamFsbW3lX7bciQRsAQ6Q1VXO2b8P2/C9qNz2VTOuFsbSngH5wt2PUJ8aYGdjDTubaghlx7jy7tt5z6FX3vLUd/B4urhaGGtGjvKpi57HnUtXgBBIIXi0rYPNG04ut2lVj6uF0RC3uL9r8ZzjW9s6y2DNiYWrhZFXpVw5PDTn+PIhb0vVUuNqYeyqr7Xe+/c7qE+nJo+1xmO89d77y2jViYGrO7gs9P4xLdq17euf5/frNqLZDpfs3MGtLd4SxVLj6i5xgBe/6jF59c13sCrWg43K1oal/H15i/z8lhe62ttVOq72GAAnP7Zr6KpXvLixOZYBIJoZ451/3OZ6uysd13uMCV7w0kc+nDT0P975mw3bym3LiUDFCMNjYfHqaY+ieMLwKIonDI+iVIwwrj3j5mWfuOCOReW240TB9cHntRv+eO7uZYvu7m+oRSBpGxijJpPq+MJt53oTekuI6z3G/q72u/sao0hF4CgKR1vryWq+o+W2q9pxfUdRX1OUk/YcZuXhQm7PPYtaeXxpe5mtqn5cL4zVB3vZsO/I5OsN+47gKAJYWj6jTgBcX5WsPNI/59gyb+vukuNqYXxy/R8/qhYLjucuO/SYZ1wtjHgo+B9Zv4EzTQiOEIxFQk9ylcd84GphKDnTWb2/j1hDmFhd4WessYausQES4v2e2yghrhZGQzKtDkXDPLG0k52rFrNz1SL2drVTm0rzg7XnvLfc9lUzrm6VBAX6Y2sXkQtMy4Xhg3vXryE/OLq8fJZVP672GAHLFunA3AQpPfUNRLO218lVQlwtjFAqR208Oed4IGeSNWgtg0knDK4WRsAGw7KpH4mh53IMqwo9ukrH4ChLRsfeVW77qhnXDqK955w7apenM2Mn7e+lNpnB1FS2rV3MbRuWIbMZPvynG1mZO+QPye97OyqWAFcGn7f4fviZ5HlrPrxmZz+1yQw5TeXRJW3kLIc1fcPc3d6IPw85lKwi3oZExUFBYOMjjoIF2JMZ+yQg0SUIBKYU0CvgAHAQ+Aly863lKqtbcZ3HuEv97g2PddVd0hup5aXb9xEP+Pj8y5/JYG2YTQe7edWWrbTFYzRYfTRwEIcQJnVIwEEhTDcqVtF7S1QsgujM2QjnI8jNXlLYabguxgg41iV90Trq8oV9Vm/buJzB2jBtozHef/PtLB4ZxbAcEjQzyHIM4ijkEICP2DFFASCwEUgc1NlvfRhx6VPnhzyBcJ0wFCRNiTS16RwZVaO/NgzAmQcOozkzvVuKeiQClUKYIZ5EFBMIHIoUuwbw+tmn4Tph5FCToyEDPW+DKmkfjAGQ8PvmnFuIJSQOhX3T8oSf9N6F6kZHMCdLz+3IzcPHb3314DphgFh73s5DsqeuhpCVozFr0pjLce/yJfRGamacWc9RLELY+HAAB5U8/vFgc/aPwCaIQmr6LRzgz8BrF6RoFYTrgs8JTnvNzuRX/nhH6PaTNnJ4UTM24MubbNp7iEg6w9kHttCaPyJtLGmjSgfbNLDv1FHer2DHVehzCDoKaYnc7JS7PJWGK5urAA/9cm346xt2S1UWPlMVsHSD+9euxJ/L0XFwl1wiv/CkHs+F7rBicPWz04TCgyu6UPL25DEpJUv3HeGoEd5eRtOqHlcLoy02RiBn0hMJMarrxHSNrCPZeOgIvz/ztD+X275qxtXC2NHeJp//+HYi2RxCSvy5PK2ZDHtWdNKRSM1NAOoxb7g2xgBQ4nHnkVUr1dq8BZYNUpL1+TjU0kwmNhopt33VjKs9RlrqtqnpU5N/RWHRkSVU1vcnvlle66obVwtD04Rw1LkmRseSvHP3y3aXwaQTBlcLw5fO7qqJp+ccbxqIl8GaEwtXC+PD+16xcd2Ow+jm1BhI+9Fh2nu93utS4+rgE6Ctf5SLbn2U0fow/myemkQGI+9tZFRqXC8MRxXUJrMEcoXWqS9vkQoZZbaq+nF1VQJwqKsOgSRgWgRMCykEhzvqnvpCj+PC9R4jrqr2Y2ta1OahJI4iGGwIM2Zao+W2q9pxvce4+vEXaUO6ftdAtEb2R0JywOf78n/seWl9ue2qdlw77O5RXlzvMTzKgycMj6J4wvAoiicMj6K4vrkKcMmlW3sPdLa0Zg2NcCbrPLq0TZNXaV7UXEJc7zEuu+Th0fvXLW1d3jPMRVv3YwlVWXNkyJvcW2Jc31x99uv3yNff+igre6c2yPvqS87ivo1LAoc/VZsto2lVjes9xsZDAzNEAfDGWx+l8ejwJWUy6YTA9cJYPJJAAts6GtmypBVLEdSmsjQmEk++7MzjuHB98HmktY6vnb2Bgw21AATyFp/9v7vJGtrcGTwe84brPcYfV3VNigIgo2t88LJnkNSMp17B7PG0cb0weoskZ8sJBStv/rwM5pwwuFoY31zxu2fV54ssH9EU6i3L9dVgJeNqYWSkvOnFB3tQtWlJgDXBhkSCdG3EywxcQlwtDJ+h6X7gX/Z1sy6foVNYXDwyynP391Kb8xailRJXu+NALo+Ry3Pm/j4uuv8JelrqONrWgK2q+JJeo6SUuFoYttDEhffu5K6lbew4aSVjhkYkkyPZHmXQ8xglxdVVSVMizUNL2xla3kGrCstUhRZV5c76OpoTcQbFR7w4o0S4dqzkduM7HzjaWv+FwaYoml3I2Zny+zjY1Yo+0suGkd0c9DXx7ofukA1Wv1CwZSHtmkSAlICCcEAKCtmWcsARUA+D/ScBv0Ju7i1nGd2M64RxQHw2uMfflNrf1UB9Ns9g/cy8W1KTXPHIn8Dxo2DhYwg/aQQ6YFNIqwUggIn1JyYgC5JBIHCQhTOuB17ppWKai+uqkl619okjbVF0oRa+69MImRmes+thNMdfSL1EkCSLcZjoBJv++UrAolBEHQCBREyKAyRcBrymtCWqTFwnDAel01YVAjmTYHbmUsTzD+2gLTkGFPyBjgkIctQyUxRTdyswvZjjvmLyX545X7ZXE64ThpAyqTgSUxG0DcXJaIUsvrpt0ZyOzTwXUCarj2JFmZ5NfPoxOf3oo/NnffXguuZqnZM8vbXft6unOcL2xS3c39XEtmXtBM08Fx94kKZUatYVEh8jFPL6KcyMMXQKH7857Vgh6+e439gJ/KjkhapAXOcx1skP7F6f6jM6Dw+NxcN+uobj7Gpr4JHFrVzzgufN2FFRIlFJABIba9x3iPFksFJK8lKSlRIpJYqUKI6EnERsFfAyYD1ys9dTVgTXtUqm87VNt8iddUEyQR9/OnUlaZ/OJY88Tlt8mNP3HpYXH3j43lb57fPKbWc14mphXNf6a/mdZ5/CRQeO0hhLkddVbl+3hN+fvIIX3fWI8383nDlnGwGP+cF1McZ0ctKR5/UMioCikKyrwRGCcw8PsKutAUv3cmSUElcLY3drPbVmnvuWtPKXNYtI+XTW9I2y9ugQgcTcTfQ85g/XBZ/TCWfTVl8owOZTVpD0G0gh2NlWz57GWixFuLcOrAJcLYx80H/tHcvb5xx/bFEzGV33BtBKiKuF8fFHnv+RwZq5qwRUW3Kgsc7zGCXE1cIA8OfyhKbPvZCS5niGobDvneWzqvpxdfAJIDSVCw4O0xMJkNUUWhM5bAF9juUtTywhrheGz5GoErpimcljeUXg8yqSkuL6qsRXZPlA3KehgNdeLSGuF8aikSTDfm1yaCypq9RksvQ2N95QVsOqHNdXJYMCqzOZ1dKaSl5AQzaHksky9IPF3mzgEuJ6j/H3/12pdxs4NekMzfEU/YYi/768dW7nhse84upBNI/y4XqP4VEePGF4FMUThkdRPGF4FMX1zdUJnmh4++mRbOo+Rwqzu6ktfM6hz3uLhEpIRXiMR+relumub3lgNFynHm1sDQjHtnfWvvXqcttVzVSExxhpaPU/Z+/UVu4ZTWd/tPHzwBfKZ1V143phZMSl/36aPzDjWMDKMxCpY32ZbDoRcH1VYsJr8+pc/QqvY66kuF4YOTDH/MEZxxxgbf+R8hh0guB6YXzpghefEcnOXCymAOFcpvgFHvOCq4UhrrX0v558jpLWfXPeMxwbxKVXlMGsEwJXCwPYcrChmRozN+cNTUqy8F9lsOmEwO3CWDMSijAYjhR985BqeHt5lwi3C0MD+OXJ587IcOFQSGaQ1XRv/9US4XZhZAHOOLKP6auLlPGfjblUc1msOgFwuzB8um3RmojNyIsxgQD+uPijdy+8WdWPa2dwiWstgZTOopFBcrpBfyTKOQd38d3rv8fGvu7J8wZ1H03mL73livOMq4QhrrUE0Ac0YNsCVVWElMhp3mLpcD97P/8elHG7HeCOhva/P2vovy8sftNL11DIzvcN5OZ4qctQLcy7MMS1Vh1wP7BiXm88jYe/cjWn9hwEwFQULEXFb+VnxCFP4UI+itz8mVLZVw2UIsZ4jBKKQrVtWhNjk68NxyFo5VFgPIPnU4oC4NOIS88vkYlVwbwKQ1xrdQIlndr/zntupm1cGMc5U+erx21MFTPfHsOe5/tNUp9K8PLtW3jjA3/jG+c+l0vedDXbW7uO55Zzu1M9JilFjLEbWDmvNx1n0eggh+uaJl83JuNs+/JVkx7kn+Qk5ObtT33aiUkpYoyTgLuYmY73n0dKcAoOSDgOpx/ew6f+9Eu+dMNPqRkfbR0KR7j09f9OwvCRVVRsJtK7Pvmdgcs8UTw5rmquAohrrc3AhUAtoIZyGZqTcaQQLB3uZ/VAD8/d8yiXvqkw5fOSx+7nu//z1Q+3Ob/+3LFveqmG3Oxtx/lP4LqpffIq7VIAca2lIKUdyaT58G2/4y333zbp3oYDIU7qOcBjrYt56fYtPKkoAE8U/zyuE8YE8irNEZ9JJ888si/8tvtvm/FeQybF137/EwYDQboG+79UJhOrGtcKAwAp02d07yu6h/uFB3YyrBs0mtddtdBmnQi4exDN5/M/1LH0mG/3ovUtoDUnFO4WBlh/XbGB604+d8bBiZZHu5X1xj5KhNuF8Q1b1UgZPj72nMvZ2rqIhKZPdnuHpVOyDrUTHVcLQ16l/acDxA0/n7rlN5zSd5gaK4+k0B3ug2vKbGLV4mphAJy181HrtKP7ZxwTQMIXALn5uvJYVf24roNrNo+G3vjQmlxmk8+e2RUhASE3exN0SoTrPYZh5cVsUXiUHtcLI6AbN5bbhhMR11cliEuFBGd2nWGB1ORm1wu7UnH/g5WbZRYSE/KdaJFocOyeL4/jxv0eYwJxqQ84DGxDbn5Ouc2pdipHGB4LivurEo+y4AnDoyieMDyK4gnDoyjunqgzi53imkyKOj8UxksCDB1ZJz95XGsIPIpTMR5jp/j4x1LU+XUhCWCjCkmahs6d4nOBp77a45+lYoSRo+YaHzZCSkxUFCnRBZjYiXLbVo1UTFViKTq2o5HDmDzmk3mk8HkjrCWgYjyGKrPk0Gccy6Ehj3cFq0dRKkYYUTkq5q5jFyjSG5IvBRUjDISKMmvNtMDBwNtMsRRUjDB8EjoYQKPgIVQsOunHEhUTJlUUFfNUTQyipAlziDwa+rhABtVoeQ2rUirGY+wKdpFWDQRgYCGAUTWEZnnBZymoCGF85ZQ/rU/5/WxpWMWhYBPDRpjd4Ta21S5BQfWaqyWgIqqSRDT8mOwexVQNdtZO9YDXpDP48XYhKAWu9xgXvPXgkttXdhKJZxH21KQiYUvqkmnylaHtisMVTzV69YiZDvl01XJkIJX72ciXG98IsPJdPX+r82nPXNM3hpQK0cEcpk8BAUbWIScMDIqkDPY4bso+tS/4HwkHQxMdQwnqsyZB26Y1mcFBcrQ2hF9Klg7FufiOx6kfyVJDCg2LGGFsv2BDdj89tEgTQQ2jR07i4TOQm/vLWqgqoKzCaPxo3MgpIqfFckghaE9nuPzQEQynYNOh+lpMY3wTGyl56f33cvHerQDkUdkV7iKSHsNxapGAiYqj2Jzl3H4mcvMD5SlVdVDWGCOVyg4ExkUBcEH/0KQocpo6JQoAIbhp0+nktELtp2OzJN3L0UAH5vj1BjZxJcwYdd9e2JJUH2UVRj7gq5k+0lFnTnVvm9rc8CenGwxP29Qm4JjkVB95RZkcRdGQpIisKpHJJwxlFYY/Z37bN82Cg+Gp3RL9Zr6Q0nEaNZk0LbERBDYCBwvQnTx+2xpPpiKwUGjmyM8WpgTVS1mFkfxy07u68hYRxwYpuaepgf6AHyjsq5rQBPnxasIB3veX32JICwWJgkOAPKuSe1EQSCCGjyXW/mEd+0PlK1V1UPbm6hFdpE9O5oIDAQO/Y/F4SwM/C+mO9PkVFYeTR1OEHFjdc5jm5NiMawVQzxiHaIzXk/rfc7j3I4b8ZawsBakyyi6Mnq+2hGo/OFpXk7cPWpq6ve8rjTN2BXj2FUNOY94Wp+3pLrqrgInGRfK9tQtk7glD2fsx/hE+deHf5UvufJgueRSDqY0THcBEygb5ddf34FYaFfFAtXTGkUIwSBMZ/ON9FjpHtFbyzN2s1+P4qQiPAfCI+lXpd6ZstRH49BhWXpVr5ccqQuCVRNljjH8UxxEkFB1NsZFC4nOyZPJBJJnKUHaFUTHftP5QHXHDh2pLtLxgUK1n1KirHGVXGBUjjLymkvIFSBk6QsuDBqaqYlZOESqKivnCBWSSVYkBIjIFQIc1yJioYcjQn+JKj6dDxQhjRbxbRrAmuzIEUCuTZEx/Ga2qXirHD4u5pipInAoqQiVRMU81q6lz9jqzESjHufWaR3EqRhj5vHBMbJzxjnEbQUqR5JHeavcSUDExRnfdYp8RO2RZuiRspYlrNfhyNqfKj0fLbVs1UjEe48Ujb7RjangjOZ807Qgyp8m8CITKbVe1UjFd4h4LS8V4DI+FxROGR1E8YXgUxROGR1Eqorm66K1PPPS6fY9sWjHWz5aWJc53b3qZWm6bqh3Xe4y6t+8b+sI9N2zKKVG2N27ghft2KV8/8zteU6rEuF4Yp8WHGu5ZdCH9DV30NLTzSOd56JaPDW945H/LbVs14/qqRBDi52csYiRUSAAcMPNc8VCQhlzq1cDrymtd9eJ6YexraZ4UBUDG0Ll3aTv7vTSOJcX1VUlfJFhYqmg5kLMh7zAQDnDlg3d7AWgJcb3HyGgaZG2YNkM8kEhgBpvKaFX143phkLdBCqYnzumO1ONzvNxbpcT1VYk/n6dYNqUac6QM1pw4uFoY4kOjOafYilUBK0Z6F96gEwh3VyWarpuqypzZe0Iw5PP2ryklrvYY2E5hOrhC4f+J3xVBQzZXVtOqHXcLw1AFeacQYyjjP0KA5XCgvrXc1lU1rqxKxAfjrwV+hqGCZQMStHENO5KlA70EHM9jlJKyCENca6lAdPxnA7AVCAM9jKafBxTGQfI26GrBSzgU+jIsm0OdHTw62En0E8NOLBiearUIRc6KVS0gBtwG9AOPAQ8CQ4AN9MqrtCcdkBPXWvVAFlDkVVpy/FgzkJdXaaPTzqsB/EBcXqVVvGoXfM6nuNZ6JfBtoH7Om1LCUKbgHSwHNLVQfUzHkVBjsKl7Lw93LZ/yJE+POPAKeZV2SxE7zwZ+DKyZ+MvADcAmYNH4sR3Ai4Bvjv8PYAKfk1dpnzgew8rNggpDXGt1Agc4lqeybEiMZ+vzaWAVsU0RENCojcVIRcNY6nE7vQzQJK/SUtPs1IGDQPs/cP1hpoQynUvlVdrvjte4crHQwefFPFn1lbYKH7yhFaqHolnCJbppknbU+RAFQAA4Z9axjfxjogA41obAz3/aFrmAhRbG/id911AKVcWEIIpZ58DS/j429HXPyQN6HByY9foI/MObrWWPcfzJy+pyFlQY8irtTuD3xzzBrxdiiwlme43xvozdjW2c07cPnHnZ3ein8ipt3yw7B4DPFTk3Peu1BD7GXHHsAb43H8aVi3IEnwrwXOA1FAK2Wgr1fD8QZzC9AkVE8I9XE3YR+wQsGRng4KKOY/yROXXQ9JvkKcQFjwPfkVdpf34SW88CLgLqKHiA64EW4IMUgsxPy6u0/eJaqx14M7AKuAP4pbxKq+hRPleuRBMfjNuAgiYKH6k2a+qFIvj4337LJ+94vbdXSYlwZc+n/HxERVfqW1JjRHLZqe5wwWTzdUyPPNktPI4TV/Z8AshPhUfFR2xZm8yI5kSM995/EytG+7ll2UZ+eMqFDPnmdoN4zB+uFQaAZlvSVoS458efYPnYIACXP34/p/Qc4KunvbDM1lU3rqxKJrDQeNneRyZFMcFbHv07cd0LL0qJq4WBT33OGb375hzWbRtF9eYClxJXC0NeE77tQH0Lo/7gjOM3rjoVP3aZrDoxcHWMAfB/Kzdx+5L1XHPHb1k50sdflm3kP59xKS84tA1YWm7zqhbXC+NQpIG8YfCSV181eUzYNkOGl9+zlLi6KgEQ0ilM8ZvAkUhLcn/DEq8uKSGu9xgKFMZPrPH5nxJQBaZmlNewKsf1HkO1rMJknInWqaqAIogmhlNPeqHHceF6j2FJW6IIwbS5F8FshqPfX+vtg1ZCXC+MnB7S6saG7Jw/hKnptMZHCMdH/1ZsZqDH/OHK0VWP8uP6GMOjPHjC8CiKJwyPonjC8CiK61sl51zZFxjQ1Y/KnLnO1hTr0H+3vbLcNp0IuLpV0vT+YdMWQlcsB1OAkIJF6SwDPu1D/f/d8vly21fNuLYqWfeuvrOHDF0f1TR0XcUK+zFrfAzUBqnPW8Wm9nvMI66tSvb4jbs1KenMWRyMTs3HyIZVhIu9XLXgWmFYqirOHEuRDBic2z9GazqDZjscDgd4IuptbFRqXFuVNOcsqaoKjqbQF/VzJBokmrc4a3CMZ/aMcNGbD3lz+0qIaz2GXzocbAjRGynk2tpfX8Oehhpeu+0gi1IZdgp6gebyWlm9uNZjjPp1+mpmztIaDfg4GA0jgWxNqLE8lp0YuNZjJAxVFMvvaSmCvQ01mKq3fKCUuNZjBLKWbE9MWxcsJXreIq0q/GVFK3WZfzRLgcfTwbXCUHImzzg4yJqhOP6cBZYkL1T+3tVMKG0STFf0YnLX41phBKSgN+jnlL4YOUWZloBNEDcMhGV5dUkJcW2M4RdSuaOzntbRFHJ2rCEE6N5k4FLiWo8RCwcgbaHaEnVW5hzVcWhMZhgSb/ldUrzeiYs3O2PiDR8ok6lViWuFkRKALYkFDKKpHIplg5RotsP5R4cJWhY68mWSgBBowoAvDIkr5WHxXm/2+DzgyqpEfCTxcRDgV0mqCkkACc2JDM87MkQgnSVqWYzq9XTkj5AXGkfVLgTQZA8G94pPyoNqB5mQgaWoGDkbI2MjKdRCfmketgzt0lgocDmFBLR/B77+spErjpVo7YTDdcIQV8c60ZVP4tPnvDcQ8PFQbZizLRtfzuRwayt1R0a41/8MbFEoyj6ZZ1X2AC3OIE8ECmtbLZ+KVPMEkxZ5KUgrgUW2wgNMrVZ5EXAWcNmCFLICcF9VoimPCU1FOUZGvtP7BlAch96GegyRZZ++clIUAJbQGdJrUdHx56YcQDZQOGcyU2R+ThbRl/++/hed81mUSsZ9wgBdkxKfOXMfNID6rElEChYNjZD2+9jdvJycmLu4OSsCSKlgWFMpveVTN24FMNdNnaC4Txi2fKZj2QgpWZpKsyyWojaXZ2UsxXOPDJIxDGxFwcjn2dmymgZ7aM4tAraNpTjEg1MJ3PyZwhroCanZcyXwt5eNXDE7EewJi+tiDPn5yAPiI4n7bF2cbVhwfu/cvc9U2yaQzfGKbTew2DpATvjo0Qq1QMTKoDkmMTR8poWlKBg5B3/KwmI8+HRyybyjvC6PNj34rOik8PONa+d8av+ZkqsGk2waS+CfZmM4myUyNEowk+MDD34XlUIsIoFdrCeHap/MNh9ys5cm4ThwnceYoC6ewS/hgN/H2lQaX94imMmi2jZDkRoWZXL0Uj8axqlTsWQMbd8a+V8ry213teBaYSAKe9K0WDaWpmFpGqlAIdAMJ9OkDJ1O+b3Jlc015bKzSnGtMBKqKrOKFJYimJ2HLW1oxJsbymPYCYL7WiXjqBJ6VEGfriGmNVuFlAyFguh2sezzHvOFaz2G37LBgdVjcdpTGdJGoX0ZMPPYNSFqM17vdSlxrTBifoNoKk97JosiJeGcOflefdbknsXe9pqlxLVVieFImdEULGWuiXlVIe5zraarAtcKQ3ckq3MmWV3HmTZRx1IUTE3DzuUrfgtLN+Par11jOsfJYwksVSER8E9O1rEVBUsRhP1GsZ0LPeYJ13qMkO2gStgdCYEQ2KqKrRY25+0O+nnovzsGn/ouHk8X13qMQUNjyNDZVRtGlbA6lkQAeyIhnqgNl9u8qse1wkhpygfubIp+qSOb49b2Rv7eUo8ATFVhxeDoU17vcXy4tiqJf7nxy5ppOoeDfhTHQZESzbZpiafQHWdvue2rdlw7ujpB8/uGLs4L5X9rzHyNH6kKoazb9Y1mb95EiXG9MDzKg2urEo/y4gnDoyieMDyK4gnDoyiu7ceYzRdX3pjqiiWDrbE+2WnGRI4gPlL4iH+gS37l2nLbV21URKvkZy3XOX7HEfev6qRjOMaLdz1AC0eJU88oTZiElU3ySvcXpIJwvcd46YseOv/krkbx6Vc+A0dR+OVPv0ID/eQxCBGjmUP00hjHm/Y5r7heGF1H+m/8+qUFUZx+eA8vfOwJIkx1iY/STB0JL/HnPOP64NNn2r5YqDA73EHn2uc+h8HQlHOoZRCdvJddZ55xvTAONNdRl0gD8PCiJfzXc17Bee/8L7JaYQ6ogsSesz7Z43hxvTBSQR+JgG/ytWZbXL7tPlLGtGN4GfzmG9fHGBlDlfa0eZ9f//2Pecd9t8w4R6N4ygSPp4/rPcbpO7udNUcKk7VU2+ZND94+5xyvIpl/XOkxLnh7rxgTwlZB1J+2ls//9K986WXnsmNxg7clxQLhSmGMgm0rirCB/rpaHljWSSwc4f9+8GV8tjXjXAlYqHjJHecXVwpDKorwSTlp3B/PXk9eSlYPHyxabdjuLEZF48oYQ2WmYoUARRH01EbnnFvIj+QtMZlvXCeMC97eq2hF4ghVSr7wrEuKXuO6QlQB7vPBUl7sk5K8OnMDo77aAAebzuFFb47y5Rt/yqqh3slqRQIPi291A7XAFuB5m+SVXhv2OHDV6OoFb++9XTrOhQiBqSjkhUAAiYDG3tYI0/cv+dhfr+e/bv4VAEki7OYMZjVc2zfJK3sX0v5qwjVe+IK3954BXCiEwAbSikJWUcgoCo4DYpZ+f37q+ZO/a+Qo0pvxt9JaXN24RhjAxRO/ZFQVe5p3MPIO0cTMALMmmyWPgYl+rC7xpaUy9ETATcL4DQAS8kW2vKpJmzNev+r2PcSpwyBPHt+c84EHSmDjCYNrgs87v9O274K3934X5L9pUmLOEscr7tvBYF2QpN/g+Y/s5fT9vdQzAECCBsBhms5t4DkLaH7V4argE+CCt/eeakv5cEzTJvNi+PJ5vvWjGwvpl8bJ+mBdbiu1DNFPOz2s+zDwYuD7m+SVPy2P9dWDazzGNLZlhaDOtrGAnKayfVkjD6zs4IKdhwHI6irfu+g03vNnCBIjyDCb5JWfA7w93+cJ13kMgE3v6JOWEIRth4Z4kj9uWoIUglMO9NEcS3L/yk6e8dhBPvHbvwPQzAE65Re9QdZ5xE3B5yT6+DjJmKYSHU3y7K37Adi6tJW/nLKCtKFx5c1TsaXE2817vnFjVUIaZNhxRAjYcLiP99x8iHN2HeFPpy+jazTGB2+8jdbMMCmiZAkRGQ9CPeYPV1YlABvf0Wc6oJ/y2D7efdd2VMBPirXchzYtVXCcesKMoMjNXlUyj7iyKgHY/u1WY8e3WwXSymSMgmOrZWCGKABqmLtthcfx41phTJK3bMUEC2ime87bArwZnyXA9cIYqQmoCoU5GnEacGfFV324XhgBM4+BhQAOsZ79bMSe1Qrxgov5x/XCMKQyw8gR2niEZxEnWi6TTghcL4y6ZMqcfUyikJomjByqF2bMM64Xxp1nrd+Q8M/d8jDMGFCYvZVBPbSwVlU/rhfGY9/pOvLAohaZ1if64iRt7KdmUhiCBnndsrIZWKW4toNrNi95ycM/XzGUeE0gP5L55IM/D44vZpY5gq1h+T9e1+c8UzHC8FhYXF+VeJQHTxgeRfGE4VEUTxgeRXHlfIxifPzUW069eWXn+442Rl6n2LbVHwovy325/ki57apWXN8q+VHXby6qyWRv/dGFp3DTaSthIruOaWGMpN6V+2rTN8trYXXieo/R3RC59Q8nn8aerkZI2yDH52P4VHS/9g3AE0YJcH2Msa8pyrW/upWkUJkx5p6zyRuGN7BaIlwvjMsfeII71yxCyzuF8XVFFH4EWC6vBisZ18cYt4R/KC1Vpb8myNFohP85Zz27WxsKb9oO8jNhz2uUANd7jLymErRtlJCPZsXmmr/cR+dwDABNeqPtpcL1wgjlLLauX8RQQw2JcICjHQ28adtuAPy5OVM1POYJ1wtjoDFS2KF5GrWOjaJASnV9o6picbUwbmj6SWQo7J9z3FIUZFhHqq42v6Jx9VduTM11LzkyxJ+XtvKnjcvojYRYOhyjeSxVaLk6oHxo7Dbnc9GLym1rteFaYTxWf/XDLXprjW5H+MnZ60gEC55jR2sDfeEQm44Mc0DXeUbv8DPxJgbPO+7zxeJSsT/8ftmV6jt1fXq/2LKysyAKR0LChJjJsK1gZy0uO9CDIYV498u3e82TecZ1wjiotWfjgSAOCq2pfl635/9YMdgLaQusqT6XRxvrOBwO0pHKIoQQ771sh7fkfR4pewdXTH9tPmhlNQWJAIZopp7RGQnXDkabWfrOLzF7GdqKeIoz+4doshyw84ACUlqo6iUoyoqvXr/+vxe0MFXEgglju/j6e4EvUohrHODri3nwvSMsAhwWsQvlSRYgLn371zlYXzfj2GlDY5w/NDZ1QMpCLtCZ/5vA+q9ev37vfJepmlmQqmS7+PopwFeYCnYVP0PvHWAFI7TRyZ4nFYUE/vOPdxAwp7xIczrLmSOxmSdOJHSb/r+UBnDzey/b4XWd/xMsVIzxutkHIowSp4EgCRzlyc0YoZWN3TFu+NZm3rTlMU7vG6RD2Kj/iLcriGQZsObpmX5islDN1TkzrSQqCg55DNK6HyM3N4lrihq21a4j5jTTmMjw0PJWfvrcTUil8OWvzZqc3B+bc10RJDB8nGU4oVgoj/EjIDn9wAgNdiPd5PGTzdXSE24FwEGQ1IMAHGQdR30dGHahNXr+E0d495+2IJyCp7h7cTNDgX9oC5tvfvX69d6ipH+CBRHGRvmeONAF/B7oA+6wqV3mp2esg53kCGObPu7sOosbVz2PgVDTuHGSFaM9RNM5FCCQt3jjHdt4xX2PA5BXFR5prwPLKkQoUhZ+HGfifwm8F3jPQpSzmih7czUtXh+RiP0SkUuipHRFrmhw4gJgiHb6WIJJcMY196zq5N1veyEAL9/RzeLRBFLKnBDiB1JV3/3V69e7e5JJBVD2LvGg/FkcaAQIA6Pi1f8t4Z2FnYtMrCK7nY3UBAjk8qweiLEonkYKwVd/f9Lc0TaPp43rej7r5HXv6leb4ln8qCKDBOS0pmzO0Ohpa+KCPX1ceGSYoz4DvITy807Zq5In43d13zPz4Yj+v+espzZtklcUHu1soS1jsjyZ4dFomD1hvxz9SpPrBF7plL0qeTIsn9z/y7PXr77xzLVgSxjLgSN5ogbubHSwkBDQTy+3ndWIqz0GwOKrh+Th5igk85CZuecqAuTnarwezRLgehds5MfFYBcZWXe3pisa1wvjxdv3E86aYBQZVXe99ZWL6x9tVzLNv965rTBRR5tWawhAc735FYvrn6xiS5pTGfyZfGFBsz7xoxYCUo+S4HphrN57FNWRXLL3EGJinsXEsLrPm7RVKlwvjICSYfXjh9myvJ2WdHrqDUOpAOsrF9c/2gPBOqfGl0BzbPraohDWoUYHv4rIWt4k4BLhemG8ufvV6kNNS5Pvv+HvNI4mpixO5aWsC7i6g66ScX0H12zE58xWFBGTV+uZcttSzVScMDwWBtdXJR7lwROGR1E8YXgUpeKEsV9c/Y794kOfLLcd1U7FBJ9HxHsujNF5e44QABo5BGN/2CiveVl5LatOKqYfIEHL7TnCk68t/KhEX1pGk6qaiqlKskTmHLPxsVN8Jrrw1lQ/FeMxJIIlbCPCMAJJklr2czJhhk4Bbi+zeVVHRXiMjz7zbw908QT141t4qzhEGGUZ27DRvVXsJcD1HuM9l+1w/MFaUTtr6akA/KQYY/UaiqyN9Tg+XO0x/u2yHXcJEJrtIIuYauLHwagrcqnHceJqYRhwjgDSIT8mvhnvOTB+zN5XDtuqHVcLY4LabGJyA94JFEDBoo4jDWUxqsqpCGHUpccotngkThM+nO4FN+gEoCKE0R1tJz9rcbONyhhN5AhURtdtheFKYWj/lXqf+EzGGdYKOaFtReUga8mOp0PIEmA/JyFRSRP2Ms2XANeNlYjPZq5CVb84MRu8MZXl0h0H+ORvfkgPq1DJY6MBghDDpIhKUBs2yStHy217NeGKfoz3XrZD+emGrsfjebOdutoaYHKJQGcsTcBW+NGzX0rL6BjrHj9KKFPIm5GiAZACGIGiYYjH02RePMZ7L9vRNBQwvjLi01+V9OtqMG8LUxVOcyLTv6Ml+vhZR0fOCedtH7AXOAeoBzYD63eHDPWWNZ1YuoaQEjkrg9/bt+zG50zZWJ8c5YW3bEOZYfbECzHxQo6/6AfuBi5lpnCsTfJK/bgLXsUctzDee9mOqCXEbkXKptGAQUNmqsrf1VDDquHE7K+yxURrE/jaGStAK75wqCmR4YrHDs85/uy7ttE+cPw1xyZ5pedljsF8BJ9XaFI2DQZ9M0QBsGIkwYh/zhdTm/F3n2TPkfwxBKM487Oc5GHxrZPm5UZVyHwIox7AVuZ++VQJGf3pLyMcCxgcjIZm/rHkCGuGnnja95zFhvm6UbUxH8L4rQSnIZ0jN+vb3xfy05nIHtfN/7iqnXs7GyajiKwSZJS247rnBJvklb+YlxtVIcctjK9ev/4JR/DSvKqkhv06vWE/aV1lX10IRzrsqg/bcWOy8SOBtwIfozDcgZbLF3JyHgNLVcjo2mSckg76sY+ZYrrofexjnPyhpyjaCY0r+jHE53P9CNGElKI2a7J+MMHBujBZXWXFcIIzjo6gjdvZNBzjBXdsHb/SBiarKhv45iZ55f9b8AJUIa4QxnQiHxpKrcjZwZMGYtRm8zNaNHVjCS7cspNIqrA6UcXERgdE/yZ5ZWtZDK5SXCcMgLoPj1g+VVVfvPPI5PTfC+/bxuKemU1UjRxhBlYuk5/1ZnHNM64cKxn9bL3W96la4R+PQwC2r16KNSO4lVgY6FiLy2Bi1eNKYUwwvRpJhAPs72wm7dPRRJrFPIaGiYXSWzYDqxhXjJX8Izxzy3Y2DTyIjyw2KnEaaKSbjDe1ryRUhDACmRynD2yhjiEMckgKW1Zk8WOjui9IqgJcXZVYFLZSlEJQyygGOaBQxTTQh0CQpG7uSiSP48bVwvj29ev9EshpAj/pOe/r5NAx546yeRw3rhYGgAUr1FxGZmZtZgOQJkwewxs+LwGujzG+ef36fYCyR/xFLmUHGoXc4qO0kKCeRgasJ7+Dx9PB9cKYIEWUxzifMGPk8ZGhBp0MaaI95batGnF9VTJBiDEcNOI0kqEw+y/AKGvkR7y5niWgYoSRQMQDxCl0htoEiJEg7CWALREVI4xN8uO1Ntn3gCXBkRnUn5wqP+QlEy8RrhxE8yg/FeMxPBYWTxgeRfGE4VEUTxgeRXF9B9d7Lr5BffZjh/OPti4WeUWjLpGQ79v7Sk/QJcb1rZI7Gr4oc9kGmtMpBJDSdO5Z0iSv2nOZJ44S4mqP8X9tX7oi7mtlw8goemEEHsWSrB5KeEsLS4yrhaHH899Z5MTpZD81DAGSFPVYsZXlNq3qcbU73tPQ7FuZPUAdh9FJo5MhylG65P5ym1b1uFoYecOStQzOSXxRy0BZ7DmRcLUwzujZbxfPh+KFGKXG1cKIhWrUfrqQQJYQGWpxgBhN5Tat6nG1MA7VNMoBmhljOUk6GKODEVYwQhPnXbp1rNz2VTOubpWMBgNqJ92MEiVOLQAChzpGeMu9O2vhlPIaWMW42mNkHSyBnBQFgEQhRhRHuLtjrtJxtTBa06lcFv+c4zYaB1rry2DRiYOrhdETrNMORmtRsKkhQYQEKjZDYT+PtTWW27yqxtXCCORSxhPtjQSDYzQwRj1jtIk+fnvmCg40RxGfzUnxWfOF5bazGnFF8Kl+JLHiOdu2P/6yx5/QNBvhaH4WDcVQOlowfSbBtM1eOsniIyzTXNB9gO8+86zx3K/8UXw2B46cmBg8APwZoTShifcACoIvAgGE+Dy28yYcIggeQ4ggUgZANKOyD1iCZBOO7EZiUkg9uQyFRuAQDquAMGK8I6WQUVQicBCok68ZP6Pw3sS5hS1XCudM7EsfoDC7OYNgEEVcJz+of6zUz/sfoeyjq/4PjG059fDRMzsGMkScDB/4+4OApIX9hJQB0k4r29mINV3DusXFn3pT4SE7spBFWIz/L2XhmAOoovAznmUYRxbmEqtMHZsovsLU9RP3hZnXw1S+sOmZvQRTvtcZv+nENROXSqZl+5BTvyuAIqaVBYkiTpFX69v+uSc5v5S1KhEfTy/OqcqZq3oS3LRxMe+8ZysAdfRSTw8+xyKNb6YoAPIabaPJwoemTXzAEx+EGH/Qcu6HqogpAUwawcxjE9eLaT8zjBbTPuBxJnIRT9xvhpCm/W0x7fcJHAq2CiYEInDB5n/ljTGkvAwhOFoboiWZJmgVVhuGGZk8xUd8zmWOgOxEJkAx7YFPcKwPFY7Rw36MLvZjOdNix/8RxyuO8fvcY9F/4G4lpbzCEOJGpMRRFQ7WReipKSR7Nac1UXXMOc884fcxWhOYdp9ZJ0y4+2LVZNEP9Rif6rGGZJ7u8I08xu9zjyX+gbuVlLIKQ14T3GM4cucTHbW0xtO8/yXP4lA0wgidmOMb14zSiCUUbFGI8CwhCGbz1KRy4zeRU/X65Ovx3x0580OfiD2mH5uo+2efN1E9FBNNsac2PZaYfs1E1TJxz4n7T7+XmBFjALyoyF9YUMoefAL4Pxh71rrDh/7alsyI5kRKPGf3Ae5b0UZntp9zdveQTnTNOD8Z0Hjlxy7DUZTCh2BLgIIKHLII7kdRwii8GyEcBF8HfAjxNWznivFWyR6EMJAyBKIOlUNIlhd+5DCSXOGetKMQBEZwaAHUaa2LAmL8Z3qAOfF64j2HKUFJ7PFr1fFzbSRJFHETCq+XV+vHSlq7YLhCGMfiE2v/J/+uA3dpR8yVDIgWAIR02L7Gx2/P2MR9a5eCw8fkR4xPl9nUqsMV/RjHoimTSNdYuchquZ+IzJHDRy0J+mUrb71pC/f+dLU3MaNEuLrnsy2VVQ4Em4nTRIg89SRREZxxcBRVy5XbvKrG1cKIaQFlZ2Q5s0P+aNYkmPP2yCslrhZG19iQ6rMkfhLUc5QGugkyhkRy89q15TavqnG1MI60RGVHaohahtAx0bCoYRTUNA8t6vTqkhLiamHgSHt5cm5G6KgdY+vPV82dqOExb7haGMsHB5ViE3UsvEQ6pcbVwrh16dpcHgUwgfT4jznZO+RROlzdjxFIp/QIQwimt0BMgsTKZtOJgqs9xtqxlKqOL2aejvA8RslxtTBqHPPraUJzjptF4g6P+cXVwrgo/sEPjui143NXCkhgTEQ9l1FiXB1jAIz5alfsVzr2NuXSKDiMqTUyaites6TEuHp01aN8uLoq8SgfnjA8iuIJw6MonjA8iuKKVsnt0a9f6ajaFw40N8ZvX7vueT/bvGF7uW060Slrq+TNL37kJc94fN8fDMfC1DT8dpa/blzNisHBxz987wvXl80wj/IJ46pn3XF1KCs/P1Rfi9/Kc+qew0RTKQ7W17JjaQt1ufSSz9x68aGyGOdRnqrkrLf31m0IRz6fC0haYgledff9+DARwPqBHuJBnSYz/jXgZeWwz6NMwjjp8MHh1cODrBgb5cw9+8jjY2Jep4Lksm072NYSXVQO2zwKLHhV8rYXP9RRY40c+fjNNwMqJjrF9lTNY2ISbV8n3zt3CpdHyVkQj/GtVdd9pyOeelvMHxBtTRH72Yf24Iz/aVvA4WiUO1avQLMdLnpiN62JGC30MoT9ENC+EDZ6zKTkHuP61u/3t2ayzS2ZOLYjUBxJVKYmO1Aeae/kQy+5FFspHAnmTL67+fusG93PAM1kqL10qfz470pqpMccSuoxPnD+X+telUo0L0kOYyPYVdNB1EwxUhNi86mnMRIMMxwIYE9LQ5D2GfzupFNYf8e+cfHY1+N1xC04JRXGsx574vsdqVFyQuGO9vVoeUj5Nb7z3GeR9PtZf7iHM44MsSowyP3LughaOc48dJDmdBpJBgcNi6C3DLEMlKwqubP2O2FbZBPrY4PsqG+mP9CAqfoAsBSFo01RmuNTaSAsRXDJ7ocJ2IXkKRppJCmGWcQKtgWQv86WxFCPopTMYyQV6yah+BkJOBwJtiKVqdpAdRya4jNzg2iOpD8cZUlsCACLICHGsNDIIeM+xhNm/IOIT2X3oYplTKRKcCTk7ULuLFUAjiQvJQ42QrxRfjr0y+MudBVREo/xh9YfLFmSGDiwLLOXP3dehImBFDOn52X92pwUR6f17mdxfHjydZABDrGMBoZo4sCZQm5+4Kn+tvhM7i048vtoikBTCoLI2mA7oCoFUZjOeF4NZyqJiRCO/ExYFR9JamiKLa8JSvEfaR3LfgeSS5CsRRG/k58Nv3t+npK7eVrC+EPdL9TmfL96aupx4SedTxIKHVbb7x9Rfas0FaXedlDUDPuibcRELXnFIGDnCOZNYr4gpqqRCBpo0/624ti8aO9WdDmV+SxMNwnC+DEJMoqAtyjy+h/NLcVltR+6+JXbd7R1dv1l+UmYft9UArSsDeZ4HhJ13HuI8awmjgTLAV0FfUJE+cL/isLkYmpnIkvKeHI3hwcQ8lXyc5ED//TDqxCOKYzr2n8l8ppSa+maDI4mv685XGqrmrIse1isyu1j0FdHQjUYrWvkQMtiQmaKk/Y/zojVztFIPVKX5BQdv5VjSXyErmQh4ZqDYF+0kZ+ftJ6j0XpWD40wFAywq7GWr970R1aMjeAg8BGjlj5gIl2mxAGyiorfsejzR+TPT38GB+oa8s84uNt4+6VvJRYMH7uktgOIQvad6UV2JOiiIIQJoeTyhXPlrHOnZ9CZEJgiJLaTAvFXpPwDqnIjUgZxZK38fOSxp/WhfCqnAFJ+zCcBxNUxHahBiDH5+YgjPpTwoYjFSEfHke8CpQMhD6EpHVj2DhzOABxUZQuSwzhOHiEOIcROpByh8KEL+YXa2fkHp2zY3PALxUFe5whxOYCjCOLRIJmQj8hIimA6i2FKFAmWKjANBSkk0UyarBbAVhSGm2rpXtyCL59l0YFuVKew9iNl+GmIxTm3b+ZWVbYQfPnM8wlaDstG44wEfPxh3Qr+4693sGx0jBBjtLF3lqkzEmUigZRmcNvy9bz8zVePp10qUkIpIZEvfJiGypwsamLaIVtC3hm/z0Q+zyLPTlMK3gemqqKJ8yZOd5ypVEuT8dV49SXHbZnwYJYzlYNr4roJAlrBo0HB8+WLZGEyNPBNCxdtBzLWzGcw/f/pTHjWwjfvEQTvkZ+P3K1J+BFCXD5huuJIpKLQ1DeGZjmo1pQoUuFCXKDlHbJaoRtbdSTN/WPkdY2cT2FT3yFaU4UUjAdqGojpc9eFqFJy0cGDLEkMo0qHw+EGarMmrYkUEgWD9NwCzPpABaBJyRWvey+OOjFpXM4Vx8QDn/jQQ8bMm0zPuamJqSSxCJDH+EJN5tYaTyVpy8J9HDl1P0UFy55K1jaRXlJTC9WRNmHzeF5R0y78ven2+9TChz75Wiu8b03YJaeOT2cilrJnJaebTHo7/Y+IqV8UuQnJX8QH42s0CZfOLnftaAopJEKCMm6D6VMmb6zZc5UXHU3SbI/QmoojgZ317eyvbS48F0WwbqhnPKcyxPx+zuzfP9lr1ZRJoEiH0UCQQCJBhgjQM+svzP2bu5raSPkDc45PZfKjIAZVKWQOtpzCNxJR+FYZRVYhqErhA518aEW+ZdNjEzEuIGfi/GkoyriHkFMfihBzu+smks7O1qFexD5NmSYMxltYRVAVsP+JHG+SicS5QeCyiRXDM8+Zlt3WUSaOiZnvz8I0NBrTBU9xuKaBfdGWwjVCcCRSz9665sJ5ispQKDjn2SxJDKFZEgdBhgjDtE/7SGZWI1N2zHpwEymfbaay6qliKi2zoBBcpnKF/4u55dnfsGMlh3WYqkb+mfj9nwn2nSLnzr6+yJf0mNf+4yQVAV+eHV+N1ReCOFsVOIogEfajWFNnmboyM34T4PdnaE0XdjfsD9bO+UsHapvY0rqMu1tXUZM2J/9WjDB9NJI1Q9RkTGxUQBKjlUEWkR9vBsxGAht7+1ndf3RWMDnrRCGm4oGp1I8FctbMBzhR38++fvbr6S7Zmf30ptvizL1Hsb8xkX909rclZ83KSSqnWljT/5Zpzbhs0qMVo1jVAlMBNRwGfi2klFzf+MsrbYUvACFHEeT8OtmAQSCdQzUt8j4fsWiYnE8lGksSzGU4eWgHAcvCRqXT7CXg5BgVUTI0sLu+lYO1Mze0E45EtxwUW1JnpjhteB9xakgyvSUhCZFBm/bpGqTpoBDc2wiShp+gaaGgI7D4w/J1XPqmD4BPL1yQL/JAbAdydsF7ZGYtkhZA0Cg86JxdqNOnfQlQxZRnmS0ShZlCkxPBB1PxyYSLhqmEtBOeTFPGPc949SaLBJ+qAn5tqoNuMtnteDU3GbsIUNXC9dase0w0t2c7kUJ5JlaApoHvA1+Qn4/0/kP9GD9edr2iZ+wfJmuCrw1ks1o0mRIBJ0na76M+k2ZF+iBBO8VjxgZiSpRWZ4CdrR2YauHDUqRDR2wES+rkNIWTk9tozMY4IE/CmZUExcAkMKt2i3KYu1a38Z1znsuFe3fwqkfvsjJCf3RXS3vf71efOvSTM579RgxtKov/bPfqjMcTlgPx3Fw3GzQKD90aP0+b6MMY9zATmYMnYoWJ36W0QdyHKu5H8u/yM+Hj8t9PF/HRlCo/HZqsF8XVMR+2tOSXok87key89Xya4vUv6KXt3w4Em5+f1Wp8a9K9DAdrcISgNTXKE6HFxPUQzxj7G01Wodv7cc7BZGbw6COHf1rqAwnU0IOPHieC04TcPEIRxKezX8Sy3oehq5M7BExUHfq0DzrvQNqcEkdAH//wp32rJncjEFOtDdsZt0ZIFLEKVdknrwmWRQgLQUm6xO9o++pJkRHz0SYzR1w32F3Tga2oBOw0Lxz94+R5g3RwlNWTrxUcaogzfQhHI0MHj/Oq171f/c3Pzjlmh8x0xKcyj+DTTgEK3d/TydtTrlxSqDYcCSpZeU2oSBMHxIeTCsjTUMRh+elw/z/2FCqbko2u3l3zrURHMh7uC0Q4GqoDQHdMXjxyAxOha07V+Gvz6ejpGtpTSVqsURQ1hWYXcmBomNTSw9bGJZlNg5+fO//vSRCfMS9Flb9BCqXgPRxwpI2UCjYWUm5FEATxCIZ6tfyoz5tCOI2SCePe8DdXNKWTe2yhsqOuDSkKIfepyYdYlt3PqC/I+Zd/kMcbOgAImSa/+sOveX7PIyjTtg9K4SMsv+/NyVhgSjq178/137tzeWz0/Jyi0xuMkldV6nIJ1qR38b1NG7mn5TTOONJP12ichKGzrynEV7b8YsY9YtQSld/whLHAlHQGV6w+9PL9PmdwzeAgDfHk+FGHAEmUfBOX79jHSUf60Mfb+xfvhyFaaGSqGreQZd+i4USkpHMpX733tUP9SuiRBxcvYdhncCBaR29EJeFXaUzZNCZTk6KYYJjC9hMOCqPUMUJXRylt9CjOgqwr+exZN61qGRy7TSpKZFdz87YOc/S8mhGbjrE4XaOz9zyThEnhIHi0qfnDLx142+dKbqDHHBZ8wdHlL9/u+9xffpo9EF7KgNHASUf6ZrgtjTwGNjlhydXOx7zZ4WViwR/8b363MXfTopPTpw7vZG3yCfrrdfK6g0aGBrqpoZ8m9vCnFatuWWjbPKYoy9rVd+58feg7q1X7rXtuUDQ5NcmpO9BGa7YPW9FpTed2lsM2jwJlc9V1CXHan1ov5HCwnZhew87ICnKqjiptvnvyi1h9NP6+ctnmUebEKY+KbzT/bU1Htx3yG+vHnsDnmPxlyRkE49Zn/uPB5320bIZ5uCPP54D4WPTejsVX/OL003f96ven3lpuezzg/wOcq/F4dARtxAAAAABJRU5ErkJggg==" id="imagedf16d237d6" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature64_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-34" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imageaa6e4191e3" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p011d44bb4d">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
