<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="772.857406pt" height="679.5765pt" viewBox="0 0 772.857406 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T03:27:05.988111</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 772.857406 679.5765 
L 772.857406 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
L 520.118406 27.789 
L 405.646406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 476.005177 638.149 
L 476.005177 27.789 
" clip-path="url(#pe31779171b)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 405.646406 609.084238 
L 520.118406 609.084238 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 405.646406 580.019476 
L 520.118406 580.019476 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 405.646406 550.954714 
L 520.118406 550.954714 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 405.646406 521.889952 
L 520.118406 521.889952 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 405.646406 492.82519 
L 520.118406 492.82519 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 405.646406 463.760429 
L 520.118406 463.760429 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 405.646406 434.695667 
L 520.118406 434.695667 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 405.646406 405.630905 
L 520.118406 405.630905 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 405.646406 376.566143 
L 520.118406 376.566143 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 405.646406 347.501381 
L 520.118406 347.501381 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 405.646406 318.436619 
L 520.118406 318.436619 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 405.646406 289.371857 
L 520.118406 289.371857 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 405.646406 260.307095 
L 520.118406 260.307095 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 405.646406 231.242333 
L 520.118406 231.242333 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 405.646406 202.177571 
L 520.118406 202.177571 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 405.646406 173.11281 
L 520.118406 173.11281 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 405.646406 144.048048 
L 520.118406 144.048048 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 405.646406 114.983286 
L 520.118406 114.983286 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 405.646406 85.918524 
L 520.118406 85.918524 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 405.646406 56.853762 
L 520.118406 56.853762 
" clip-path="url(#pe31779171b)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m4d322ffaa9" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m4d322ffaa9" x="425.300838" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(417.192634 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m4d322ffaa9" x="476.005177" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(472.505802 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(340.201 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(42.799687 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(192.825781 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(140.9375 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_24 -->
      <g style="fill: #333333" transform="translate(192.825781 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(131.130625 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(69.906719 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_mean_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1936.771484"/>
       <use xlink:href="#DejaVuSans-61" x="2000.248047"/>
       <use xlink:href="#DejaVuSans-63" x="2061.527344"/>
       <use xlink:href="#DejaVuSans-65" x="2116.507812"/>
       <use xlink:href="#DejaVuSans-47" x="2178.03125"/>
       <use xlink:href="#DejaVuSans-72" x="2255.521484"/>
       <use xlink:href="#DejaVuSans-6f" x="2294.384766"/>
       <use xlink:href="#DejaVuSans-75" x="2355.566406"/>
       <use xlink:href="#DejaVuSans-70" x="2418.945312"/>
       <use xlink:href="#DejaVuSans-4e" x="2482.421875"/>
       <use xlink:href="#DejaVuSans-75" x="2557.226562"/>
       <use xlink:href="#DejaVuSans-6d" x="2620.605469"/>
       <use xlink:href="#DejaVuSans-62" x="2718.017578"/>
       <use xlink:href="#DejaVuSans-65" x="2781.494141"/>
       <use xlink:href="#DejaVuSans-72" x="2843.017578"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(131.130625 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_mode_NUnfilled -->
      <g style="fill: #333333" transform="translate(79.469844 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- VoronoiFingerprint_std_dev_Voro_area_maximum -->
      <g style="fill: #333333" transform="translate(63.073594 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-61" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-78" x="2109.220703"/>
       <use xlink:href="#DejaVuSans-69" x="2168.400391"/>
       <use xlink:href="#DejaVuSans-6d" x="2196.183594"/>
       <use xlink:href="#DejaVuSans-75" x="2293.595703"/>
       <use xlink:href="#DejaVuSans-6d" x="2356.974609"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_square_pyramidal_CN_5 -->
      <g style="fill: #333333" transform="translate(40.520625 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-73" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-71" x="1478.296875"/>
       <use xlink:href="#DejaVuSans-75" x="1541.773438"/>
       <use xlink:href="#DejaVuSans-61" x="1605.152344"/>
       <use xlink:href="#DejaVuSans-72" x="1666.431641"/>
       <use xlink:href="#DejaVuSans-65" x="1705.294922"/>
       <use xlink:href="#DejaVuSans-5f" x="1766.818359"/>
       <use xlink:href="#DejaVuSans-70" x="1816.818359"/>
       <use xlink:href="#DejaVuSans-79" x="1880.294922"/>
       <use xlink:href="#DejaVuSans-72" x="1939.474609"/>
       <use xlink:href="#DejaVuSans-61" x="1980.587891"/>
       <use xlink:href="#DejaVuSans-6d" x="2041.867188"/>
       <use xlink:href="#DejaVuSans-69" x="2139.279297"/>
       <use xlink:href="#DejaVuSans-64" x="2167.0625"/>
       <use xlink:href="#DejaVuSans-61" x="2230.539062"/>
       <use xlink:href="#DejaVuSans-6c" x="2291.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="2319.601562"/>
       <use xlink:href="#DejaVuSans-43" x="2369.601562"/>
       <use xlink:href="#DejaVuSans-4e" x="2439.425781"/>
       <use xlink:href="#DejaVuSans-5f" x="2514.230469"/>
       <use xlink:href="#DejaVuSans-35" x="2564.230469"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- Stoichiometry_2-norm -->
      <g style="fill: #333333" transform="translate(238.0475 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-32" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(131.120469 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(119.89375 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(79.471875 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(131.130625 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(63.104062 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(73.380156 119.92227) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_mode_MeltingT -->
      <g style="fill: #333333" transform="translate(82.644687 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(260.204375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 405.646406 638.149 
L 520.118406 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagea575b65a1c" transform="scale(1 -1) translate(0 -578.16)" x="408.24" y="-43.2" width="108.72" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature1_fold0 -->
    <g transform="translate(160.107406 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.272906 638.149 
L 534.902406 638.149 
L 534.902406 27.789 
L 527.272906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagebc92c91e10" transform="scale(1 -1) translate(0 -609.84)" x="527.04" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(538.402406 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(538.402406 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(572.803344 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pe31779171b">
   <rect x="405.646406" y="27.789" width="114.472" height="610.36"/>
  </clipPath>
 </defs>
</svg>
