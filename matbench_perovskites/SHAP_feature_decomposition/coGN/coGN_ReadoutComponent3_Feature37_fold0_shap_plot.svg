<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:36:59.202791</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 464.140878 638.149 
L 464.140878 27.789 
" clip-path="url(#pcf23367060)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#pcf23367060)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m17baa8e7f8" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m17baa8e7f8" x="464.140878" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(460.641503 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m17baa8e7f8" x="511.736489" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(508.237114 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CrystalNNFingerprint_std_dev_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(90.028281 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6f" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-63" x="1592.75"/>
       <use xlink:href="#DejaVuSans-74" x="1647.730469"/>
       <use xlink:href="#DejaVuSans-61" x="1686.939453"/>
       <use xlink:href="#DejaVuSans-68" x="1748.21875"/>
       <use xlink:href="#DejaVuSans-65" x="1811.597656"/>
       <use xlink:href="#DejaVuSans-64" x="1873.121094"/>
       <use xlink:href="#DejaVuSans-72" x="1936.597656"/>
       <use xlink:href="#DejaVuSans-61" x="1977.710938"/>
       <use xlink:href="#DejaVuSans-6c" x="2038.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2066.773438"/>
       <use xlink:href="#DejaVuSans-43" x="2116.773438"/>
       <use xlink:href="#DejaVuSans-4e" x="2186.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2261.402344"/>
       <use xlink:href="#DejaVuSans-36" x="2311.402344"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ValenceOrbital_frac_d_valence_electrons -->
      <g style="fill: #333333" transform="translate(134.006875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-61" x="60.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="121.9375"/>
       <use xlink:href="#DejaVuSans-65" x="149.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="211.244141"/>
       <use xlink:href="#DejaVuSans-63" x="274.623047"/>
       <use xlink:href="#DejaVuSans-65" x="329.603516"/>
       <use xlink:href="#DejaVuSans-4f" x="391.126953"/>
       <use xlink:href="#DejaVuSans-72" x="469.837891"/>
       <use xlink:href="#DejaVuSans-62" x="510.951172"/>
       <use xlink:href="#DejaVuSans-69" x="574.427734"/>
       <use xlink:href="#DejaVuSans-74" x="602.210938"/>
       <use xlink:href="#DejaVuSans-61" x="641.419922"/>
       <use xlink:href="#DejaVuSans-6c" x="702.699219"/>
       <use xlink:href="#DejaVuSans-5f" x="730.482422"/>
       <use xlink:href="#DejaVuSans-66" x="780.482422"/>
       <use xlink:href="#DejaVuSans-72" x="815.6875"/>
       <use xlink:href="#DejaVuSans-61" x="856.800781"/>
       <use xlink:href="#DejaVuSans-63" x="918.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="973.060547"/>
       <use xlink:href="#DejaVuSans-64" x="1023.060547"/>
       <use xlink:href="#DejaVuSans-5f" x="1086.537109"/>
       <use xlink:href="#DejaVuSans-76" x="1136.537109"/>
       <use xlink:href="#DejaVuSans-61" x="1195.716797"/>
       <use xlink:href="#DejaVuSans-6c" x="1256.996094"/>
       <use xlink:href="#DejaVuSans-65" x="1284.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1346.302734"/>
       <use xlink:href="#DejaVuSans-63" x="1409.681641"/>
       <use xlink:href="#DejaVuSans-65" x="1464.662109"/>
       <use xlink:href="#DejaVuSans-5f" x="1526.185547"/>
       <use xlink:href="#DejaVuSans-65" x="1576.185547"/>
       <use xlink:href="#DejaVuSans-6c" x="1637.708984"/>
       <use xlink:href="#DejaVuSans-65" x="1665.492188"/>
       <use xlink:href="#DejaVuSans-63" x="1727.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1781.996094"/>
       <use xlink:href="#DejaVuSans-72" x="1821.205078"/>
       <use xlink:href="#DejaVuSans-6f" x="1860.068359"/>
       <use xlink:href="#DejaVuSans-6e" x="1921.25"/>
       <use xlink:href="#DejaVuSans-73" x="1984.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(44.918281 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(102.477812 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(122.835 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(209.467812 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_27 -->
      <g style="fill: #333333" transform="translate(209.467812 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(143.87875 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(20.096406 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(209.467812 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_range_Column -->
      <g style="fill: #333333" transform="translate(104.5375 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-6c" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-75" x="2039.261719"/>
       <use xlink:href="#DejaVuSans-6d" x="2102.640625"/>
       <use xlink:href="#DejaVuSans-6e" x="2200.052734"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(209.467812 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(234.119062 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(147.772656 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- Stoichiometry_2-norm -->
      <g style="fill: #333333" transform="translate(254.689531 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-32" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(62.252969 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(147.772656 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(206.997812 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image995dae8bdc" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature37_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-37" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image1b1066023b" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pcf23367060">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
