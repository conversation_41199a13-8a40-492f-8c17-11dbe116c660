<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.549906pt" height="679.5765pt" viewBox="0 0 788.549906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T09:31:58.357763</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.549906 679.5765 
L 788.549906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
L 525.730906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 495.076741 638.149 
L 495.076741 27.789 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.730906 609.084238 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.730906 580.019476 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.730906 550.954714 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.730906 521.889952 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.730906 492.82519 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.730906 463.760429 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.730906 434.695667 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.730906 405.630905 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.730906 376.566143 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.730906 347.501381 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.730906 318.436619 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.730906 289.371857 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.730906 260.307095 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.730906 231.242333 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.730906 202.177571 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.730906 173.11281 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.730906 144.048048 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.730906 114.983286 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.730906 85.918524 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.730906 56.853762 
" clip-path="url(#pd691e0553e)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mc81848d8d7" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mc81848d8d7" x="435.829439" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(427.721235 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mc81848d8d7" x="495.076741" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(491.577366 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8935 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_88 -->
      <g style="fill: #333333" transform="translate(218.598281 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(141.102031 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_120 -->
      <g style="fill: #333333" transform="translate(210.327031 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_3 -->
      <g style="fill: #333333" transform="translate(59.279219 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-33" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_165 -->
      <g style="fill: #333333" transform="translate(210.327031 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(95.679219 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_11 -->
      <g style="fill: #333333" transform="translate(218.598281 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_avg_dev_NpUnfilled -->
      <g style="fill: #333333" transform="translate(79.498281 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-55" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-6e" x="2219.291016"/>
       <use xlink:href="#DejaVuSans-66" x="2282.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2317.875"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2373.441406"/>
       <use xlink:href="#DejaVuSans-65" x="2401.224609"/>
       <use xlink:href="#DejaVuSans-64" x="2462.748047"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_50 -->
      <g style="fill: #333333" transform="translate(218.598281 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MaximumPackingEfficiency_max_packing_efficiency -->
      <g style="fill: #333333" transform="translate(69.622344 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-61" x="86.279297"/>
       <use xlink:href="#DejaVuSans-78" x="147.558594"/>
       <use xlink:href="#DejaVuSans-69" x="206.738281"/>
       <use xlink:href="#DejaVuSans-6d" x="234.521484"/>
       <use xlink:href="#DejaVuSans-75" x="331.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="395.3125"/>
       <use xlink:href="#DejaVuSans-50" x="492.724609"/>
       <use xlink:href="#DejaVuSans-61" x="548.527344"/>
       <use xlink:href="#DejaVuSans-63" x="609.806641"/>
       <use xlink:href="#DejaVuSans-6b" x="664.787109"/>
       <use xlink:href="#DejaVuSans-69" x="722.697266"/>
       <use xlink:href="#DejaVuSans-6e" x="750.480469"/>
       <use xlink:href="#DejaVuSans-67" x="813.859375"/>
       <use xlink:href="#DejaVuSans-45" x="877.335938"/>
       <use xlink:href="#DejaVuSans-66" x="940.519531"/>
       <use xlink:href="#DejaVuSans-66" x="975.724609"/>
       <use xlink:href="#DejaVuSans-69" x="1010.929688"/>
       <use xlink:href="#DejaVuSans-63" x="1038.712891"/>
       <use xlink:href="#DejaVuSans-69" x="1093.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1121.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="1183"/>
       <use xlink:href="#DejaVuSans-63" x="1246.378906"/>
       <use xlink:href="#DejaVuSans-79" x="1301.359375"/>
       <use xlink:href="#DejaVuSans-5f" x="1360.539062"/>
       <use xlink:href="#DejaVuSans-6d" x="1410.539062"/>
       <use xlink:href="#DejaVuSans-61" x="1507.951172"/>
       <use xlink:href="#DejaVuSans-78" x="1569.230469"/>
       <use xlink:href="#DejaVuSans-5f" x="1628.410156"/>
       <use xlink:href="#DejaVuSans-70" x="1678.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1741.886719"/>
       <use xlink:href="#DejaVuSans-63" x="1803.166016"/>
       <use xlink:href="#DejaVuSans-6b" x="1858.146484"/>
       <use xlink:href="#DejaVuSans-69" x="1916.056641"/>
       <use xlink:href="#DejaVuSans-6e" x="1943.839844"/>
       <use xlink:href="#DejaVuSans-67" x="2007.21875"/>
       <use xlink:href="#DejaVuSans-5f" x="2070.695312"/>
       <use xlink:href="#DejaVuSans-65" x="2120.695312"/>
       <use xlink:href="#DejaVuSans-66" x="2182.21875"/>
       <use xlink:href="#DejaVuSans-66" x="2217.423828"/>
       <use xlink:href="#DejaVuSans-69" x="2252.628906"/>
       <use xlink:href="#DejaVuSans-63" x="2280.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2335.392578"/>
       <use xlink:href="#DejaVuSans-65" x="2363.175781"/>
       <use xlink:href="#DejaVuSans-6e" x="2424.699219"/>
       <use xlink:href="#DejaVuSans-63" x="2488.078125"/>
       <use xlink:href="#DejaVuSans-79" x="2543.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- VoronoiFingerprint_std_dev_Voro_area_sum -->
      <g style="fill: #333333" transform="translate(126.672031 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-73" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-75" x="2002.628906"/>
       <use xlink:href="#DejaVuSans-6d" x="2066.007812"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(156.903125 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_minimum_NValence -->
      <g style="fill: #333333" transform="translate(78.990469 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-56" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-61" x="2199.724609"/>
       <use xlink:href="#DejaVuSans-6c" x="2261.003906"/>
       <use xlink:href="#DejaVuSans-65" x="2288.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="2350.310547"/>
       <use xlink:href="#DejaVuSans-63" x="2413.689453"/>
       <use xlink:href="#DejaVuSans-65" x="2468.669922"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- DensityFeatures_density -->
      <g style="fill: #333333" transform="translate(248.445469 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-64" x="860.072266"/>
       <use xlink:href="#DejaVuSans-65" x="923.548828"/>
       <use xlink:href="#DejaVuSans-6e" x="985.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1048.451172"/>
       <use xlink:href="#DejaVuSans-69" x="1100.550781"/>
       <use xlink:href="#DejaVuSans-74" x="1128.333984"/>
       <use xlink:href="#DejaVuSans-79" x="1167.542969"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(218.598281 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(59.279219 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(285.976875 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(99.152656 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image42a72a7fe3" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature9_fold0 -->
    <g transform="translate(175.799906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-39" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-5f" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-66" x="2902.59375"/>
     <use xlink:href="#DejaVuSans-6f" x="2937.798828"/>
     <use xlink:href="#DejaVuSans-6c" x="2998.980469"/>
     <use xlink:href="#DejaVuSans-64" x="3026.763672"/>
     <use xlink:href="#DejaVuSans-30" x="3090.240234"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.625406 638.149 
L 539.254906 638.149 
L 539.254906 27.789 
L 531.625406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image2fb542f161" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.754906 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.754906 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.155844 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pd691e0553e">
   <rect x="431.418906" y="27.789" width="94.312" height="610.36"/>
  </clipPath>
 </defs>
</svg>
