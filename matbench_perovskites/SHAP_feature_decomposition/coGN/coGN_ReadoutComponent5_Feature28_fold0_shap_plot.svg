<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="781.271031pt" height="679.5765pt" viewBox="0 0 781.271031 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T06:17:13.082719</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 781.271031 679.5765 
L 781.271031 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
L 520.984031 27.789 
L 409.392031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 456.444292 638.149 
L 456.444292 27.789 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 409.392031 609.084238 
L 520.984031 609.084238 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 409.392031 580.019476 
L 520.984031 580.019476 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 409.392031 550.954714 
L 520.984031 550.954714 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 409.392031 521.889952 
L 520.984031 521.889952 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 409.392031 492.82519 
L 520.984031 492.82519 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 409.392031 463.760429 
L 520.984031 463.760429 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 409.392031 434.695667 
L 520.984031 434.695667 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 409.392031 405.630905 
L 520.984031 405.630905 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 409.392031 376.566143 
L 520.984031 376.566143 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 409.392031 347.501381 
L 520.984031 347.501381 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 409.392031 318.436619 
L 520.984031 318.436619 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 409.392031 289.371857 
L 520.984031 289.371857 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 409.392031 260.307095 
L 520.984031 260.307095 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 409.392031 231.242333 
L 520.984031 231.242333 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 409.392031 202.177571 
L 520.984031 202.177571 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 409.392031 173.11281 
L 520.984031 173.11281 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 409.392031 144.048048 
L 520.984031 144.048048 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 409.392031 114.983286 
L 520.984031 114.983286 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 409.392031 85.918524 
L 520.984031 85.918524 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 409.392031 56.853762 
L 520.984031 56.853762 
" clip-path="url(#p00ea4bcba3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="me53ecfdf16" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#me53ecfdf16" x="456.444292" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(447.697573 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#me53ecfdf16" x="509.284167" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(500.537449 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(342.506625 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(196.571406 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_152 -->
      <g style="fill: #333333" transform="translate(188.300156 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(57.759844 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_avg_dev_MeltingT -->
      <g style="fill: #333333" transform="translate(68.899219 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4d" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-65" x="2094.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="2155.619141"/>
       <use xlink:href="#DejaVuSans-74" x="2183.402344"/>
       <use xlink:href="#DejaVuSans-69" x="2222.611328"/>
       <use xlink:href="#DejaVuSans-6e" x="2250.394531"/>
       <use xlink:href="#DejaVuSans-67" x="2313.773438"/>
       <use xlink:href="#DejaVuSans-54" x="2377.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(73.652344 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(182.192187 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(134.87625 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- BandCenter_band_center -->
      <g style="fill: #333333" transform="translate(221.222656 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-61" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.882812"/>
       <use xlink:href="#DejaVuSans-64" x="193.261719"/>
       <use xlink:href="#DejaVuSans-43" x="256.738281"/>
       <use xlink:href="#DejaVuSans-65" x="326.5625"/>
       <use xlink:href="#DejaVuSans-6e" x="388.085938"/>
       <use xlink:href="#DejaVuSans-74" x="451.464844"/>
       <use xlink:href="#DejaVuSans-65" x="490.673828"/>
       <use xlink:href="#DejaVuSans-72" x="552.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="593.310547"/>
       <use xlink:href="#DejaVuSans-62" x="643.310547"/>
       <use xlink:href="#DejaVuSans-61" x="706.787109"/>
       <use xlink:href="#DejaVuSans-6e" x="768.066406"/>
       <use xlink:href="#DejaVuSans-64" x="831.445312"/>
       <use xlink:href="#DejaVuSans-5f" x="894.921875"/>
       <use xlink:href="#DejaVuSans-63" x="944.921875"/>
       <use xlink:href="#DejaVuSans-65" x="999.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="1061.425781"/>
       <use xlink:href="#DejaVuSans-74" x="1124.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1164.013672"/>
       <use xlink:href="#DejaVuSans-72" x="1225.537109"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(32.021875 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(166.649062 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(194.101406 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(73.652344 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_1_0_1_0 -->
      <g style="fill: #333333" transform="translate(67.509844 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-5f" x="2335.466797"/>
       <use xlink:href="#DejaVuSans-30" x="2385.466797"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(196.571406 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(188.300156 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(87.521719 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(266.198594 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(144.683125 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(103.099375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAJQAAAMjCAYAAABDGzmEAABb7klEQVR4nO2dd5wdZb3/38/MnH72bN9NssmmhwQIBIgUAWkCYjdwAbHXa8G+V0WKV7yKylp+4lWvgt2rFzVYUEBEpYggvQTS6+4m2b57+pny/P44u5utyW4yuyc7z7x58UrOnDNzPmfyme/Tv4+QUqISu8XHv5il6tMCKcP0XdAom/9eak1eQqhkqK3imkI/tYEa9iERdFNPGbszy+Q3YqXW5hWMUguYSRzCgRN5BA0BwHx2soOV0RLL8hRKGWoB29AAQTEqa8BcdpVUk9fQSi1gJgmRH4hNRQQQIVcqOZ5EKUMxwk5F5DjHfA4fpQzVST0ANhrOwE/vYk4pJXkOpQzVQy02BjoOGg4mQbqpKbUsT6FUpXw+O9GxYKBSHqDAXFpKK8pjKGWoEBnAGXZEEiFdKjmeRKkiz2f6UcpQGtaYYwK7BEq8izKG6hbv7Bjv5+rjmMzn8FHGUGEiNXkqkcN+sgRswqUT5UGUqJT3ivcWgoSR6GSpQycPgE6ePq3K7zhwESUMZSAGwpJDgCS7y2t5cu6J5PQwASfDRz7VIa+978kHpRCrCqEAuUh4A0J84fo/n35vaZXPPpQo8iwcBDZBMmgYzEmliRdydMcq2F82j7O3tHH9S5afrUlZE84VasLZ3DnAHz9/0SPHlVr7bEMJQwVAD5BDDIzbhe0CF+z4FxGzODB88r5OqgIH6lLBvDlwGlfNvNrZjRKGYshKg0h0aTMn1QmALTScYfMM5YEP+72eU0QJQxXALvpFIjCH/l/YWxx2+fPCucT3tg59Ph8OAXQBP5pxsbMcRSrlRSsJrKHJdQAndGwm5wT41MtPlTffs+sWW9NOzoeDshAOPQHccv2fT28rmehZihJzyjvEO3JVECoaaiQmGkH5Y39SlEsoUeTVyh+GCwSBsQ+P5g+9uIoShgIwyb88j4EcYSpJinjJNHkRZQyVkD+6by+NmESxCGIRIk+M7oFZnD7uoESlfJAaegEdBx0ozjCvpruUkjyHMhEKIDgwhjecsL/qxVWUMtR47dnBaOXjDkoZqp8KTHTkwH8mBv2Ul1qWp1CqDmUTokBxsBgYWPsSLK0oj6GUoQwsDMyhzk0NG8sv8lxFqSIvMMxMUGzlGZilkuNJlDLUyCVURcS4VXWfw0UpQ42Xx0D6hnIVpQzVQ7p1cL6BBCwE3Zi3lFqXl1BitsFodomPpQBzofx6Zam1eA0lDeUzfShV5PlMP76hfFzFN5SPqyhpqH97xy5/yu80oVSl/Nj37LlMGNqv5udMLE2wK2DIrd9tUPKhmi6UGsuLw6/Oa+1AG3iIVuuaWPF+7M3fafAH9FxCKUOt7epFHxaRI7bD8cmMH6FcRKmbWWaNzAUlgPrs2FmcPoePUobKaWNLtv6AUkF62lHKUDvLRy6ZcoBt5f4yKjdR6vG0AgFaEmUk8nkcIeiJhImo08idEdQylJQ8VFvJ7kgIHcmydI6GdLbUsjyFUoZ6uLqclFGsR9kIXiiL0uN3cbqKMoaKfKyzPhcOgTUsl4Em6IiESifKgyhTKS/kCrfijJoC7EgchUYKZgJlDOU4ljPebF9HU+YWzAjq3M2AcS5inAqT7lei3EQdQ0UiCUKjqoy6BoY6t2AmUKZSjqFDUAdNgJTFPzVBWTbPz8u/bratXvNTgbgP+HXTQ+f64zGHiTqPpyPBEEVThQwI6KBrrO7YT+eKlYaTTL9Dws+AB5vP+rvf9DtMlDCUuD5TRVBnRO7oAf7VOB+pCQKajtQEwEuAS2dao1dQwlAIjkGCPrrbAJBCY35/W7FIPEDjjGnzGEoYSt4Y/aeWMxFpa0yUauzpIhuIQbYwmEBKAn8qgUxPoIShAGIFE8sB0iYUbDBtyFqs2tOKaQmsqjKE4+wH3tn00LnPllrvbEUZQ+UMvdgNFRz4yRLQ4J5lqyi3+ys0R4aFEHObHjr3RyWUOetRptvAcaSUQV2M6NwUAsfQuHTTu/tKp8xbKBOhnFz2kXGSr6CN0/LzOXyUMZQUog197M+1xxuO8TlslDEUaFvRAMsp9pRLCbbjj7y4jFILPbVrU1IWRu7tEtMg9eVyP0y5hFLPp2aO3ShIt/zNg9xEKUONF4Ycf/qKqyhlqLhlj1g5HHActLGjMT5HgDL9UAAB26HWciiI4h7EASmxND9CuYlShkIWN4oNDYtS0u+HchWlirxOTWBJiRz4vyAlaT9CuYpSESosoWPkNBXqTX8nBTdRKkKtSGWd4LA5UZWWRWU601NCSZ5DqY5NgJPeuTNZ0I24Lh0pNXHfc99bcGGpNXkJ5QzlM70oVeT5TD++oXxcxTeUj6v4hvJxFaX6oQD2iA//b4HQG9EEISfTPl9+q77UmryEUq28reLD2+bTvSREMWudg8E2GpwV8mt+nnKXUCpC1ZBaEiIzNI1Fx2QprX6x7yJKGSpBEhD0UoNFgHI6MLAOeZ7P5FHKUDY6W1lLljIANCyW8vTAKx83UCrct9NAUkvQFY/QURYlY4RoY3mpZXkKpSJUUkuwq6YCe2A5VU8sjNPjcEyJdXkJpSJUdyw+ZCYAhKA7HimdIA+ilKHsgb1eAk6BqJ0GwPEn2LmKMkXey1/92KbPC5MT+p/hmPRmdBy6ApVsjq4qtTRPoUyESsJ8zbY5Nr0RR9OwhUa12cOS9LZSS/MUykSoTGVZIEKa206/kmcajiVkFbhg80O8/MUHuSPxnfe8of/93y+1Ri+gjKEqbCnWn3YenZVzAcgGI9x5/IWUpdLoe/pOLLE8z6BMkbc7FBYdiToS6Sw1fUkqk2kMy+LxhhNxyPmGcgllIlRPIkbl3g7CdnGRguE4BJIWaREhUBY+vcTyPIPnDSU+l12KaT9Th9BD9sh15xoQzeUR4YQ/28AlvF/k2fKPSBFLmNa4ycXK+gsUiIl7Ajf7y6lcwPuGcuQyAFPX6BQjA1EoW6CmPYmWlUQ0vbwk+jyG54s8hOhFyuoVPf1sDwR5yQttJMsjhHImNfv60BxJvbOXtCn8PCwu4P0IZYiPIiUbq8qp0CRGMs/Cre3MaenBsByCjkXCSrFj8fJAqaV6Ac8bSn428jNC+tLucEDqdoq52SRBaRGyTMrzWYJ6hrxdcN657VJ15kJPI543FID8bGR7RSrrnLdlB1lhEMzaBPM2eaGTDkTY6pS1llqjV/B+HWqA8mxWYmlEUiYgkAiMAtgaIPSflVqfV1AiQgFU9KVlIKWPybMZzEvS8er+kojyIMoYypJOYV5vesxxTUJq7rybSyDJkyhjKCdgFCIUEKO2Rg9T4F2bXuvnlnYJZQwVKC9b/MKCOqpIEaFACJMEGWTQ735yE2UM9fD3F/bVdqfQybKc51nFU1TQAYVSK/MWyrTyABp6O1jBEwQGFndWsh+TE0qsylsoE6EA5ojdQ2YaZD7bS6TGmyhlqIAYW77p+PVxN1HKUH16OaPHVzqMupJo8SpK1aF2hReBENRZ7RjSpEOvY2tkCY2lFuYhlDJUdW6/81xitRaxM+jSJhVIUJvrKLUsT6FUkXdy4bP64tRmKdAwtTDz07tpzLX6yVdcRKkMdj7Tj1IRymf68Q3l4yq+oXxcxTeUj6so1W0wyN8C//MgkhM0XSw7J/9ev9/ARZRq5f0t+P1g0LTzDjog0LEwcTLnyA/ESq3NKygVoQJmPhfQoMzoI2oWyMow/USjpdblJZSqQ+1YUCk6F5bx15PX8ODq4xERh3JSbBc3KXUfphNlItSxb9v26jesXEi6rHLo2MaGBbzjvnvIFULqlPvTjDJPZt7O/SgTKwcpWdi+n1V7dmPqOhvmL6SKrlLL8wzKRKiCI4TuOFzxjwdY0NUJQN4weHL+Ep6rWcKcEuvzCspEqIRhlJ2wfduQmQBClsXxLbuJdLc9XkJpnkKZCGUFggg7z4MrVvDkokVUpjNc9Nxz1Pb3o1GYX2p9XkEZQxV0nUeWLGfH3AOF22OLF/ORP94NPW3+2heXUKbISwc09tTWjjwWDvPowhWAHi6NKu+hjKFsR2gj9nkZoC8WQYBZAkmeRBlDlUkHbfQwk5QYlkVendsw7ShThwqbFgFbIHCwhUAAAUdS2ZPHMOL+RtYuoYyhorYj5rT3018Z45xNG5jT18v26noW7emkYMT9LalcQhlDtcUiora/hY//7U7K82l0KTlz60ZagtWkdCEQ6wbLQwe4Bbn+oyWUO2tRovJgfDb1445YRERllve9aR31X/ocJ36miV+vOYF5hS6ElMMXgGrARxDrPlMywbMYJeZDhf+jW1blLWq1PM/Onzt0XHMcHvzqLcjeAGcW/j76tD7k+ooZlOkJlIhQwnY4oT85wkwAjqZxz7HHEJbZEinzHkrUocyy8C/ac9k3JrI5cobB6VtaqE5mea6xjrreLDGnFwmj829+vSRiZzlKFHnipoI4des+e15fpzhhTzeNXQdytC7r2k+ot5/TeXTwkAS+h1z/vlJone0oEaHkNUF50jtsGe/Li+FmAthRWcOKZE5irVei+J9ulLmJpiZkd2Ts9HFL07FExvtheoZQxlAh2xSL8n1jtjiL2RmCVo+/k4JLKFHkAeTDYXnVpifI5x22lC3EcAQN6Q5W926jHUKl1ucVlDFUOJWVOyqqueKZhzh736ah422RGnLYfpHnEsoUeX0BXV538cU8VbeQXSxgG4tpCdZy1+KTsFGgqTtDKBOhCpEQmWCEvelFZMkPHKxnd1kdJ6Ircx+mG2VuZHkyy8Ub26hI50ccP/35FgTsKJEsz6FMkdcvne7jW/eNOV6Wy9NI7rQSSPIkyhhKkzhRTSJHJZaOyiw7IstLpMp7KGMoWRX72HF7dzOXbkIUMLCoIMkcpxs7Gyy1PM+gxFjeIJuD/yVj5sjdpyQOu8Q8/Uznvf62VC6gTIQCyEjdEaO24rA1iW8m91DKUB1adVlfIECQNCEy2HqB7VRlSq3LSyhV5A3y5+B3ntEQy4XkhAvM920ttR4voaShfKYPpYo8n+nHN5SPq/iG8nEV31A+rqLM4DCAFG8OSJy8oDhtU+L0afIXFSWW5SmUilBFM9kCLMBC4JQ74nI/Y6uLKBWhimYyOPAc2YBTVUJJnkOxCKUz8ifriNHLO32OCKUiVHFtsEkxwQqARkEE/BUKLqKUoQQWYsR8KIeAzE/4eZ+po5ShQCLRAH3gtc2BaOXjBkoZSqIjRvxkDQdLrYrkNKOOocSVAgwkzkCmFYlAIIailY8bKPNwSgIAZLUybMqwSPDwgmNpTSRKrMxbKGOoF8orKkxCCEenQAiTICfvaeU7p1+MuLaru9T6vIIyRV5XKtWymBA5PcL/nnQKjy9YwMktLaxpaYWzQuWl1ucVlDFUlR6ISDvI2974Ju487ngAfnzqaZy+czsIZQL1tKPMnZzr5NlUWzNkpkEeWbSEUCbrd5e7hDKG2lNeQ3ti/PV3eRESoqlv5QxL8iRKGEqKt2o7ymtEY88+sEd1ZNoOFGxAvlAScR5DCUM5sD6RMykvZJnXuq9oIEcW/+wfHHoRQnyyz9/z5QhRwlDAeS9r20RDqoffrf8WK3fshO4sVe3dEA9CQBvMKf3h0sqc/SixjMoWb70mq8kvxp1iNHKA/dEENbk0sf/6AWbWgawFumbIL5XZB7+az8FQIkLp8ic3GY6NRCAJIAhRl8nTGU1gCq1Y/DmO45vpyFHCUABBLOkQADQ6YnGemddIV6Scc7a9CEENdE2ZPrnpRJmbKAFtoKJUm05Rm07REYuTKBRAOlJ+pdz7Zf8MoEyEyhIkr42cWVCbTnFK604IGr6ZXEIZQ+3C2etoYzvEK3NZ/CTA7qGMoY4lP78jPHJrDkcI/rDyRMiZd5dIludQpg719yVr6EdQCAaJmiYd8QTfPf0cXrHhCe69dc2rSq3PKyjRDzXI/sh75NN1DZzbsoW28ko6AxEa+7uoz/7AHxx2CWUiFEBtLsfLd+/gibqFCAknt7ciMEsty1MoZSgGlnWubfc3n5oulKmUF7HGOeYvo3ITxQzlUDSVHPh7AVu1WzDNKHU3HQIIQJBHUEAg6AvHSy3LUyhlqF1lFQN/CwABHDTSeqCEiryHUpXyvlDU7igU9ELAwNY0qjIZuiOhPywstTAPoVQ/FMBdyz6/tT6VXpoKBLEMcfP5Oz73yVJr8hLKGcpnelGqDuUz/fiG8nEV31A+ruIbysdVlDPUq9c9bVZd0yMXfmK/84ZX/usnpdbjNZRq5S2+usXJhkLiNc9tp7Mswl+WL+A1Dz+z9X8fONffdNgllOrYPKajT3z2Vw+hDzxE74uHufptFy4tsSxPoUyR95p/e3bex//02JCZACpTOa56dKM/uc5FlIlQac1IxLIF0jEwyyTChvJOh5VtnaWW5imUiVAVff3luxdEOC6zCyckqbH6qQu2kw+XWpm3UCZC9VfG04Zh8pE3v5NEwS5Oq7MynP/ss6WW5imUMVQ+W3j8l2eezePz69hdHiVkO6xt7aYy2c8bSy3OQyhjqKpcTjzZsITdFTEA8obOPxbWEjALJVbmLZSpQ81rT7GnPDrm+O5yf8ammyhjqJ1zKqhJZQCo6U9Rls0V/55Ol1KW51CmyAvZJs23/w5hG5y5dTcFXeOe45fTF48CjaWW5xmUMZQ0DJa19TMnmQIgaDu85plNPNY4r8TKvIUyRV55ztTlONlX6vsyJVDjXZSJUHWZnPjtmpX86KxT6EzESGTyfPC+Bzl3y1ZSYt1dcfgJ8GcOLNzLINePtzLU5yB4frbBjRc/uhS4dbvtnHvfKStoqT2wrYth2Tx182c4tnsnIAfW7I3AAj6CXP/tmVM8u1GhyLu9pqvj3IgmRpgJwDJ0frrqlWgjEiaOwAD+G7HuZTOi1AN42lA3XvzofODkpa1baOg+0FUwnGXJfWSIjtqLeAyvnS6NXsPThgJ6gLRphOmPh/jIfQ+MePOkPXt43YanByLTQWextEybQo+hQh3qGmzni/t1wVX/+iepKo17j1nJ4q4urnjqCXJWhIZh27yMY6sWYDVyfe+MiZ7FeN5QADde/OjZbbnc/Rdt3SWO69vFgnQHAEktwr5QOcdln0QbSOszLGRngGagGbk+OfOqZydKGArgulPutle3dmt/PGUl+ypizO3v4+yNe5i3P8kr+9/uz9p0CWX6oXqDhvU/Lz8juLeiOBi8p7KKJ+Yv5PyNO3llibV5Ca9XyofIhIIMmmkQW9d4caG/o5mbKGOoQDKP7oxNf1ie9ode3EQZQ1mhACt7+0ccK88XiDtq1CFnCmXqUMI2WdGXpCpfYF80TMy0WZhK0xLxVym4iTKG6i1PUO841OXy1OWKGzEKxyE6Tu+5z+GjTJFX1dn9YDyTIZTPo1sWAdMkls2yoqe31NI8hTKGam+s+5QGhEyTWC5HJJ9HkxLd9jfxdBNlDJUNhdudUZ24EugJ+lmA3UQZQ1maE/zHojnoloVwHDTbRlgWTy6oK7U0T6FMpfzM3V3tPznzeFLhICe2dpAKBfnbigXU9PQf+mSfSaPMWB5A9fW9MhcOkQkVi7mG7n4aW7uch3+8XD/EqT6TRJkiD+BVDz79TMg0QUqE41CVzErfTO6iVIQa5P8t+m3lgr5M77qeq9T78dOMkobymT6UKvJ8ph/fUD6u4hvKx1WUNNTHzry/+aNnPbC+1Dq8iFKV8mtPva8hWii0PD23jlzQ4NRdbewui/72+w+e84ZSa/MKyvSUA+BYe2664DRqTRtDSv62rJE3P/7860sty0soZag/rFklzutJUmYXpwKbAv5y/IoSq/IWStWhYpo2ZCaAgITFeT/HppsoZai67FjzJEw/Y4+bKGWoNdv2jDnWuK+rBEq8i1KGmtfRTWPLfjTbASmp6epl5XY/D4abKFUp7ymLsWxXG8F0BlvTWNjRQzoULLUsT6GUoZ5cNJfueJyusmLy+7JsjkUdfpHnJkoVeduqKuiJRThhRwsXPLeJeK7A0w1z2Bb+VKTU2ryCUhHK1DSuufOvrGnZD4AtBN86/3Sek5FdS8GfXO4CSkWoVa17h8wEoEvJVY88zb5gbOyeHT6HhTKGEl8yhSYk11x1HjtW2DQaLzBX30ZjugOZKPNr5i6hjKEomOb6lx7Pazb9i/ZYGZ2xOAk7xXy205jr8ueVu4SnZxuILxVW4/A0Ag0kUcskEwgBYNgWt/36R7z56Ufp1gOyxvq+Og/XNOLZmyhuNuegi2cJahoaIOWQmQAs3aDpVZdT0HWidkFsKvvgO0qn1jt41lDY8quIgdSZpsN4+X074gk6YnECSHKavHVmBXoT7xoKqob+NkGpvqJjH/P6ewHIBsNevhczhndvoiY+w2D9MKCBgKp0eijbyuLuDn5y+60IoD0UoyaX/UPpxHoHzxpKfjLwFJa8DluCJkDCbT//Oaft2cJrNzzFHT++hZXtbQgKPFHTKJclv+Nvv+ECnu4pl9cEvwB8AUB8IWfP2VfQHvifm7B0SdC2EECBCBHTz2LnFp6NUGPRXh82+umhAceOYREiQyUZElS0m37WMZdQxlDy2uAfqsx+KmghI+J0aXNxECTYy9Zg3DeUS3i6yBtNxo5x3Zmv4r9PPJ+8bvCmjY/y5b//nqiW9+cBu4RShvr2KWdxy0vWDr3+0XFnIgsxLtzywltKKMtTeHroZTQnXrVFPltfPeLYvP4Urbc1+psHuYQydSiAef1jdymrT/lbc7iJUoa6dMNWKrL5odch0+Ly57aUUJH3UKoOVVPIcfPdD/Nw4xxMTeP0PftZlOwptSxPoZShHjlmEec8s5WLthYbdWGrwPb51Yc4y2cqKFXkPd0wn4dOWk6t3cvcQjfbF1fzmzVrSi3LUygVocp7k9y/dBn/WrIUgIKAcK+/nbCbKBWhFnf20YekJWDQGtDZp2mcuHv/oU/0mTRKRSgt23P/mx95/pzOcBhb15jXl6IzJs1S6/ISSkWoLz79unPr0/sbtYDpGLolY2bXu7/0r0v8FS8uolRPuc/0o1SE8pl+fEP5uIpvKB9X8Q3l4ypKdRsAvHndM90JW1bamiCDsH96xwnK3YPpRKkIdcW6Z9PJRLQyICQhJD1Vcf1N655zDn2mz2RR6ulckuqJLmppY35nJ33RKDXz5vFcfaU/uc5FlDHUI2VfC12UiLO87cBQy6o9e+h/2ZklVOU9lCnyMqboXLKvfcSx2v5+VrW0lkiRN1EmQoXzuaCOJKfr/PmYZaSDQS7etJWA6e+k4CbKGCofDsgd4TLedtXr2VFdzKPx+QvP4T1PvlBiZd5CmSKvyuzX/9+ZLx0yE0AyHOK+xXNKqMp7KGOogO2wr3xsbtbWRHkJ1HgXZYq8lAiyurudynSei1/cSWdZlNvOWM3cXB6oKbU8z6DM9JUnjM/b+yoXavM7+4aOpcIh/vTS1TxfG0v+7pcnJEoozzMoU+SlA5XUjZo/Hs/lqehJYkrDz1PuEsoUeTJviYDUGJ1rMxsK0F5RpsyDNd0ocyMrSZExRs72lUBPWZi2ilhpRHkQZQxl6UHRFYrTFwhjCo2sbtAeKSOlGRi2DWLdq0qt0QsoUeT9rvwn8xOhOALoC0XpCw1WmSR/XLWY6nROAHci1u0GliLX+/miDhMlIlS0v3u3WYgWd/IcRm91jIZMlqU9Qy2/RuA/Z1iep1AiQlWTFl1mJR0NCfqrooQzJvlIgHwkwCVbdvHY3EocNDQcgLNLrXc2o4SheglJiRSFsE4hHKAQDgy9V54tUJ4rIBiKXveWRKRHUKLI66+cU2+HHWLJPIzqyO0MBpGWHOxM2AR8ceYVegclDPX67jd35CIG4ZxJfVsvgZyJsGwqOlPsjMXZWFstgbOR61ci1/tTgo8AJYo8ALJZKaK62DOnhq2L5mDrGtVd/bwwv46QtECuf6jUEr2AMobKVcV45Phj6KutHDrWVVPOAtMkY+UPcqbPVFCiyAOIFDKyPzG2R7y6YOKAX8y5hDKGSmQyrNq7c8zxjK6xqyzuZx1zCWUMFbJs54LtDzC378CqF8M22V0m2PHteZUHOdVnCihThzLsAmv2bWb1Xd/g8QWryQQjnLxnA9dfeBmwuNTyPIMyhnIckAiCtsVLdz41dLw27a96cRNlirys0JwHFp884lhnNMHj81eXSJE3USZC5RJBfnPiebxQt4DT9rxAa6KWn510EUhlnqkZQRlD1ad68rajBX+9+nx+seZCADTHoSLj7/XiJso8nv3BhhrLEOR0HeE46LaDqWmkQqFSS/MUyhjqvPS/Fwq6DkJg6ToFo/j3oKPGqp+ZQhlDAUgpM5X5AkJKkJK4aVKXyfqOchFl1uUNctlVmzpiUtRIDSxHWv/7i2MChz7LZ7IoZyif6UWpIs9n+vEN5eMqvqF8XMU3lI+rKNNTPsjyq9v6ctFwWTibT2+5ZW5ZqfV4DaVaecs/uk+e++wOjm3tZNO8av564lI2f6PeTyvtIsoYauW7d/R8/L6nKtbu3Dt07JkFdXzlorV9L966uKJ0yryFMnWoxmwhMdxMACfuaWd5MusXey6ijKHi+fEn0pVn/RUvbqKMoVLhMLurRyZo3VsRpz/qzzZwE4VaeRo/ufh0Vm3dQ2cgQMK22bq4gYp0WpmHaiZQxlABy+LZqgr+eMYJQ8dqLZvzUv4KKjdR5unsDwXEnuDI56fD0BEFP7eYmyhjqK3RyLjH2yP+7BU3UcZQhZCO1Mb2YS7u7y6BGu+ijKFiEqyKKI5e/MlSE5gVEV6xY0OJlXkLZSrlIJG64JKWNs7duAvdcXhkyTwq0320ig9d1iBv+XWpFXoBdYZe/n2Pc0brfnH6tpEbLjZ2dJHVeux17Z9Q6OGaPpQp8hLZgjhxb9eY4z1lMZZ29CtzH6Ybzz+VHeI6PULP/y163fuwNX3M+y0VCb713reIx7+QkmYoPPrtfqBGNhnmTGj1Ap5/MkMkv3HnkpdeWmZZ1Kf70R176D3dsblg40b6Q1HC4xf9CWD3TGn1Ap6PUAFyVxh5HT0cpC8WYk91jHVP3I8hJcv6txMxTV7z5PF86fXnTXQJf8vPKeB5Q0m0tCNELUJw/8qVXPPXWzmpc9OIz6zteB6Y0FBqtFpcwvNFXoHItQl7H7YjyQWDzEl2jvmMFbaY3zP2+AB3T6tAj+F5Q1XIr/3vS/duf3UhYGA4Ds80rBz7mWSWlorq0Ycd4FbZZLxyJnR6BWX6oa68/EVpxmNEC1k+9ODPWbt7A5auk5Y1tFLrHG9/bmwT0GfKeL4ONYhtaBIQmWCEL1/wbqKFLJam8/677iXaucM+5AV8JoXni7xBdNtBcw6kI88EI+AIzILBWvmV4EFO9ZkCyhgqp2mcsmEbwUKxjzKcK/CS57eSDvhTgN1EmSJPIpjb3sO8jh6yoSDRXB5Ngmn4VSc3USZCxaTNo4sb0B1JPFs0E1LSUelvYO0mykSovfEwFdUJ/h5awurde3E0jX3zaihIfwqwmyhjqFh/VqZCQZFMlPG3hjoAIoU8J2zYXmJl3kKZIi9cMEmGQiAOTAPOBoJsqS1XoyNuhlDGUE4oMsJMAAhBb1WFbygXUcZQiUx6zH7DSIlu+lOd3EQZQ1V3p1i+c+8BU0nJih1tVPWkSivMYyhTKf/X3MovXf2PDZ9ZvXkP+2ormNvRi4bke2cff2OptXkJZQaHAa56xRP2JS/s1MK5AtlIiN+vXuz8+s6T/J5NF1HKUABLPrw/Vp3MfrG9LHLtrm/W++WdyyhnKJ/pRZlKuc/M4BvKx1V8Q/m4ipKGuvbiPy+49J27/XTS04BSlfJvrvpVKpYPxAphg0DBpqCZ9gc2X6ZMX9xMoEyEuuZl91UYRjSWropgxoJkKsI4kajefOz675Vam5dQ5uls3NvVEQlpXPjiA1TleugNVfDnxWcjtci7gfeWWp9XUCZCZaJRceWLv6Uh00LESTM328qVL/6WTNifU+4myhgq7nSLsJMZcSwkcwTtjhIp8ibKGKouM/5S8znpsTmjfA4fZepQLRWVZPQ4fz3mFLbULGRJdwsXbHyM1kRVqaV5CmUMZdtBbrz439lZ0wDAo4vWcPeKszh905MlVuYtlCnyojmGzDRIT7yC7aOO+RwZyhiqo7xm3J5xU/qtPDdRxlCLelqJJ7vZYTlstWz6TJuaVA+RXH+ppXkKZepQL9bPY339XNKhA3kxTnrkObbXVvhjei6iTITaGKsUw80EcNsJp3Lc3g7Etf2nlEiW51DGUOnAgZTRDmBpGj3xMP9csBJy1oOlU+YtlJhtIK7PGJ+59yHzF8evJGo7bKipHFr0KQCZCEJQKx4TIgV8H/i0bDLG31fWZ0KUiFAv2b1vz9J0iotb9rGhtmrECmIJkLUOvJDEgY8Bz8+80tmP5w0lrs+ceOau1jnJYIzNleXjf8iWYMliuDrActFsnTUDEj2F5w0FzBEBg0IoQHVugh3QBUVDjXPuNOryJCoY6oGWYEj2xyKc3N7FvNTIGQcIikWgPqb3wAbunRmJ3kGJSnngM6nT3r559yNbA4LjepLsKIvxfE0luysTRTMZAmLGgbqVEDng9bLJuKekwmchShgK4LMv/btc0L+N97z+jSOOL+3uYFtZuUVNrEr+R8DfIv0IUaHIAyCUtTipfT9XP/EYp3T1sbwvxVuefoyqTAZs+SffTO6gzNBLJia47lVXkQ2GiDsQL9j0VTXQVlYO0eDrS63PKygToTbPmyuzwZEzC/oiMeKWjbwxpka5PwMoY6hUaPzNEurS/b6ZXEQZQ9VmMtIZdUwiqU+nS6LHqyhjqEUd+6jOF3AoDg5LoCZX4Phuf9WLmyhTKQ8VsizoTxKPRZGAJiW1yRSRvJ9zzE2UMVRbOMwpezayrWYR+YBBpGCyvGMbj5TXllqap1DGUMuSSTY0zOOcbY9jiSBBO8cDS1czrztbammeQhlDFQjLXVXz+En1yFUuazq3lkiRN1GmUt4TjLNqS9uIY4t2d6Dl/V4DN1EmQoUKuefOenrHmsbWbvbVlVPblWTZjv388+R5/vawLqLM4DDA/1t1tzxx44EotWVRLe/Z8Rp/1YuLKBOhAD7y4ivETSffnY7nZCQX1Jx8KBI+9Fk+U0GpCOUz/ShTKfeZGXxD+biKbygfV/EN5eMqSrXyAP4hvnlPDZmLdGx6ieTWyo9HSq3JSyjVyvun+MYzS2k/IUxxhbmDoI1qeaz8tB+pXUKpCFUvkieEZIE8QSQaAQpUk/Q7Nl1EKUMZ0qKfcuyhnx0lij8fyk2UCvVpLTrMTACCLNGS6fEiShlKY+w4sMQv8dxEqSKvgh6C5EmwH4FDjnL68GdsuolSEcoRBRLsI0slKerRMYnTwUPR/1bqwZpOlDJU3E7RzVLS1JGlil4WAgZGtuPtpdbmFZQyVIE4DoERx7JUYwjrOyWS5DmUMdSCD+5t79XH7usi0dCl5tfMXUIZQ3VGwjXZgMFAVs0hdGxio5Mh+hw2yhgqblvivsXLsZHkhIEpdFJaCImNgx+h3EKJ1k3iP7r/bUnW5LMXnU9Vp6AieyBbdJA8ZdmMbyiXUCJCzcmZv9xeFuX4lq4RZgIoEMJW4i7MDErcyrSha0ldxzTG+blSUmf3gVgnEevqZl6dt/C8oRo+uD/XGw2hAU8vn8Pu6rIR78+3Wok5eUAH2F0CiZ7C83WosHRCjiYICMiHAlxz1bm89vEtNHb1s2leJb/40z100YCNjo7tb553hHjeUHlNc/KGpkULFnkgGQ3x85cdD4Bu21h3a1hOcHDgeHROMp8p4vkiTzP0hki2gKVrMGp26mUbnsC2yjEwEUUvXVwSkR5CiSnA8U91O2YkLAq2wxltrewtT3DRpo1c95f7iBdMkhg08mIZcr0/2+4I8XyRB2DkcnemNeM1VIY5dc9uPvuDvwy9Z6HRSRmNvplcQYkIBTD3ox3yvC3baWmo5bXPv8ipO3dQ3Z8mkrbZK2Kc6XzE79x0ASUiFEBIk/Kt/9wgNqxajKPHeXTpagBO2bKVXtPwK+Mu4flK+SDCcqxdc+twtJE/edPcBupyXWqE6RlAGUPt+OacIHJsqWZpOuFMrgSKvIkyhgIIpAtjug6qOvrZRej/SiTJcyhThwLICo1VG9poa6jA1jVq25PUtPezzOh6W6m1eQWlDKVpkoreDBW9B3b17IsEmZ/5olVCWZ5CqSLvhF2t9MdGzimPOJkJPu1zOCgVoRY5O6hL72FT+BjyIsyy7DbI+91PbqKUoYIiRUCaHJ97fuiYpdYtmHaUKvKenLN0zLGOYHUJlHgXpQyVNWPZh+ccN7TupT1UxR+XvLSkmryGMmN5g/y+/ge5PQ3RkK1JKnuEnNvRH3t5/3v9HYRcQjlD+UwvShV5PtOPbygfV/EN5eMqvqF8XEU5Q3WID3+rQ3z4kSUfa73e+HLhHaXW4zWUaeXtF59IVNLSJ4C8oXPvsjV87YzX8eKcWgrh0JL+G2I7Sq3RCygz7lDBvt4AJgXd4Isvu5w/rDqFPfUJMkaIyt7UNogpF62nA2UilCUulw6CRyNnk8hatIfK2NBQx3VvvwDDsuj9XMIfJXYBZSJUmgr6mceybBcAtfk+YnvyXPzEVv6yZkmJ1XkHZcJ8P/MwhiWqE0CD2cMJu/bj+LHJNZQxlD1OMA5gMbc7RdmonFE+h48yhjIYa5oMIar6s+THyxvlc1gocycraaePCM5AsZcjSCcVzOlNc8Ezfo+BWyhTKbcIkiVANxUIHJxigjEEcNrmFuCkkurzCspEqAwxEvTgIIbMVMShrTJRMl1eQxlD2RhkqCRCnsFc5QKH5xrreGbRnNKK8xDKFHkB8ggEQQ4swQtiE7ItHjlufgmVeQtlDFUghIMgTXhoj7w8koDlkAsGDnG2z2RRosjbLt7/XIg0OQJIBAKJQAKC8nSeeNpPluEWno5Q7eITH43R8/Ug1URJEiJHEANrYEcqCQgpOXFbGz9YeE9P0JG6bsu/ag6XXrHvirHbf/ocEs9GqBbxnw0VtH89SYIQKXRgOTtZzlaqtf10BKJk9CA16Sy75lYTKWgVjq6VmUH9dY7G30qtf7biWUMZZH4eIEcvtZRhIwkCxX6neqebObKLtBGkOxphd30F8Z409sAeQpahnVlC6bMazxoKRIGB2tLoTRcBEk4xR2t7IkY4VwAhEAem8qgxp2ca8KyhTKJXFghTSdfQcMtw0iICwMOrFmAGDPorImgDNjIs596Z1OolPGuoBfKz3T3MfW2MPtlB5YgtErKEkXYATTP506lLCJkWtrB365bTEczbt0az9itLJnyWo8SMzb3iPb/J0bDOIYiDRp5idHpsyTz+84qzCWQFW79e78+KcgFPdxsM0seqy0IknSwxNs6r4UuvPYsNC+o4tqWD3VU1rNnSWmqJnsGzRd5wVsqPywJhTF3jA+98NU8sbSAXDPDkknkAnLK9rcQKvYMShoJis+2JxXPpKI+Nea+63+8pdwtlDBUmTX3/+Nu5dJRFZliNd1HGUEFsTmzfxIXPbB1xfNmeTh5etaBEqryHEpVygDxh0kg+8b+PsnbDPjbNr2blni5OfbGFy2+4tNTyPIMyhorSQ4u2BMORXPDUDi54agcMdHgev3UfsLik+ryCQkVelq4qnSgFyslRTo4IBRzkeFvA+BwmyhgqT5x4VieIjaAYm0LYdNREOXWn3w/lFsoYKoBDPDN2VGBef4pCMFgCRd5EGUMZFBBy7D6Luu3w2JK5JVDkTZQxlIPt1LBvxCCxAzzZWM8LDTXeH9CcIZRp5cXJhgz2mhJJF/XY6Pz+pBU0X/5SHMOoKrU+r6DEbIPhpMV7rgJeUv1f3/h5PhTKySbj+UOe5DNplDOUz/SiTB3KZ2bwDeXjKr6hfFzFN5SPqyjTbQBwt3FzR6UdqbEJoGFjRfvkWelr/IfKRZRp5T0Q/Laum4Y13D0SkIEW68zCjX62DJdQ5unsFWZ29KQCAeTFPKWi9HSjjKF6yhL6eLNUeuJj55j7HD7KGCpODxYCe9j/DrBzXrjU0jyFMoaKaBbZoA5Ds6EEDhpLe/aVWJm3UKb+4PQHZLRgU9A1TEMnFQoQzxVoCcwrtTRPoYyhWiMxEXZ0IqZNyHaI5036w0EMzFJL8xTKFHl9iRgRc2RSukSuwOb62hIp8ibKGCpsadIWY9t5/RF/+q+bKGGoeR9sv/KpZY16Jjry5xpYvHLLxhKp8iZKGCom+MXzSxuZl+2hin6i5EiQpp4eTmptL7U8T6GEoXSgPxrmwWOXECdHDf1UkEZHog9LhO9z5HjeUKuu7tAlAg34xSkn0ZYoG/au5NkGv1LuJp4aHF55dbu+KxTIJzI57cIXd4mr738aGynf9cHLhqrjhm2zdncLq/bv401PPMlfli/mvL1/Y0GqFwAxKmFreyh6x0cveVvNAwtX9bcmqm6QN0afGvPF4srjgAuBzcDdyF8eWFwjLn8ZcArwCPL2f07H7z6a8EyEWnl1u7YtErJyAUNvL4+Ln59+HJdcfSkOATF86ZSl6zyyeCHpYBABnL/zORpTvUP95wzvSgdRk8+s21pV/7LWRNWrDdt+UtyQ+eCILxZXvh94Dvg68EfgTsSVxUuJy78N3A98DXgYcfmXp/EWHBV4xlBtuuiytJHdAr2xMH8+tpECI8POKXt2cOVTT5AOGMw1dx/0uhrwP3f+ACiaEfjC0JviyhDwRRiRZvgS4ELE5SuA94+63CcQlzdM4WfNOjzTU24beoJx+pmeWVCHFJAFqrNpvnfnj1mzfw8AWyqr0HsOvd/wgv7u4S+HV8KqgIpxTlkK4+SyLrYPFgGeTabgmQgVLtg/ZnR9UEquePRFbCAbNvjwv/4yZCaA5T3dSA492+CepccPf3kgY5n85V7g2VEfd4B7gYeB5Kj3OoDHD/mFsxjPGKrrltp3RizbGTKVlLzmme0s6E1jG4Luiignto8t3p6vWYipjX8bJNAejvGhV7wNzXGImIVO4OJRH3sjMFhRbwfegfzlVuTtSeAyYOfAe1uBy5C354/ohx7leMZQAJmvVuqVmXxtKJt7+A/N/2d//J5HnSzOpzUJtq7x/Jyx1ZeQaaM5DhY4djG6dFGsZD8g4C11uXToQ4/du9jRtMbMTRW18sbozhEXkL98AfnLk4EaoAH5y58ceO/2P1Ms/mqBFcjbH5iu33604Klug/F4SNxqXPnRN5itdWUs6e7k3u9/nca+HgC6QnF+1XgyV29+q59yzCU8byiAuo90SDsapLs8StAyuWDrRipSef79jufZVxXgio63+4ZyCc+08g5GxkFKxxJv+vuzbJ9TSUU7XPXgFqQQJArZUsvzFEpEKIDvNvzAvv2M07TLH93MCS0dtFbE+X8XruXk/Tv55v0X+RHKJZSIUAArOrvY0FDD+9+2mIBlYxo6AAsyHSVW5i081co7GLqR4VWbNgAQsi1CVnGWwcu2bymlLM+hTIQK5C2+fN8dvOXpZzh2fxd5w2B7TZxlnXtLLc1TKGOovFMhkXnW7t+JQR7bChDdl0eil1qap1DGUNlIWMQyScIMtupyBMnSQ31JdXkNZepQhWCBECO7CAwsEP4yKjdRxlBVmcy4w/89UX/Vi5soYyizwI6+wMh98Rw0/rxosRodcTOEMoa6QF677HcLTyZlRHDQsDG4v+EYKtrSPy+1Ni+hTE85QIf4aOBHx5yRD4YKoi8Qkov39Pz2Lfv/fV2pdXkJpQzlM/0oU+T5zAy+oXxcxTeUj6v4hvJxFWWGXoazV3wkHcKO2uhOgMKKCvmdbaXW5BWUa+V1iatlFfsQA0s/s8SxMSri8ra+EkvzBEoVebvEx26J0oNJGAcDixABcmSJdB/6bJ/JoFSRp2O/P4SFNhCdNCwkEg3TnwLsEkpFqDgpIRi5kbUAYqR9Q7mEUoYKYyLGzDkQjDaZz+GjlKH6iSAZ3QiRFCaR38BncihlKEEAk5EbT0kEOvYEZ/hMFcUM5Qg56icLBAEOndLHZ3IoZaiW8jnkgiFGJqljqNXnc+Qo07F58yn3HmtoYsOStl0URJCFmRZO7n0WQzrkMdhL4xmL5E2PlFrnbEeZfqisJp5b2NFLX6AcgI2J5dhCZ1V3Cykq0dn/W2BOSUV6AGWKPMOytdEdBltji+ljDjYhAlBdEmEeQwlDzflgy1c74lEkAqMgqOzKE0vaSKmR0QKAxCTqr/h0AU8XedWf6A0nc9k7RCz2io7yBIFt7ZT3FdfmhXMmwbxJRyDBkvweNDWerWnHs3dxzse6t9bnClkjEnmFbsODDdVDZhpEk7C/opJ6thOjXzji8v8sjVrv4ElDVTX1vrUxby7dEwmQ1TXymmBxKoNlaOQMnfuOa+SOtcvZWxFDC9g4hIiQxIHPllr7bMeTRV7Ydj4ugVQxUT112TzVms4LK+bx7TOOpaU6AcCPzlnNd3/9W3qYTzl7MEiDeHMl8mc9JZQ/q/FkhLIFT2pAwCn2sZ2cyhFD8NuXHDNkJihmBv7qOWdjY5ClAlns6BydW9xnCnjSUN2h4Id7dY2FeROkZLC/oC8UGPPZ1ooEfcQR2DhQQP7M3+/sCPCkocyb4qm2aCjSq4vOmnxBbouFSUtJQ2ZszvlzNu8CNBzyBOTtoZlX6y2UGHqJX9Ntv/vhF7TORJxwPsvtpxxLKhTk3M07uem391GezVPHi7JK3urJB2wmUcJQAO96+b/kmdt3M7+3j/l9vUhdEjIdHHQ0CoToshfIZk82UmYSZW5gPhqUsbQp0kGDatlDwCzOgcoRpJs4GrY/h8UFlAnxC3e1bQ4HM5zQtZuAPDChLkyBBnYSwvxTCeV5BmWKPIAtgetljZUlSicB0tgEyVKDRFIuv+kvVHABZYo8AGkhy2gTBjkANLIYtJDB38jaLZQp8gAi5Bk00yACSdDvy3QNpQwlKIxbwDvjpnP1ORyUMpSGTpK6EcfyROlhbokUeQ+l6lBB0uxjKSkqidKLSZhe5gKWP/fXJZQylBzYkDhJLclhFXF/GZV7KFXkOTi9CXpg2LIpA5MQ/er0nUwzSkWoevn9mqR4vwxikieCho1GDovMH0qtzSsoFaEAkgS+atBDGXuJsp8ChfRc+Z3XlVqXV1Cqp9xn+lEuQvlML76hfFzFN5SPq/iG8nEVpboNALaLLwQhkwErs0R+OXHoM3ymglKtvG3i2kKfURfoM2Lo0qHKTFLmtJ22UH71X6XW5hWUKvJ6AvWBfaEK4rIHTeTYGaknrdX5OaFcRJkib7u4/o1OqJKL0n8sbl4N9IlKNhgn+3NXXESZCNWHU7c6//SQmQDKZQ/zrZ2lE+VBlDFUBBEOkRlzvEr6aQzcRBlD2QRbc8THHM8TK4Ea76KQoUS2j3lYFFebSyBLBX3+AgVXUaZSbpCrcwjSxTIMcjgYOATAn0/uKspEKB0SOYKAwCKCQwAJ/h4KLqNMhMoRywUJ0ItBlBw2Giki/rYcLqOMoTTMbZIwJjp9HMjaI3xDuYoSRd4W8WURx/x9cJzFCBGy45zhc7goYahq2pxFbBIV9BAkT3EPKkmMNGX+qmFX8XyRt0l8PLmU/aSoJUEHIZK0Mh8DCBj9/K1xEW/7ZKdtBg1HBAN6IV/IO1+ojJRa92zF87MN2sR/ODV0iABJxMD2iykq+Id+LpsbKrn7jCUsa++hMlugqyLK31cuZGtVgi/d/jcrUjD3C7gfIb773o2vf/CHS37T6AhxnW3o5+ejoWyqPPYz2zCSCPEKYDfw1evuPX1HiX9ySfG0oXrFOwo25YEq9gyYSUcSBnRSRHhaO4HWmipS5UEAbE2Qi0f4/stOZOHudt7w7LbiCj5N2DjytYblfE9Aw+D1M2UR+qrLcfShXT3agWOvu/f0rhn+qUcNnq5DOVQYAmfATAJJFCj+48fJssZ5DqNwoJWnO5JAweKl29q4/7jFBy4kpa5J+bnhZgKIJLMI24EDD2UdcOX0/qqjG08bSqJhoQ+sEzYY3SseJ0PQGNXKkxKQ2OaYja2D433HOJs3jvs5VfC0oQR9dpTMwE7oY4t2CUTEgeksUoAZNHhmXg0v29Ey7EICKcTNEvqGn5+LhrF1HcSQUVPA7a7/kFmEpw1VJW8N5AciVLEDc2RO+zQVCA32VsTpi4boTsT4/XGLeaKyglc/u11KyAAPAVe8Z/MbfibgHEeIeyxD78mURdr7q8r+x9G0rwBPAb8Dzr3u3tNbZ/p3Hk14ulIOsFt8KDOXvohBGgCHEHkSZKigQJRn59bzmVdfLDsjYdkbDmlBx3a66isCsskYU+b5HBrPGwogJT4gDUwyRMlRyYHALMkSYKn8jD/lwCU8XeQN0knkVXmCMk8FBhY6FjomBiY6fiByEyUMtUh+9U+9RM7RsREUd3bRBvaeCmKWWp6nUMJQABnC1XLcyXR+hHITZQyVIvqSLMERnQcmBs443Qk+h4/nB4cHqSD5TB9z6UUQIYeDRoYI5Sg7SjItKGOoNPGcg45EIzVs9Usef2KBmyhT5GnYWQMLgUMQc2DBp1TnBswQykQogYyHySPID1XNHQSmX4dyFWUeUB25VA6M6g2iIdEnPMPncFDGUA7Bp7RxuggCfj+UqyhjqOPlZ+4bzzw6Bb/McxFlDAWQw3L0IVNJAuTow/5FSUV5DCUGh4fzrPjc18NoH3YQaBBYIa/zu8pdRDlD+UwvShV5PtOPbygfV/EN5eMqvqF8XEWZoReAj73qiQo9m+1OhcNCSElFOuvc9MDL/M5yF1HKUGbB7GnIFIjs78MxNLLxkPaPqq/Ihp6OwiJ5c+jQV/A5FMoUeTee+8/jF/SlcQI6ydoE6co4hi1JxmuAUPApcXNlqTV6AWUMlTT0C7SgjtQ1BMU1xGY4yMb6BegEqKKzo9QavYAyhkohI44+9ueawQCOAQUC/lIqF1CmDiU0bdzch6Fsgd5QFMsam93OZ+ooY6iQJsJa1sKJBA7kzHAk83Z2U5lLYfnTWFxBmSKvKpnK7EuU0bhpH0kpEMkCK55pJWbmCNvmeFlUfA4DZSKUGdCjqYhO0ztfTltVGZrjcPGT27j5V38iSwTDX/3iCspEqFCm0PLA8gbaqsoAcDSNu9Yu597VyzAJ009ViRV6A2UMlQuG2VlTPub4E4sb0DEZL+W0z9RRpsjLR4xTa5IZWqoTvOGZR7n2L3cQN3O0RecQJUOcDq1PvG1jufzxylJrnc0oY6htiVj1xc/vIJzv4T/v/QM9NOCgs6KriyjdpJhDlP3HlFrnbMdThhJfzH2YgnUTmkgRDc2XTcZQX8Bj82v+LZG1+N2td7CdlRSETkhaSGrQsNEQZKihrJQ/wAN4wlBz39/2ynxF5E6tPCqcWIhowYq+9MVdhZPfb9grOpNin66LnuULqOjrpIVGcloYgLSAnAxgyAJlZLAJ8Ij46o2ny0/cMFUNG8XXzgTmAH9ZKT/ed6jPe5VZXyk//arNF8WD2h97KuLCERo4kAkaPL5wDsd3J/UcaB21CZEKGMzry5AT4RHnZwhiDfRCWQQox77+fvH97r+KH0wqm+9G8bXgRvG1uynm4vw1sHuj+NpZrv/QWcKsN5QTj9y9dV71qIPQGwlSnTNZ3d7N5oYaDBzqs+O05IRAolFAR+KgoxOjUAni85OUcCVw8bDXCeAbh/VjPMCsN1Q2qI87qCscSbxgEpQSy9Bp7M/y7KJ6+qMjA09IFohQwCFEYGCrs2Dxz9MmKWH1OMdOmPwv8Baz3lAVBXPcMZPG9n5sIegzDHTbQThgGTrffe2pbFpQTT6oYUagQXYxeBscdECSxwDkHyYp4cFxjj1wOL/FC8x6Q9X3ppfN6+o/cEBKartTnLdrH4/XVNBSmeC0rW2kwwEA2ivjfP/VL+Had13EI2vmD2wUO3QyORwyBJ8/X77zq5OU8AfgmxzYbXYj8IEj/mGzFE8s9BSfSoozOjrtPeUxURCCsnyBhr40fWGjdVlvOtCrGdW751boIVtjcKrBnP4kX/n1H6jM5orXwCZCH/upWrdafvqOqWrYKL5WD9QAL6yUH5/9N/Uw8YShBnnJR/Ya+6Kh06Qm9rd8oXLriPf+fY9z2r5uIfJ5KlNZalMZGrs6WdOymxBpQmQwyFMmv+NPtDsCPGWog/EfZ93/pQTap/bPq2HV5u0samknbJpESNMXjrG2fSMCqJFf8w11BMz6OtRkCQcCL2iO5PjndzB/Zy+WFSQpYrRrdcRzeZKEgNSHSq1ztuOJnvLJUDD0E3VdUP9iL2IgKA/uUSVsAehOjfzet0oo0RMoY6hEOtORyFjozqgiXoKOTY7w+Cf6TAllirxsOLRCTFBfrMv3jNr4zOdwUcZQ3fHo093VZdjayDp3Nh7CskIY/hYdrqBMkRcumC0B2+GFNYsI5QsETYuVu3dzQvtWgtj0oKnR3J1mlIlQApZoto0VMkgnovRUJ3j0xGPJRUOEKGCQ9bsLXEAZQ4Ucpz+ZiI445mgaG+YvQkcS8mtRrqCMoQRyhzNBDJJIMpT5lSgXUMZQQUc+OLrHQHMcjmvZiU6WNLV+Oh8XUMZQn/3LGTlyBZnXdAqaRjSX5rwXHyOa7WE3lY+9RH7Qj1AuoEwrD4B4VO8xTas+b2p9RpgfHn9a/rY9H/J7NF1EmcFhn5lBmSLPZ2bwDeXjKr6hfFzFN5SPq6jVygNEs5UCBrvMH5RNxjml1OM1lGrliWbLAUb3l9uyyVDuwZoulCnyRLN1IWPNBKCLZssfGHYJZQwFrD/Iez+fMRUeRyVDHaxHvG7GVHgclQw1bp7yAfbNmAqPo5Khsgd5r3emRHgdlQw1/vQUKVm+Zc87z3nzc6+dYT2eRJluA9FsZRmvHiUl2JKQZVHbl5R7bq5X6SFzHZVuXnqiN1a27ycfCNBSXSlWvWvzwYpGn0OgUofeuEVe2DR58utfoisa48o3v5NdNTX+/KgjQKUINW7n5TsfeZiOeAVVmTR/vO3bXPH0g/zmuJvlR877kzz1A7tjMy1ytqNSHaqPYv7LIlJy4ZY9LEyaSKERsEze/dCdWHmLqlQP8+xWPnneW/jvey/xe9GnwFEboUSzdZFotkzRbHW6dMnE8BdleZParESK4i2wdIMt9fM4pXszSwp7SWrlnLV/i0tfrQ5HVYQSzVYD8A9gAeOb/WeyyXjLYVy3DOgf84aULO9OcdauTl7/7P28asPDB95C8s+6+Xz5jEu6WqvqVj71g2XjGvvSS58Vf1+5sKY7Hu2S1wSUX+hQckOJZmspsPWQHzyABApAnmI3wAsDr9dSrCc9AZwNmMDfgTOZoP40SF0qx9PNn6EunRpx/LcrV/PGy99L3MxR3ZtkqW1QlTXZEwvQrhusau3gNU9tIZK3eHDVAn5w/hqW9iTli3MqH0WIk4BWio2BCuA3wIdkkzHW2CPvx0eQ8tNIagZ0Pwd8TH4y8HeA4Jfy72joznyjKpVPLE2mqM/mKGia+VhlwuzS9Uh1wRQb59cQzxUQtkN7RTRJQP+Q/GTgx5O+w0fA0dDKm2q5Iij+Iw222taMen8tcCfQB0wqAX17PIytjQ2Im2saMA2DHiNOTyTOyueexAjVsycSpr4nyZsf3jD02Qs27KIvHuaXZx0vEOL0gcNLhl3urQN/vm3CH9ZsvQ74xqi9INcAd4qvmAvRxOIFvdkfVKcKLOlPsSBT7OEIOnbg/P1dgQcry/nXigYAsqEAwpHotl1mG/KH4ivmZvnJwD8ncz+OhKOhDjUdld6XAhdM5YRvn3neiNc5I8BjC48bceyfjcsoALsryzh9W9uYa5y+qRWpHfTnXHoIGZcyfokRo5hc/w2V6WLy/tpcfsQHyvqzPLF07ohjUhNUJXNgOQJ4wyG+2xWOhgg1HWQojt2N3SBvNFKCENx0wavYH09w6bNPkg2E2dBwArsqa0Z8NJ5LowPRgklvdGy3Vk8sPHS9CTjUIPT+Q7xXa+oaAdumoGuEnQNVNjNokMjm6YlHRpxU0LVBPQe7tmscDRGqfRqueSPwmYN+QkrImpAsMBgVfnDa2bzq3R/msje/l5vPPIWNFQe6oSKFPBfvbkUHlnYnufuEpfSHD+zKYAvBb05fSfl4238MfCPwuUPo/hZCdIxz/H7gr8BP9pdH9ktgVyw2omTsLIuydkvriJPKUzn6EmEwtJ3Ajw7x3a5Q8ko5gGi2/gmcfsgPFvklxcquQfEfaQPFZPOfpVhJ/5JsMv44cN3zKf4jnkSx2DhA1oTUwO5nhgZlweKTPOx2nLpjFw19e9kVq6Ismyvsm784UGZaoisckCKZkwu7ktpJO/dTMDR+d9oKhK47mVDgT52JyG0U62/bB763Blgvm4xHJ3Ev5gJvR8o1SLIUd2X4X/nJQG7g/arKVP7ainThnPJ8QV+Qzpgh037hr9WVnVlDX7m0p+8V0tD1dDDArtqytAwaX0SI78pPBroneX+PiKPCUKMRzdYvgSuGHXIodhlMWKGdxDVH/tC+PBSGTZEqD8Oo+s/q1naevaXB79icAkdlHUo2GVdS3OXJTRyGF/GGYMQ2w6YNoZG3Y2FHF9Dgsgxvc1QaaprIcWD5FEQCkLfBHghcWZOAdLCCASKmyfkbd9InCv7O1lNEJUON7MXWBFSGi3WptAUSzKzNqdt2c1rbXlojWtsDd57rh6cpopKhxiJEMVKlB9MhSspzffKb951/NLR+ZyUq3bjxk2gKMdS1GhKSAIEPz6Amz6FShJpwTnmFnWdF934C6dzTf/zVWn97jiNAJUON3z8iBL31lf949OZaZTeedhOViryDrcszZ0yFx1HJUAf7rUdf7+4sRSVDHazHO3qQ93ymgEqG2naQ9z44Yyo8jkqGOmmiN2ST8cRMCvEyyhhKNhk2xem0o3nXTGvxMkflbIPpRjRbbwLaZZNxb6m1eA0lDeUzfShT5PnMDL6hfFzFN5SPqyhpKNFs/V40Wx8ptQ4volSlXDRb3UDlqMMJ2WQkS6HHi8za2Qai2bIAfdghSzYZgYN8XmOsmaCY88BfiOASs7LIG8dMAIZotnYe5DQ/0+8MMCsNxVgzDbLwIOeMF518XGa2GspV/K053EMlQx3MNL6hXGLWVsoPg/HqXYMEKa7bmzxinaCYkCNIcTZoGLn+YLNClUClCHWw7HITtg7HRaw7fuB6IYrRzQAsxLq1h63OI3jOUKLZmijqTmiaw+iHGm8aDMC/pngdz+E5QwETmWPCepJotha79N3K18W8aKjDSVxf5dq3i3USsc5BrHuFa9ecRXjRUIdDn8vXE8BdiHU1h/ykx1DJUAdbezf+quIjZ0bSEB5NqGSog/3WzdP0ncrVqTzZDzUqW92DFJPpT9QHhWwypmvlsECsG65FUuxuGK2lAFyFXP+badLhLmLdEuA9wCkUd/n6M/BD5PrcrJu+IpqtRmCXm9eUTcbkI4lYdyfwKje/fxhfRq7/9DRd2x3EuhXAY4za6gS4D7n+5bOxyHvK7QuKZmvVFD7+Sre/fxgfRqw72nfA+iBjzQRwAWLdqbPRUMFDf2TKTGUmwnTWiyID/x/NHKyLpXI2GuoD03DNqWwPsncavn+Qe5Hr3dp9a7r45QTH9wH3zzpDySbjp1M8xebQda7VU7jeMVP8/snyf8Cbpuna7iHX/5FihXwbxcHxDPAX4KJZWSmHcXKOjyUB6LLJ6B12Tp6Ji8s1ssl4ZvIC1k32pklgB3A+cv1IU4t15RRbe73I9Z7ZFs2T3QYTDPZO2G3AdKbzkeuXTnDc7d75o4JZV+RNE+MndD1ypqO+d1TjGwqQTcZj03DZDHL9d6fhukc1XizyJqrf2By82HPp29crN9wyHC9GqDdOcNyLv/Wow3M3WTYZ/zfBW262pB6a4PhdLn7HrMRzhjoI7hVFcv3ZFMezhvMccv10DsvMCrxYh5oIdx8euf5UV6/nEVSKUD4zgEqGmn1DArMQrxnqYBsmKr8IcyaYrYZ6+QTHD7bi5ZLpEOIzklk5ODyIaLbuAV4GfEQ2Gd+bxOcdxrb2dsomw611ecozqw11OIhmazcwf+Dlq2WT8adS6vEayhnKZ3qZrXUon6MU31A+ruIbysdVfEP5uIpKY3kzgvhSYfHc9v6tmbKwlg0Z6KZFNmi8V14T/n6ptc0EfivPRcSXTbF4b4+zs7YcaQ/MlgnqaEgcTauTnwx0lFbh9ONHKBeJpHP/1Rs2kJlhU9TzDkR0opnkPqia/hmjJcavQ7mIo/GxaC5bfKELCGggwDEdjunqU2JqsG8oFwlYjlaeSUPUgLIgxAKQCFJm5rhw41QWJ89efEO5SKI3mQxpDgSHlWxCkEnEeHGuGsns/DqUi3SUx7S2stoxx21d575jVpRA0czjmqFEs6UDnUDFwCEJnCKbDNfT7xytmOGQPm7Ql5JMaGqp0GcrrhR5otn6LMXVtxXDDwNPimZroxvfMTvQJEiwHRjsjpESbHng9QCXXv7gG977mrvv+UfjJy8vgdBp44j7oUSzdQxwKNP8RjYZlx3RF80CxOezHRh6DQ4jH1UHcCwpr49qH7/krvfZwvrOX1adQsjRWd6dRMuluemuW/5tUf+tvy6RdNdwo8ibTNaSS134nqMfx4mAXozNg8/pgedVXHPxn258cW7D9feuXImtFR23uTrKxx/6B9e+4gO/+rkHkry6UeRNKqOcaLa+48J3uYJoti4TzdbZrl9YQ0cIRv7PwG4wBi/UzL1+49z5Q2YCSAfD7KgIcNaercQ+21/nuqYZZia7Dd43+BfRbF0pmq2MaLb2i2Zr/sFOchPRbH1yILfUr4AHRLMlRbNV7doXmJYzZm2NEKAVA095LkdXdGzGw6fnLeKsnU/yyqef2n/1hb+brozEM8IRGUo0W1NqugwYSAK/oJhLsg7YI5ote4Y2QfzyOMdcG1+r6e8Xg2WcbjvEMwXC+YFhGCFIBTRSg9FpIIIFbYfNtfO5/YRT2dm4kk3zFhkfffn6D7qlaaY50jpUL1Mr9ycK6RrgiGarSjYZPYMHRbNVDyyjmPk3SjEv9lLgUdlkPDHwmQZgEfC4bDLyotk6FvgoB3IZnAdUU0zdNx6uGFncZIZD8bIIQDRnUt+dHnpa02GDbFjjubrFLOzN0hfT6I9EOH9HOyu6irnRnl5wEruiIfRCHTl9zy3iZuvjiGJ1nmLrOUjxfseBGJACnqb4b7gBKKeYLa+a4v2KU1xW9k+KD+9jwIkU9wV8CcUHKTRwnb8BjUD9wHctBF6kmMNhHvB+oBv4b9lkTHQfi/fhcFp5A9Eki/tbWkjg3bLJ+IFotj4N3EhxW7LBjQ6HD64+RTG348cHjncCbcAJU/7SqeQpnwDx2ewdC/v7X7+rKkFjV5qAPfK+atLGEjoO0FUdY1VvktNbu0d85umaMnaaDsdl+3li2Vw6ysqPVNZwLCYOICbjb/+WY+TSNAm8UzYZP5roSw7XUFcDt0z5xMlhAmcDj0zT9ccjLpuM9OGeLK7LJBCyVwtqQtqSJV1jL2UDjij6NhkNsDiZYW8oSEBK1nQnWZjJ0RYL8bJ/Pc8PT1nG1Zvu492Xv/+wf9A0kqd4v8bN+ne4dag3H76eQxIA3jCN1x+PI10EuhqEcKRACo2cMfa2Dj62liboDQd4sjLB3miY3bEIf5hfS1skRH8oQMSyWdSd5pztLxyhpGkjRLF4HJfDNdR0dsBZzHyepTuP8PwXgaHe8I54mIJejEYOYOsCORCdciEdOao4lELwbGWcR6sTvFhdwbLeDjbULzhCSdOGCbRM9ObhGuprTF+i04/LJuN+4AscqFibjE12sQX4n2GvkxRzZ08Z2WRMbQPr0ef/V7QbTfxSOEWJBUNnT2WM3VUxds1JsCKdRhsw2xktm4otvFH0BCT9eYe/Lm7gnH1Pcu0lVx6JpPE4WMK1ifI+jNeF8QnZZEyYQ+KIhl5Es5XD3Yr5CtlkDE0cEs3WImAV8CjFls25FBPPPzy44lc0W8sptvwelk1G/0CH5bUUDWgAJ1Fs8ThMsO2FG5VyAP26fscJBQWGVpxgByAlJ7V1k5aSPPDLX3+dV7z9U/RpB+q6muPQ2LWfnbFqlia7+NCzj/DRy67sG2jlSYoVY4Niq82gWC0oUGyEDEYMfeDPMoqtvxjF+s7fB957mGLrDuBUYLA1nQR+T7E1PY9iS28pxUbPXcAK4JMDn/+cbDIO2s3ixljeVC4wUWtCAgtlk7HniMQcggm0Stk0TqXnMIhc19eXi4YTjLpcNG+SCQZY09rGX773n6QiAT54ydt5Ys4iGvs6+fo9P+Ntr3gX28pq+fld3yeWL7/+tTve919uaJpp3BjLk0yyL0c2GUEA0Wz9HLiCYrP0VNlkzFQN9BsU+6iGc7xbF7cDmhiKTMPIBA2Qkp5onK2JRk7reI47f9E89L6F4IR9u/nAsw+RCc/Lv3HjW2almcCdCDVeRpPxeFE2Gcce0Ze5hGi2bgXaZJNxg5vXjX+qpzVdFZs3po4kJTiSM1v2IdImf/rp5ylzDmz20GVU0BFP8GT1yWdctfWtM9ld4jpuGKqXYi/tQXGrnnI0c8K7trU/t2JB7RhDOZKy3iSXbXqh5b6VK+Yvae3jAw/+nTmZTuJWisWpFv5vyYX8+4tvmfX3yI26w2RGyI/2LbtcYW8irmPZI9ujAxWCRT1J+YM7zl5w7sZNP13S28Y/V86lMx5gb7CeH614DT9f+xJXu8VLxREbaqAJ+Y6DfCQtm4yxE609SCJvxsKpfLGIkwwZK5TOEzaLrfYf3/Gyt972+3NFu5b41AvVc3t/e/zKL3z0uX8TD/x0ZX/plLuHqyuHR20hJoFjhncDeJ3yT3f19UejCSwbwgONWVtS1p0iFNRlx5dn5YaXU8LVVS+yyXB7sHhW0R8JFTv8IsPmHBqCZEUckcuXSNXM4vknZiapzJpGLD9O57IhqExnZ15QCfAN5SLhghmytPEbarpQI6u1bygX2VsRs4P2qCEzKcEQtFRXlETTTOMbyk2Efkt9MlO8q0OLEzQQglDB9My+wgfDzw/lMtp/5WRZwaI/HirOKpASYTsEbHtu/rrovlLrm2783AYuIyWVeSG7sR2BgFgmT0DylZ7PJzxvJvAjlI/L+HUoH1fxDeXjKr6hfFzFN5SPqxxWK++GSx7XgZ9QXO7UCpx+411ru9wU5jM7mXIr74ZLHq8F2sd56y833rX2QldUeQDRbP0OKV8LDExjkSaaFpFNhqfHYA6nyNs5wfGX33DJ48r3a4lm64yBxRCvHUrpowkQIoDjeH7KwZQMcMMlj/+KYtKKifgWw9L2KMrD4x4tTgvWB/JC7GPkTNffyiZjpldLTwtTjVCHSmvoqXyRU0U0W6sn8TGTsdOmX380JWQ7Etxu5VXecMnjn3L5mrOJYybxmYm25/h3N4WUiunoNrhpGq45Wzj4Gr9xlqAPf9dVJSVi0oa64ZLHH53kRz1xYw4Tz1e6D8WkDHXDJY9XUlwP73NwDtZgOSSi2Zr12ZInG6H+MZWL3nDJ40d0Y2cxrUd4/qzPUz5ZQzVM8boj15iJdWvyYt2vkvqbXjHF68w29o3eMWGqiGbrQZe0lITJGmqq9aJiS0asq9oXerd8rGL1U7YWuizqZO8qiHVyV/Rqx9L+7VVTvOZRjWi2YsAdh6h4T4azRLN1mguSSsJkDRU+9EfGsjm2qL0tPIe1vc8RdfLoFFeBzs+2iWfLVt6JWHdY1z1KmWyjZTL83sVrzSiHNNQNlzz+dsbP6XRINpav0E/qf2FMeNOAVf0b6BPh/Ydz3aOUVS5eq040W2tcvN6MMeHg8A2XPF4O/B9wEVMt8qTk7Mf/lmvM7QuvSO+c8GQHKMDvw3L966Z0/WnitmXrExSzuHUCC4DN79q6blLZgcVXCg5CCBeKvCLF/AjvRhM/xZafAvlpis/irejah2WTManKmmi2IgPnLQCup5jFT1DM+b4fuJlilr8rKWavCw28n6b4b/+0bDImvUpVSCm54ZLHE8AeIDHZEw+KI5m/o4XV3S9yRtfjE35MAh3BajpClfZxya1XINf/Zvj7ty1bXw18CTgf2Eoxfd9JFPt7QhTzlxcophP6O7AXeA/FtIC7gWvetXXdHbctW/874LUu/LIC8CzFXOhD6833lsfYXZXg+xe/ZMITD4vhD/ugUQePuWXcqdEOXCSbjAk3jBo0VBdQ5eY3V+3v4vIXfsu8/KFLtf2hGhyhMTfX/jLk+qFWzm3L1v+V4k4Ih4ukOKTxvSO4xqT46qvP4MX5SiSZyQAVsskYd08abWB+k6tmAtBtmzn58aZNjaU+38m22CIYlv/8tmXr53NkZoJi6P7qEV7jkFiaUMVMUOy8vXiiNzXGTx18RBgFk8buFrQxmaDHRwIhWYDiviOD5Jg43fFUOOjeJG5gOJIKRZJhDDBhLivtxrvW9nLoHTknjZ4vMGfPPnJaeJJ2gt2RBlb2bykwLO/4u7au6wR+eIRyssC6I7zGpLj84Q1jtoH1KG2yyXhgojcHJ9itplipPfNIv80OBWmfW0vVjh72hOfSmNs74Wcl0Bqup8xMbiuz0q9Grt886iPvA/5FsVK+kWKu7NMpRq/BSnkWqKG4o9JOoIni1hFPA19419Z1z922bP15wF8Zv7VqM/GUktFyNwG3U5z3tYSBirkEwgWTmr40nRXxSVxqMt82jjkHlrYPVMgHPzA9tfOxDQIJ3Mshdmc95JzyGy553OQwFjNc9Ni9O8/oemzRwf6lMkAUTkWuf2yq1z/amEI25MkhpUSIWtlkdA3sS3gMxTrl7bLJOKL+O9FsGcAcoFs2GRnRbGkUtzV7A8XugyTw/sP5nskYaiETzyOfkBvvWis2JD4lA9JmeWr7mDstAQd+rsv107kR0YwxKh2kG9TLJmNyrZqjiEP2lN9419pdjKwsT5pjklu2dIUqWT//1eyOzhvabGQwU75XzDTAD1y81q7ZaCaY/FjeZOoYYzDk+hVru55Ivrb1LgK2SW8ggYVAFneTPKzhnKMV2WS8n4Nv0DMV3BzGmVEmWzfKMsHGOxMwdGMDcn0CYK5YJ5Drvd4MupJipf1IuGgqQx1HG5ONUPdN8bplY45430xw5HP0pWwy7nVFSYmY7A2Y0oqMG+9aO+2diUcpC4/wfDcr9SVhUoa68a61PUx+j2EVItFEPHkknZsT7eM7m5h0iL7xrrUfnuRHdxymFi9w8IQhCvSkT8e6vBOn4Zqzhb1HMK1k1lbEhzNVQx3qEfvJjXetPaw+Ky8gm4zJJGadaGB1pZtaSsVUDXWosb6nDleIh+g+RNFWBaznwH5VXUC1bDJ2z4C2aedw8kO9wMQdb/qNd61VIsH7wRh3U8rioG6PbDJcn3t2NDHlOtSNd609luJo/2i+55upiGwyKoCPMLyKIMRjXjcTHEGe8oHl6VdQnGf9wxvvWuv9JozPIfn/Mbt6IkMUP74AAAAASUVORK5CYII=" id="image394abddb1e" transform="scale(1 -1) translate(0 -578.16)" x="411.84" y="-43.2" width="106.56" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature28_fold0 -->
    <g transform="translate(156.305031 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-32" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-38" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.958531 638.149 
L 535.588031 638.149 
L 535.588031 27.789 
L 527.958531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image554dc30a24" transform="scale(1 -1) translate(0 -609.84)" x="527.76" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(539.088031 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(539.088031 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(573.488969 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p00ea4bcba3">
   <rect x="409.392031" y="27.789" width="111.592" height="610.36"/>
  </clipPath>
 </defs>
</svg>
