<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="756.422062pt" height="679.5765pt" viewBox="0 0 756.422062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T11:03:50.751125</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 756.422062 679.5765 
L 756.422062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 368.379062 638.149 
L 512.299062 638.149 
L 512.299062 27.789 
L 368.379062 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 434.066211 638.149 
L 434.066211 27.789 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 368.379062 609.084238 
L 512.299062 609.084238 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 368.379062 580.019476 
L 512.299062 580.019476 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 368.379062 550.954714 
L 512.299062 550.954714 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 368.379062 521.889952 
L 512.299062 521.889952 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 368.379062 492.82519 
L 512.299062 492.82519 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 368.379062 463.760429 
L 512.299062 463.760429 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 368.379062 434.695667 
L 512.299062 434.695667 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 368.379062 405.630905 
L 512.299062 405.630905 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 368.379062 376.566143 
L 512.299062 376.566143 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 368.379062 347.501381 
L 512.299062 347.501381 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 368.379062 318.436619 
L 512.299062 318.436619 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 368.379062 289.371857 
L 512.299062 289.371857 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 368.379062 260.307095 
L 512.299062 260.307095 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 368.379062 231.242333 
L 512.299062 231.242333 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 368.379062 202.177571 
L 512.299062 202.177571 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 368.379062 173.11281 
L 512.299062 173.11281 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 368.379062 144.048048 
L 512.299062 144.048048 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 368.379062 114.983286 
L 512.299062 114.983286 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 368.379062 85.918524 
L 512.299062 85.918524 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 368.379062 56.853762 
L 512.299062 56.853762 
" clip-path="url(#pde5cd31e63)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mbdda6cdc2b" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mbdda6cdc2b" x="395.013869" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(386.905666 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mbdda6cdc2b" x="434.066211" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(430.566836 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mbdda6cdc2b" x="473.118553" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(469.619178 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_25">
      <g>
       <use xlink:href="#mbdda6cdc2b" x="512.170895" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(508.67152 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_5">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(317.657656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_6">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(153.088437 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_7">
      <!-- XRDPowderPattern_xrd_31 -->
      <g style="fill: #333333" transform="translate(172.0075 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-31" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_8">
      <!-- CrystalNNFingerprint_mean_square_co-planar_CN_4 -->
      <g style="fill: #333333" transform="translate(7.2 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-73" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-71" x="1478.296875"/>
       <use xlink:href="#DejaVuSans-75" x="1541.773438"/>
       <use xlink:href="#DejaVuSans-61" x="1605.152344"/>
       <use xlink:href="#DejaVuSans-72" x="1666.431641"/>
       <use xlink:href="#DejaVuSans-65" x="1705.294922"/>
       <use xlink:href="#DejaVuSans-5f" x="1766.818359"/>
       <use xlink:href="#DejaVuSans-63" x="1816.818359"/>
       <use xlink:href="#DejaVuSans-6f" x="1871.798828"/>
       <use xlink:href="#DejaVuSans-2d" x="1934.855469"/>
       <use xlink:href="#DejaVuSans-70" x="1970.939453"/>
       <use xlink:href="#DejaVuSans-6c" x="2034.416016"/>
       <use xlink:href="#DejaVuSans-61" x="2062.199219"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2186.857422"/>
       <use xlink:href="#DejaVuSans-72" x="2248.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="2289.25"/>
       <use xlink:href="#DejaVuSans-43" x="2339.25"/>
       <use xlink:href="#DejaVuSans-4e" x="2409.074219"/>
       <use xlink:href="#DejaVuSans-5f" x="2483.878906"/>
       <use xlink:href="#DejaVuSans-34" x="2533.878906"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(25.836719 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(26.740625 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(147.287187 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_12">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(32.639375 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_13">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(141.179219 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(155.558437 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(32.519531 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_16">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(78.062187 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(93.863281 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(32.639375 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(147.287187 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_20">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(155.558437 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_21">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(93.863281 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_22">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(46.50875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_23">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(225.185625 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_24">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(103.670156 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_25">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(62.086406 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 368.379062 638.149 
L 512.299062 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image59de08b3f1" transform="scale(1 -1) translate(0 -578.16)" x="372.24" y="-43.2" width="136.08" height="578.16"/>
   <g id="text_26">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature67_fold0 -->
    <g transform="translate(131.456062 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-37" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 521.294062 638.149 
L 528.923563 638.149 
L 528.923563 27.789 
L 521.294062 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image72980134a9" transform="scale(1 -1) translate(0 -609.84)" x="521.28" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- Low -->
      <g transform="translate(532.423563 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_27"/>
     <g id="text_28">
      <!-- High -->
      <g transform="translate(532.423563 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_29">
     <!-- Feature value -->
     <g transform="translate(566.8245 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pde5cd31e63">
   <rect x="368.379062" y="27.789" width="143.92" height="610.36"/>
  </clipPath>
 </defs>
</svg>
