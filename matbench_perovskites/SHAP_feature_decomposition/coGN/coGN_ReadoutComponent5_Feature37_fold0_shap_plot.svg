<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="774.453344pt" height="679.5765pt" viewBox="0 0 774.453344 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T07:56:10.304116</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 774.453344 679.5765 
L 774.453344 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 398.362344 638.149 
L 518.378344 638.149 
L 518.378344 27.789 
L 398.362344 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 447.884147 638.149 
L 447.884147 27.789 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 398.362344 609.084238 
L 518.378344 609.084238 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 398.362344 580.019476 
L 518.378344 580.019476 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 398.362344 550.954714 
L 518.378344 550.954714 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 398.362344 521.889952 
L 518.378344 521.889952 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 398.362344 492.82519 
L 518.378344 492.82519 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 398.362344 463.760429 
L 518.378344 463.760429 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 398.362344 434.695667 
L 518.378344 434.695667 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 398.362344 405.630905 
L 518.378344 405.630905 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 398.362344 376.566143 
L 518.378344 376.566143 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 398.362344 347.501381 
L 518.378344 347.501381 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 398.362344 318.436619 
L 518.378344 318.436619 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 398.362344 289.371857 
L 518.378344 289.371857 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 398.362344 260.307095 
L 518.378344 260.307095 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 398.362344 231.242333 
L 518.378344 231.242333 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 398.362344 202.177571 
L 518.378344 202.177571 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 398.362344 173.11281 
L 518.378344 173.11281 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 398.362344 144.048048 
L 518.378344 144.048048 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 398.362344 114.983286 
L 518.378344 114.983286 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 398.362344 85.918524 
L 518.378344 85.918524 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 398.362344 56.853762 
L 518.378344 56.853762 
" clip-path="url(#p086fcabd81)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m81c9b825ce" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m81c9b825ce" x="447.884147" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(444.384772 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m81c9b825ce" x="499.663816" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(496.164441 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(335.688937 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(7.2 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(155.619375 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_31 -->
      <g style="fill: #333333" transform="translate(185.541719 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(254.392969 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(123.846562 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_mean_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(90.192812 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-4c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1480.160156"/>
       <use xlink:href="#DejaVuSans-73" x="1516.244141"/>
       <use xlink:href="#DejaVuSans-68" x="1568.34375"/>
       <use xlink:href="#DejaVuSans-61" x="1631.722656"/>
       <use xlink:href="#DejaVuSans-70" x="1693.001953"/>
       <use xlink:href="#DejaVuSans-65" x="1756.478516"/>
       <use xlink:href="#DejaVuSans-64" x="1818.001953"/>
       <use xlink:href="#DejaVuSans-5f" x="1881.478516"/>
       <use xlink:href="#DejaVuSans-43" x="1931.478516"/>
       <use xlink:href="#DejaVuSans-4e" x="2001.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="2076.107422"/>
       <use xlink:href="#DejaVuSans-32" x="2126.107422"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- MaximumPackingEfficiency_max_packing_efficiency -->
      <g style="fill: #333333" transform="translate(36.565781 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-61" x="86.279297"/>
       <use xlink:href="#DejaVuSans-78" x="147.558594"/>
       <use xlink:href="#DejaVuSans-69" x="206.738281"/>
       <use xlink:href="#DejaVuSans-6d" x="234.521484"/>
       <use xlink:href="#DejaVuSans-75" x="331.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="395.3125"/>
       <use xlink:href="#DejaVuSans-50" x="492.724609"/>
       <use xlink:href="#DejaVuSans-61" x="548.527344"/>
       <use xlink:href="#DejaVuSans-63" x="609.806641"/>
       <use xlink:href="#DejaVuSans-6b" x="664.787109"/>
       <use xlink:href="#DejaVuSans-69" x="722.697266"/>
       <use xlink:href="#DejaVuSans-6e" x="750.480469"/>
       <use xlink:href="#DejaVuSans-67" x="813.859375"/>
       <use xlink:href="#DejaVuSans-45" x="877.335938"/>
       <use xlink:href="#DejaVuSans-66" x="940.519531"/>
       <use xlink:href="#DejaVuSans-66" x="975.724609"/>
       <use xlink:href="#DejaVuSans-69" x="1010.929688"/>
       <use xlink:href="#DejaVuSans-63" x="1038.712891"/>
       <use xlink:href="#DejaVuSans-69" x="1093.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1121.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="1183"/>
       <use xlink:href="#DejaVuSans-63" x="1246.378906"/>
       <use xlink:href="#DejaVuSans-79" x="1301.359375"/>
       <use xlink:href="#DejaVuSans-5f" x="1360.539062"/>
       <use xlink:href="#DejaVuSans-6d" x="1410.539062"/>
       <use xlink:href="#DejaVuSans-61" x="1507.951172"/>
       <use xlink:href="#DejaVuSans-78" x="1569.230469"/>
       <use xlink:href="#DejaVuSans-5f" x="1628.410156"/>
       <use xlink:href="#DejaVuSans-70" x="1678.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1741.886719"/>
       <use xlink:href="#DejaVuSans-63" x="1803.166016"/>
       <use xlink:href="#DejaVuSans-6b" x="1858.146484"/>
       <use xlink:href="#DejaVuSans-69" x="1916.056641"/>
       <use xlink:href="#DejaVuSans-6e" x="1943.839844"/>
       <use xlink:href="#DejaVuSans-67" x="2007.21875"/>
       <use xlink:href="#DejaVuSans-5f" x="2070.695312"/>
       <use xlink:href="#DejaVuSans-65" x="2120.695312"/>
       <use xlink:href="#DejaVuSans-66" x="2182.21875"/>
       <use xlink:href="#DejaVuSans-66" x="2217.423828"/>
       <use xlink:href="#DejaVuSans-69" x="2252.628906"/>
       <use xlink:href="#DejaVuSans-63" x="2280.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2335.392578"/>
       <use xlink:href="#DejaVuSans-65" x="2363.175781"/>
       <use xlink:href="#DejaVuSans-6e" x="2424.699219"/>
       <use xlink:href="#DejaVuSans-63" x="2488.078125"/>
       <use xlink:href="#DejaVuSans-79" x="2543.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(108.045469 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(185.541719 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(46.730156 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_51 -->
      <g style="fill: #333333" transform="translate(185.541719 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(74.428281 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(62.622656 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(177.270469 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- VoronoiFingerprint_std_dev_Voro_area_mean -->
      <g style="fill: #333333" transform="translate(84.422031 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-65" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-61" x="2109.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="2170.744141"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_88 -->
      <g style="fill: #333333" transform="translate(185.541719 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(76.492031 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(255.168906 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(133.653437 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(92.069687 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 398.362344 638.149 
L 518.378344 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image7c133331e7" transform="scale(1 -1) translate(0 -578.16)" x="401.76" y="-43.2" width="113.76" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature37_fold0 -->
    <g transform="translate(149.487344 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-37" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 525.879344 638.149 
L 533.508844 638.149 
L 533.508844 27.789 
L 525.879344 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagedbabf501f0" transform="scale(1 -1) translate(0 -609.84)" x="525.6" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(537.008844 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(537.008844 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(571.409781 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p086fcabd81">
   <rect x="398.362344" y="27.789" width="120.016" height="610.36"/>
  </clipPath>
 </defs>
</svg>
