<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="785.611781pt" height="679.5765pt" viewBox="0 0 785.611781 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T20:11:37.735695</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 785.611781 679.5765 
L 785.611781 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 406.580781 638.149 
L 520.260781 638.149 
L 520.260781 27.789 
L 406.580781 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 483.846841 638.149 
L 483.846841 27.789 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 406.580781 609.084238 
L 520.260781 609.084238 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 406.580781 580.019476 
L 520.260781 580.019476 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 406.580781 550.954714 
L 520.260781 550.954714 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 406.580781 521.889952 
L 520.260781 521.889952 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 406.580781 492.82519 
L 520.260781 492.82519 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 406.580781 463.760429 
L 520.260781 463.760429 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 406.580781 434.695667 
L 520.260781 434.695667 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 406.580781 405.630905 
L 520.260781 405.630905 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 406.580781 376.566143 
L 520.260781 376.566143 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 406.580781 347.501381 
L 520.260781 347.501381 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 406.580781 318.436619 
L 520.260781 318.436619 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 406.580781 289.371857 
L 520.260781 289.371857 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 406.580781 260.307095 
L 520.260781 260.307095 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 406.580781 231.242333 
L 520.260781 231.242333 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 406.580781 202.177571 
L 520.260781 202.177571 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 406.580781 173.11281 
L 520.260781 173.11281 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 406.580781 144.048048 
L 520.260781 144.048048 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 406.580781 114.983286 
L 520.260781 114.983286 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 406.580781 85.918524 
L 520.260781 85.918524 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 406.580781 56.853762 
L 520.260781 56.853762 
" clip-path="url(#p13c6ddfafc)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m3840a4cde5" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m3840a4cde5" x="436.864889" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(428.756686 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m3840a4cde5" x="483.846841" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(480.347466 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(340.739375 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_9 -->
      <g style="fill: #333333" transform="translate(202.031406 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- BondOrientationParameter_std_dev_BOOP_Q_l=7 -->
      <g style="fill: #333333" transform="translate(62.116875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-51" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 3406 84 
L 4238 -825 
L 3475 -825 
L 2784 -78 
Q 2681 -84 2626 -87 
Q 2572 -91 2522 -91 
Q 1538 -91 948 567 
Q 359 1225 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1516 4351 937 
Q 4025 359 3406 84 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-4f" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="335.351562"/>
       <use xlink:href="#DejaVuSans-69" x="376.464844"/>
       <use xlink:href="#DejaVuSans-65" x="404.248047"/>
       <use xlink:href="#DejaVuSans-6e" x="465.771484"/>
       <use xlink:href="#DejaVuSans-74" x="529.150391"/>
       <use xlink:href="#DejaVuSans-61" x="568.359375"/>
       <use xlink:href="#DejaVuSans-74" x="629.638672"/>
       <use xlink:href="#DejaVuSans-69" x="668.847656"/>
       <use xlink:href="#DejaVuSans-6f" x="696.630859"/>
       <use xlink:href="#DejaVuSans-6e" x="757.8125"/>
       <use xlink:href="#DejaVuSans-50" x="821.191406"/>
       <use xlink:href="#DejaVuSans-61" x="876.994141"/>
       <use xlink:href="#DejaVuSans-72" x="938.273438"/>
       <use xlink:href="#DejaVuSans-61" x="979.386719"/>
       <use xlink:href="#DejaVuSans-6d" x="1040.666016"/>
       <use xlink:href="#DejaVuSans-65" x="1138.078125"/>
       <use xlink:href="#DejaVuSans-74" x="1199.601562"/>
       <use xlink:href="#DejaVuSans-65" x="1238.810547"/>
       <use xlink:href="#DejaVuSans-72" x="1300.333984"/>
       <use xlink:href="#DejaVuSans-5f" x="1341.447266"/>
       <use xlink:href="#DejaVuSans-73" x="1391.447266"/>
       <use xlink:href="#DejaVuSans-74" x="1443.546875"/>
       <use xlink:href="#DejaVuSans-64" x="1482.755859"/>
       <use xlink:href="#DejaVuSans-5f" x="1546.232422"/>
       <use xlink:href="#DejaVuSans-64" x="1596.232422"/>
       <use xlink:href="#DejaVuSans-65" x="1659.708984"/>
       <use xlink:href="#DejaVuSans-76" x="1721.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="1780.412109"/>
       <use xlink:href="#DejaVuSans-42" x="1830.412109"/>
       <use xlink:href="#DejaVuSans-4f" x="1897.265625"/>
       <use xlink:href="#DejaVuSans-4f" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-50" x="2054.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="2114.990234"/>
       <use xlink:href="#DejaVuSans-51" x="2164.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2243.701172"/>
       <use xlink:href="#DejaVuSans-6c" x="2293.701172"/>
       <use xlink:href="#DejaVuSans-3d" x="2321.484375"/>
       <use xlink:href="#DejaVuSans-37" x="2405.273438"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(70.841094 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_wt_CN_5 -->
      <g style="fill: #333333" transform="translate(141.871875 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-35" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_79 -->
      <g style="fill: #333333" transform="translate(193.760156 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(132.054844 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_range_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(7.2 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-53" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-70" x="1943.949219"/>
       <use xlink:href="#DejaVuSans-61" x="2007.425781"/>
       <use xlink:href="#DejaVuSans-63" x="2068.705078"/>
       <use xlink:href="#DejaVuSans-65" x="2123.685547"/>
       <use xlink:href="#DejaVuSans-47" x="2185.208984"/>
       <use xlink:href="#DejaVuSans-72" x="2262.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="2301.5625"/>
       <use xlink:href="#DejaVuSans-75" x="2362.744141"/>
       <use xlink:href="#DejaVuSans-70" x="2426.123047"/>
       <use xlink:href="#DejaVuSans-4e" x="2489.599609"/>
       <use xlink:href="#DejaVuSans-75" x="2564.404297"/>
       <use xlink:href="#DejaVuSans-6d" x="2627.783203"/>
       <use xlink:href="#DejaVuSans-62" x="2725.195312"/>
       <use xlink:href="#DejaVuSans-65" x="2788.671875"/>
       <use xlink:href="#DejaVuSans-72" x="2850.195312"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mode_MeltingT -->
      <g style="fill: #333333" transform="translate(83.579063 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(80.40625 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(70.841094 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_avg_dev_Electronegativity -->
      <g style="fill: #333333" transform="translate(11.7175 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-45" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6c" x="2071"/>
       <use xlink:href="#DejaVuSans-65" x="2098.783203"/>
       <use xlink:href="#DejaVuSans-63" x="2160.306641"/>
       <use xlink:href="#DejaVuSans-74" x="2215.287109"/>
       <use xlink:href="#DejaVuSans-72" x="2254.496094"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.359375"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.541016"/>
       <use xlink:href="#DejaVuSans-65" x="2417.919922"/>
       <use xlink:href="#DejaVuSans-67" x="2479.443359"/>
       <use xlink:href="#DejaVuSans-61" x="2542.919922"/>
       <use xlink:href="#DejaVuSans-74" x="2604.199219"/>
       <use xlink:href="#DejaVuSans-69" x="2643.408203"/>
       <use xlink:href="#DejaVuSans-76" x="2671.191406"/>
       <use xlink:href="#DejaVuSans-69" x="2730.371094"/>
       <use xlink:href="#DejaVuSans-74" x="2758.154297"/>
       <use xlink:href="#DejaVuSans-79" x="2797.363281"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(132.065 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(132.065 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CrystalNNFingerprint_std_dev_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(74.320625 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6f" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-63" x="1592.75"/>
       <use xlink:href="#DejaVuSans-74" x="1647.730469"/>
       <use xlink:href="#DejaVuSans-61" x="1686.939453"/>
       <use xlink:href="#DejaVuSans-68" x="1748.21875"/>
       <use xlink:href="#DejaVuSans-65" x="1811.597656"/>
       <use xlink:href="#DejaVuSans-64" x="1873.121094"/>
       <use xlink:href="#DejaVuSans-72" x="1936.597656"/>
       <use xlink:href="#DejaVuSans-61" x="1977.710938"/>
       <use xlink:href="#DejaVuSans-6c" x="2038.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2066.773438"/>
       <use xlink:href="#DejaVuSans-43" x="2116.773438"/>
       <use xlink:href="#DejaVuSans-4e" x="2186.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2261.402344"/>
       <use xlink:href="#DejaVuSans-36" x="2311.402344"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(43.520781 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(55.584375 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(116.263906 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_2 -->
      <g style="fill: #333333" transform="translate(34.441094 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-32" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(261.13875 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(74.314531 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 406.580781 638.149 
L 520.260781 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image8b7fb4b096" transform="scale(1 -1) translate(0 -578.16)" x="409.68" y="-43.2" width="108" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature108_fold0 -->
    <g transform="translate(148.429781 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-38" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.365781 638.149 
L 534.995281 638.149 
L 534.995281 27.789 
L 527.365781 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imageb30b704f85" transform="scale(1 -1) translate(0 -609.84)" x="527.04" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(538.495281 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(538.495281 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(572.896219 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p13c6ddfafc">
   <rect x="406.580781" y="27.789" width="113.68" height="610.36"/>
  </clipPath>
 </defs>
</svg>
