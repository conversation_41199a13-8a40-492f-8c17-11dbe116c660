<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="772.495187pt" height="679.5765pt" viewBox="0 0 772.495187 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T02:25:16.237882</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 772.495187 679.5765 
L 772.495187 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 384.572187 638.149 
L 516.036187 638.149 
L 516.036187 27.789 
L 384.572187 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 442.91701 638.149 
L 442.91701 27.789 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 384.572187 609.084238 
L 516.036187 609.084238 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 384.572187 580.019476 
L 516.036187 580.019476 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 384.572187 550.954714 
L 516.036187 550.954714 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 384.572187 521.889952 
L 516.036187 521.889952 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 384.572187 492.82519 
L 516.036187 492.82519 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 384.572187 463.760429 
L 516.036187 463.760429 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 384.572187 434.695667 
L 516.036187 434.695667 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 384.572187 405.630905 
L 516.036187 405.630905 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 384.572187 376.566143 
L 516.036187 376.566143 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 384.572187 347.501381 
L 516.036187 347.501381 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 384.572187 318.436619 
L 516.036187 318.436619 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 384.572187 289.371857 
L 516.036187 289.371857 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 384.572187 260.307095 
L 516.036187 260.307095 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 384.572187 231.242333 
L 516.036187 231.242333 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 384.572187 202.177571 
L 516.036187 202.177571 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 384.572187 173.11281 
L 516.036187 173.11281 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 384.572187 144.048048 
L 516.036187 144.048048 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 384.572187 114.983286 
L 516.036187 114.983286 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 384.572187 85.918524 
L 516.036187 85.918524 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 384.572187 56.853762 
L 516.036187 56.853762 
" clip-path="url(#pce65d557fd)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m4449800f36" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m4449800f36" x="398.403952" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(390.295749 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m4449800f36" x="442.91701" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(439.417635 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m4449800f36" x="487.430068" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(483.930693 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(327.622781 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(24.536719 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(48.8325 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- XRDPowderPattern_xrd_38 -->
      <g style="fill: #333333" transform="translate(188.200625 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-38" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(33.575781 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_51 -->
      <g style="fill: #333333" transform="translate(171.751562 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(171.751562 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_149 -->
      <g style="fill: #333333" transform="translate(163.480312 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-39" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(64.761562 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(171.751562 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(106.1625 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(48.712656 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_range_CovalentRadius -->
      <g style="fill: #333333" transform="translate(15.383906 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-43" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6f" x="1950.296875"/>
       <use xlink:href="#DejaVuSans-76" x="2011.478516"/>
       <use xlink:href="#DejaVuSans-61" x="2070.658203"/>
       <use xlink:href="#DejaVuSans-6c" x="2131.9375"/>
       <use xlink:href="#DejaVuSans-65" x="2159.720703"/>
       <use xlink:href="#DejaVuSans-6e" x="2221.244141"/>
       <use xlink:href="#DejaVuSans-74" x="2284.623047"/>
       <use xlink:href="#DejaVuSans-52" x="2323.832031"/>
       <use xlink:href="#DejaVuSans-61" x="2391.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2452.34375"/>
       <use xlink:href="#DejaVuSans-69" x="2515.820312"/>
       <use xlink:href="#DejaVuSans-75" x="2543.603516"/>
       <use xlink:href="#DejaVuSans-73" x="2606.982422"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- AverageBondLength_std_dev_Average_bond_length -->
      <g style="fill: #333333" transform="translate(25.389844 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-73" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-74" x="1114.298828"/>
       <use xlink:href="#DejaVuSans-64" x="1153.507812"/>
       <use xlink:href="#DejaVuSans-5f" x="1216.984375"/>
       <use xlink:href="#DejaVuSans-64" x="1266.984375"/>
       <use xlink:href="#DejaVuSans-65" x="1330.460938"/>
       <use xlink:href="#DejaVuSans-76" x="1391.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1451.164062"/>
       <use xlink:href="#DejaVuSans-41" x="1501.164062"/>
       <use xlink:href="#DejaVuSans-76" x="1563.697266"/>
       <use xlink:href="#DejaVuSans-65" x="1622.876953"/>
       <use xlink:href="#DejaVuSans-72" x="1684.400391"/>
       <use xlink:href="#DejaVuSans-61" x="1725.513672"/>
       <use xlink:href="#DejaVuSans-67" x="1786.792969"/>
       <use xlink:href="#DejaVuSans-65" x="1850.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1911.792969"/>
       <use xlink:href="#DejaVuSans-62" x="1961.792969"/>
       <use xlink:href="#DejaVuSans-6f" x="2025.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="2086.451172"/>
       <use xlink:href="#DejaVuSans-64" x="2149.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="2213.306641"/>
       <use xlink:href="#DejaVuSans-6c" x="2263.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2291.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2352.613281"/>
       <use xlink:href="#DejaVuSans-67" x="2415.992188"/>
       <use xlink:href="#DejaVuSans-74" x="2479.46875"/>
       <use xlink:href="#DejaVuSans-68" x="2518.677734"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(48.8325 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_mode_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(62.701875 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(238.622344 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(241.37875 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(119.863281 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(78.279531 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 384.572187 638.149 
L 516.036187 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagee5ad715fd9" transform="scale(1 -1) translate(0 -578.16)" x="388.08" y="-43.2" width="124.56" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature121_fold0 -->
    <g transform="translate(135.313188 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-31" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 524.252687 638.149 
L 531.882187 638.149 
L 531.882187 27.789 
L 524.252687 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagecbe1ce75a3" transform="scale(1 -1) translate(0 -609.84)" x="524.16" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(535.382187 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(535.382187 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(569.783125 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pce65d557fd">
   <rect x="384.572187" y="27.789" width="131.464" height="610.36"/>
  </clipPath>
 </defs>
</svg>
