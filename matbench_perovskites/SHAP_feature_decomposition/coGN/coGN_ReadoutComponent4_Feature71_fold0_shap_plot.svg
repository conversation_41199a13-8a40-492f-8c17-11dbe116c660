<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T07:35:57.951981</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 493.991381 638.149 
L 493.991381 27.789 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#pbe9fc097bf)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mcad0373587" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mcad0373587" x="443.66473" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −5 -->
      <g style="fill: #333333" transform="translate(435.556527 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-35" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mcad0373587" x="493.991381" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(490.492006 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- MEGNet_OFMEncoded_v1_108 -->
      <g style="fill: #333333" transform="translate(201.196562 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-38" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_65 -->
      <g style="fill: #333333" transform="translate(209.467812 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CrystalNNFingerprint_mean_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(103.729062 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6f" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-63" x="1487.378906"/>
       <use xlink:href="#DejaVuSans-74" x="1542.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.568359"/>
       <use xlink:href="#DejaVuSans-68" x="1642.847656"/>
       <use xlink:href="#DejaVuSans-65" x="1706.226562"/>
       <use xlink:href="#DejaVuSans-64" x="1767.75"/>
       <use xlink:href="#DejaVuSans-72" x="1831.226562"/>
       <use xlink:href="#DejaVuSans-61" x="1872.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1933.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1961.402344"/>
       <use xlink:href="#DejaVuSans-43" x="2011.402344"/>
       <use xlink:href="#DejaVuSans-4e" x="2081.226562"/>
       <use xlink:href="#DejaVuSans-5f" x="2156.03125"/>
       <use xlink:href="#DejaVuSans-36" x="2206.03125"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- MEGNet_OFMEncoded_v1_27 -->
      <g style="fill: #333333" transform="translate(209.467812 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(86.54875 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(122.835 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_mode_MeltingT -->
      <g style="fill: #333333" transform="translate(99.286719 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(147.772656 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- VoronoiFingerprint_mean_Voro_area_minimum -->
      <g style="fill: #333333" transform="translate(97.227031 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-72" x="1633.492188"/>
       <use xlink:href="#DejaVuSans-65" x="1672.355469"/>
       <use xlink:href="#DejaVuSans-61" x="1733.878906"/>
       <use xlink:href="#DejaVuSans-5f" x="1795.158203"/>
       <use xlink:href="#DejaVuSans-6d" x="1845.158203"/>
       <use xlink:href="#DejaVuSans-69" x="1942.570312"/>
       <use xlink:href="#DejaVuSans-6e" x="1970.353516"/>
       <use xlink:href="#DejaVuSans-69" x="2033.732422"/>
       <use xlink:href="#DejaVuSans-6d" x="2061.515625"/>
       <use xlink:href="#DejaVuSans-75" x="2158.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2222.306641"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_mode_NUnfilled -->
      <g style="fill: #333333" transform="translate(96.111875 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(157.579531 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(209.467812 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(86.54875 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(20.096406 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(209.467812 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(276.846406 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(90.022187 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagedf0ffe1c6b" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature71_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-37" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image5ab3976de3" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pbe9fc097bf">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
