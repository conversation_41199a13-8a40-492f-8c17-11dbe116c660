<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.657906pt" height="679.5765pt" viewBox="0 0 794.657906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T16:11:05.363723</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.657906 679.5765 
L 794.657906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
L 525.730906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 468.272201 638.149 
L 468.272201 27.789 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.730906 609.084238 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.730906 580.019476 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.730906 550.954714 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.730906 521.889952 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.730906 492.82519 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.730906 463.760429 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.730906 434.695667 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.730906 405.630905 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.730906 376.566143 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.730906 347.501381 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.730906 318.436619 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.730906 289.371857 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.730906 260.307095 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.730906 231.242333 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.730906 202.177571 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.730906 173.11281 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.730906 144.048048 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.730906 114.983286 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.730906 85.918524 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.730906 56.853762 
" clip-path="url(#p8d3c55d23a)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="ma421e7f25a" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#ma421e7f25a" x="468.272201" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(459.525482 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#ma421e7f25a" x="522.302466" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(513.555747 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8935 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(95.679219 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=6_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(21.74375 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-36" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_48 -->
      <g style="fill: #333333" transform="translate(218.598281 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_maximum_CovalentRadius -->
      <g style="fill: #333333" transform="translate(34.5325 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-43" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-6f" x="2163.382812"/>
       <use xlink:href="#DejaVuSans-76" x="2224.564453"/>
       <use xlink:href="#DejaVuSans-61" x="2283.744141"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.023438"/>
       <use xlink:href="#DejaVuSans-65" x="2372.806641"/>
       <use xlink:href="#DejaVuSans-6e" x="2434.330078"/>
       <use xlink:href="#DejaVuSans-74" x="2497.708984"/>
       <use xlink:href="#DejaVuSans-52" x="2536.917969"/>
       <use xlink:href="#DejaVuSans-61" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-64" x="2665.429688"/>
       <use xlink:href="#DejaVuSans-69" x="2728.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2756.689453"/>
       <use xlink:href="#DejaVuSans-73" x="2820.068359"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(204.219063 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(79.786719 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- GaussianSymmFunc_std_dev_G2_4_0 -->
      <g style="fill: #333333" transform="translate(165.164219 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-34" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- GaussianSymmFunc_std_dev_G4_0_005_4_0_1_0 -->
      <g style="fill: #333333" transform="translate(89.536719 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-34" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-5f" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1803.728516"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
       <use xlink:href="#DejaVuSans-35" x="1930.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="1994.597656"/>
       <use xlink:href="#DejaVuSans-34" x="2044.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2108.220703"/>
       <use xlink:href="#DejaVuSans-30" x="2158.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2221.84375"/>
       <use xlink:href="#DejaVuSans-31" x="2271.84375"/>
       <use xlink:href="#DejaVuSans-5f" x="2335.466797"/>
       <use xlink:href="#DejaVuSans-30" x="2385.466797"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_116 -->
      <g style="fill: #333333" transform="translate(210.327031 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-36" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- AtomicOrbitals_HOMO_energy -->
      <g style="fill: #333333" transform="translate(212.130781 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1282.478516"/>
       <use xlink:href="#DejaVuSans-72" x="1344.001953"/>
       <use xlink:href="#DejaVuSans-67" x="1383.365234"/>
       <use xlink:href="#DejaVuSans-79" x="1446.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_avg_dev_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(57.686719 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-76" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-6f" x="2207.962891"/>
       <use xlink:href="#DejaVuSans-6c" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-75" x="2296.927734"/>
       <use xlink:href="#DejaVuSans-6d" x="2360.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2457.71875"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.242188"/>
       <use xlink:href="#DejaVuSans-70" x="2569.242188"/>
       <use xlink:href="#DejaVuSans-61" x="2632.71875"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(16.330469 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_mean_Electronegativity -->
      <g style="fill: #333333" transform="translate(54.04875 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(95.679219 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(68.358906 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- XRDPowderPattern_xrd_25 -->
      <g style="fill: #333333" transform="translate(235.047344 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-35" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(216.128281 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(88.876563 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(288.225469 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagecd84e7912e" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature52_fold0 -->
    <g transform="translate(169.691906 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-35" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.625406 638.149 
L 539.254906 638.149 
L 539.254906 27.789 
L 531.625406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image215b9a56b8" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.754906 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.754906 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.155844 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p8d3c55d23a">
   <rect x="431.418906" y="27.789" width="94.312" height="610.36"/>
  </clipPath>
 </defs>
</svg>
