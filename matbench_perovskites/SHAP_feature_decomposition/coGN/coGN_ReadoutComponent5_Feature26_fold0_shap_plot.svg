<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="757.727pt" height="679.5765pt" viewBox="0 0 757.727 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T05:56:58.055732</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 757.727 679.5765 
L 757.727 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 370.26 638.149 
L 513.028 638.149 
L 513.028 27.789 
L 370.26 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 432.065514 638.149 
L 432.065514 27.789 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 370.26 609.084238 
L 513.028 609.084238 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 370.26 580.019476 
L 513.028 580.019476 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 370.26 550.954714 
L 513.028 550.954714 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 370.26 521.889952 
L 513.028 521.889952 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 370.26 492.82519 
L 513.028 492.82519 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 370.26 463.760429 
L 513.028 463.760429 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 370.26 434.695667 
L 513.028 434.695667 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 370.26 405.630905 
L 513.028 405.630905 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 370.26 376.566143 
L 513.028 376.566143 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 370.26 347.501381 
L 513.028 347.501381 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 370.26 318.436619 
L 513.028 318.436619 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 370.26 289.371857 
L 513.028 289.371857 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 370.26 260.307095 
L 513.028 260.307095 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 370.26 231.242333 
L 513.028 231.242333 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 370.26 202.177571 
L 513.028 202.177571 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 370.26 173.11281 
L 513.028 173.11281 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 370.26 144.048048 
L 513.028 144.048048 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 370.26 114.983286 
L 513.028 114.983286 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 370.26 85.918524 
L 513.028 85.918524 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 370.26 56.853762 
L 513.028 56.853762 
" clip-path="url(#p83da2b1ee7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m2dca029bef" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2dca029bef" x="373.833752" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(365.725549 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m2dca029bef" x="432.065514" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(428.566139 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m2dca029bef" x="490.297276" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(486.797901 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(318.962594 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_9 -->
      <g style="fill: #333333" transform="translate(165.710625 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- VoronoiFingerprint_mean_Voro_dist_maximum -->
      <g style="fill: #333333" transform="translate(46.63875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-61" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-78" x="1963.472656"/>
       <use xlink:href="#DejaVuSans-69" x="2022.652344"/>
       <use xlink:href="#DejaVuSans-6d" x="2050.435547"/>
       <use xlink:href="#DejaVuSans-75" x="2147.847656"/>
       <use xlink:href="#DejaVuSans-6d" x="2211.226562"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- XRDPowderPattern_xrd_38 -->
      <g style="fill: #333333" transform="translate(173.888437 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-38" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- OPSiteFingerprint_std_dev_q2_CN_10 -->
      <g style="fill: #333333" transform="translate(104.765 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-71" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-32" x="1425.953125"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.576172"/>
       <use xlink:href="#DejaVuSans-43" x="1539.576172"/>
       <use xlink:href="#DejaVuSans-4e" x="1609.400391"/>
       <use xlink:href="#DejaVuSans-5f" x="1684.205078"/>
       <use xlink:href="#DejaVuSans-31" x="1734.205078"/>
       <use xlink:href="#DejaVuSans-30" x="1797.828125"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_maximum_NUnfilled -->
      <g style="fill: #333333" transform="translate(15.452969 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-55" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.556641"/>
       <use xlink:href="#DejaVuSans-66" x="2304.935547"/>
       <use xlink:href="#DejaVuSans-69" x="2340.140625"/>
       <use xlink:href="#DejaVuSans-6c" x="2367.923828"/>
       <use xlink:href="#DejaVuSans-6c" x="2395.707031"/>
       <use xlink:href="#DejaVuSans-65" x="2423.490234"/>
       <use xlink:href="#DejaVuSans-64" x="2485.013672"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- BondFractions_O_-_S_bond_frac_ -->
      <g style="fill: #333333" transform="translate(134.817344 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-46" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="306.910156"/>
       <use xlink:href="#DejaVuSans-61" x="348.023438"/>
       <use xlink:href="#DejaVuSans-63" x="409.302734"/>
       <use xlink:href="#DejaVuSans-74" x="464.283203"/>
       <use xlink:href="#DejaVuSans-69" x="503.492188"/>
       <use xlink:href="#DejaVuSans-6f" x="531.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="592.457031"/>
       <use xlink:href="#DejaVuSans-73" x="655.835938"/>
       <use xlink:href="#DejaVuSans-5f" x="707.935547"/>
       <use xlink:href="#DejaVuSans-4f" x="757.935547"/>
       <use xlink:href="#DejaVuSans-5f" x="836.646484"/>
       <use xlink:href="#DejaVuSans-2d" x="886.646484"/>
       <use xlink:href="#DejaVuSans-5f" x="922.730469"/>
       <use xlink:href="#DejaVuSans-53" x="972.730469"/>
       <use xlink:href="#DejaVuSans-5f" x="1036.207031"/>
       <use xlink:href="#DejaVuSans-62" x="1086.207031"/>
       <use xlink:href="#DejaVuSans-6f" x="1149.683594"/>
       <use xlink:href="#DejaVuSans-6e" x="1210.865234"/>
       <use xlink:href="#DejaVuSans-64" x="1274.244141"/>
       <use xlink:href="#DejaVuSans-5f" x="1337.720703"/>
       <use xlink:href="#DejaVuSans-66" x="1387.720703"/>
       <use xlink:href="#DejaVuSans-72" x="1422.925781"/>
       <use xlink:href="#DejaVuSans-61" x="1464.039062"/>
       <use xlink:href="#DejaVuSans-63" x="1525.318359"/>
       <use xlink:href="#DejaVuSans-5f" x="1580.298828"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(70.806562 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_avg_dev_NpValence -->
      <g style="fill: #333333" transform="translate(16.907344 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-4e" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2082.621094"/>
       <use xlink:href="#DejaVuSans-56" x="2146.097656"/>
       <use xlink:href="#DejaVuSans-61" x="2206.755859"/>
       <use xlink:href="#DejaVuSans-6c" x="2268.035156"/>
       <use xlink:href="#DejaVuSans-65" x="2295.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="2357.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2420.720703"/>
       <use xlink:href="#DejaVuSans-65" x="2475.701172"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(37.99375 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(62.3525 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(50.449375 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(44.085469 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(18.627813 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_maximum_NdUnfilled -->
      <g style="fill: #333333" transform="translate(7.2 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-55" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-6e" x="2305.033203"/>
       <use xlink:href="#DejaVuSans-66" x="2368.412109"/>
       <use xlink:href="#DejaVuSans-69" x="2403.617188"/>
       <use xlink:href="#DejaVuSans-6c" x="2431.400391"/>
       <use xlink:href="#DejaVuSans-6c" x="2459.183594"/>
       <use xlink:href="#DejaVuSans-65" x="2486.966797"/>
       <use xlink:href="#DejaVuSans-64" x="2548.490234"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(34.520312 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- GaussianSymmFunc_std_dev_G2_80_0 -->
      <g style="fill: #333333" transform="translate(95.734062 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-61" x="77.490234"/>
       <use xlink:href="#DejaVuSans-75" x="138.769531"/>
       <use xlink:href="#DejaVuSans-73" x="202.148438"/>
       <use xlink:href="#DejaVuSans-73" x="254.248047"/>
       <use xlink:href="#DejaVuSans-69" x="306.347656"/>
       <use xlink:href="#DejaVuSans-61" x="334.130859"/>
       <use xlink:href="#DejaVuSans-6e" x="395.410156"/>
       <use xlink:href="#DejaVuSans-53" x="458.789062"/>
       <use xlink:href="#DejaVuSans-79" x="522.265625"/>
       <use xlink:href="#DejaVuSans-6d" x="581.445312"/>
       <use xlink:href="#DejaVuSans-6d" x="678.857422"/>
       <use xlink:href="#DejaVuSans-46" x="776.269531"/>
       <use xlink:href="#DejaVuSans-75" x="828.289062"/>
       <use xlink:href="#DejaVuSans-6e" x="891.667969"/>
       <use xlink:href="#DejaVuSans-63" x="955.046875"/>
       <use xlink:href="#DejaVuSans-5f" x="1010.027344"/>
       <use xlink:href="#DejaVuSans-73" x="1060.027344"/>
       <use xlink:href="#DejaVuSans-74" x="1112.126953"/>
       <use xlink:href="#DejaVuSans-64" x="1151.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="1214.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1264.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1328.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1389.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1448.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1498.992188"/>
       <use xlink:href="#DejaVuSans-32" x="1576.482422"/>
       <use xlink:href="#DejaVuSans-5f" x="1640.105469"/>
       <use xlink:href="#DejaVuSans-38" x="1690.105469"/>
       <use xlink:href="#DejaVuSans-30" x="1753.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.351562"/>
       <use xlink:href="#DejaVuSans-30" x="1867.351562"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(48.389687 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(227.066562 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(105.551094 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(63.967344 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 370.26 638.149 
L 513.028 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image49c9d3f103" transform="scale(1 -1) translate(0 -578.16)" x="374.4" y="-43.2" width="134.64" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature26_fold0 -->
    <g transform="translate(132.761 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-32" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-36" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 521.951 638.149 
L 529.5805 638.149 
L 529.5805 27.789 
L 521.951 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagebbdfcec15d" transform="scale(1 -1) translate(0 -609.84)" x="522" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(533.0805 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(533.0805 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(567.481437 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p83da2b1ee7">
   <rect x="370.26" y="27.789" width="142.768" height="610.36"/>
  </clipPath>
 </defs>
</svg>
