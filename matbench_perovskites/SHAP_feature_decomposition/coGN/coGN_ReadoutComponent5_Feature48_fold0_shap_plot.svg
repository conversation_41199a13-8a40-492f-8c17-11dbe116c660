<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="781.271031pt" height="679.5765pt" viewBox="0 0 781.271031 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T09:19:44.942508</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 781.271031 679.5765 
L 781.271031 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
L 520.984031 27.789 
L 409.392031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 451.998164 638.149 
L 451.998164 27.789 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 409.392031 609.084238 
L 520.984031 609.084238 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 409.392031 580.019476 
L 520.984031 580.019476 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 409.392031 550.954714 
L 520.984031 550.954714 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 409.392031 521.889952 
L 520.984031 521.889952 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 409.392031 492.82519 
L 520.984031 492.82519 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 409.392031 463.760429 
L 520.984031 463.760429 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 409.392031 434.695667 
L 520.984031 434.695667 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 409.392031 405.630905 
L 520.984031 405.630905 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 409.392031 376.566143 
L 520.984031 376.566143 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 409.392031 347.501381 
L 520.984031 347.501381 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 409.392031 318.436619 
L 520.984031 318.436619 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 409.392031 289.371857 
L 520.984031 289.371857 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 409.392031 260.307095 
L 520.984031 260.307095 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 409.392031 231.242333 
L 520.984031 231.242333 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 409.392031 202.177571 
L 520.984031 202.177571 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 409.392031 173.11281 
L 520.984031 173.11281 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 409.392031 144.048048 
L 520.984031 144.048048 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 409.392031 114.983286 
L 520.984031 114.983286 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 409.392031 85.918524 
L 520.984031 85.918524 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 409.392031 56.853762 
L 520.984031 56.853762 
" clip-path="url(#pfcbef643c7)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m4b73fbdd6e" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m4b73fbdd6e" x="451.998164" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(448.498789 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m4b73fbdd6e" x="503.680827" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(500.181452 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(342.506625 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- VoronoiFingerprint_std_dev_Voro_area_maximum -->
      <g style="fill: #333333" transform="translate(66.819219 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-6d" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-61" x="2047.941406"/>
       <use xlink:href="#DejaVuSans-78" x="2109.220703"/>
       <use xlink:href="#DejaVuSans-69" x="2168.400391"/>
       <use xlink:href="#DejaVuSans-6d" x="2196.183594"/>
       <use xlink:href="#DejaVuSans-75" x="2293.595703"/>
       <use xlink:href="#DejaVuSans-6d" x="2356.974609"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_1 -->
      <g style="fill: #333333" transform="translate(37.252344 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-31" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElectronegativityDiff_range_EN_difference -->
      <g style="fill: #333333" transform="translate(112.085625 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-72" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-61" x="1115.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="1176.314453"/>
       <use xlink:href="#DejaVuSans-67" x="1239.693359"/>
       <use xlink:href="#DejaVuSans-65" x="1303.169922"/>
       <use xlink:href="#DejaVuSans-5f" x="1364.693359"/>
       <use xlink:href="#DejaVuSans-45" x="1414.693359"/>
       <use xlink:href="#DejaVuSans-4e" x="1477.876953"/>
       <use xlink:href="#DejaVuSans-5f" x="1552.681641"/>
       <use xlink:href="#DejaVuSans-64" x="1602.681641"/>
       <use xlink:href="#DejaVuSans-69" x="1666.158203"/>
       <use xlink:href="#DejaVuSans-66" x="1693.941406"/>
       <use xlink:href="#DejaVuSans-66" x="1729.146484"/>
       <use xlink:href="#DejaVuSans-65" x="1764.351562"/>
       <use xlink:href="#DejaVuSans-72" x="1825.875"/>
       <use xlink:href="#DejaVuSans-65" x="1864.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="1926.261719"/>
       <use xlink:href="#DejaVuSans-63" x="1989.640625"/>
       <use xlink:href="#DejaVuSans-65" x="2044.621094"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(44.9 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- XRDPowderPattern_xrd_37 -->
      <g style="fill: #333333" transform="translate(213.020469 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-37" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_88 -->
      <g style="fill: #333333" transform="translate(196.571406 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_trigonal_non-coplanar_CN_3 -->
      <g style="fill: #333333" transform="translate(18.229688 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-6e" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6f" x="1924.78125"/>
       <use xlink:href="#DejaVuSans-6e" x="1985.962891"/>
       <use xlink:href="#DejaVuSans-2d" x="2049.341797"/>
       <use xlink:href="#DejaVuSans-63" x="2085.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="2140.40625"/>
       <use xlink:href="#DejaVuSans-70" x="2201.587891"/>
       <use xlink:href="#DejaVuSans-6c" x="2265.064453"/>
       <use xlink:href="#DejaVuSans-61" x="2292.847656"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.126953"/>
       <use xlink:href="#DejaVuSans-61" x="2417.505859"/>
       <use xlink:href="#DejaVuSans-72" x="2478.785156"/>
       <use xlink:href="#DejaVuSans-5f" x="2519.898438"/>
       <use xlink:href="#DejaVuSans-43" x="2569.898438"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.722656"/>
       <use xlink:href="#DejaVuSans-5f" x="2714.527344"/>
       <use xlink:href="#DejaVuSans-33" x="2764.527344"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(86.392344 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CrystalNNFingerprint_mean_wt_CN_4 -->
      <g style="fill: #333333" transform="translate(144.683125 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-34" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementFraction_B -->
      <g style="fill: #333333" transform="translate(264.756406 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-42" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(73.652344 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(101.484531 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(188.300156 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(194.101406 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(196.571406 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(87.521719 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(266.198594 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(144.683125 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(103.099375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 409.392031 638.149 
L 520.984031 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image35b6b43b38" transform="scale(1 -1) translate(0 -578.16)" x="411.84" y="-43.2" width="106.56" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature48_fold0 -->
    <g transform="translate(156.305031 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-38" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 527.958531 638.149 
L 535.588031 638.149 
L 535.588031 27.789 
L 527.958531 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image27d25fcf90" transform="scale(1 -1) translate(0 -609.84)" x="527.76" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(539.088031 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(539.088031 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(573.488969 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pfcbef643c7">
   <rect x="409.392031" y="27.789" width="111.592" height="610.36"/>
  </clipPath>
 </defs>
</svg>
