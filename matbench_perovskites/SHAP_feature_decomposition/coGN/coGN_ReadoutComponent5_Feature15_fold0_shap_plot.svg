<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="762.673844pt" height="679.5765pt" viewBox="0 0 762.673844 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T04:30:44.340843</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 762.673844 679.5765 
L 762.673844 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 378.374844 638.149 
L 514.806844 638.149 
L 514.806844 27.789 
L 378.374844 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 441.932381 638.149 
L 441.932381 27.789 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 378.374844 609.084238 
L 514.806844 609.084238 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 378.374844 580.019476 
L 514.806844 580.019476 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 378.374844 550.954714 
L 514.806844 550.954714 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 378.374844 521.889952 
L 514.806844 521.889952 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 378.374844 492.82519 
L 514.806844 492.82519 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 378.374844 463.760429 
L 514.806844 463.760429 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 378.374844 434.695667 
L 514.806844 434.695667 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 378.374844 405.630905 
L 514.806844 405.630905 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 378.374844 376.566143 
L 514.806844 376.566143 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 378.374844 347.501381 
L 514.806844 347.501381 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 378.374844 318.436619 
L 514.806844 318.436619 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 378.374844 289.371857 
L 514.806844 289.371857 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 378.374844 260.307095 
L 514.806844 260.307095 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 378.374844 231.242333 
L 514.806844 231.242333 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 378.374844 202.177571 
L 514.806844 202.177571 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 378.374844 173.11281 
L 514.806844 173.11281 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 378.374844 144.048048 
L 514.806844 144.048048 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 378.374844 114.983286 
L 514.806844 114.983286 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 378.374844 85.918524 
L 514.806844 85.918524 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 378.374844 56.853762 
L 514.806844 56.853762 
" clip-path="url(#pccc3775dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m5e982997e8" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m5e982997e8" x="402.571958" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(394.463755 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m5e982997e8" x="441.932381" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(438.433006 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m5e982997e8" x="481.292805" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(477.79343 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(323.909438 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_40 -->
      <g style="fill: #333333" transform="translate(165.554219 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- AtomicOrbitals_LUMO_energy -->
      <g style="fill: #333333" transform="translate(162.98875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-4c" x="788.679688"/>
       <use xlink:href="#DejaVuSans-55" x="839.392578"/>
       <use xlink:href="#DejaVuSans-4d" x="912.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="998.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1077.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1127.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="1189.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1252.478516"/>
       <use xlink:href="#DejaVuSans-72" x="1314.001953"/>
       <use xlink:href="#DejaVuSans-67" x="1353.365234"/>
       <use xlink:href="#DejaVuSans-79" x="1416.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(150.780938 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- XRDPowderPattern_xrd_22 -->
      <g style="fill: #333333" transform="translate(182.003281 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_46 -->
      <g style="fill: #333333" transform="translate(165.554219 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_water-like_CN_2 -->
      <g style="fill: #333333" transform="translate(66.215937 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-61" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-74" x="1569.263672"/>
       <use xlink:href="#DejaVuSans-65" x="1608.472656"/>
       <use xlink:href="#DejaVuSans-72" x="1669.996094"/>
       <use xlink:href="#DejaVuSans-2d" x="1704.734375"/>
       <use xlink:href="#DejaVuSans-6c" x="1740.818359"/>
       <use xlink:href="#DejaVuSans-69" x="1768.601562"/>
       <use xlink:href="#DejaVuSans-6b" x="1796.384766"/>
       <use xlink:href="#DejaVuSans-65" x="1850.669922"/>
       <use xlink:href="#DejaVuSans-5f" x="1912.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1962.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="2032.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="2106.822266"/>
       <use xlink:href="#DejaVuSans-32" x="2156.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_Column -->
      <g style="fill: #333333" transform="translate(61.558281 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-43" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6f" x="1943.119141"/>
       <use xlink:href="#DejaVuSans-6c" x="2004.300781"/>
       <use xlink:href="#DejaVuSans-75" x="2032.083984"/>
       <use xlink:href="#DejaVuSans-6d" x="2095.462891"/>
       <use xlink:href="#DejaVuSans-6e" x="2192.875"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- ElementFraction_Ir -->
      <g style="fill: #333333" transform="translate(233.479219 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-49" x="863.208984"/>
       <use xlink:href="#DejaVuSans-72" x="892.701172"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_68 -->
      <g style="fill: #333333" transform="translate(165.554219 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_166 -->
      <g style="fill: #333333" transform="translate(157.282969 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-36" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(165.554219 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- AverageBondLength_mean_Average_bond_length -->
      <g style="fill: #333333" transform="translate(32.893281 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-6d" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-65" x="1159.611328"/>
       <use xlink:href="#DejaVuSans-61" x="1221.134766"/>
       <use xlink:href="#DejaVuSans-6e" x="1282.414062"/>
       <use xlink:href="#DejaVuSans-5f" x="1345.792969"/>
       <use xlink:href="#DejaVuSans-41" x="1395.792969"/>
       <use xlink:href="#DejaVuSans-76" x="1458.326172"/>
       <use xlink:href="#DejaVuSans-65" x="1517.505859"/>
       <use xlink:href="#DejaVuSans-72" x="1579.029297"/>
       <use xlink:href="#DejaVuSans-61" x="1620.142578"/>
       <use xlink:href="#DejaVuSans-67" x="1681.421875"/>
       <use xlink:href="#DejaVuSans-65" x="1744.898438"/>
       <use xlink:href="#DejaVuSans-5f" x="1806.421875"/>
       <use xlink:href="#DejaVuSans-62" x="1856.421875"/>
       <use xlink:href="#DejaVuSans-6f" x="1919.898438"/>
       <use xlink:href="#DejaVuSans-6e" x="1981.080078"/>
       <use xlink:href="#DejaVuSans-64" x="2044.458984"/>
       <use xlink:href="#DejaVuSans-5f" x="2107.935547"/>
       <use xlink:href="#DejaVuSans-6c" x="2157.935547"/>
       <use xlink:href="#DejaVuSans-65" x="2185.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="2247.242188"/>
       <use xlink:href="#DejaVuSans-67" x="2310.621094"/>
       <use xlink:href="#DejaVuSans-74" x="2374.097656"/>
       <use xlink:href="#DejaVuSans-68" x="2413.306641"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(42.635156 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(165.554219 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(157.282969 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_maximum_GSbandgap -->
      <g style="fill: #333333" transform="translate(7.2 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-47" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-53" x="2171.048828"/>
       <use xlink:href="#DejaVuSans-62" x="2234.525391"/>
       <use xlink:href="#DejaVuSans-61" x="2298.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="2359.28125"/>
       <use xlink:href="#DejaVuSans-64" x="2422.660156"/>
       <use xlink:href="#DejaVuSans-67" x="2486.136719"/>
       <use xlink:href="#DejaVuSans-61" x="2549.613281"/>
       <use xlink:href="#DejaVuSans-70" x="2610.892578"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(56.504531 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(235.181406 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(113.665938 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(72.082188 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 378.374844 638.149 
L 514.806844 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image0b139fc8d7" transform="scale(1 -1) translate(0 -578.16)" x="382.32" y="-43.2" width="128.88" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature15_fold0 -->
    <g transform="translate(137.707844 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-35" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 523.333844 638.149 
L 530.963344 638.149 
L 530.963344 27.789 
L 523.333844 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image41c501c0f9" transform="scale(1 -1) translate(0 -609.84)" x="523.44" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(534.463344 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(534.463344 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(568.864281 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pccc3775dd2">
   <rect x="378.374844" y="27.789" width="136.432" height="610.36"/>
  </clipPath>
 </defs>
</svg>
