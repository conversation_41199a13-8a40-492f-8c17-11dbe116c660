<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.657906pt" height="679.5765pt" viewBox="0 0 794.657906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:17:55.706153</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.657906 679.5765 
L 794.657906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
L 525.730906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 462.232375 638.149 
L 462.232375 27.789 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.730906 609.084238 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.730906 580.019476 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.730906 550.954714 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.730906 521.889952 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.730906 492.82519 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.730906 463.760429 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.730906 434.695667 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.730906 405.630905 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.730906 376.566143 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.730906 347.501381 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.730906 318.436619 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.730906 289.371857 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.730906 260.307095 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.730906 231.242333 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.730906 202.177571 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.730906 173.11281 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.730906 144.048048 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.730906 114.983286 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.730906 85.918524 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.730906 56.853762 
" clip-path="url(#pd6c5983ad3)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mbcafea9676" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mbcafea9676" x="462.232375" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(453.485656 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mbcafea9676" x="509.218834" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(500.472115 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8935 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(80.4225 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- CrystalNNFingerprint_mean_pentagonal_pyramidal_CN_6 -->
      <g style="fill: #333333" transform="translate(36.996406 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-70" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-65" x="1489.673828"/>
       <use xlink:href="#DejaVuSans-6e" x="1551.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1614.576172"/>
       <use xlink:href="#DejaVuSans-61" x="1653.785156"/>
       <use xlink:href="#DejaVuSans-67" x="1715.064453"/>
       <use xlink:href="#DejaVuSans-6f" x="1778.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="1839.722656"/>
       <use xlink:href="#DejaVuSans-61" x="1903.101562"/>
       <use xlink:href="#DejaVuSans-6c" x="1964.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1992.164062"/>
       <use xlink:href="#DejaVuSans-70" x="2042.164062"/>
       <use xlink:href="#DejaVuSans-79" x="2105.640625"/>
       <use xlink:href="#DejaVuSans-72" x="2164.820312"/>
       <use xlink:href="#DejaVuSans-61" x="2205.933594"/>
       <use xlink:href="#DejaVuSans-6d" x="2267.212891"/>
       <use xlink:href="#DejaVuSans-69" x="2364.625"/>
       <use xlink:href="#DejaVuSans-64" x="2392.408203"/>
       <use xlink:href="#DejaVuSans-61" x="2455.884766"/>
       <use xlink:href="#DejaVuSans-6c" x="2517.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="2544.947266"/>
       <use xlink:href="#DejaVuSans-43" x="2594.947266"/>
       <use xlink:href="#DejaVuSans-4e" x="2664.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="2739.576172"/>
       <use xlink:href="#DejaVuSans-36" x="2789.576172"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_avg_dev_SpaceGroupNumber -->
      <g style="fill: #333333" transform="translate(15.479375 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-70" x="2071.292969"/>
       <use xlink:href="#DejaVuSans-61" x="2134.769531"/>
       <use xlink:href="#DejaVuSans-63" x="2196.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2251.029297"/>
       <use xlink:href="#DejaVuSans-47" x="2312.552734"/>
       <use xlink:href="#DejaVuSans-72" x="2390.042969"/>
       <use xlink:href="#DejaVuSans-6f" x="2428.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2490.087891"/>
       <use xlink:href="#DejaVuSans-70" x="2553.466797"/>
       <use xlink:href="#DejaVuSans-4e" x="2616.943359"/>
       <use xlink:href="#DejaVuSans-75" x="2691.748047"/>
       <use xlink:href="#DejaVuSans-6d" x="2755.126953"/>
       <use xlink:href="#DejaVuSans-62" x="2852.539062"/>
       <use xlink:href="#DejaVuSans-65" x="2916.015625"/>
       <use xlink:href="#DejaVuSans-72" x="2977.539062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(204.219063 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(156.903125 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- XRDPowderPattern_xrd_33 -->
      <g style="fill: #333333" transform="translate(235.047344 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(166.71 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-32" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(88.876563 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(71.383438 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- OxidationStates_std_dev_oxidation_state -->
      <g style="fill: #333333" transform="translate(141.102031 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-73" x="848.779297"/>
       <use xlink:href="#DejaVuSans-74" x="900.878906"/>
       <use xlink:href="#DejaVuSans-64" x="940.087891"/>
       <use xlink:href="#DejaVuSans-5f" x="1003.564453"/>
       <use xlink:href="#DejaVuSans-64" x="1053.564453"/>
       <use xlink:href="#DejaVuSans-65" x="1117.041016"/>
       <use xlink:href="#DejaVuSans-76" x="1178.564453"/>
       <use xlink:href="#DejaVuSans-5f" x="1237.744141"/>
       <use xlink:href="#DejaVuSans-6f" x="1287.744141"/>
       <use xlink:href="#DejaVuSans-78" x="1345.800781"/>
       <use xlink:href="#DejaVuSans-69" x="1404.980469"/>
       <use xlink:href="#DejaVuSans-64" x="1432.763672"/>
       <use xlink:href="#DejaVuSans-61" x="1496.240234"/>
       <use xlink:href="#DejaVuSans-74" x="1557.519531"/>
       <use xlink:href="#DejaVuSans-69" x="1596.728516"/>
       <use xlink:href="#DejaVuSans-6f" x="1624.511719"/>
       <use xlink:href="#DejaVuSans-6e" x="1685.693359"/>
       <use xlink:href="#DejaVuSans-5f" x="1749.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1799.072266"/>
       <use xlink:href="#DejaVuSans-74" x="1851.171875"/>
       <use xlink:href="#DejaVuSans-61" x="1890.380859"/>
       <use xlink:href="#DejaVuSans-74" x="1951.660156"/>
       <use xlink:href="#DejaVuSans-65" x="1990.869141"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(218.598281 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(75.179844 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-76" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2073.441406"/>
       <use xlink:href="#DejaVuSans-6c" x="2134.623047"/>
       <use xlink:href="#DejaVuSans-75" x="2162.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="2225.785156"/>
       <use xlink:href="#DejaVuSans-65" x="2323.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="2384.720703"/>
       <use xlink:href="#DejaVuSans-70" x="2434.720703"/>
       <use xlink:href="#DejaVuSans-61" x="2498.197266"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(111.608281 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- TMetalFraction_transition_metal_fraction -->
      <g style="fill: #333333" transform="translate(143.490781 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-54"/>
       <use xlink:href="#DejaVuSans-4d" x="61.083984"/>
       <use xlink:href="#DejaVuSans-65" x="147.363281"/>
       <use xlink:href="#DejaVuSans-74" x="208.886719"/>
       <use xlink:href="#DejaVuSans-61" x="248.095703"/>
       <use xlink:href="#DejaVuSans-6c" x="309.375"/>
       <use xlink:href="#DejaVuSans-46" x="337.158203"/>
       <use xlink:href="#DejaVuSans-72" x="387.427734"/>
       <use xlink:href="#DejaVuSans-61" x="428.541016"/>
       <use xlink:href="#DejaVuSans-63" x="489.820312"/>
       <use xlink:href="#DejaVuSans-74" x="544.800781"/>
       <use xlink:href="#DejaVuSans-69" x="584.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="611.792969"/>
       <use xlink:href="#DejaVuSans-6e" x="672.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="736.353516"/>
       <use xlink:href="#DejaVuSans-74" x="786.353516"/>
       <use xlink:href="#DejaVuSans-72" x="825.5625"/>
       <use xlink:href="#DejaVuSans-61" x="866.675781"/>
       <use xlink:href="#DejaVuSans-6e" x="927.955078"/>
       <use xlink:href="#DejaVuSans-73" x="991.333984"/>
       <use xlink:href="#DejaVuSans-69" x="1043.433594"/>
       <use xlink:href="#DejaVuSans-74" x="1071.216797"/>
       <use xlink:href="#DejaVuSans-69" x="1110.425781"/>
       <use xlink:href="#DejaVuSans-6f" x="1138.208984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.390625"/>
       <use xlink:href="#DejaVuSans-5f" x="1262.769531"/>
       <use xlink:href="#DejaVuSans-6d" x="1312.769531"/>
       <use xlink:href="#DejaVuSans-65" x="1410.181641"/>
       <use xlink:href="#DejaVuSans-74" x="1471.705078"/>
       <use xlink:href="#DejaVuSans-61" x="1510.914062"/>
       <use xlink:href="#DejaVuSans-6c" x="1572.193359"/>
       <use xlink:href="#DejaVuSans-5f" x="1599.976562"/>
       <use xlink:href="#DejaVuSans-66" x="1649.976562"/>
       <use xlink:href="#DejaVuSans-72" x="1685.181641"/>
       <use xlink:href="#DejaVuSans-61" x="1726.294922"/>
       <use xlink:href="#DejaVuSans-63" x="1787.574219"/>
       <use xlink:href="#DejaVuSans-74" x="1842.554688"/>
       <use xlink:href="#DejaVuSans-69" x="1881.763672"/>
       <use xlink:href="#DejaVuSans-6f" x="1909.546875"/>
       <use xlink:href="#DejaVuSans-6e" x="1970.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_Column -->
      <g style="fill: #333333" transform="translate(89.780469 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-43" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2134.085938"/>
       <use xlink:href="#DejaVuSans-6c" x="2195.267578"/>
       <use xlink:href="#DejaVuSans-75" x="2223.050781"/>
       <use xlink:href="#DejaVuSans-6d" x="2286.429688"/>
       <use xlink:href="#DejaVuSans-6e" x="2383.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_range_NpValence -->
      <g style="fill: #333333" transform="translate(94.625 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4e" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-70" x="1955.277344"/>
       <use xlink:href="#DejaVuSans-56" x="2018.753906"/>
       <use xlink:href="#DejaVuSans-61" x="2079.412109"/>
       <use xlink:href="#DejaVuSans-6c" x="2140.691406"/>
       <use xlink:href="#DejaVuSans-65" x="2168.474609"/>
       <use xlink:href="#DejaVuSans-6e" x="2229.998047"/>
       <use xlink:href="#DejaVuSans-63" x="2293.376953"/>
       <use xlink:href="#DejaVuSans-65" x="2348.357422"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(216.128281 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- Stoichiometry_2-norm -->
      <g style="fill: #333333" transform="translate(263.82 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6f" x="102.685547"/>
       <use xlink:href="#DejaVuSans-69" x="163.867188"/>
       <use xlink:href="#DejaVuSans-63" x="191.650391"/>
       <use xlink:href="#DejaVuSans-68" x="246.630859"/>
       <use xlink:href="#DejaVuSans-69" x="310.009766"/>
       <use xlink:href="#DejaVuSans-6f" x="337.792969"/>
       <use xlink:href="#DejaVuSans-6d" x="398.974609"/>
       <use xlink:href="#DejaVuSans-65" x="496.386719"/>
       <use xlink:href="#DejaVuSans-74" x="557.910156"/>
       <use xlink:href="#DejaVuSans-72" x="597.119141"/>
       <use xlink:href="#DejaVuSans-79" x="638.232422"/>
       <use xlink:href="#DejaVuSans-5f" x="697.412109"/>
       <use xlink:href="#DejaVuSans-32" x="747.412109"/>
       <use xlink:href="#DejaVuSans-2d" x="811.035156"/>
       <use xlink:href="#DejaVuSans-6e" x="847.119141"/>
       <use xlink:href="#DejaVuSans-6f" x="910.498047"/>
       <use xlink:href="#DejaVuSans-72" x="971.679688"/>
       <use xlink:href="#DejaVuSans-6d" x="1011.042969"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image5562fd5e7b" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature29_fold0 -->
    <g transform="translate(169.691906 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-32" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-39" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.625406 638.149 
L 539.254906 638.149 
L 539.254906 27.789 
L 531.625406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image89a9fe07fb" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.754906 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.754906 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.155844 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pd6c5983ad3">
   <rect x="431.418906" y="27.789" width="94.312" height="610.36"/>
  </clipPath>
 </defs>
</svg>
