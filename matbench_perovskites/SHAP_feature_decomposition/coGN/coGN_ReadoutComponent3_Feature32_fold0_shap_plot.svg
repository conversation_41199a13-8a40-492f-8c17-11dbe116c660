<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="794.657906pt" height="679.5765pt" viewBox="0 0 794.657906 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:26:49.351935</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 794.657906 679.5765 
L 794.657906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
L 525.730906 27.789 
L 431.418906 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 468.754313 638.149 
L 468.754313 27.789 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 431.418906 609.084238 
L 525.730906 609.084238 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 431.418906 580.019476 
L 525.730906 580.019476 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 431.418906 550.954714 
L 525.730906 550.954714 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 431.418906 521.889952 
L 525.730906 521.889952 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 431.418906 492.82519 
L 525.730906 492.82519 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 431.418906 463.760429 
L 525.730906 463.760429 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 431.418906 434.695667 
L 525.730906 434.695667 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 431.418906 405.630905 
L 525.730906 405.630905 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 431.418906 376.566143 
L 525.730906 376.566143 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 431.418906 347.501381 
L 525.730906 347.501381 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 431.418906 318.436619 
L 525.730906 318.436619 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 431.418906 289.371857 
L 525.730906 289.371857 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 431.418906 260.307095 
L 525.730906 260.307095 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 431.418906 231.242333 
L 525.730906 231.242333 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 431.418906 202.177571 
L 525.730906 202.177571 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 431.418906 173.11281 
L 525.730906 173.11281 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 431.418906 144.048048 
L 525.730906 144.048048 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 431.418906 114.983286 
L 525.730906 114.983286 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 431.418906 85.918524 
L 525.730906 85.918524 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 431.418906 56.853762 
L 525.730906 56.853762 
" clip-path="url(#p22c8769dd2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mc584f72e49" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mc584f72e49" x="468.754313" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(460.007594 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mc584f72e49" x="525.567561" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(516.820842 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(355.8935 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(79.786719 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- BondFractions_N_-_Tl_bond_frac_ -->
      <g style="fill: #333333" transform="translate(193.185313 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-46" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="306.910156"/>
       <use xlink:href="#DejaVuSans-61" x="348.023438"/>
       <use xlink:href="#DejaVuSans-63" x="409.302734"/>
       <use xlink:href="#DejaVuSans-74" x="464.283203"/>
       <use xlink:href="#DejaVuSans-69" x="503.492188"/>
       <use xlink:href="#DejaVuSans-6f" x="531.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="592.457031"/>
       <use xlink:href="#DejaVuSans-73" x="655.835938"/>
       <use xlink:href="#DejaVuSans-5f" x="707.935547"/>
       <use xlink:href="#DejaVuSans-4e" x="757.935547"/>
       <use xlink:href="#DejaVuSans-5f" x="832.740234"/>
       <use xlink:href="#DejaVuSans-2d" x="882.740234"/>
       <use xlink:href="#DejaVuSans-5f" x="918.824219"/>
       <use xlink:href="#DejaVuSans-54" x="968.824219"/>
       <use xlink:href="#DejaVuSans-6c" x="1029.908203"/>
       <use xlink:href="#DejaVuSans-5f" x="1057.691406"/>
       <use xlink:href="#DejaVuSans-62" x="1107.691406"/>
       <use xlink:href="#DejaVuSans-6f" x="1171.167969"/>
       <use xlink:href="#DejaVuSans-6e" x="1232.349609"/>
       <use xlink:href="#DejaVuSans-64" x="1295.728516"/>
       <use xlink:href="#DejaVuSans-5f" x="1359.205078"/>
       <use xlink:href="#DejaVuSans-66" x="1409.205078"/>
       <use xlink:href="#DejaVuSans-72" x="1444.410156"/>
       <use xlink:href="#DejaVuSans-61" x="1485.523438"/>
       <use xlink:href="#DejaVuSans-63" x="1546.802734"/>
       <use xlink:href="#DejaVuSans-5f" x="1601.783203"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(156.903125 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(156.903125 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(218.598281 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(105.244375 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(16.330469 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_172 -->
      <g style="fill: #333333" transform="translate(210.327031 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-32" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_87 -->
      <g style="fill: #333333" transform="translate(218.598281 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(156.903125 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(110.868906 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-38" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-30" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-30" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2d" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2157.714844"/>
       <use xlink:href="#DejaVuSans-31" x="2221.337891"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(218.598281 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(99.152656 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(95.679219 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(153.009219 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(7.2 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_mean_NdValence -->
      <g style="fill: #333333" transform="translate(95.559375 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-64" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_mode_NUnfilled -->
      <g style="fill: #333333" transform="translate(105.242344 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(88.876563 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(95.679219 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 431.418906 638.149 
L 525.730906 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image4c7ee99ac5" transform="scale(1 -1) translate(0 -578.16)" x="433.44" y="-43.2" width="90" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature32_fold0 -->
    <g transform="translate(169.691906 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-33" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.625406 638.149 
L 539.254906 638.149 
L 539.254906 27.789 
L 531.625406 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="image38b0dd63b3" transform="scale(1 -1) translate(0 -609.84)" x="531.36" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(542.754906 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(542.754906 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.155844 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p22c8769dd2">
   <rect x="431.418906" y="27.789" width="94.312" height="610.36"/>
  </clipPath>
 </defs>
</svg>
