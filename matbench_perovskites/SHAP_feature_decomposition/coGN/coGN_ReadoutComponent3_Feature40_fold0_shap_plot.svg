<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="791.282062pt" height="679.5765pt" viewBox="0 0 791.282062 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T15:44:47.787312</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 791.282062 679.5765 
L 791.282062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
L 524.299063 27.789 
L 426.099063 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 449.78943 638.149 
L 449.78943 27.789 
" clip-path="url(#p26ff015508)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 426.099063 609.084238 
L 524.299063 609.084238 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 426.099063 580.019476 
L 524.299063 580.019476 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 426.099063 550.954714 
L 524.299063 550.954714 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 426.099063 521.889952 
L 524.299063 521.889952 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 426.099063 492.82519 
L 524.299063 492.82519 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 426.099063 463.760429 
L 524.299063 463.760429 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 426.099063 434.695667 
L 524.299063 434.695667 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 426.099063 405.630905 
L 524.299063 405.630905 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 426.099063 376.566143 
L 524.299063 376.566143 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 426.099063 347.501381 
L 524.299063 347.501381 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 426.099063 318.436619 
L 524.299063 318.436619 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 426.099063 289.371857 
L 524.299063 289.371857 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 426.099063 260.307095 
L 524.299063 260.307095 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 426.099063 231.242333 
L 524.299063 231.242333 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 426.099063 202.177571 
L 524.299063 202.177571 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 426.099063 173.11281 
L 524.299063 173.11281 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 426.099063 144.048048 
L 524.299063 144.048048 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 426.099063 114.983286 
L 524.299063 114.983286 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 426.099063 85.918524 
L 524.299063 85.918524 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 426.099063 56.853762 
L 524.299063 56.853762 
" clip-path="url(#p26ff015508)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m0b546568a7" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m0b546568a7" x="449.78943" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(446.290055 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m0b546568a7" x="514.533731" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(511.034356 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(352.517656 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(198.899219 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- AGNIFingerPrint_std_dev_AGNI_eta=4_43e+00 -->
      <g style="fill: #333333" transform="translate(99.345625 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2b" d="M 2944 4013 
L 2944 2272 
L 4684 2272 
L 4684 1741 
L 2944 1741 
L 2944 0 
L 2419 0 
L 2419 1741 
L 678 1741 
L 678 2272 
L 2419 2272 
L 2419 4013 
L 2944 4013 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-65" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-74" x="1634.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1674.169922"/>
       <use xlink:href="#DejaVuSans-3d" x="1735.449219"/>
       <use xlink:href="#DejaVuSans-34" x="1819.238281"/>
       <use xlink:href="#DejaVuSans-5f" x="1882.861328"/>
       <use xlink:href="#DejaVuSans-34" x="1932.861328"/>
       <use xlink:href="#DejaVuSans-33" x="1996.484375"/>
       <use xlink:href="#DejaVuSans-65" x="2060.107422"/>
       <use xlink:href="#DejaVuSans-2b" x="2121.630859"/>
       <use xlink:href="#DejaVuSans-30" x="2205.419922"/>
       <use xlink:href="#DejaVuSans-30" x="2269.042969"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(213.278437 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_range_Electronegativity -->
      <g style="fill: #333333" transform="translate(47.794531 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-45" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="1943.65625"/>
       <use xlink:href="#DejaVuSans-65" x="1971.439453"/>
       <use xlink:href="#DejaVuSans-63" x="2032.962891"/>
       <use xlink:href="#DejaVuSans-74" x="2087.943359"/>
       <use xlink:href="#DejaVuSans-72" x="2127.152344"/>
       <use xlink:href="#DejaVuSans-6f" x="2166.015625"/>
       <use xlink:href="#DejaVuSans-6e" x="2227.197266"/>
       <use xlink:href="#DejaVuSans-65" x="2290.576172"/>
       <use xlink:href="#DejaVuSans-67" x="2352.099609"/>
       <use xlink:href="#DejaVuSans-61" x="2415.576172"/>
       <use xlink:href="#DejaVuSans-74" x="2476.855469"/>
       <use xlink:href="#DejaVuSans-69" x="2516.064453"/>
       <use xlink:href="#DejaVuSans-76" x="2543.847656"/>
       <use xlink:href="#DejaVuSans-69" x="2603.027344"/>
       <use xlink:href="#DejaVuSans-74" x="2630.810547"/>
       <use xlink:href="#DejaVuSans-79" x="2670.019531"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(119.806406 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_17 -->
      <g style="fill: #333333" transform="translate(213.278437 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(151.583281 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_99 -->
      <g style="fill: #333333" transform="translate(213.278437 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- ElementProperty_MagpieData_avg_dev_Electronegativity -->
      <g style="fill: #333333" transform="translate(31.235781 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-45" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-6c" x="2071"/>
       <use xlink:href="#DejaVuSans-65" x="2098.783203"/>
       <use xlink:href="#DejaVuSans-63" x="2160.306641"/>
       <use xlink:href="#DejaVuSans-74" x="2215.287109"/>
       <use xlink:href="#DejaVuSans-72" x="2254.496094"/>
       <use xlink:href="#DejaVuSans-6f" x="2293.359375"/>
       <use xlink:href="#DejaVuSans-6e" x="2354.541016"/>
       <use xlink:href="#DejaVuSans-65" x="2417.919922"/>
       <use xlink:href="#DejaVuSans-67" x="2479.443359"/>
       <use xlink:href="#DejaVuSans-61" x="2542.919922"/>
       <use xlink:href="#DejaVuSans-74" x="2604.199219"/>
       <use xlink:href="#DejaVuSans-69" x="2643.408203"/>
       <use xlink:href="#DejaVuSans-76" x="2671.191406"/>
       <use xlink:href="#DejaVuSans-69" x="2730.371094"/>
       <use xlink:href="#DejaVuSans-74" x="2758.154297"/>
       <use xlink:href="#DejaVuSans-79" x="2797.363281"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_31 -->
      <g style="fill: #333333" transform="translate(213.278437 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElectronegativityDiff_minimum_EN_difference -->
      <g style="fill: #333333" transform="translate(104.905156 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-63" x="152.490234"/>
       <use xlink:href="#DejaVuSans-74" x="207.470703"/>
       <use xlink:href="#DejaVuSans-72" x="246.679688"/>
       <use xlink:href="#DejaVuSans-6f" x="285.542969"/>
       <use xlink:href="#DejaVuSans-6e" x="346.724609"/>
       <use xlink:href="#DejaVuSans-65" x="410.103516"/>
       <use xlink:href="#DejaVuSans-67" x="471.626953"/>
       <use xlink:href="#DejaVuSans-61" x="535.103516"/>
       <use xlink:href="#DejaVuSans-74" x="596.382812"/>
       <use xlink:href="#DejaVuSans-69" x="635.591797"/>
       <use xlink:href="#DejaVuSans-76" x="663.375"/>
       <use xlink:href="#DejaVuSans-69" x="722.554688"/>
       <use xlink:href="#DejaVuSans-74" x="750.337891"/>
       <use xlink:href="#DejaVuSans-79" x="789.546875"/>
       <use xlink:href="#DejaVuSans-44" x="848.726562"/>
       <use xlink:href="#DejaVuSans-69" x="925.728516"/>
       <use xlink:href="#DejaVuSans-66" x="953.511719"/>
       <use xlink:href="#DejaVuSans-66" x="988.716797"/>
       <use xlink:href="#DejaVuSans-5f" x="1023.921875"/>
       <use xlink:href="#DejaVuSans-6d" x="1073.921875"/>
       <use xlink:href="#DejaVuSans-69" x="1171.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="1199.117188"/>
       <use xlink:href="#DejaVuSans-69" x="1262.496094"/>
       <use xlink:href="#DejaVuSans-6d" x="1290.279297"/>
       <use xlink:href="#DejaVuSans-75" x="1387.691406"/>
       <use xlink:href="#DejaVuSans-6d" x="1451.070312"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.482422"/>
       <use xlink:href="#DejaVuSans-45" x="1598.482422"/>
       <use xlink:href="#DejaVuSans-4e" x="1661.666016"/>
       <use xlink:href="#DejaVuSans-5f" x="1736.470703"/>
       <use xlink:href="#DejaVuSans-64" x="1786.470703"/>
       <use xlink:href="#DejaVuSans-69" x="1849.947266"/>
       <use xlink:href="#DejaVuSans-66" x="1877.730469"/>
       <use xlink:href="#DejaVuSans-66" x="1912.935547"/>
       <use xlink:href="#DejaVuSans-65" x="1948.140625"/>
       <use xlink:href="#DejaVuSans-72" x="2009.664062"/>
       <use xlink:href="#DejaVuSans-65" x="2048.527344"/>
       <use xlink:href="#DejaVuSans-6e" x="2110.050781"/>
       <use xlink:href="#DejaVuSans-63" x="2173.429688"/>
       <use xlink:href="#DejaVuSans-65" x="2228.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(103.099375 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(282.905625 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(83.556719 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(61.607031 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(280.149219 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(210.808437 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(11.010625 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- MEGNet_OFMEncoded_v1_83 -->
      <g style="fill: #333333" transform="translate(213.278437 61.792746) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 426.099063 638.149 
L 524.299063 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagef6532b919c" transform="scale(1 -1) translate(0 -578.16)" x="428.4" y="-43.2" width="93.6" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature40_fold0 -->
    <g transform="translate(166.316063 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-34" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 530.436563 638.149 
L 538.066063 638.149 
L 538.066063 27.789 
L 530.436563 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image62ebcd5511" transform="scale(1 -1) translate(0 -609.84)" x="530.64" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(541.566063 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(541.566063 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.967 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p26ff015508">
   <rect x="426.099063" y="27.789" width="98.2" height="610.36"/>
  </clipPath>
 </defs>
</svg>
