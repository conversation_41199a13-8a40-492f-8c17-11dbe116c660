<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="804.844844pt" height="679.5765pt" viewBox="0 0 804.844844 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T20:27:40.881071</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 804.844844 679.5765 
L 804.844844 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 438.629844 638.149 
L 526.677844 638.149 
L 526.677844 27.789 
L 438.629844 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 475.868166 638.149 
L 475.868166 27.789 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 438.629844 609.084238 
L 526.677844 609.084238 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 438.629844 580.019476 
L 526.677844 580.019476 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 438.629844 550.954714 
L 526.677844 550.954714 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 438.629844 521.889952 
L 526.677844 521.889952 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 438.629844 492.82519 
L 526.677844 492.82519 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 438.629844 463.760429 
L 526.677844 463.760429 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 438.629844 434.695667 
L 526.677844 434.695667 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 438.629844 405.630905 
L 526.677844 405.630905 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 438.629844 376.566143 
L 526.677844 376.566143 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 438.629844 347.501381 
L 526.677844 347.501381 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 438.629844 318.436619 
L 526.677844 318.436619 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 438.629844 289.371857 
L 526.677844 289.371857 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 438.629844 260.307095 
L 526.677844 260.307095 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 438.629844 231.242333 
L 526.677844 231.242333 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 438.629844 202.177571 
L 526.677844 202.177571 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 438.629844 173.11281 
L 526.677844 173.11281 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 438.629844 144.048048 
L 526.677844 144.048048 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 438.629844 114.983286 
L 526.677844 114.983286 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 438.629844 85.918524 
L 526.677844 85.918524 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 438.629844 56.853762 
L 526.677844 56.853762 
" clip-path="url(#p0b653761e0)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m20a6c54200" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m20a6c54200" x="475.868166" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(472.368791 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m20a6c54200" x="516.071491" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(512.572116 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(359.972437 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- XRDPowderPattern_xrd_28 -->
      <g style="fill: #333333" transform="translate(242.258281 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-38" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(217.537969 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_99 -->
      <g style="fill: #333333" transform="translate(225.809219 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(211.035938 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- XRDPowderPattern_xrd_47 -->
      <g style="fill: #333333" transform="translate(242.258281 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-34" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-37" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(211.43 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(102.890156 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(223.339219 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(225.809219 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(217.537969 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_maximum_CovalentRadius -->
      <g style="fill: #333333" transform="translate(41.743438 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-43" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-6f" x="2163.382812"/>
       <use xlink:href="#DejaVuSans-76" x="2224.564453"/>
       <use xlink:href="#DejaVuSans-61" x="2283.744141"/>
       <use xlink:href="#DejaVuSans-6c" x="2345.023438"/>
       <use xlink:href="#DejaVuSans-65" x="2372.806641"/>
       <use xlink:href="#DejaVuSans-6e" x="2434.330078"/>
       <use xlink:href="#DejaVuSans-74" x="2497.708984"/>
       <use xlink:href="#DejaVuSans-52" x="2536.917969"/>
       <use xlink:href="#DejaVuSans-61" x="2604.150391"/>
       <use xlink:href="#DejaVuSans-64" x="2665.429688"/>
       <use xlink:href="#DejaVuSans-69" x="2728.90625"/>
       <use xlink:href="#DejaVuSans-75" x="2756.689453"/>
       <use xlink:href="#DejaVuSans-73" x="2820.068359"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- StructuralHeterogeneity_avg_dev_neighbor_distance_variation -->
      <g style="fill: #333333" transform="translate(7.2 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-61" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-76" x="1319.771484"/>
       <use xlink:href="#DejaVuSans-67" x="1378.951172"/>
       <use xlink:href="#DejaVuSans-5f" x="1442.427734"/>
       <use xlink:href="#DejaVuSans-64" x="1492.427734"/>
       <use xlink:href="#DejaVuSans-65" x="1555.904297"/>
       <use xlink:href="#DejaVuSans-76" x="1617.427734"/>
       <use xlink:href="#DejaVuSans-5f" x="1676.607422"/>
       <use xlink:href="#DejaVuSans-6e" x="1726.607422"/>
       <use xlink:href="#DejaVuSans-65" x="1789.986328"/>
       <use xlink:href="#DejaVuSans-69" x="1851.509766"/>
       <use xlink:href="#DejaVuSans-67" x="1879.292969"/>
       <use xlink:href="#DejaVuSans-68" x="1942.769531"/>
       <use xlink:href="#DejaVuSans-62" x="2006.148438"/>
       <use xlink:href="#DejaVuSans-6f" x="2069.625"/>
       <use xlink:href="#DejaVuSans-72" x="2130.806641"/>
       <use xlink:href="#DejaVuSans-5f" x="2171.919922"/>
       <use xlink:href="#DejaVuSans-64" x="2221.919922"/>
       <use xlink:href="#DejaVuSans-69" x="2285.396484"/>
       <use xlink:href="#DejaVuSans-73" x="2313.179688"/>
       <use xlink:href="#DejaVuSans-74" x="2365.279297"/>
       <use xlink:href="#DejaVuSans-61" x="2404.488281"/>
       <use xlink:href="#DejaVuSans-6e" x="2465.767578"/>
       <use xlink:href="#DejaVuSans-63" x="2529.146484"/>
       <use xlink:href="#DejaVuSans-65" x="2584.126953"/>
       <use xlink:href="#DejaVuSans-5f" x="2645.650391"/>
       <use xlink:href="#DejaVuSans-76" x="2695.650391"/>
       <use xlink:href="#DejaVuSans-61" x="2754.830078"/>
       <use xlink:href="#DejaVuSans-72" x="2816.109375"/>
       <use xlink:href="#DejaVuSans-69" x="2857.222656"/>
       <use xlink:href="#DejaVuSans-61" x="2885.005859"/>
       <use xlink:href="#DejaVuSans-74" x="2946.285156"/>
       <use xlink:href="#DejaVuSans-69" x="2985.494141"/>
       <use xlink:href="#DejaVuSans-6f" x="3013.277344"/>
       <use xlink:href="#DejaVuSans-6e" x="3074.458984"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(102.890156 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(160.220156 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(294.660469 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(36.437813 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(116.759531 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(295.436406 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(173.920938 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(132.337188 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 438.629844 638.149 
L 526.677844 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image9660e27810" transform="scale(1 -1) translate(0 -578.16)" x="440.64" y="-43.2" width="84.24" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature106_fold0 -->
    <g transform="translate(167.662844 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-30" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-36" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 532.180844 638.149 
L 539.810344 638.149 
L 539.810344 27.789 
L 532.180844 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagef0e4c63dc3" transform="scale(1 -1) translate(0 -609.84)" x="532.08" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(543.310344 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(543.310344 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.711281 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p0b653761e0">
   <rect x="438.629844" y="27.789" width="88.048" height="610.36"/>
  </clipPath>
 </defs>
</svg>
