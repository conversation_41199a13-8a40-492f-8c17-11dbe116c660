<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="766.387187pt" height="679.5765pt" viewBox="0 0 766.387187 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T04:15:38.357930</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 766.387187 679.5765 
L 766.387187 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 384.572187 638.149 
L 516.036187 638.149 
L 516.036187 27.789 
L 384.572187 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 440.418453 638.149 
L 440.418453 27.789 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 384.572187 609.084238 
L 516.036187 609.084238 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 384.572187 580.019476 
L 516.036187 580.019476 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 384.572187 550.954714 
L 516.036187 550.954714 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 384.572187 521.889952 
L 516.036187 521.889952 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 384.572187 492.82519 
L 516.036187 492.82519 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 384.572187 463.760429 
L 516.036187 463.760429 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 384.572187 434.695667 
L 516.036187 434.695667 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 384.572187 405.630905 
L 516.036187 405.630905 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 384.572187 376.566143 
L 516.036187 376.566143 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 384.572187 347.501381 
L 516.036187 347.501381 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 384.572187 318.436619 
L 516.036187 318.436619 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 384.572187 289.371857 
L 516.036187 289.371857 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 384.572187 260.307095 
L 516.036187 260.307095 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 384.572187 231.242333 
L 516.036187 231.242333 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 384.572187 202.177571 
L 516.036187 202.177571 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 384.572187 173.11281 
L 516.036187 173.11281 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 384.572187 144.048048 
L 516.036187 144.048048 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 384.572187 114.983286 
L 516.036187 114.983286 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 384.572187 85.918524 
L 516.036187 85.918524 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 384.572187 56.853762 
L 516.036187 56.853762 
" clip-path="url(#pcb54b5b5ae)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m5112ce9fa9" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m5112ce9fa9" x="440.418453" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(436.919078 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m5112ce9fa9" x="504.267772" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(500.768397 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(327.622781 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_mode_Electronegativity -->
      <g style="fill: #333333" transform="translate(7.2 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-45" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.478516"/>
       <use xlink:href="#DejaVuSans-65" x="1964.261719"/>
       <use xlink:href="#DejaVuSans-63" x="2025.785156"/>
       <use xlink:href="#DejaVuSans-74" x="2080.765625"/>
       <use xlink:href="#DejaVuSans-72" x="2119.974609"/>
       <use xlink:href="#DejaVuSans-6f" x="2158.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2220.019531"/>
       <use xlink:href="#DejaVuSans-65" x="2283.398438"/>
       <use xlink:href="#DejaVuSans-67" x="2344.921875"/>
       <use xlink:href="#DejaVuSans-61" x="2408.398438"/>
       <use xlink:href="#DejaVuSans-74" x="2469.677734"/>
       <use xlink:href="#DejaVuSans-69" x="2508.886719"/>
       <use xlink:href="#DejaVuSans-76" x="2536.669922"/>
       <use xlink:href="#DejaVuSans-69" x="2595.849609"/>
       <use xlink:href="#DejaVuSans-74" x="2623.632812"/>
       <use xlink:href="#DejaVuSans-79" x="2662.841797"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_121 -->
      <g style="fill: #333333" transform="translate(163.480312 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(48.8325 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(85.11875 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- StructuralHeterogeneity_max_relative_bond_length -->
      <g style="fill: #333333" transform="translate(26.474531 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-61" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-78" x="1417.183594"/>
       <use xlink:href="#DejaVuSans-5f" x="1476.363281"/>
       <use xlink:href="#DejaVuSans-72" x="1526.363281"/>
       <use xlink:href="#DejaVuSans-65" x="1565.226562"/>
       <use xlink:href="#DejaVuSans-6c" x="1626.75"/>
       <use xlink:href="#DejaVuSans-61" x="1654.533203"/>
       <use xlink:href="#DejaVuSans-74" x="1715.8125"/>
       <use xlink:href="#DejaVuSans-69" x="1755.021484"/>
       <use xlink:href="#DejaVuSans-76" x="1782.804688"/>
       <use xlink:href="#DejaVuSans-65" x="1841.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.507812"/>
       <use xlink:href="#DejaVuSans-62" x="1953.507812"/>
       <use xlink:href="#DejaVuSans-6f" x="2016.984375"/>
       <use xlink:href="#DejaVuSans-6e" x="2078.166016"/>
       <use xlink:href="#DejaVuSans-64" x="2141.544922"/>
       <use xlink:href="#DejaVuSans-5f" x="2205.021484"/>
       <use xlink:href="#DejaVuSans-6c" x="2255.021484"/>
       <use xlink:href="#DejaVuSans-65" x="2282.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2344.328125"/>
       <use xlink:href="#DejaVuSans-67" x="2407.707031"/>
       <use xlink:href="#DejaVuSans-74" x="2471.183594"/>
       <use xlink:href="#DejaVuSans-68" x="2510.392578"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_40 -->
      <g style="fill: #333333" transform="translate(171.751562 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_minimum_NsValence -->
      <g style="fill: #333333" transform="translate(25.371562 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-73" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-56" x="2191.166016"/>
       <use xlink:href="#DejaVuSans-61" x="2251.824219"/>
       <use xlink:href="#DejaVuSans-6c" x="2313.103516"/>
       <use xlink:href="#DejaVuSans-65" x="2340.886719"/>
       <use xlink:href="#DejaVuSans-6e" x="2402.410156"/>
       <use xlink:href="#DejaVuSans-63" x="2465.789062"/>
       <use xlink:href="#DejaVuSans-65" x="2520.769531"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_90 -->
      <g style="fill: #333333" transform="translate(171.751562 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-30" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CoulombMatrix_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(110.056406 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-31" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(48.8325 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(32.94 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(21.725469 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(76.664687 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(163.480312 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(48.8325 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_81 -->
      <g style="fill: #333333" transform="translate(171.751562 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(62.701875 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(241.37875 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(119.863281 90.857508) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(78.279531 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 384.572187 638.149 
L 516.036187 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagea866b2313d" transform="scale(1 -1) translate(0 -578.16)" x="388.08" y="-43.2" width="124.56" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature14_fold0 -->
    <g transform="translate(141.421188 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-34" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 524.252687 638.149 
L 531.882187 638.149 
L 531.882187 27.789 
L 524.252687 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagef52ccd0e83" transform="scale(1 -1) translate(0 -609.84)" x="524.16" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(535.382187 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(535.382187 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(569.783125 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pcb54b5b5ae">
   <rect x="384.572187" y="27.789" width="131.464" height="610.36"/>
  </clipPath>
 </defs>
</svg>
