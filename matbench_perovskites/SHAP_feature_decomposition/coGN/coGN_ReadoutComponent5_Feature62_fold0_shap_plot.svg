<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T10:46:21.118222</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 469.180265 638.149 
L 469.180265 27.789 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#p03b4c50fe1)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m302582dc59" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m302582dc59" x="427.82773" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2 -->
      <g style="fill: #333333" transform="translate(419.719527 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m302582dc59" x="469.180265" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(465.68089 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m302582dc59" x="510.5328" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(507.033425 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- CrystalNNFingerprint_std_dev_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(122.835 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-69" x="1559.351562"/>
       <use xlink:href="#DejaVuSans-6e" x="1587.134766"/>
       <use xlink:href="#DejaVuSans-65" x="1650.513672"/>
       <use xlink:href="#DejaVuSans-61" x="1712.037109"/>
       <use xlink:href="#DejaVuSans-72" x="1773.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="1814.429688"/>
       <use xlink:href="#DejaVuSans-43" x="1864.429688"/>
       <use xlink:href="#DejaVuSans-4e" x="1934.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="2009.058594"/>
       <use xlink:href="#DejaVuSans-32" x="2059.058594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(147.772656 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CrystalNNFingerprint_mean_T-shaped_CN_3 -->
      <g style="fill: #333333" transform="translate(114.380937 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-54" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-2d" x="1478.15625"/>
       <use xlink:href="#DejaVuSans-73" x="1514.240234"/>
       <use xlink:href="#DejaVuSans-68" x="1566.339844"/>
       <use xlink:href="#DejaVuSans-61" x="1629.71875"/>
       <use xlink:href="#DejaVuSans-70" x="1690.998047"/>
       <use xlink:href="#DejaVuSans-65" x="1754.474609"/>
       <use xlink:href="#DejaVuSans-64" x="1815.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="1879.474609"/>
       <use xlink:href="#DejaVuSans-43" x="1929.474609"/>
       <use xlink:href="#DejaVuSans-4e" x="1999.298828"/>
       <use xlink:href="#DejaVuSans-5f" x="2074.103516"/>
       <use xlink:href="#DejaVuSans-33" x="2124.103516"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- XRDPowderPattern_xrd_38 -->
      <g style="fill: #333333" transform="translate(225.916875 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-38" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- MEGNet_OFMEncoded_v1_83 -->
      <g style="fill: #333333" transform="translate(209.467812 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- CrystalNNFingerprint_mean_linear_CN_2 -->
      <g style="fill: #333333" transform="translate(136.535781 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-6c" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-69" x="1453.980469"/>
       <use xlink:href="#DejaVuSans-6e" x="1481.763672"/>
       <use xlink:href="#DejaVuSans-65" x="1545.142578"/>
       <use xlink:href="#DejaVuSans-61" x="1606.666016"/>
       <use xlink:href="#DejaVuSans-72" x="1667.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.058594"/>
       <use xlink:href="#DejaVuSans-43" x="1759.058594"/>
       <use xlink:href="#DejaVuSans-4e" x="1828.882812"/>
       <use xlink:href="#DejaVuSans-5f" x="1903.6875"/>
       <use xlink:href="#DejaVuSans-32" x="1953.6875"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- AtomicOrbitals_HOMO_element -->
      <g style="fill: #333333" transform="translate(194.694531 439.634651) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-4f" x="347.224609"/>
       <use xlink:href="#DejaVuSans-72" x="425.935547"/>
       <use xlink:href="#DejaVuSans-62" x="467.048828"/>
       <use xlink:href="#DejaVuSans-69" x="530.525391"/>
       <use xlink:href="#DejaVuSans-74" x="558.308594"/>
       <use xlink:href="#DejaVuSans-61" x="597.517578"/>
       <use xlink:href="#DejaVuSans-6c" x="658.796875"/>
       <use xlink:href="#DejaVuSans-73" x="686.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="738.679688"/>
       <use xlink:href="#DejaVuSans-48" x="788.679688"/>
       <use xlink:href="#DejaVuSans-4f" x="863.875"/>
       <use xlink:href="#DejaVuSans-4d" x="942.585938"/>
       <use xlink:href="#DejaVuSans-4f" x="1028.865234"/>
       <use xlink:href="#DejaVuSans-5f" x="1107.576172"/>
       <use xlink:href="#DejaVuSans-65" x="1157.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1219.099609"/>
       <use xlink:href="#DejaVuSans-65" x="1246.882812"/>
       <use xlink:href="#DejaVuSans-6d" x="1308.40625"/>
       <use xlink:href="#DejaVuSans-65" x="1405.818359"/>
       <use xlink:href="#DejaVuSans-6e" x="1467.341797"/>
       <use xlink:href="#DejaVuSans-74" x="1530.720703"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(143.87875 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(201.196562 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(201.196562 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(209.467812 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CrystalNNFingerprint_std_dev_octahedral_CN_6 -->
      <g style="fill: #333333" transform="translate(90.028281 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-6f" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-63" x="1592.75"/>
       <use xlink:href="#DejaVuSans-74" x="1647.730469"/>
       <use xlink:href="#DejaVuSans-61" x="1686.939453"/>
       <use xlink:href="#DejaVuSans-68" x="1748.21875"/>
       <use xlink:href="#DejaVuSans-65" x="1811.597656"/>
       <use xlink:href="#DejaVuSans-64" x="1873.121094"/>
       <use xlink:href="#DejaVuSans-72" x="1936.597656"/>
       <use xlink:href="#DejaVuSans-61" x="1977.710938"/>
       <use xlink:href="#DejaVuSans-6c" x="2038.990234"/>
       <use xlink:href="#DejaVuSans-5f" x="2066.773438"/>
       <use xlink:href="#DejaVuSans-43" x="2116.773438"/>
       <use xlink:href="#DejaVuSans-4e" x="2186.597656"/>
       <use xlink:href="#DejaVuSans-5f" x="2261.402344"/>
       <use xlink:href="#DejaVuSans-36" x="2311.402344"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_2 -->
      <g style="fill: #333333" transform="translate(143.87875 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-32" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(100.418125 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(20.096406 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(279.095 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(157.579531 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(115.995781 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagefe6b8b547f" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature62_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-32" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imaged0125e9578" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p03b4c50fe1">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
