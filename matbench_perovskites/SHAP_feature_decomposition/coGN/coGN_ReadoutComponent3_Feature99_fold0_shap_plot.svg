<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="797.236594pt" height="679.5765pt" viewBox="0 0 797.236594 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T18:27:25.389303</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 797.236594 679.5765 
L 797.236594 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
L 526.293594 27.789 
L 436.013594 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 470.589676 638.149 
L 470.589676 27.789 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 436.013594 609.084238 
L 526.293594 609.084238 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 436.013594 580.019476 
L 526.293594 580.019476 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 436.013594 550.954714 
L 526.293594 550.954714 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 436.013594 521.889952 
L 526.293594 521.889952 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 436.013594 492.82519 
L 526.293594 492.82519 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 436.013594 463.760429 
L 526.293594 463.760429 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 436.013594 434.695667 
L 526.293594 434.695667 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 436.013594 405.630905 
L 526.293594 405.630905 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 436.013594 376.566143 
L 526.293594 376.566143 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 436.013594 347.501381 
L 526.293594 347.501381 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 436.013594 318.436619 
L 526.293594 318.436619 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 436.013594 289.371857 
L 526.293594 289.371857 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 436.013594 260.307095 
L 526.293594 260.307095 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 436.013594 231.242333 
L 526.293594 231.242333 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 436.013594 202.177571 
L 526.293594 202.177571 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 436.013594 173.11281 
L 526.293594 173.11281 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 436.013594 144.048048 
L 526.293594 144.048048 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 436.013594 114.983286 
L 526.293594 114.983286 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 436.013594 85.918524 
L 526.293594 85.918524 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 436.013594 56.853762 
L 526.293594 56.853762 
" clip-path="url(#pa83594adf2)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m806abead0d" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m806abead0d" x="470.589676" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(461.842957 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m806abead0d" x="525.745947" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(516.999228 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(358.472188 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- DensityFeatures_packing_fraction -->
      <g style="fill: #333333" transform="translate(193.270625 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-70" x="860.072266"/>
       <use xlink:href="#DejaVuSans-61" x="923.548828"/>
       <use xlink:href="#DejaVuSans-63" x="984.828125"/>
       <use xlink:href="#DejaVuSans-6b" x="1039.808594"/>
       <use xlink:href="#DejaVuSans-69" x="1097.71875"/>
       <use xlink:href="#DejaVuSans-6e" x="1125.501953"/>
       <use xlink:href="#DejaVuSans-67" x="1188.880859"/>
       <use xlink:href="#DejaVuSans-5f" x="1252.357422"/>
       <use xlink:href="#DejaVuSans-66" x="1302.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1337.5625"/>
       <use xlink:href="#DejaVuSans-61" x="1378.675781"/>
       <use xlink:href="#DejaVuSans-63" x="1439.955078"/>
       <use xlink:href="#DejaVuSans-74" x="1494.935547"/>
       <use xlink:href="#DejaVuSans-69" x="1534.144531"/>
       <use xlink:href="#DejaVuSans-6f" x="1561.927734"/>
       <use xlink:href="#DejaVuSans-6e" x="1623.109375"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_1 -->
      <g style="fill: #333333" transform="translate(100.273906 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-31" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- MEGNet_OFMEncoded_v1_113 -->
      <g style="fill: #333333" transform="translate(214.921719 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-33" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- DensityFeatures_density -->
      <g style="fill: #333333" transform="translate(253.040156 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-44"/>
       <use xlink:href="#DejaVuSans-65" x="77.001953"/>
       <use xlink:href="#DejaVuSans-6e" x="138.525391"/>
       <use xlink:href="#DejaVuSans-73" x="201.904297"/>
       <use xlink:href="#DejaVuSans-69" x="254.003906"/>
       <use xlink:href="#DejaVuSans-74" x="281.787109"/>
       <use xlink:href="#DejaVuSans-79" x="320.996094"/>
       <use xlink:href="#DejaVuSans-46" x="380.175781"/>
       <use xlink:href="#DejaVuSans-65" x="432.195312"/>
       <use xlink:href="#DejaVuSans-61" x="493.71875"/>
       <use xlink:href="#DejaVuSans-74" x="554.998047"/>
       <use xlink:href="#DejaVuSans-75" x="594.207031"/>
       <use xlink:href="#DejaVuSans-72" x="657.585938"/>
       <use xlink:href="#DejaVuSans-65" x="696.449219"/>
       <use xlink:href="#DejaVuSans-73" x="757.972656"/>
       <use xlink:href="#DejaVuSans-5f" x="810.072266"/>
       <use xlink:href="#DejaVuSans-64" x="860.072266"/>
       <use xlink:href="#DejaVuSans-65" x="923.548828"/>
       <use xlink:href="#DejaVuSans-6e" x="985.072266"/>
       <use xlink:href="#DejaVuSans-73" x="1048.451172"/>
       <use xlink:href="#DejaVuSans-69" x="1100.550781"/>
       <use xlink:href="#DejaVuSans-74" x="1128.333984"/>
       <use xlink:href="#DejaVuSans-79" x="1167.542969"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(223.192969 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_mode_GSbandgap -->
      <g style="fill: #333333" transform="translate(93.469219 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-6f" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-64" x="1698.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1761.771484"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(161.497812 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(100.273906 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_45 -->
      <g style="fill: #333333" transform="translate(223.192969 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(161.497812 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(100.273906 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementFraction_S -->
      <g style="fill: #333333" transform="translate(292.044219 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-53" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(161.497812 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- OxidationStates_minimum_oxidation_state -->
      <g style="fill: #333333" transform="translate(134.575625 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-78" x="78.710938"/>
       <use xlink:href="#DejaVuSans-69" x="137.890625"/>
       <use xlink:href="#DejaVuSans-64" x="165.673828"/>
       <use xlink:href="#DejaVuSans-61" x="229.150391"/>
       <use xlink:href="#DejaVuSans-74" x="290.429688"/>
       <use xlink:href="#DejaVuSans-69" x="329.638672"/>
       <use xlink:href="#DejaVuSans-6f" x="357.421875"/>
       <use xlink:href="#DejaVuSans-6e" x="418.603516"/>
       <use xlink:href="#DejaVuSans-53" x="481.982422"/>
       <use xlink:href="#DejaVuSans-74" x="545.458984"/>
       <use xlink:href="#DejaVuSans-61" x="584.667969"/>
       <use xlink:href="#DejaVuSans-74" x="645.947266"/>
       <use xlink:href="#DejaVuSans-65" x="685.15625"/>
       <use xlink:href="#DejaVuSans-73" x="746.679688"/>
       <use xlink:href="#DejaVuSans-5f" x="798.779297"/>
       <use xlink:href="#DejaVuSans-6d" x="848.779297"/>
       <use xlink:href="#DejaVuSans-69" x="946.191406"/>
       <use xlink:href="#DejaVuSans-6e" x="973.974609"/>
       <use xlink:href="#DejaVuSans-69" x="1037.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1065.136719"/>
       <use xlink:href="#DejaVuSans-75" x="1162.548828"/>
       <use xlink:href="#DejaVuSans-6d" x="1225.927734"/>
       <use xlink:href="#DejaVuSans-5f" x="1323.339844"/>
       <use xlink:href="#DejaVuSans-6f" x="1373.339844"/>
       <use xlink:href="#DejaVuSans-78" x="1431.396484"/>
       <use xlink:href="#DejaVuSans-69" x="1490.576172"/>
       <use xlink:href="#DejaVuSans-64" x="1518.359375"/>
       <use xlink:href="#DejaVuSans-61" x="1581.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
       <use xlink:href="#DejaVuSans-69" x="1682.324219"/>
       <use xlink:href="#DejaVuSans-6f" x="1710.107422"/>
       <use xlink:href="#DejaVuSans-6e" x="1771.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1834.667969"/>
       <use xlink:href="#DejaVuSans-73" x="1884.667969"/>
       <use xlink:href="#DejaVuSans-74" x="1936.767578"/>
       <use xlink:href="#DejaVuSans-61" x="1975.976562"/>
       <use xlink:href="#DejaVuSans-74" x="2037.255859"/>
       <use xlink:href="#DejaVuSans-65" x="2076.464844"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(220.722969 207.116556) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- AtomicPackingEfficiency_mean_abs_simul__packing_efficiency -->
      <g style="fill: #333333" transform="translate(7.2 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-6d" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-65" x="1362.451172"/>
       <use xlink:href="#DejaVuSans-61" x="1423.974609"/>
       <use xlink:href="#DejaVuSans-6e" x="1485.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.632812"/>
       <use xlink:href="#DejaVuSans-61" x="1598.632812"/>
       <use xlink:href="#DejaVuSans-62" x="1659.912109"/>
       <use xlink:href="#DejaVuSans-73" x="1723.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1775.488281"/>
       <use xlink:href="#DejaVuSans-73" x="1825.488281"/>
       <use xlink:href="#DejaVuSans-69" x="1877.587891"/>
       <use xlink:href="#DejaVuSans-6d" x="1905.371094"/>
       <use xlink:href="#DejaVuSans-75" x="2002.783203"/>
       <use xlink:href="#DejaVuSans-6c" x="2066.162109"/>
       <use xlink:href="#DejaVuSans-5f" x="2093.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="2143.945312"/>
       <use xlink:href="#DejaVuSans-70" x="2193.945312"/>
       <use xlink:href="#DejaVuSans-61" x="2257.421875"/>
       <use xlink:href="#DejaVuSans-63" x="2318.701172"/>
       <use xlink:href="#DejaVuSans-6b" x="2373.681641"/>
       <use xlink:href="#DejaVuSans-69" x="2431.591797"/>
       <use xlink:href="#DejaVuSans-6e" x="2459.375"/>
       <use xlink:href="#DejaVuSans-67" x="2522.753906"/>
       <use xlink:href="#DejaVuSans-5f" x="2586.230469"/>
       <use xlink:href="#DejaVuSans-65" x="2636.230469"/>
       <use xlink:href="#DejaVuSans-66" x="2697.753906"/>
       <use xlink:href="#DejaVuSans-66" x="2732.958984"/>
       <use xlink:href="#DejaVuSans-69" x="2768.164062"/>
       <use xlink:href="#DejaVuSans-63" x="2795.947266"/>
       <use xlink:href="#DejaVuSans-69" x="2850.927734"/>
       <use xlink:href="#DejaVuSans-65" x="2878.710938"/>
       <use xlink:href="#DejaVuSans-6e" x="2940.234375"/>
       <use xlink:href="#DejaVuSans-63" x="3003.613281"/>
       <use xlink:href="#DejaVuSans-79" x="3058.59375"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(17.114531 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(75.978125 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(290.06375 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(100.273906 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imageb9b1edb91c" transform="scale(1 -1) translate(0 -578.16)" x="437.76" y="-43.2" width="87.12" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature99_fold0 -->
    <g transform="translate(172.270594 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-39" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-39" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.936094 638.149 
L 539.565594 638.149 
L 539.565594 27.789 
L 531.936094 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image7178bbb390" transform="scale(1 -1) translate(0 -609.84)" x="532.08" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(543.065594 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(543.065594 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.466531 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="pa83594adf2">
   <rect x="436.013594" y="27.789" width="90.28" height="610.36"/>
  </clipPath>
 </defs>
</svg>
