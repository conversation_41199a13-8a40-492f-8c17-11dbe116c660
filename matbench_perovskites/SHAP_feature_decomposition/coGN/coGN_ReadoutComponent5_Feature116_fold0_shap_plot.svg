<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="764.691031pt" height="679.5765pt" viewBox="0 0 764.691031 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T22:01:04.331088</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 764.691031 679.5765 
L 764.691031 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 371.692031 638.149 
L 513.308031 638.149 
L 513.308031 27.789 
L 371.692031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 434.754911 638.149 
L 434.754911 27.789 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 371.692031 609.084238 
L 513.308031 609.084238 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 371.692031 580.019476 
L 513.308031 580.019476 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 371.692031 550.954714 
L 513.308031 550.954714 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 371.692031 521.889952 
L 513.308031 521.889952 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 371.692031 492.82519 
L 513.308031 492.82519 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 371.692031 463.760429 
L 513.308031 463.760429 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 371.692031 434.695667 
L 513.308031 434.695667 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 371.692031 405.630905 
L 513.308031 405.630905 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 371.692031 376.566143 
L 513.308031 376.566143 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 371.692031 347.501381 
L 513.308031 347.501381 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 371.692031 318.436619 
L 513.308031 318.436619 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 371.692031 289.371857 
L 513.308031 289.371857 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 371.692031 260.307095 
L 513.308031 260.307095 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 371.692031 231.242333 
L 513.308031 231.242333 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 371.692031 202.177571 
L 513.308031 202.177571 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 371.692031 173.11281 
L 513.308031 173.11281 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 371.692031 144.048048 
L 513.308031 144.048048 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 371.692031 114.983286 
L 513.308031 114.983286 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 371.692031 85.918524 
L 513.308031 85.918524 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 371.692031 56.853762 
L 513.308031 56.853762 
" clip-path="url(#p86423d7b83)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m25a4cf8a66" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m25a4cf8a66" x="434.754911" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(431.255536 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m25a4cf8a66" x="503.957394" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g style="fill: #333333" transform="translate(500.458019 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(319.818625 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_minimum_NsValence -->
      <g style="fill: #333333" transform="translate(12.491406 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-73" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-56" x="2191.166016"/>
       <use xlink:href="#DejaVuSans-61" x="2251.824219"/>
       <use xlink:href="#DejaVuSans-6c" x="2313.103516"/>
       <use xlink:href="#DejaVuSans-65" x="2340.886719"/>
       <use xlink:href="#DejaVuSans-6e" x="2402.410156"/>
       <use xlink:href="#DejaVuSans-63" x="2465.789062"/>
       <use xlink:href="#DejaVuSans-65" x="2520.769531"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_63 -->
      <g style="fill: #333333" transform="translate(158.871406 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-36" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_0 -->
      <g style="fill: #333333" transform="translate(35.952344 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-30" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- OPSiteFingerprint_mean_pentagonal_planar_CN_5 -->
      <g style="fill: #333333" transform="translate(23.234688 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="923.511719"/>
       <use xlink:href="#DejaVuSans-65" x="1020.923828"/>
       <use xlink:href="#DejaVuSans-61" x="1082.447266"/>
       <use xlink:href="#DejaVuSans-6e" x="1143.726562"/>
       <use xlink:href="#DejaVuSans-5f" x="1207.105469"/>
       <use xlink:href="#DejaVuSans-70" x="1257.105469"/>
       <use xlink:href="#DejaVuSans-65" x="1320.582031"/>
       <use xlink:href="#DejaVuSans-6e" x="1382.105469"/>
       <use xlink:href="#DejaVuSans-74" x="1445.484375"/>
       <use xlink:href="#DejaVuSans-61" x="1484.693359"/>
       <use xlink:href="#DejaVuSans-67" x="1545.972656"/>
       <use xlink:href="#DejaVuSans-6f" x="1609.449219"/>
       <use xlink:href="#DejaVuSans-6e" x="1670.630859"/>
       <use xlink:href="#DejaVuSans-61" x="1734.009766"/>
       <use xlink:href="#DejaVuSans-6c" x="1795.289062"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.072266"/>
       <use xlink:href="#DejaVuSans-70" x="1873.072266"/>
       <use xlink:href="#DejaVuSans-6c" x="1936.548828"/>
       <use xlink:href="#DejaVuSans-61" x="1964.332031"/>
       <use xlink:href="#DejaVuSans-6e" x="2025.611328"/>
       <use xlink:href="#DejaVuSans-61" x="2088.990234"/>
       <use xlink:href="#DejaVuSans-72" x="2150.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="2191.382812"/>
       <use xlink:href="#DejaVuSans-43" x="2241.382812"/>
       <use xlink:href="#DejaVuSans-4e" x="2311.207031"/>
       <use xlink:href="#DejaVuSans-5f" x="2386.011719"/>
       <use xlink:href="#DejaVuSans-35" x="2436.011719"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(35.8325 497.764175) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- ElementProperty_MagpieData_maximum_NdValence -->
      <g style="fill: #333333" transform="translate(7.2 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4e" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-64" x="2168.363281"/>
       <use xlink:href="#DejaVuSans-56" x="2231.839844"/>
       <use xlink:href="#DejaVuSans-61" x="2292.498047"/>
       <use xlink:href="#DejaVuSans-6c" x="2353.777344"/>
       <use xlink:href="#DejaVuSans-65" x="2381.560547"/>
       <use xlink:href="#DejaVuSans-6e" x="2443.083984"/>
       <use xlink:href="#DejaVuSans-63" x="2506.462891"/>
       <use xlink:href="#DejaVuSans-65" x="2561.443359"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- CrystalNNFingerprint_std_dev_see-saw-like_CN_4 -->
      <g style="fill: #333333" transform="translate(29.210625 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-73" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1583.667969"/>
       <use xlink:href="#DejaVuSans-65" x="1645.191406"/>
       <use xlink:href="#DejaVuSans-2d" x="1706.714844"/>
       <use xlink:href="#DejaVuSans-73" x="1742.798828"/>
       <use xlink:href="#DejaVuSans-61" x="1794.898438"/>
       <use xlink:href="#DejaVuSans-77" x="1856.177734"/>
       <use xlink:href="#DejaVuSans-2d" x="1937.964844"/>
       <use xlink:href="#DejaVuSans-6c" x="1974.048828"/>
       <use xlink:href="#DejaVuSans-69" x="2001.832031"/>
       <use xlink:href="#DejaVuSans-6b" x="2029.615234"/>
       <use xlink:href="#DejaVuSans-65" x="2083.900391"/>
       <use xlink:href="#DejaVuSans-5f" x="2145.423828"/>
       <use xlink:href="#DejaVuSans-43" x="2195.423828"/>
       <use xlink:href="#DejaVuSans-4e" x="2265.248047"/>
       <use xlink:href="#DejaVuSans-5f" x="2340.052734"/>
       <use xlink:href="#DejaVuSans-34" x="2390.052734"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_147 -->
      <g style="fill: #333333" transform="translate(150.600156 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-34" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-37" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- CrystalNNFingerprint_mean_trigonal_planar_CN_3 -->
      <g style="fill: #333333" transform="translate(24.754062 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-72" x="1465.40625"/>
       <use xlink:href="#DejaVuSans-69" x="1506.519531"/>
       <use xlink:href="#DejaVuSans-67" x="1534.302734"/>
       <use xlink:href="#DejaVuSans-6f" x="1597.779297"/>
       <use xlink:href="#DejaVuSans-6e" x="1658.960938"/>
       <use xlink:href="#DejaVuSans-61" x="1722.339844"/>
       <use xlink:href="#DejaVuSans-6c" x="1783.619141"/>
       <use xlink:href="#DejaVuSans-5f" x="1811.402344"/>
       <use xlink:href="#DejaVuSans-70" x="1861.402344"/>
       <use xlink:href="#DejaVuSans-6c" x="1924.878906"/>
       <use xlink:href="#DejaVuSans-61" x="1952.662109"/>
       <use xlink:href="#DejaVuSans-6e" x="2013.941406"/>
       <use xlink:href="#DejaVuSans-61" x="2077.320312"/>
       <use xlink:href="#DejaVuSans-72" x="2138.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="2179.712891"/>
       <use xlink:href="#DejaVuSans-43" x="2229.712891"/>
       <use xlink:href="#DejaVuSans-4e" x="2299.537109"/>
       <use xlink:href="#DejaVuSans-5f" x="2374.341797"/>
       <use xlink:href="#DejaVuSans-33" x="2424.341797"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- XRDPowderPattern_xrd_31 -->
      <g style="fill: #333333" transform="translate(175.320469 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-33" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-31" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01 -->
      <g style="fill: #333333" transform="translate(8.845312 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-47" x="66.658203"/>
       <use xlink:href="#DejaVuSans-4e" x="144.148438"/>
       <use xlink:href="#DejaVuSans-49" x="218.953125"/>
       <use xlink:href="#DejaVuSans-46" x="248.445312"/>
       <use xlink:href="#DejaVuSans-69" x="298.714844"/>
       <use xlink:href="#DejaVuSans-6e" x="326.498047"/>
       <use xlink:href="#DejaVuSans-67" x="389.876953"/>
       <use xlink:href="#DejaVuSans-65" x="453.353516"/>
       <use xlink:href="#DejaVuSans-72" x="514.876953"/>
       <use xlink:href="#DejaVuSans-50" x="555.990234"/>
       <use xlink:href="#DejaVuSans-72" x="614.542969"/>
       <use xlink:href="#DejaVuSans-69" x="655.65625"/>
       <use xlink:href="#DejaVuSans-6e" x="683.439453"/>
       <use xlink:href="#DejaVuSans-74" x="746.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="786.027344"/>
       <use xlink:href="#DejaVuSans-73" x="836.027344"/>
       <use xlink:href="#DejaVuSans-74" x="888.126953"/>
       <use xlink:href="#DejaVuSans-64" x="927.335938"/>
       <use xlink:href="#DejaVuSans-5f" x="990.8125"/>
       <use xlink:href="#DejaVuSans-64" x="1040.8125"/>
       <use xlink:href="#DejaVuSans-65" x="1104.289062"/>
       <use xlink:href="#DejaVuSans-76" x="1165.8125"/>
       <use xlink:href="#DejaVuSans-5f" x="1224.992188"/>
       <use xlink:href="#DejaVuSans-41" x="1274.992188"/>
       <use xlink:href="#DejaVuSans-47" x="1341.650391"/>
       <use xlink:href="#DejaVuSans-4e" x="1419.140625"/>
       <use xlink:href="#DejaVuSans-49" x="1493.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="1523.4375"/>
       <use xlink:href="#DejaVuSans-64" x="1573.4375"/>
       <use xlink:href="#DejaVuSans-69" x="1636.914062"/>
       <use xlink:href="#DejaVuSans-72" x="1664.697266"/>
       <use xlink:href="#DejaVuSans-3d" x="1705.810547"/>
       <use xlink:href="#DejaVuSans-78" x="1789.599609"/>
       <use xlink:href="#DejaVuSans-5f" x="1848.779297"/>
       <use xlink:href="#DejaVuSans-65" x="1898.779297"/>
       <use xlink:href="#DejaVuSans-74" x="1960.302734"/>
       <use xlink:href="#DejaVuSans-61" x="1999.511719"/>
       <use xlink:href="#DejaVuSans-3d" x="2060.791016"/>
       <use xlink:href="#DejaVuSans-38" x="2144.580078"/>
       <use xlink:href="#DejaVuSans-5f" x="2208.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2258.203125"/>
       <use xlink:href="#DejaVuSans-30" x="2321.826172"/>
       <use xlink:href="#DejaVuSans-65" x="2385.449219"/>
       <use xlink:href="#DejaVuSans-2d" x="2446.972656"/>
       <use xlink:href="#DejaVuSans-30" x="2483.056641"/>
       <use xlink:href="#DejaVuSans-31" x="2546.679688"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(20.059844 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- OPSiteFingerprint_std_dev_sgl_bd_CN_1 -->
      <g style="fill: #333333" transform="translate(89.349844 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4f"/>
       <use xlink:href="#DejaVuSans-50" x="78.710938"/>
       <use xlink:href="#DejaVuSans-53" x="139.013672"/>
       <use xlink:href="#DejaVuSans-69" x="202.490234"/>
       <use xlink:href="#DejaVuSans-74" x="230.273438"/>
       <use xlink:href="#DejaVuSans-65" x="269.482422"/>
       <use xlink:href="#DejaVuSans-46" x="331.005859"/>
       <use xlink:href="#DejaVuSans-69" x="381.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="409.058594"/>
       <use xlink:href="#DejaVuSans-67" x="472.4375"/>
       <use xlink:href="#DejaVuSans-65" x="535.914062"/>
       <use xlink:href="#DejaVuSans-72" x="597.4375"/>
       <use xlink:href="#DejaVuSans-70" x="638.550781"/>
       <use xlink:href="#DejaVuSans-72" x="702.027344"/>
       <use xlink:href="#DejaVuSans-69" x="743.140625"/>
       <use xlink:href="#DejaVuSans-6e" x="770.923828"/>
       <use xlink:href="#DejaVuSans-74" x="834.302734"/>
       <use xlink:href="#DejaVuSans-5f" x="873.511719"/>
       <use xlink:href="#DejaVuSans-73" x="923.511719"/>
       <use xlink:href="#DejaVuSans-74" x="975.611328"/>
       <use xlink:href="#DejaVuSans-64" x="1014.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1078.296875"/>
       <use xlink:href="#DejaVuSans-64" x="1128.296875"/>
       <use xlink:href="#DejaVuSans-65" x="1191.773438"/>
       <use xlink:href="#DejaVuSans-76" x="1253.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1312.476562"/>
       <use xlink:href="#DejaVuSans-73" x="1362.476562"/>
       <use xlink:href="#DejaVuSans-67" x="1414.576172"/>
       <use xlink:href="#DejaVuSans-6c" x="1478.052734"/>
       <use xlink:href="#DejaVuSans-5f" x="1505.835938"/>
       <use xlink:href="#DejaVuSans-62" x="1555.835938"/>
       <use xlink:href="#DejaVuSans-64" x="1619.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1682.789062"/>
       <use xlink:href="#DejaVuSans-43" x="1732.789062"/>
       <use xlink:href="#DejaVuSans-4e" x="1802.613281"/>
       <use xlink:href="#DejaVuSans-5f" x="1877.417969"/>
       <use xlink:href="#DejaVuSans-31" x="1927.417969"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(35.952344 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(150.600156 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(158.871406 178.051794) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(49.821719 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(228.498594 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(106.983125 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(65.399375 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 371.692031 638.149 
L 513.308031 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image3b19aed3a1" transform="scale(1 -1) translate(0 -578.16)" x="375.84" y="-43.2" width="133.2" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature116_fold0 -->
    <g transform="translate(127.509031 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-36" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 522.159031 638.149 
L 529.788531 638.149 
L 529.788531 27.789 
L 522.159031 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagee0ceefb78d" transform="scale(1 -1) translate(0 -609.84)" x="522" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(533.288531 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(533.288531 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(567.689469 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p86423d7b83">
   <rect x="371.692031" y="27.789" width="141.616" height="610.36"/>
  </clipPath>
 </defs>
</svg>
