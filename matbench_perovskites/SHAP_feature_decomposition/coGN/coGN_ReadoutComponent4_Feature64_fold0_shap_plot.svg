<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="788.983437pt" height="679.5765pt" viewBox="0 0 788.983437 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-19T07:11:45.372690</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 788.983437 679.5765 
L 788.983437 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
L 523.512437 27.789 
L 422.288437 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 493.562183 638.149 
L 493.562183 27.789 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 422.288437 609.084238 
L 523.512437 609.084238 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 422.288437 580.019476 
L 523.512437 580.019476 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 422.288437 550.954714 
L 523.512437 550.954714 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 422.288437 521.889952 
L 523.512437 521.889952 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 422.288437 492.82519 
L 523.512437 492.82519 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 422.288437 463.760429 
L 523.512437 463.760429 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 422.288437 434.695667 
L 523.512437 434.695667 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 422.288437 405.630905 
L 523.512437 405.630905 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 422.288437 376.566143 
L 523.512437 376.566143 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 422.288437 347.501381 
L 523.512437 347.501381 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 422.288437 318.436619 
L 523.512437 318.436619 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 422.288437 289.371857 
L 523.512437 289.371857 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 422.288437 260.307095 
L 523.512437 260.307095 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 422.288437 231.242333 
L 523.512437 231.242333 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 422.288437 202.177571 
L 523.512437 202.177571 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 422.288437 173.11281 
L 523.512437 173.11281 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 422.288437 144.048048 
L 523.512437 144.048048 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 422.288437 114.983286 
L 523.512437 114.983286 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 422.288437 85.918524 
L 523.512437 85.918524 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 422.288437 56.853762 
L 523.512437 56.853762 
" clip-path="url(#p24d9bf8930)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="m2b7a75cb5f" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2b7a75cb5f" x="456.633101" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −2.5 -->
      <g style="fill: #333333" transform="translate(443.277554 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
       <use xlink:href="#DejaVuSans-2e" x="147.412109"/>
       <use xlink:href="#DejaVuSans-35" x="179.199219"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m2b7a75cb5f" x="493.562183" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(484.815464 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(350.219031 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- GeneralizedRDF_std_dev_Gaussian_center=4_0_width=1_0 -->
      <g style="fill: #333333" transform="translate(12.613281 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-65" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6e" x="139.013672"/>
       <use xlink:href="#DejaVuSans-65" x="202.392578"/>
       <use xlink:href="#DejaVuSans-72" x="263.916016"/>
       <use xlink:href="#DejaVuSans-61" x="305.029297"/>
       <use xlink:href="#DejaVuSans-6c" x="366.308594"/>
       <use xlink:href="#DejaVuSans-69" x="394.091797"/>
       <use xlink:href="#DejaVuSans-7a" x="421.875"/>
       <use xlink:href="#DejaVuSans-65" x="474.365234"/>
       <use xlink:href="#DejaVuSans-64" x="535.888672"/>
       <use xlink:href="#DejaVuSans-52" x="599.365234"/>
       <use xlink:href="#DejaVuSans-44" x="668.847656"/>
       <use xlink:href="#DejaVuSans-46" x="745.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="803.369141"/>
       <use xlink:href="#DejaVuSans-73" x="853.369141"/>
       <use xlink:href="#DejaVuSans-74" x="905.46875"/>
       <use xlink:href="#DejaVuSans-64" x="944.677734"/>
       <use xlink:href="#DejaVuSans-5f" x="1008.154297"/>
       <use xlink:href="#DejaVuSans-64" x="1058.154297"/>
       <use xlink:href="#DejaVuSans-65" x="1121.630859"/>
       <use xlink:href="#DejaVuSans-76" x="1183.154297"/>
       <use xlink:href="#DejaVuSans-5f" x="1242.333984"/>
       <use xlink:href="#DejaVuSans-47" x="1292.333984"/>
       <use xlink:href="#DejaVuSans-61" x="1369.824219"/>
       <use xlink:href="#DejaVuSans-75" x="1431.103516"/>
       <use xlink:href="#DejaVuSans-73" x="1494.482422"/>
       <use xlink:href="#DejaVuSans-73" x="1546.582031"/>
       <use xlink:href="#DejaVuSans-69" x="1598.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1626.464844"/>
       <use xlink:href="#DejaVuSans-6e" x="1687.744141"/>
       <use xlink:href="#DejaVuSans-5f" x="1751.123047"/>
       <use xlink:href="#DejaVuSans-63" x="1801.123047"/>
       <use xlink:href="#DejaVuSans-65" x="1856.103516"/>
       <use xlink:href="#DejaVuSans-6e" x="1917.626953"/>
       <use xlink:href="#DejaVuSans-74" x="1981.005859"/>
       <use xlink:href="#DejaVuSans-65" x="2020.214844"/>
       <use xlink:href="#DejaVuSans-72" x="2081.738281"/>
       <use xlink:href="#DejaVuSans-3d" x="2122.851562"/>
       <use xlink:href="#DejaVuSans-34" x="2206.640625"/>
       <use xlink:href="#DejaVuSans-5f" x="2270.263672"/>
       <use xlink:href="#DejaVuSans-30" x="2320.263672"/>
       <use xlink:href="#DejaVuSans-5f" x="2383.886719"/>
       <use xlink:href="#DejaVuSans-77" x="2433.886719"/>
       <use xlink:href="#DejaVuSans-69" x="2515.673828"/>
       <use xlink:href="#DejaVuSans-64" x="2543.457031"/>
       <use xlink:href="#DejaVuSans-74" x="2606.933594"/>
       <use xlink:href="#DejaVuSans-68" x="2646.142578"/>
       <use xlink:href="#DejaVuSans-3d" x="2709.521484"/>
       <use xlink:href="#DejaVuSans-31" x="2793.310547"/>
       <use xlink:href="#DejaVuSans-5f" x="2856.933594"/>
       <use xlink:href="#DejaVuSans-30" x="2906.933594"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_0 -->
      <g style="fill: #333333" transform="translate(86.54875 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-30" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- VoronoiFingerprint_std_dev_Voro_area_std_dev -->
      <g style="fill: #333333" transform="translate(94.647344 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-61" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-72" x="1738.863281"/>
       <use xlink:href="#DejaVuSans-65" x="1777.726562"/>
       <use xlink:href="#DejaVuSans-61" x="1839.25"/>
       <use xlink:href="#DejaVuSans-5f" x="1900.529297"/>
       <use xlink:href="#DejaVuSans-73" x="1950.529297"/>
       <use xlink:href="#DejaVuSans-74" x="2002.628906"/>
       <use xlink:href="#DejaVuSans-64" x="2041.837891"/>
       <use xlink:href="#DejaVuSans-5f" x="2105.314453"/>
       <use xlink:href="#DejaVuSans-64" x="2155.314453"/>
       <use xlink:href="#DejaVuSans-65" x="2218.791016"/>
       <use xlink:href="#DejaVuSans-76" x="2280.314453"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(99.28875 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- ChemicalOrdering_mean_ordering_parameter_shell_2 -->
      <g style="fill: #333333" transform="translate(50.14875 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-68" x="69.824219"/>
       <use xlink:href="#DejaVuSans-65" x="133.203125"/>
       <use xlink:href="#DejaVuSans-6d" x="194.726562"/>
       <use xlink:href="#DejaVuSans-69" x="292.138672"/>
       <use xlink:href="#DejaVuSans-63" x="319.921875"/>
       <use xlink:href="#DejaVuSans-61" x="374.902344"/>
       <use xlink:href="#DejaVuSans-6c" x="436.181641"/>
       <use xlink:href="#DejaVuSans-4f" x="463.964844"/>
       <use xlink:href="#DejaVuSans-72" x="542.675781"/>
       <use xlink:href="#DejaVuSans-64" x="582.039062"/>
       <use xlink:href="#DejaVuSans-65" x="645.515625"/>
       <use xlink:href="#DejaVuSans-72" x="707.039062"/>
       <use xlink:href="#DejaVuSans-69" x="748.152344"/>
       <use xlink:href="#DejaVuSans-6e" x="775.935547"/>
       <use xlink:href="#DejaVuSans-67" x="839.314453"/>
       <use xlink:href="#DejaVuSans-5f" x="902.791016"/>
       <use xlink:href="#DejaVuSans-6d" x="952.791016"/>
       <use xlink:href="#DejaVuSans-65" x="1050.203125"/>
       <use xlink:href="#DejaVuSans-61" x="1111.726562"/>
       <use xlink:href="#DejaVuSans-6e" x="1173.005859"/>
       <use xlink:href="#DejaVuSans-5f" x="1236.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="1286.384766"/>
       <use xlink:href="#DejaVuSans-72" x="1347.566406"/>
       <use xlink:href="#DejaVuSans-64" x="1386.929688"/>
       <use xlink:href="#DejaVuSans-65" x="1450.40625"/>
       <use xlink:href="#DejaVuSans-72" x="1511.929688"/>
       <use xlink:href="#DejaVuSans-69" x="1553.042969"/>
       <use xlink:href="#DejaVuSans-6e" x="1580.826172"/>
       <use xlink:href="#DejaVuSans-67" x="1644.205078"/>
       <use xlink:href="#DejaVuSans-5f" x="1707.681641"/>
       <use xlink:href="#DejaVuSans-70" x="1757.681641"/>
       <use xlink:href="#DejaVuSans-61" x="1821.158203"/>
       <use xlink:href="#DejaVuSans-72" x="1882.4375"/>
       <use xlink:href="#DejaVuSans-61" x="1923.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="1984.830078"/>
       <use xlink:href="#DejaVuSans-65" x="2082.242188"/>
       <use xlink:href="#DejaVuSans-74" x="2143.765625"/>
       <use xlink:href="#DejaVuSans-65" x="2182.974609"/>
       <use xlink:href="#DejaVuSans-72" x="2244.498047"/>
       <use xlink:href="#DejaVuSans-5f" x="2285.611328"/>
       <use xlink:href="#DejaVuSans-73" x="2335.611328"/>
       <use xlink:href="#DejaVuSans-68" x="2387.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2451.089844"/>
       <use xlink:href="#DejaVuSans-6c" x="2512.613281"/>
       <use xlink:href="#DejaVuSans-6c" x="2540.396484"/>
       <use xlink:href="#DejaVuSans-5f" x="2568.179688"/>
       <use xlink:href="#DejaVuSans-32" x="2618.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- VoronoiFingerprint_std_dev_Voro_dist_std_dev -->
      <g style="fill: #333333" transform="translate(99.898125 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-73" x="966.734375"/>
       <use xlink:href="#DejaVuSans-74" x="1018.833984"/>
       <use xlink:href="#DejaVuSans-64" x="1058.042969"/>
       <use xlink:href="#DejaVuSans-5f" x="1121.519531"/>
       <use xlink:href="#DejaVuSans-64" x="1171.519531"/>
       <use xlink:href="#DejaVuSans-65" x="1234.996094"/>
       <use xlink:href="#DejaVuSans-76" x="1296.519531"/>
       <use xlink:href="#DejaVuSans-5f" x="1355.699219"/>
       <use xlink:href="#DejaVuSans-56" x="1405.699219"/>
       <use xlink:href="#DejaVuSans-6f" x="1466.357422"/>
       <use xlink:href="#DejaVuSans-72" x="1527.539062"/>
       <use xlink:href="#DejaVuSans-6f" x="1566.402344"/>
       <use xlink:href="#DejaVuSans-5f" x="1627.583984"/>
       <use xlink:href="#DejaVuSans-64" x="1677.583984"/>
       <use xlink:href="#DejaVuSans-69" x="1741.060547"/>
       <use xlink:href="#DejaVuSans-73" x="1768.84375"/>
       <use xlink:href="#DejaVuSans-74" x="1820.943359"/>
       <use xlink:href="#DejaVuSans-5f" x="1860.152344"/>
       <use xlink:href="#DejaVuSans-73" x="1910.152344"/>
       <use xlink:href="#DejaVuSans-74" x="1962.251953"/>
       <use xlink:href="#DejaVuSans-64" x="2001.460938"/>
       <use xlink:href="#DejaVuSans-5f" x="2064.9375"/>
       <use xlink:href="#DejaVuSans-64" x="2114.9375"/>
       <use xlink:href="#DejaVuSans-65" x="2178.414062"/>
       <use xlink:href="#DejaVuSans-76" x="2239.9375"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(86.54875 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(86.54875 410.569889) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-34" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_38 -->
      <g style="fill: #333333" transform="translate(209.467812 381.505127) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-33" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_minimum_Electronegativity -->
      <g style="fill: #333333" transform="translate(20.096406 352.440365) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-45" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-6c" x="2127.445312"/>
       <use xlink:href="#DejaVuSans-65" x="2155.228516"/>
       <use xlink:href="#DejaVuSans-63" x="2216.751953"/>
       <use xlink:href="#DejaVuSans-74" x="2271.732422"/>
       <use xlink:href="#DejaVuSans-72" x="2310.941406"/>
       <use xlink:href="#DejaVuSans-6f" x="2349.804688"/>
       <use xlink:href="#DejaVuSans-6e" x="2410.986328"/>
       <use xlink:href="#DejaVuSans-65" x="2474.365234"/>
       <use xlink:href="#DejaVuSans-67" x="2535.888672"/>
       <use xlink:href="#DejaVuSans-61" x="2599.365234"/>
       <use xlink:href="#DejaVuSans-74" x="2660.644531"/>
       <use xlink:href="#DejaVuSans-69" x="2699.853516"/>
       <use xlink:href="#DejaVuSans-76" x="2727.636719"/>
       <use xlink:href="#DejaVuSans-69" x="2786.816406"/>
       <use xlink:href="#DejaVuSans-74" x="2814.599609"/>
       <use xlink:href="#DejaVuSans-79" x="2853.808594"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- YangSolidSolution_Yang_omega -->
      <g style="fill: #333333" transform="translate(195.088594 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-59" d="M -13 4666 
L 666 4666 
L 1959 2747 
L 3244 4666 
L 3922 4666 
L 2272 2222 
L 2272 0 
L 1638 0 
L 1638 2222 
L -13 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-6f" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-6d" x="1283.193359"/>
       <use xlink:href="#DejaVuSans-65" x="1380.605469"/>
       <use xlink:href="#DejaVuSans-67" x="1442.128906"/>
       <use xlink:href="#DejaVuSans-61" x="1505.605469"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_minimum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(7.2 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4d" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-65" x="2150.541016"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.064453"/>
       <use xlink:href="#DejaVuSans-64" x="2275.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2338.919922"/>
       <use xlink:href="#DejaVuSans-6c" x="2400.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2428.226562"/>
       <use xlink:href="#DejaVuSans-65" x="2489.75"/>
       <use xlink:href="#DejaVuSans-76" x="2551.273438"/>
       <use xlink:href="#DejaVuSans-4e" x="2610.453125"/>
       <use xlink:href="#DejaVuSans-75" x="2685.257812"/>
       <use xlink:href="#DejaVuSans-6d" x="2748.636719"/>
       <use xlink:href="#DejaVuSans-62" x="2846.048828"/>
       <use xlink:href="#DejaVuSans-65" x="2909.525391"/>
       <use xlink:href="#DejaVuSans-72" x="2971.048828"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- AverageBondLength_std_dev_Average_bond_length -->
      <g style="fill: #333333" transform="translate(63.106094 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-4c" x="667.269531"/>
       <use xlink:href="#DejaVuSans-65" x="721.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="782.755859"/>
       <use xlink:href="#DejaVuSans-67" x="846.134766"/>
       <use xlink:href="#DejaVuSans-74" x="909.611328"/>
       <use xlink:href="#DejaVuSans-68" x="948.820312"/>
       <use xlink:href="#DejaVuSans-5f" x="1012.199219"/>
       <use xlink:href="#DejaVuSans-73" x="1062.199219"/>
       <use xlink:href="#DejaVuSans-74" x="1114.298828"/>
       <use xlink:href="#DejaVuSans-64" x="1153.507812"/>
       <use xlink:href="#DejaVuSans-5f" x="1216.984375"/>
       <use xlink:href="#DejaVuSans-64" x="1266.984375"/>
       <use xlink:href="#DejaVuSans-65" x="1330.460938"/>
       <use xlink:href="#DejaVuSans-76" x="1391.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1451.164062"/>
       <use xlink:href="#DejaVuSans-41" x="1501.164062"/>
       <use xlink:href="#DejaVuSans-76" x="1563.697266"/>
       <use xlink:href="#DejaVuSans-65" x="1622.876953"/>
       <use xlink:href="#DejaVuSans-72" x="1684.400391"/>
       <use xlink:href="#DejaVuSans-61" x="1725.513672"/>
       <use xlink:href="#DejaVuSans-67" x="1786.792969"/>
       <use xlink:href="#DejaVuSans-65" x="1850.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1911.792969"/>
       <use xlink:href="#DejaVuSans-62" x="1961.792969"/>
       <use xlink:href="#DejaVuSans-6f" x="2025.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="2086.451172"/>
       <use xlink:href="#DejaVuSans-64" x="2149.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="2213.306641"/>
       <use xlink:href="#DejaVuSans-6c" x="2263.306641"/>
       <use xlink:href="#DejaVuSans-65" x="2291.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2352.613281"/>
       <use xlink:href="#DejaVuSans-67" x="2415.992188"/>
       <use xlink:href="#DejaVuSans-74" x="2479.46875"/>
       <use xlink:href="#DejaVuSans-68" x="2518.677734"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- ElementFraction_O -->
      <g style="fill: #333333" transform="translate(276.338594 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4f" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- VoronoiFingerprint_mean_Voro_dist_minimum -->
      <g style="fill: #333333" transform="translate(102.477812 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-56"/>
       <use xlink:href="#DejaVuSans-6f" x="60.658203"/>
       <use xlink:href="#DejaVuSans-72" x="121.839844"/>
       <use xlink:href="#DejaVuSans-6f" x="160.703125"/>
       <use xlink:href="#DejaVuSans-6e" x="221.884766"/>
       <use xlink:href="#DejaVuSans-6f" x="285.263672"/>
       <use xlink:href="#DejaVuSans-69" x="346.445312"/>
       <use xlink:href="#DejaVuSans-46" x="374.228516"/>
       <use xlink:href="#DejaVuSans-69" x="424.498047"/>
       <use xlink:href="#DejaVuSans-6e" x="452.28125"/>
       <use xlink:href="#DejaVuSans-67" x="515.660156"/>
       <use xlink:href="#DejaVuSans-65" x="579.136719"/>
       <use xlink:href="#DejaVuSans-72" x="640.660156"/>
       <use xlink:href="#DejaVuSans-70" x="681.773438"/>
       <use xlink:href="#DejaVuSans-72" x="745.25"/>
       <use xlink:href="#DejaVuSans-69" x="786.363281"/>
       <use xlink:href="#DejaVuSans-6e" x="814.146484"/>
       <use xlink:href="#DejaVuSans-74" x="877.525391"/>
       <use xlink:href="#DejaVuSans-5f" x="916.734375"/>
       <use xlink:href="#DejaVuSans-6d" x="966.734375"/>
       <use xlink:href="#DejaVuSans-65" x="1064.146484"/>
       <use xlink:href="#DejaVuSans-61" x="1125.669922"/>
       <use xlink:href="#DejaVuSans-6e" x="1186.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1250.328125"/>
       <use xlink:href="#DejaVuSans-56" x="1300.328125"/>
       <use xlink:href="#DejaVuSans-6f" x="1360.986328"/>
       <use xlink:href="#DejaVuSans-72" x="1422.167969"/>
       <use xlink:href="#DejaVuSans-6f" x="1461.03125"/>
       <use xlink:href="#DejaVuSans-5f" x="1522.212891"/>
       <use xlink:href="#DejaVuSans-64" x="1572.212891"/>
       <use xlink:href="#DejaVuSans-69" x="1635.689453"/>
       <use xlink:href="#DejaVuSans-73" x="1663.472656"/>
       <use xlink:href="#DejaVuSans-74" x="1715.572266"/>
       <use xlink:href="#DejaVuSans-5f" x="1754.78125"/>
       <use xlink:href="#DejaVuSans-6d" x="1804.78125"/>
       <use xlink:href="#DejaVuSans-69" x="1902.193359"/>
       <use xlink:href="#DejaVuSans-6e" x="1929.976562"/>
       <use xlink:href="#DejaVuSans-69" x="1993.355469"/>
       <use xlink:href="#DejaVuSans-6d" x="2021.138672"/>
       <use xlink:href="#DejaVuSans-75" x="2118.550781"/>
       <use xlink:href="#DejaVuSans-6d" x="2181.929688"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_97 -->
      <g style="fill: #333333" transform="translate(209.467812 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- YangSolidSolution_Yang_delta -->
      <g style="fill: #333333" transform="translate(206.997812 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-59"/>
       <use xlink:href="#DejaVuSans-61" x="47.333984"/>
       <use xlink:href="#DejaVuSans-6e" x="108.613281"/>
       <use xlink:href="#DejaVuSans-67" x="171.992188"/>
       <use xlink:href="#DejaVuSans-53" x="235.46875"/>
       <use xlink:href="#DejaVuSans-6f" x="298.945312"/>
       <use xlink:href="#DejaVuSans-6c" x="360.126953"/>
       <use xlink:href="#DejaVuSans-69" x="387.910156"/>
       <use xlink:href="#DejaVuSans-64" x="415.693359"/>
       <use xlink:href="#DejaVuSans-53" x="479.169922"/>
       <use xlink:href="#DejaVuSans-6f" x="542.646484"/>
       <use xlink:href="#DejaVuSans-6c" x="603.828125"/>
       <use xlink:href="#DejaVuSans-75" x="631.611328"/>
       <use xlink:href="#DejaVuSans-74" x="694.990234"/>
       <use xlink:href="#DejaVuSans-69" x="734.199219"/>
       <use xlink:href="#DejaVuSans-6f" x="761.982422"/>
       <use xlink:href="#DejaVuSans-6e" x="823.164062"/>
       <use xlink:href="#DejaVuSans-5f" x="886.542969"/>
       <use xlink:href="#DejaVuSans-59" x="936.542969"/>
       <use xlink:href="#DejaVuSans-61" x="983.876953"/>
       <use xlink:href="#DejaVuSans-6e" x="1045.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1108.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="1172.011719"/>
       <use xlink:href="#DejaVuSans-64" x="1222.011719"/>
       <use xlink:href="#DejaVuSans-65" x="1285.488281"/>
       <use xlink:href="#DejaVuSans-6c" x="1347.011719"/>
       <use xlink:href="#DejaVuSans-74" x="1374.794922"/>
       <use xlink:href="#DejaVuSans-61" x="1414.003906"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- ElementFraction_N -->
      <g style="fill: #333333" transform="translate(276.846406 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-4e" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_mean_GSbandgap -->
      <g style="fill: #333333" transform="translate(79.746094 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-62" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-61" x="2077.738281"/>
       <use xlink:href="#DejaVuSans-6e" x="2139.017578"/>
       <use xlink:href="#DejaVuSans-64" x="2202.396484"/>
       <use xlink:href="#DejaVuSans-67" x="2265.873047"/>
       <use xlink:href="#DejaVuSans-61" x="2329.349609"/>
       <use xlink:href="#DejaVuSans-70" x="2390.628906"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(90.022187 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 422.288437 638.149 
L 523.512437 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="imagebd2f07a9c6" transform="scale(1 -1) translate(0 -578.16)" x="424.8" y="-43.2" width="96.48" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent4_Feature64_fold0 -->
    <g transform="translate(164.017437 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-34" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-34" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 529.838937 638.149 
L 537.468437 638.149 
L 537.468437 27.789 
L 529.838937 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="image34eacdfb94" transform="scale(1 -1) translate(0 -609.84)" x="529.92" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(540.968437 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(540.968437 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(575.369375 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p24d9bf8930">
   <rect x="422.288437" y="27.789" width="101.224" height="610.36"/>
  </clipPath>
 </defs>
</svg>
