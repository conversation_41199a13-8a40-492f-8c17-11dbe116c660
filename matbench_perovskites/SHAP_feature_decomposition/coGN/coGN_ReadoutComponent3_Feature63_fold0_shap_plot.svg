<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="797.236594pt" height="679.5765pt" viewBox="0 0 797.236594 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T17:05:12.237463</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 797.236594 679.5765 
L 797.236594 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
L 526.293594 27.789 
L 436.013594 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 475.901637 638.149 
L 475.901637 27.789 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 436.013594 609.084238 
L 526.293594 609.084238 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 436.013594 580.019476 
L 526.293594 580.019476 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 436.013594 550.954714 
L 526.293594 550.954714 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 436.013594 521.889952 
L 526.293594 521.889952 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 436.013594 492.82519 
L 526.293594 492.82519 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 436.013594 463.760429 
L 526.293594 463.760429 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 436.013594 434.695667 
L 526.293594 434.695667 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 436.013594 405.630905 
L 526.293594 405.630905 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 436.013594 376.566143 
L 526.293594 376.566143 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 436.013594 347.501381 
L 526.293594 347.501381 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 436.013594 318.436619 
L 526.293594 318.436619 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 436.013594 289.371857 
L 526.293594 289.371857 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 436.013594 260.307095 
L 526.293594 260.307095 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 436.013594 231.242333 
L 526.293594 231.242333 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 436.013594 202.177571 
L 526.293594 202.177571 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 436.013594 173.11281 
L 526.293594 173.11281 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 436.013594 144.048048 
L 526.293594 144.048048 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 436.013594 114.983286 
L 526.293594 114.983286 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 436.013594 85.918524 
L 526.293594 85.918524 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 436.013594 56.853762 
L 526.293594 56.853762 
" clip-path="url(#p5e88af2331)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="md41d9cb316" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#md41d9cb316" x="475.901637" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0.0 -->
      <g style="fill: #333333" transform="translate(467.154919 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#md41d9cb316" x="517.057614" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.5 -->
      <g style="fill: #333333" transform="translate(508.310896 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(358.472188 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_4">
      <!-- ElementProperty_MagpieData_maximum_MeltingT -->
      <g style="fill: #333333" transform="translate(84.381406 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6c" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-74" x="2269.144531"/>
       <use xlink:href="#DejaVuSans-69" x="2308.353516"/>
       <use xlink:href="#DejaVuSans-6e" x="2336.136719"/>
       <use xlink:href="#DejaVuSans-67" x="2399.515625"/>
       <use xlink:href="#DejaVuSans-54" x="2462.992188"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_5">
      <!-- MEGNet_OFMEncoded_v1_120 -->
      <g style="fill: #333333" transform="translate(214.921719 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-32" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-30" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_GSvolume_pa -->
      <g style="fill: #333333" transform="translate(79.774531 555.893699) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-47" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-53" x="1950.785156"/>
       <use xlink:href="#DejaVuSans-76" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-6f" x="2073.441406"/>
       <use xlink:href="#DejaVuSans-6c" x="2134.623047"/>
       <use xlink:href="#DejaVuSans-75" x="2162.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="2225.785156"/>
       <use xlink:href="#DejaVuSans-65" x="2323.197266"/>
       <use xlink:href="#DejaVuSans-5f" x="2384.720703"/>
       <use xlink:href="#DejaVuSans-70" x="2434.720703"/>
       <use xlink:href="#DejaVuSans-61" x="2498.197266"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_7">
      <!-- ElementProperty_MagpieData_range_MeltingT -->
      <g style="fill: #333333" transform="translate(112.079531 526.828937) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-72" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1580.814453"/>
       <use xlink:href="#DejaVuSans-6e" x="1642.09375"/>
       <use xlink:href="#DejaVuSans-67" x="1705.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1768.949219"/>
       <use xlink:href="#DejaVuSans-5f" x="1830.472656"/>
       <use xlink:href="#DejaVuSans-4d" x="1880.472656"/>
       <use xlink:href="#DejaVuSans-65" x="1966.751953"/>
       <use xlink:href="#DejaVuSans-6c" x="2028.275391"/>
       <use xlink:href="#DejaVuSans-74" x="2056.058594"/>
       <use xlink:href="#DejaVuSans-69" x="2095.267578"/>
       <use xlink:href="#DejaVuSans-6e" x="2123.050781"/>
       <use xlink:href="#DejaVuSans-67" x="2186.429688"/>
       <use xlink:href="#DejaVuSans-54" x="2249.90625"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_8">
      <!-- XRDPowderPattern_xrd_49 -->
      <g style="fill: #333333" transform="translate(239.642031 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-58" d="M 403 4666 
L 1081 4666 
L 2241 2931 
L 3406 4666 
L 4084 4666 
L 2584 2425 
L 4184 0 
L 3506 0 
L 2194 1984 
L 872 0 
L 191 0 
L 1856 2491 
L 403 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-34" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-39" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_9">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(157.603906 468.699413) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_10">
      <!-- XRDPowderPattern_xrd_84 -->
      <g style="fill: #333333" transform="translate(239.642031 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-38" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-34" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_11">
      <!-- MEGNet_OFMEncoded_v1_78 -->
      <g style="fill: #333333" transform="translate(223.192969 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-38" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_12">
      <!-- MEGNet_OFMEncoded_v1_19 -->
      <g style="fill: #333333" transform="translate(223.192969 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-39" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_13">
      <!-- CrystalNNFingerprint_mean_q2_CN_10 -->
      <g style="fill: #333333" transform="translate(162.237187 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-71" d="M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
M 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 -1331 
L 2906 -1331 
L 2906 525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-71" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-32" x="1489.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="1553.296875"/>
       <use xlink:href="#DejaVuSans-43" x="1603.296875"/>
       <use xlink:href="#DejaVuSans-4e" x="1673.121094"/>
       <use xlink:href="#DejaVuSans-5f" x="1747.925781"/>
       <use xlink:href="#DejaVuSans-31" x="1797.925781"/>
       <use xlink:href="#DejaVuSans-30" x="1861.548828"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_14">
      <!-- XRDPowderPattern_xrd_29 -->
      <g style="fill: #333333" transform="translate(239.642031 323.375603) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-58"/>
       <use xlink:href="#DejaVuSans-52" x="68.505859"/>
       <use xlink:href="#DejaVuSans-44" x="137.988281"/>
       <use xlink:href="#DejaVuSans-50" x="214.990234"/>
       <use xlink:href="#DejaVuSans-6f" x="271.667969"/>
       <use xlink:href="#DejaVuSans-77" x="332.849609"/>
       <use xlink:href="#DejaVuSans-64" x="414.636719"/>
       <use xlink:href="#DejaVuSans-65" x="478.113281"/>
       <use xlink:href="#DejaVuSans-72" x="539.636719"/>
       <use xlink:href="#DejaVuSans-50" x="580.75"/>
       <use xlink:href="#DejaVuSans-61" x="636.552734"/>
       <use xlink:href="#DejaVuSans-74" x="697.832031"/>
       <use xlink:href="#DejaVuSans-74" x="737.041016"/>
       <use xlink:href="#DejaVuSans-65" x="776.25"/>
       <use xlink:href="#DejaVuSans-72" x="837.773438"/>
       <use xlink:href="#DejaVuSans-6e" x="877.136719"/>
       <use xlink:href="#DejaVuSans-5f" x="940.515625"/>
       <use xlink:href="#DejaVuSans-78" x="990.515625"/>
       <use xlink:href="#DejaVuSans-72" x="1049.695312"/>
       <use xlink:href="#DejaVuSans-64" x="1089.058594"/>
       <use xlink:href="#DejaVuSans-5f" x="1152.535156"/>
       <use xlink:href="#DejaVuSans-32" x="1202.535156"/>
       <use xlink:href="#DejaVuSans-39" x="1266.158203"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_15">
      <!-- AtomicPackingEfficiency_mean_abs_simul__packing_efficiency -->
      <g style="fill: #333333" transform="translate(7.2 294.310842) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-6d" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-65" x="1362.451172"/>
       <use xlink:href="#DejaVuSans-61" x="1423.974609"/>
       <use xlink:href="#DejaVuSans-6e" x="1485.253906"/>
       <use xlink:href="#DejaVuSans-5f" x="1548.632812"/>
       <use xlink:href="#DejaVuSans-61" x="1598.632812"/>
       <use xlink:href="#DejaVuSans-62" x="1659.912109"/>
       <use xlink:href="#DejaVuSans-73" x="1723.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1775.488281"/>
       <use xlink:href="#DejaVuSans-73" x="1825.488281"/>
       <use xlink:href="#DejaVuSans-69" x="1877.587891"/>
       <use xlink:href="#DejaVuSans-6d" x="1905.371094"/>
       <use xlink:href="#DejaVuSans-75" x="2002.783203"/>
       <use xlink:href="#DejaVuSans-6c" x="2066.162109"/>
       <use xlink:href="#DejaVuSans-5f" x="2093.945312"/>
       <use xlink:href="#DejaVuSans-5f" x="2143.945312"/>
       <use xlink:href="#DejaVuSans-70" x="2193.945312"/>
       <use xlink:href="#DejaVuSans-61" x="2257.421875"/>
       <use xlink:href="#DejaVuSans-63" x="2318.701172"/>
       <use xlink:href="#DejaVuSans-6b" x="2373.681641"/>
       <use xlink:href="#DejaVuSans-69" x="2431.591797"/>
       <use xlink:href="#DejaVuSans-6e" x="2459.375"/>
       <use xlink:href="#DejaVuSans-67" x="2522.753906"/>
       <use xlink:href="#DejaVuSans-5f" x="2586.230469"/>
       <use xlink:href="#DejaVuSans-65" x="2636.230469"/>
       <use xlink:href="#DejaVuSans-66" x="2697.753906"/>
       <use xlink:href="#DejaVuSans-66" x="2732.958984"/>
       <use xlink:href="#DejaVuSans-69" x="2768.164062"/>
       <use xlink:href="#DejaVuSans-63" x="2795.947266"/>
       <use xlink:href="#DejaVuSans-69" x="2850.927734"/>
       <use xlink:href="#DejaVuSans-65" x="2878.710938"/>
       <use xlink:href="#DejaVuSans-6e" x="2940.234375"/>
       <use xlink:href="#DejaVuSans-63" x="3003.613281"/>
       <use xlink:href="#DejaVuSans-79" x="3058.59375"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_16">
      <!-- Miedema_Miedema_deltaH_amor -->
      <g style="fill: #333333" transform="translate(196.965469 265.24608) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-69" x="86.279297"/>
       <use xlink:href="#DejaVuSans-65" x="114.0625"/>
       <use xlink:href="#DejaVuSans-64" x="175.585938"/>
       <use xlink:href="#DejaVuSans-65" x="239.0625"/>
       <use xlink:href="#DejaVuSans-6d" x="300.585938"/>
       <use xlink:href="#DejaVuSans-61" x="397.998047"/>
       <use xlink:href="#DejaVuSans-5f" x="459.277344"/>
       <use xlink:href="#DejaVuSans-4d" x="509.277344"/>
       <use xlink:href="#DejaVuSans-69" x="595.556641"/>
       <use xlink:href="#DejaVuSans-65" x="623.339844"/>
       <use xlink:href="#DejaVuSans-64" x="684.863281"/>
       <use xlink:href="#DejaVuSans-65" x="748.339844"/>
       <use xlink:href="#DejaVuSans-6d" x="809.863281"/>
       <use xlink:href="#DejaVuSans-61" x="907.275391"/>
       <use xlink:href="#DejaVuSans-5f" x="968.554688"/>
       <use xlink:href="#DejaVuSans-64" x="1018.554688"/>
       <use xlink:href="#DejaVuSans-65" x="1082.03125"/>
       <use xlink:href="#DejaVuSans-6c" x="1143.554688"/>
       <use xlink:href="#DejaVuSans-74" x="1171.337891"/>
       <use xlink:href="#DejaVuSans-61" x="1210.546875"/>
       <use xlink:href="#DejaVuSans-48" x="1271.826172"/>
       <use xlink:href="#DejaVuSans-5f" x="1347.021484"/>
       <use xlink:href="#DejaVuSans-61" x="1397.021484"/>
       <use xlink:href="#DejaVuSans-6d" x="1458.300781"/>
       <use xlink:href="#DejaVuSans-6f" x="1555.712891"/>
       <use xlink:href="#DejaVuSans-72" x="1616.894531"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_17">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(161.497812 236.181318) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_18">
      <!-- AtomicPackingEfficiency_dist_from_5_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(11.794687 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-35" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_19">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(161.497812 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_20">
      <!-- AtomicPackingEfficiency_dist_from_1_clusters__APE____0_010 -->
      <g style="fill: #333333" transform="translate(11.794687 148.987032) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-74" x="66.658203"/>
       <use xlink:href="#DejaVuSans-6f" x="105.867188"/>
       <use xlink:href="#DejaVuSans-6d" x="167.048828"/>
       <use xlink:href="#DejaVuSans-69" x="264.460938"/>
       <use xlink:href="#DejaVuSans-63" x="292.244141"/>
       <use xlink:href="#DejaVuSans-50" x="347.224609"/>
       <use xlink:href="#DejaVuSans-61" x="403.027344"/>
       <use xlink:href="#DejaVuSans-63" x="464.306641"/>
       <use xlink:href="#DejaVuSans-6b" x="519.287109"/>
       <use xlink:href="#DejaVuSans-69" x="577.197266"/>
       <use xlink:href="#DejaVuSans-6e" x="604.980469"/>
       <use xlink:href="#DejaVuSans-67" x="668.359375"/>
       <use xlink:href="#DejaVuSans-45" x="731.835938"/>
       <use xlink:href="#DejaVuSans-66" x="795.019531"/>
       <use xlink:href="#DejaVuSans-66" x="830.224609"/>
       <use xlink:href="#DejaVuSans-69" x="865.429688"/>
       <use xlink:href="#DejaVuSans-63" x="893.212891"/>
       <use xlink:href="#DejaVuSans-69" x="948.193359"/>
       <use xlink:href="#DejaVuSans-65" x="975.976562"/>
       <use xlink:href="#DejaVuSans-6e" x="1037.5"/>
       <use xlink:href="#DejaVuSans-63" x="1100.878906"/>
       <use xlink:href="#DejaVuSans-79" x="1155.859375"/>
       <use xlink:href="#DejaVuSans-5f" x="1215.039062"/>
       <use xlink:href="#DejaVuSans-64" x="1265.039062"/>
       <use xlink:href="#DejaVuSans-69" x="1328.515625"/>
       <use xlink:href="#DejaVuSans-73" x="1356.298828"/>
       <use xlink:href="#DejaVuSans-74" x="1408.398438"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.607422"/>
       <use xlink:href="#DejaVuSans-66" x="1497.607422"/>
       <use xlink:href="#DejaVuSans-72" x="1532.8125"/>
       <use xlink:href="#DejaVuSans-6f" x="1571.675781"/>
       <use xlink:href="#DejaVuSans-6d" x="1632.857422"/>
       <use xlink:href="#DejaVuSans-5f" x="1730.269531"/>
       <use xlink:href="#DejaVuSans-31" x="1780.269531"/>
       <use xlink:href="#DejaVuSans-5f" x="1843.892578"/>
       <use xlink:href="#DejaVuSans-63" x="1893.892578"/>
       <use xlink:href="#DejaVuSans-6c" x="1948.873047"/>
       <use xlink:href="#DejaVuSans-75" x="1976.65625"/>
       <use xlink:href="#DejaVuSans-73" x="2040.035156"/>
       <use xlink:href="#DejaVuSans-74" x="2092.134766"/>
       <use xlink:href="#DejaVuSans-65" x="2131.34375"/>
       <use xlink:href="#DejaVuSans-72" x="2192.867188"/>
       <use xlink:href="#DejaVuSans-73" x="2233.980469"/>
       <use xlink:href="#DejaVuSans-5f" x="2286.080078"/>
       <use xlink:href="#DejaVuSans-5f" x="2336.080078"/>
       <use xlink:href="#DejaVuSans-41" x="2386.080078"/>
       <use xlink:href="#DejaVuSans-50" x="2454.488281"/>
       <use xlink:href="#DejaVuSans-45" x="2514.791016"/>
       <use xlink:href="#DejaVuSans-5f" x="2577.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2627.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2677.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2727.974609"/>
       <use xlink:href="#DejaVuSans-30" x="2777.974609"/>
       <use xlink:href="#DejaVuSans-5f" x="2841.597656"/>
       <use xlink:href="#DejaVuSans-30" x="2891.597656"/>
       <use xlink:href="#DejaVuSans-31" x="2955.220703"/>
       <use xlink:href="#DejaVuSans-30" x="3018.84375"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_pentagonal_pyramidal_CN_6 -->
      <g style="fill: #333333" transform="translate(27.890312 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-70" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-65" x="1595.044922"/>
       <use xlink:href="#DejaVuSans-6e" x="1656.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1719.947266"/>
       <use xlink:href="#DejaVuSans-61" x="1759.15625"/>
       <use xlink:href="#DejaVuSans-67" x="1820.435547"/>
       <use xlink:href="#DejaVuSans-6f" x="1883.912109"/>
       <use xlink:href="#DejaVuSans-6e" x="1945.09375"/>
       <use xlink:href="#DejaVuSans-61" x="2008.472656"/>
       <use xlink:href="#DejaVuSans-6c" x="2069.751953"/>
       <use xlink:href="#DejaVuSans-5f" x="2097.535156"/>
       <use xlink:href="#DejaVuSans-70" x="2147.535156"/>
       <use xlink:href="#DejaVuSans-79" x="2211.011719"/>
       <use xlink:href="#DejaVuSans-72" x="2270.191406"/>
       <use xlink:href="#DejaVuSans-61" x="2311.304688"/>
       <use xlink:href="#DejaVuSans-6d" x="2372.583984"/>
       <use xlink:href="#DejaVuSans-69" x="2469.996094"/>
       <use xlink:href="#DejaVuSans-64" x="2497.779297"/>
       <use xlink:href="#DejaVuSans-61" x="2561.255859"/>
       <use xlink:href="#DejaVuSans-6c" x="2622.535156"/>
       <use xlink:href="#DejaVuSans-5f" x="2650.318359"/>
       <use xlink:href="#DejaVuSans-43" x="2700.318359"/>
       <use xlink:href="#DejaVuSans-4e" x="2770.142578"/>
       <use xlink:href="#DejaVuSans-5f" x="2844.947266"/>
       <use xlink:href="#DejaVuSans-36" x="2894.947266"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_22">
      <!-- ElementProperty_MagpieData_maximum_MendeleevNumber -->
      <g style="fill: #333333" transform="translate(17.114531 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-78" x="1698.392578"/>
       <use xlink:href="#DejaVuSans-69" x="1757.572266"/>
       <use xlink:href="#DejaVuSans-6d" x="1785.355469"/>
       <use xlink:href="#DejaVuSans-75" x="1882.767578"/>
       <use xlink:href="#DejaVuSans-6d" x="1946.146484"/>
       <use xlink:href="#DejaVuSans-5f" x="2043.558594"/>
       <use xlink:href="#DejaVuSans-4d" x="2093.558594"/>
       <use xlink:href="#DejaVuSans-65" x="2179.837891"/>
       <use xlink:href="#DejaVuSans-6e" x="2241.361328"/>
       <use xlink:href="#DejaVuSans-64" x="2304.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2368.216797"/>
       <use xlink:href="#DejaVuSans-6c" x="2429.740234"/>
       <use xlink:href="#DejaVuSans-65" x="2457.523438"/>
       <use xlink:href="#DejaVuSans-65" x="2519.046875"/>
       <use xlink:href="#DejaVuSans-76" x="2580.570312"/>
       <use xlink:href="#DejaVuSans-4e" x="2639.75"/>
       <use xlink:href="#DejaVuSans-75" x="2714.554688"/>
       <use xlink:href="#DejaVuSans-6d" x="2777.933594"/>
       <use xlink:href="#DejaVuSans-62" x="2875.345703"/>
       <use xlink:href="#DejaVuSans-65" x="2938.822266"/>
       <use xlink:href="#DejaVuSans-72" x="3000.345703"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_23">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(100.273906 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-32" x="2338.183594"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 436.013594 638.149 
L 526.293594 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image1523dcc578" transform="scale(1 -1) translate(0 -578.16)" x="437.76" y="-43.2" width="87.12" height="578.16"/>
   <g id="text_24">
    <!-- SHAP Summary for coGN_ReadoutComponent3_Feature63_fold0 -->
    <g transform="translate(172.270594 21.789) scale(0.192 -0.192)">
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-33" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-36" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-33" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-5f" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-66" x="2966.216797"/>
     <use xlink:href="#DejaVuSans-6f" x="3001.421875"/>
     <use xlink:href="#DejaVuSans-6c" x="3062.603516"/>
     <use xlink:href="#DejaVuSans-64" x="3090.386719"/>
     <use xlink:href="#DejaVuSans-30" x="3153.863281"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 531.936094 638.149 
L 539.565594 638.149 
L 539.565594 27.789 
L 531.936094 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAoAAANPCAYAAADjT07/AAACGElEQVR4nO1dbW7DIBQLCa3UXb1Hrtb1BHHJRDCO/X4jDM8fkCXbyvJ8/S0NtbYMOjSwLk3AJ0G3YfeHVhhIZCa7nnUg1a6N2J7tya5heYa9AjOe7SGmWUQBi5gUYQaWp1012hNmQHn6WuHK5Slcz6QIM1+gE6SzQocZDD19ezyPYaYo7rwMfwgccT88Udx6z3gAev6kqDeeZ1r9ega0gGc2nmdq7xkVdt2+xpXHzMaz6yZwLVyJ7eGJYuUFKa/hsSuG5olCghliUhSFI84xUoh6VFhjLY6iUPB1gnQwtESQUvXYecZrtccyKSTOmZLLB6h4ZjQ0T48SzFhCSwQp1TMCzDhGSpgJ9P8HeranlrfhrhXW6AmtsEZPu0q0h/iJloRnmGH/5kGHGQBN/KIylw9U3PZ0nvFizFg+X2v8CJkInRdi+2XqGYULu+U79ogCFlUUjvcejfa0PcSdokcBz5RfR2YU0swy7GvryDN2LfB9OPNDoN4zKuw67ZkVOszMCh1mMLTC7yF5MmO56wOvXRzTjCgKkfaEmf0KM4OhRdoTZvbLlBnq6dp5xmsxYwmtoB7PG6nCGvMoBctTuDlnMLSrKOa3a464sdCXWqPnrhOkg6GTZhg6RxwqT2YUBnoKV+Jv7lsKV2HXnmkWz8w6MKKA5dkeBSt4vpWKZzA08/9zhRkELdCeMBPoAdAKa/SEVhgYZmB5tucDUTUt2pRAVXoAAAAASUVORK5CYII=" id="imagecda95ca3fe" transform="scale(1 -1) translate(0 -609.84)" x="532.08" y="-27.36" width="7.2" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_24"/>
     <g id="text_25">
      <!-- Low -->
      <g transform="translate(543.065594 642.328141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- High -->
      <g transform="translate(543.065594 31.968141) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Feature value -->
     <g transform="translate(577.466531 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p5e88af2331">
   <rect x="436.013594" y="27.789" width="90.28" height="610.36"/>
  </clipPath>
 </defs>
</svg>
