<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="762.070469pt" height="679.5765pt" viewBox="0 0 762.070469 679.5765" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-18T22:20:02.850208</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 679.5765 
L 762.070469 679.5765 
L 762.070469 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 367.235469 638.149 
L 512.523469 638.149 
L 512.523469 27.789 
L 367.235469 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <g id="line2d_1">
    <path d="M 427.637107 638.149 
L 427.637107 27.789 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke: #999999; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 367.235469 609.084238 
L 512.523469 609.084238 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 367.235469 580.019476 
L 512.523469 580.019476 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_4">
    <path d="M 367.235469 550.954714 
L 512.523469 550.954714 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_5">
    <path d="M 367.235469 521.889952 
L 512.523469 521.889952 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_6">
    <path d="M 367.235469 492.82519 
L 512.523469 492.82519 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_7">
    <path d="M 367.235469 463.760429 
L 512.523469 463.760429 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_8">
    <path d="M 367.235469 434.695667 
L 512.523469 434.695667 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_9">
    <path d="M 367.235469 405.630905 
L 512.523469 405.630905 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_10">
    <path d="M 367.235469 376.566143 
L 512.523469 376.566143 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_11">
    <path d="M 367.235469 347.501381 
L 512.523469 347.501381 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_12">
    <path d="M 367.235469 318.436619 
L 512.523469 318.436619 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_13">
    <path d="M 367.235469 289.371857 
L 512.523469 289.371857 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_14">
    <path d="M 367.235469 260.307095 
L 512.523469 260.307095 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_15">
    <path d="M 367.235469 231.242333 
L 512.523469 231.242333 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_16">
    <path d="M 367.235469 202.177571 
L 512.523469 202.177571 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_17">
    <path d="M 367.235469 173.11281 
L 512.523469 173.11281 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_18">
    <path d="M 367.235469 144.048048 
L 512.523469 144.048048 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_19">
    <path d="M 367.235469 114.983286 
L 512.523469 114.983286 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_20">
    <path d="M 367.235469 85.918524 
L 512.523469 85.918524 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="line2d_21">
    <path d="M 367.235469 56.853762 
L 512.523469 56.853762 
" clip-path="url(#p4f51af2810)" style="fill: none; stroke-dasharray: 0.5,2.5; stroke-dashoffset: 0; stroke: #cccccc; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_22">
      <defs>
       <path id="mfba686a0e4" d="M 0 0 
L 0 3.5 
" style="stroke: #333333; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mfba686a0e4" x="380.202306" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −1 -->
      <g style="fill: #333333" transform="translate(372.094103 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-31" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mfba686a0e4" x="427.637107" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0 -->
      <g style="fill: #333333" transform="translate(424.137732 653.507281) scale(0.11 -0.11)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mfba686a0e4" x="475.071907" y="638.149" style="fill: #333333; stroke: #333333; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 1 -->
      <g style="fill: #333333" transform="translate(471.572532 653.507281) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- SHAP value (impact on model output) -->
     <g transform="translate(317.198062 669.672906) scale(0.13 -0.13)">
      <defs>
       <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-48" d="M 628 4666 
L 1259 4666 
L 1259 2753 
L 3553 2753 
L 3553 4666 
L 4184 4666 
L 4184 0 
L 3553 0 
L 3553 2222 
L 1259 2222 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-53"/>
      <use xlink:href="#DejaVuSans-48" x="63.476562"/>
      <use xlink:href="#DejaVuSans-41" x="138.671875"/>
      <use xlink:href="#DejaVuSans-50" x="207.080078"/>
      <use xlink:href="#DejaVuSans-20" x="267.382812"/>
      <use xlink:href="#DejaVuSans-76" x="299.169922"/>
      <use xlink:href="#DejaVuSans-61" x="358.349609"/>
      <use xlink:href="#DejaVuSans-6c" x="419.628906"/>
      <use xlink:href="#DejaVuSans-75" x="447.412109"/>
      <use xlink:href="#DejaVuSans-65" x="510.791016"/>
      <use xlink:href="#DejaVuSans-20" x="572.314453"/>
      <use xlink:href="#DejaVuSans-28" x="604.101562"/>
      <use xlink:href="#DejaVuSans-69" x="643.115234"/>
      <use xlink:href="#DejaVuSans-6d" x="670.898438"/>
      <use xlink:href="#DejaVuSans-70" x="768.310547"/>
      <use xlink:href="#DejaVuSans-61" x="831.787109"/>
      <use xlink:href="#DejaVuSans-63" x="893.066406"/>
      <use xlink:href="#DejaVuSans-74" x="948.046875"/>
      <use xlink:href="#DejaVuSans-20" x="987.255859"/>
      <use xlink:href="#DejaVuSans-6f" x="1019.042969"/>
      <use xlink:href="#DejaVuSans-6e" x="1080.224609"/>
      <use xlink:href="#DejaVuSans-20" x="1143.603516"/>
      <use xlink:href="#DejaVuSans-6d" x="1175.390625"/>
      <use xlink:href="#DejaVuSans-6f" x="1272.802734"/>
      <use xlink:href="#DejaVuSans-64" x="1333.984375"/>
      <use xlink:href="#DejaVuSans-65" x="1397.460938"/>
      <use xlink:href="#DejaVuSans-6c" x="1458.984375"/>
      <use xlink:href="#DejaVuSans-20" x="1486.767578"/>
      <use xlink:href="#DejaVuSans-6f" x="1518.554688"/>
      <use xlink:href="#DejaVuSans-75" x="1579.736328"/>
      <use xlink:href="#DejaVuSans-74" x="1643.115234"/>
      <use xlink:href="#DejaVuSans-70" x="1682.324219"/>
      <use xlink:href="#DejaVuSans-75" x="1745.800781"/>
      <use xlink:href="#DejaVuSans-74" x="1809.179688"/>
      <use xlink:href="#DejaVuSans-29" x="1848.388672"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="text_5">
      <!-- CrystalNNFingerprint_std_dev_wt_CN_6 -->
      <g style="fill: #333333" transform="translate(88.825781 614.023222) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-5f" d="M 3263 -1063 
L 3263 -1509 
L -63 -1509 
L -63 -1063 
L 3263 -1063 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-77" d="M 269 3500 
L 844 3500 
L 1563 769 
L 2278 3500 
L 2956 3500 
L 3675 769 
L 4391 3500 
L 4966 3500 
L 4050 0 
L 3372 0 
L 2619 2869 
L 1863 0 
L 1184 0 
L 269 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-77" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-74" x="1613.355469"/>
       <use xlink:href="#DejaVuSans-5f" x="1652.564453"/>
       <use xlink:href="#DejaVuSans-43" x="1702.564453"/>
       <use xlink:href="#DejaVuSans-4e" x="1772.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1847.193359"/>
       <use xlink:href="#DejaVuSans-36" x="1897.193359"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="text_6">
      <!-- ElementProperty_MagpieData_mean_NpValence -->
      <g style="fill: #333333" transform="translate(31.375937 584.958461) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-56" d="M 1831 0 
L 50 4666 
L 709 4666 
L 2188 738 
L 3669 4666 
L 4325 4666 
L 2547 0 
L 1831 0 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-70" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-56" x="2011.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2072.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2133.513672"/>
       <use xlink:href="#DejaVuSans-65" x="2161.296875"/>
       <use xlink:href="#DejaVuSans-6e" x="2222.820312"/>
       <use xlink:href="#DejaVuSans-63" x="2286.199219"/>
       <use xlink:href="#DejaVuSans-65" x="2341.179688"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="text_7">
      <!-- CoulombMatrix_coulomb_matrix_eig_4 -->
      <g style="fill: #333333" transform="translate(92.719687 555.893699) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-34" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="text_8">
      <!-- MEGNet_OFMEncoded_v1_115 -->
      <g style="fill: #333333" transform="translate(146.143594 526.828937) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-47" d="M 3809 666 
L 3809 1919 
L 2778 1919 
L 2778 2438 
L 4434 2438 
L 4434 434 
Q 4069 175 3628 42 
Q 3188 -91 2688 -91 
Q 1594 -91 976 548 
Q 359 1188 359 2328 
Q 359 3472 976 4111 
Q 1594 4750 2688 4750 
Q 3144 4750 3555 4637 
Q 3966 4525 4313 4306 
L 4313 3634 
Q 3963 3931 3569 4081 
Q 3175 4231 2741 4231 
Q 1884 4231 1454 3753 
Q 1025 3275 1025 2328 
Q 1025 1384 1454 906 
Q 1884 428 2741 428 
Q 3075 428 3337 486 
Q 3600 544 3809 666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-35" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="text_9">
      <!-- CoulombMatrix_coulomb_matrix_eig_2 -->
      <g style="fill: #333333" transform="translate(92.719687 497.764175) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-32" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="text_10">
      <!-- ElementProperty_MagpieData_avg_dev_GSbandgap -->
      <g style="fill: #333333" transform="translate(7.2 468.699413) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-61" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-76" x="1600.980469"/>
       <use xlink:href="#DejaVuSans-67" x="1660.160156"/>
       <use xlink:href="#DejaVuSans-5f" x="1723.636719"/>
       <use xlink:href="#DejaVuSans-64" x="1773.636719"/>
       <use xlink:href="#DejaVuSans-65" x="1837.113281"/>
       <use xlink:href="#DejaVuSans-76" x="1898.636719"/>
       <use xlink:href="#DejaVuSans-5f" x="1957.816406"/>
       <use xlink:href="#DejaVuSans-47" x="2007.816406"/>
       <use xlink:href="#DejaVuSans-53" x="2085.306641"/>
       <use xlink:href="#DejaVuSans-62" x="2148.783203"/>
       <use xlink:href="#DejaVuSans-61" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-6e" x="2273.539062"/>
       <use xlink:href="#DejaVuSans-64" x="2336.917969"/>
       <use xlink:href="#DejaVuSans-67" x="2400.394531"/>
       <use xlink:href="#DejaVuSans-61" x="2463.871094"/>
       <use xlink:href="#DejaVuSans-70" x="2525.150391"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="text_11">
      <!-- ElementProperty_MagpieData_minimum_NUnfilled -->
      <g style="fill: #333333" transform="translate(16.239062 439.634651) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-55" d="M 556 4666 
L 1191 4666 
L 1191 1831 
Q 1191 1081 1462 751 
Q 1734 422 2344 422 
Q 2950 422 3222 751 
Q 3494 1081 3494 1831 
L 3494 4666 
L 4128 4666 
L 4128 1753 
Q 4128 841 3676 375 
Q 3225 -91 2344 -91 
Q 1459 -91 1007 375 
Q 556 841 556 1753 
L 556 4666 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-69" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-6e" x="1664.896484"/>
       <use xlink:href="#DejaVuSans-69" x="1728.275391"/>
       <use xlink:href="#DejaVuSans-6d" x="1756.058594"/>
       <use xlink:href="#DejaVuSans-75" x="1853.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="1916.849609"/>
       <use xlink:href="#DejaVuSans-5f" x="2014.261719"/>
       <use xlink:href="#DejaVuSans-4e" x="2064.261719"/>
       <use xlink:href="#DejaVuSans-55" x="2139.066406"/>
       <use xlink:href="#DejaVuSans-6e" x="2212.259766"/>
       <use xlink:href="#DejaVuSans-66" x="2275.638672"/>
       <use xlink:href="#DejaVuSans-69" x="2310.84375"/>
       <use xlink:href="#DejaVuSans-6c" x="2338.626953"/>
       <use xlink:href="#DejaVuSans-6c" x="2366.410156"/>
       <use xlink:href="#DejaVuSans-65" x="2394.193359"/>
       <use xlink:href="#DejaVuSans-64" x="2455.716797"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="text_12">
      <!-- SineCoulombMatrix_sine_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(31.495781 410.569889) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-69" x="63.476562"/>
       <use xlink:href="#DejaVuSans-6e" x="91.259766"/>
       <use xlink:href="#DejaVuSans-65" x="154.638672"/>
       <use xlink:href="#DejaVuSans-43" x="216.162109"/>
       <use xlink:href="#DejaVuSans-6f" x="285.986328"/>
       <use xlink:href="#DejaVuSans-75" x="347.167969"/>
       <use xlink:href="#DejaVuSans-6c" x="410.546875"/>
       <use xlink:href="#DejaVuSans-6f" x="438.330078"/>
       <use xlink:href="#DejaVuSans-6d" x="499.511719"/>
       <use xlink:href="#DejaVuSans-62" x="596.923828"/>
       <use xlink:href="#DejaVuSans-4d" x="660.400391"/>
       <use xlink:href="#DejaVuSans-61" x="746.679688"/>
       <use xlink:href="#DejaVuSans-74" x="807.958984"/>
       <use xlink:href="#DejaVuSans-72" x="847.167969"/>
       <use xlink:href="#DejaVuSans-69" x="888.28125"/>
       <use xlink:href="#DejaVuSans-78" x="916.064453"/>
       <use xlink:href="#DejaVuSans-5f" x="975.244141"/>
       <use xlink:href="#DejaVuSans-73" x="1025.244141"/>
       <use xlink:href="#DejaVuSans-69" x="1077.34375"/>
       <use xlink:href="#DejaVuSans-6e" x="1105.126953"/>
       <use xlink:href="#DejaVuSans-65" x="1168.505859"/>
       <use xlink:href="#DejaVuSans-5f" x="1230.029297"/>
       <use xlink:href="#DejaVuSans-63" x="1280.029297"/>
       <use xlink:href="#DejaVuSans-6f" x="1335.009766"/>
       <use xlink:href="#DejaVuSans-75" x="1396.191406"/>
       <use xlink:href="#DejaVuSans-6c" x="1459.570312"/>
       <use xlink:href="#DejaVuSans-6f" x="1487.353516"/>
       <use xlink:href="#DejaVuSans-6d" x="1548.535156"/>
       <use xlink:href="#DejaVuSans-62" x="1645.947266"/>
       <use xlink:href="#DejaVuSans-5f" x="1709.423828"/>
       <use xlink:href="#DejaVuSans-6d" x="1759.423828"/>
       <use xlink:href="#DejaVuSans-61" x="1856.835938"/>
       <use xlink:href="#DejaVuSans-74" x="1918.115234"/>
       <use xlink:href="#DejaVuSans-72" x="1957.324219"/>
       <use xlink:href="#DejaVuSans-69" x="1998.4375"/>
       <use xlink:href="#DejaVuSans-78" x="2026.220703"/>
       <use xlink:href="#DejaVuSans-5f" x="2085.400391"/>
       <use xlink:href="#DejaVuSans-65" x="2135.400391"/>
       <use xlink:href="#DejaVuSans-69" x="2196.923828"/>
       <use xlink:href="#DejaVuSans-67" x="2224.707031"/>
       <use xlink:href="#DejaVuSans-5f" x="2288.183594"/>
       <use xlink:href="#DejaVuSans-33" x="2338.183594"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="text_13">
      <!-- ElementProperty_MagpieData_mean_NUnfilled -->
      <g style="fill: #333333" transform="translate(41.060937 381.505127) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4e" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-55" x="1948.099609"/>
       <use xlink:href="#DejaVuSans-6e" x="2021.292969"/>
       <use xlink:href="#DejaVuSans-66" x="2084.671875"/>
       <use xlink:href="#DejaVuSans-69" x="2119.876953"/>
       <use xlink:href="#DejaVuSans-6c" x="2147.660156"/>
       <use xlink:href="#DejaVuSans-6c" x="2175.443359"/>
       <use xlink:href="#DejaVuSans-65" x="2203.226562"/>
       <use xlink:href="#DejaVuSans-64" x="2264.75"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="text_14">
      <!-- AverageBondAngle_mean_Average_bond_angle -->
      <g style="fill: #333333" transform="translate(34.969219 352.440365) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-42" d="M 1259 2228 
L 1259 519 
L 2272 519 
Q 2781 519 3026 730 
Q 3272 941 3272 1375 
Q 3272 1813 3026 2020 
Q 2781 2228 2272 2228 
L 1259 2228 
z
M 1259 4147 
L 1259 2741 
L 2194 2741 
Q 2656 2741 2882 2914 
Q 3109 3088 3109 3444 
Q 3109 3797 2882 3972 
Q 2656 4147 2194 4147 
L 1259 4147 
z
M 628 4666 
L 2241 4666 
Q 2963 4666 3353 4366 
Q 3744 4066 3744 3513 
Q 3744 3084 3544 2831 
Q 3344 2578 2956 2516 
Q 3422 2416 3680 2098 
Q 3938 1781 3938 1306 
Q 3938 681 3513 340 
Q 3088 0 2303 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-41"/>
       <use xlink:href="#DejaVuSans-76" x="62.533203"/>
       <use xlink:href="#DejaVuSans-65" x="121.712891"/>
       <use xlink:href="#DejaVuSans-72" x="183.236328"/>
       <use xlink:href="#DejaVuSans-61" x="224.349609"/>
       <use xlink:href="#DejaVuSans-67" x="285.628906"/>
       <use xlink:href="#DejaVuSans-65" x="349.105469"/>
       <use xlink:href="#DejaVuSans-42" x="410.628906"/>
       <use xlink:href="#DejaVuSans-6f" x="479.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="540.414062"/>
       <use xlink:href="#DejaVuSans-64" x="603.792969"/>
       <use xlink:href="#DejaVuSans-41" x="667.269531"/>
       <use xlink:href="#DejaVuSans-6e" x="735.677734"/>
       <use xlink:href="#DejaVuSans-67" x="799.056641"/>
       <use xlink:href="#DejaVuSans-6c" x="862.533203"/>
       <use xlink:href="#DejaVuSans-65" x="890.316406"/>
       <use xlink:href="#DejaVuSans-5f" x="951.839844"/>
       <use xlink:href="#DejaVuSans-6d" x="1001.839844"/>
       <use xlink:href="#DejaVuSans-65" x="1099.251953"/>
       <use xlink:href="#DejaVuSans-61" x="1160.775391"/>
       <use xlink:href="#DejaVuSans-6e" x="1222.054688"/>
       <use xlink:href="#DejaVuSans-5f" x="1285.433594"/>
       <use xlink:href="#DejaVuSans-41" x="1335.433594"/>
       <use xlink:href="#DejaVuSans-76" x="1397.966797"/>
       <use xlink:href="#DejaVuSans-65" x="1457.146484"/>
       <use xlink:href="#DejaVuSans-72" x="1518.669922"/>
       <use xlink:href="#DejaVuSans-61" x="1559.783203"/>
       <use xlink:href="#DejaVuSans-67" x="1621.0625"/>
       <use xlink:href="#DejaVuSans-65" x="1684.539062"/>
       <use xlink:href="#DejaVuSans-5f" x="1746.0625"/>
       <use xlink:href="#DejaVuSans-62" x="1796.0625"/>
       <use xlink:href="#DejaVuSans-6f" x="1859.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1920.720703"/>
       <use xlink:href="#DejaVuSans-64" x="1984.099609"/>
       <use xlink:href="#DejaVuSans-5f" x="2047.576172"/>
       <use xlink:href="#DejaVuSans-61" x="2097.576172"/>
       <use xlink:href="#DejaVuSans-6e" x="2158.855469"/>
       <use xlink:href="#DejaVuSans-67" x="2222.234375"/>
       <use xlink:href="#DejaVuSans-6c" x="2285.710938"/>
       <use xlink:href="#DejaVuSans-65" x="2313.494141"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="text_15">
      <!-- ElementProperty_MagpieData_mean_MeltingT -->
      <g style="fill: #333333" transform="translate(44.235781 323.375603) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-50" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="472.566406"/>
       <use xlink:href="#DejaVuSans-6f" x="511.429688"/>
       <use xlink:href="#DejaVuSans-70" x="572.611328"/>
       <use xlink:href="#DejaVuSans-65" x="636.087891"/>
       <use xlink:href="#DejaVuSans-72" x="697.611328"/>
       <use xlink:href="#DejaVuSans-74" x="738.724609"/>
       <use xlink:href="#DejaVuSans-79" x="777.933594"/>
       <use xlink:href="#DejaVuSans-5f" x="837.113281"/>
       <use xlink:href="#DejaVuSans-4d" x="887.113281"/>
       <use xlink:href="#DejaVuSans-61" x="973.392578"/>
       <use xlink:href="#DejaVuSans-67" x="1034.671875"/>
       <use xlink:href="#DejaVuSans-70" x="1098.148438"/>
       <use xlink:href="#DejaVuSans-69" x="1161.625"/>
       <use xlink:href="#DejaVuSans-65" x="1189.408203"/>
       <use xlink:href="#DejaVuSans-44" x="1250.931641"/>
       <use xlink:href="#DejaVuSans-61" x="1327.933594"/>
       <use xlink:href="#DejaVuSans-74" x="1389.212891"/>
       <use xlink:href="#DejaVuSans-61" x="1428.421875"/>
       <use xlink:href="#DejaVuSans-5f" x="1489.701172"/>
       <use xlink:href="#DejaVuSans-6d" x="1539.701172"/>
       <use xlink:href="#DejaVuSans-65" x="1637.113281"/>
       <use xlink:href="#DejaVuSans-61" x="1698.636719"/>
       <use xlink:href="#DejaVuSans-6e" x="1759.916016"/>
       <use xlink:href="#DejaVuSans-5f" x="1823.294922"/>
       <use xlink:href="#DejaVuSans-4d" x="1873.294922"/>
       <use xlink:href="#DejaVuSans-65" x="1959.574219"/>
       <use xlink:href="#DejaVuSans-6c" x="2021.097656"/>
       <use xlink:href="#DejaVuSans-74" x="2048.880859"/>
       <use xlink:href="#DejaVuSans-69" x="2088.089844"/>
       <use xlink:href="#DejaVuSans-6e" x="2115.873047"/>
       <use xlink:href="#DejaVuSans-67" x="2179.251953"/>
       <use xlink:href="#DejaVuSans-54" x="2242.728516"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="text_16">
      <!-- CoulombMatrix_coulomb_matrix_eig_3 -->
      <g style="fill: #333333" transform="translate(92.719687 294.310842) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-6f" x="69.824219"/>
       <use xlink:href="#DejaVuSans-75" x="131.005859"/>
       <use xlink:href="#DejaVuSans-6c" x="194.384766"/>
       <use xlink:href="#DejaVuSans-6f" x="222.167969"/>
       <use xlink:href="#DejaVuSans-6d" x="283.349609"/>
       <use xlink:href="#DejaVuSans-62" x="380.761719"/>
       <use xlink:href="#DejaVuSans-4d" x="444.238281"/>
       <use xlink:href="#DejaVuSans-61" x="530.517578"/>
       <use xlink:href="#DejaVuSans-74" x="591.796875"/>
       <use xlink:href="#DejaVuSans-72" x="631.005859"/>
       <use xlink:href="#DejaVuSans-69" x="672.119141"/>
       <use xlink:href="#DejaVuSans-78" x="699.902344"/>
       <use xlink:href="#DejaVuSans-5f" x="759.082031"/>
       <use xlink:href="#DejaVuSans-63" x="809.082031"/>
       <use xlink:href="#DejaVuSans-6f" x="864.0625"/>
       <use xlink:href="#DejaVuSans-75" x="925.244141"/>
       <use xlink:href="#DejaVuSans-6c" x="988.623047"/>
       <use xlink:href="#DejaVuSans-6f" x="1016.40625"/>
       <use xlink:href="#DejaVuSans-6d" x="1077.587891"/>
       <use xlink:href="#DejaVuSans-62" x="1175"/>
       <use xlink:href="#DejaVuSans-5f" x="1238.476562"/>
       <use xlink:href="#DejaVuSans-6d" x="1288.476562"/>
       <use xlink:href="#DejaVuSans-61" x="1385.888672"/>
       <use xlink:href="#DejaVuSans-74" x="1447.167969"/>
       <use xlink:href="#DejaVuSans-72" x="1486.376953"/>
       <use xlink:href="#DejaVuSans-69" x="1527.490234"/>
       <use xlink:href="#DejaVuSans-78" x="1555.273438"/>
       <use xlink:href="#DejaVuSans-5f" x="1614.453125"/>
       <use xlink:href="#DejaVuSans-65" x="1664.453125"/>
       <use xlink:href="#DejaVuSans-69" x="1725.976562"/>
       <use xlink:href="#DejaVuSans-67" x="1753.759766"/>
       <use xlink:href="#DejaVuSans-5f" x="1817.236328"/>
       <use xlink:href="#DejaVuSans-33" x="1867.236328"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="text_17">
      <!-- BondFractions_O_-_S_bond_frac_ -->
      <g style="fill: #333333" transform="translate(131.792812 265.24608) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-2d" d="M 313 2009 
L 1997 2009 
L 1997 1497 
L 313 1497 
L 313 2009 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-42"/>
       <use xlink:href="#DejaVuSans-6f" x="68.603516"/>
       <use xlink:href="#DejaVuSans-6e" x="129.785156"/>
       <use xlink:href="#DejaVuSans-64" x="193.164062"/>
       <use xlink:href="#DejaVuSans-46" x="256.640625"/>
       <use xlink:href="#DejaVuSans-72" x="306.910156"/>
       <use xlink:href="#DejaVuSans-61" x="348.023438"/>
       <use xlink:href="#DejaVuSans-63" x="409.302734"/>
       <use xlink:href="#DejaVuSans-74" x="464.283203"/>
       <use xlink:href="#DejaVuSans-69" x="503.492188"/>
       <use xlink:href="#DejaVuSans-6f" x="531.275391"/>
       <use xlink:href="#DejaVuSans-6e" x="592.457031"/>
       <use xlink:href="#DejaVuSans-73" x="655.835938"/>
       <use xlink:href="#DejaVuSans-5f" x="707.935547"/>
       <use xlink:href="#DejaVuSans-4f" x="757.935547"/>
       <use xlink:href="#DejaVuSans-5f" x="836.646484"/>
       <use xlink:href="#DejaVuSans-2d" x="886.646484"/>
       <use xlink:href="#DejaVuSans-5f" x="922.730469"/>
       <use xlink:href="#DejaVuSans-53" x="972.730469"/>
       <use xlink:href="#DejaVuSans-5f" x="1036.207031"/>
       <use xlink:href="#DejaVuSans-62" x="1086.207031"/>
       <use xlink:href="#DejaVuSans-6f" x="1149.683594"/>
       <use xlink:href="#DejaVuSans-6e" x="1210.865234"/>
       <use xlink:href="#DejaVuSans-64" x="1274.244141"/>
       <use xlink:href="#DejaVuSans-5f" x="1337.720703"/>
       <use xlink:href="#DejaVuSans-66" x="1387.720703"/>
       <use xlink:href="#DejaVuSans-72" x="1422.925781"/>
       <use xlink:href="#DejaVuSans-61" x="1464.039062"/>
       <use xlink:href="#DejaVuSans-63" x="1525.318359"/>
       <use xlink:href="#DejaVuSans-5f" x="1580.298828"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="text_18">
      <!-- MEGNet_OFMEncoded_v1_171 -->
      <g style="fill: #333333" transform="translate(146.143594 236.181318) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-31" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
       <use xlink:href="#DejaVuSans-31" x="1456.25"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="text_19">
      <!-- MEGNet_OFMEncoded_v1_57 -->
      <g style="fill: #333333" transform="translate(154.414844 207.116556) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-4d"/>
       <use xlink:href="#DejaVuSans-45" x="86.279297"/>
       <use xlink:href="#DejaVuSans-47" x="149.462891"/>
       <use xlink:href="#DejaVuSans-4e" x="226.953125"/>
       <use xlink:href="#DejaVuSans-65" x="301.757812"/>
       <use xlink:href="#DejaVuSans-74" x="363.28125"/>
       <use xlink:href="#DejaVuSans-5f" x="402.490234"/>
       <use xlink:href="#DejaVuSans-4f" x="452.490234"/>
       <use xlink:href="#DejaVuSans-46" x="531.201172"/>
       <use xlink:href="#DejaVuSans-4d" x="588.720703"/>
       <use xlink:href="#DejaVuSans-45" x="675"/>
       <use xlink:href="#DejaVuSans-6e" x="738.183594"/>
       <use xlink:href="#DejaVuSans-63" x="801.5625"/>
       <use xlink:href="#DejaVuSans-6f" x="856.542969"/>
       <use xlink:href="#DejaVuSans-64" x="917.724609"/>
       <use xlink:href="#DejaVuSans-65" x="981.201172"/>
       <use xlink:href="#DejaVuSans-64" x="1042.724609"/>
       <use xlink:href="#DejaVuSans-5f" x="1106.201172"/>
       <use xlink:href="#DejaVuSans-76" x="1156.201172"/>
       <use xlink:href="#DejaVuSans-31" x="1215.380859"/>
       <use xlink:href="#DejaVuSans-5f" x="1279.003906"/>
       <use xlink:href="#DejaVuSans-35" x="1329.003906"/>
       <use xlink:href="#DejaVuSans-37" x="1392.626953"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="text_20">
      <!-- StructuralHeterogeneity_min_relative_bond_length -->
      <g style="fill: #333333" transform="translate(12.948438 178.051794) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-53"/>
       <use xlink:href="#DejaVuSans-74" x="63.476562"/>
       <use xlink:href="#DejaVuSans-72" x="102.685547"/>
       <use xlink:href="#DejaVuSans-75" x="143.798828"/>
       <use xlink:href="#DejaVuSans-63" x="207.177734"/>
       <use xlink:href="#DejaVuSans-74" x="262.158203"/>
       <use xlink:href="#DejaVuSans-75" x="301.367188"/>
       <use xlink:href="#DejaVuSans-72" x="364.746094"/>
       <use xlink:href="#DejaVuSans-61" x="405.859375"/>
       <use xlink:href="#DejaVuSans-6c" x="467.138672"/>
       <use xlink:href="#DejaVuSans-48" x="494.921875"/>
       <use xlink:href="#DejaVuSans-65" x="570.117188"/>
       <use xlink:href="#DejaVuSans-74" x="631.640625"/>
       <use xlink:href="#DejaVuSans-65" x="670.849609"/>
       <use xlink:href="#DejaVuSans-72" x="732.373047"/>
       <use xlink:href="#DejaVuSans-6f" x="771.236328"/>
       <use xlink:href="#DejaVuSans-67" x="832.417969"/>
       <use xlink:href="#DejaVuSans-65" x="895.894531"/>
       <use xlink:href="#DejaVuSans-6e" x="957.417969"/>
       <use xlink:href="#DejaVuSans-65" x="1020.796875"/>
       <use xlink:href="#DejaVuSans-69" x="1082.320312"/>
       <use xlink:href="#DejaVuSans-74" x="1110.103516"/>
       <use xlink:href="#DejaVuSans-79" x="1149.3125"/>
       <use xlink:href="#DejaVuSans-5f" x="1208.492188"/>
       <use xlink:href="#DejaVuSans-6d" x="1258.492188"/>
       <use xlink:href="#DejaVuSans-69" x="1355.904297"/>
       <use xlink:href="#DejaVuSans-6e" x="1383.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="1447.066406"/>
       <use xlink:href="#DejaVuSans-72" x="1497.066406"/>
       <use xlink:href="#DejaVuSans-65" x="1535.929688"/>
       <use xlink:href="#DejaVuSans-6c" x="1597.453125"/>
       <use xlink:href="#DejaVuSans-61" x="1625.236328"/>
       <use xlink:href="#DejaVuSans-74" x="1686.515625"/>
       <use xlink:href="#DejaVuSans-69" x="1725.724609"/>
       <use xlink:href="#DejaVuSans-76" x="1753.507812"/>
       <use xlink:href="#DejaVuSans-65" x="1812.6875"/>
       <use xlink:href="#DejaVuSans-5f" x="1874.210938"/>
       <use xlink:href="#DejaVuSans-62" x="1924.210938"/>
       <use xlink:href="#DejaVuSans-6f" x="1987.6875"/>
       <use xlink:href="#DejaVuSans-6e" x="2048.869141"/>
       <use xlink:href="#DejaVuSans-64" x="2112.248047"/>
       <use xlink:href="#DejaVuSans-5f" x="2175.724609"/>
       <use xlink:href="#DejaVuSans-6c" x="2225.724609"/>
       <use xlink:href="#DejaVuSans-65" x="2253.507812"/>
       <use xlink:href="#DejaVuSans-6e" x="2315.03125"/>
       <use xlink:href="#DejaVuSans-67" x="2378.410156"/>
       <use xlink:href="#DejaVuSans-74" x="2441.886719"/>
       <use xlink:href="#DejaVuSans-68" x="2481.095703"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="text_21">
      <!-- CrystalNNFingerprint_std_dev_L-shaped_CN_2 -->
      <g style="fill: #333333" transform="translate(45.365156 148.987032) scale(0.13 -0.13)">
       <defs>
        <path id="DejaVuSans-4c" d="M 628 4666 
L 1259 4666 
L 1259 531 
L 3531 531 
L 3531 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-73" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-74" x="1144.703125"/>
       <use xlink:href="#DejaVuSans-64" x="1183.912109"/>
       <use xlink:href="#DejaVuSans-5f" x="1247.388672"/>
       <use xlink:href="#DejaVuSans-64" x="1297.388672"/>
       <use xlink:href="#DejaVuSans-65" x="1360.865234"/>
       <use xlink:href="#DejaVuSans-76" x="1422.388672"/>
       <use xlink:href="#DejaVuSans-5f" x="1481.568359"/>
       <use xlink:href="#DejaVuSans-4c" x="1531.568359"/>
       <use xlink:href="#DejaVuSans-2d" x="1585.53125"/>
       <use xlink:href="#DejaVuSans-73" x="1621.615234"/>
       <use xlink:href="#DejaVuSans-68" x="1673.714844"/>
       <use xlink:href="#DejaVuSans-61" x="1737.09375"/>
       <use xlink:href="#DejaVuSans-70" x="1798.373047"/>
       <use xlink:href="#DejaVuSans-65" x="1861.849609"/>
       <use xlink:href="#DejaVuSans-64" x="1923.373047"/>
       <use xlink:href="#DejaVuSans-5f" x="1986.849609"/>
       <use xlink:href="#DejaVuSans-43" x="2036.849609"/>
       <use xlink:href="#DejaVuSans-4e" x="2106.673828"/>
       <use xlink:href="#DejaVuSans-5f" x="2181.478516"/>
       <use xlink:href="#DejaVuSans-32" x="2231.478516"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="text_22">
      <!-- ElementFraction_F -->
      <g style="fill: #333333" transform="translate(224.042031 119.92227) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-45"/>
       <use xlink:href="#DejaVuSans-6c" x="63.183594"/>
       <use xlink:href="#DejaVuSans-65" x="90.966797"/>
       <use xlink:href="#DejaVuSans-6d" x="152.490234"/>
       <use xlink:href="#DejaVuSans-65" x="249.902344"/>
       <use xlink:href="#DejaVuSans-6e" x="311.425781"/>
       <use xlink:href="#DejaVuSans-74" x="374.804688"/>
       <use xlink:href="#DejaVuSans-46" x="414.013672"/>
       <use xlink:href="#DejaVuSans-72" x="464.283203"/>
       <use xlink:href="#DejaVuSans-61" x="505.396484"/>
       <use xlink:href="#DejaVuSans-63" x="566.675781"/>
       <use xlink:href="#DejaVuSans-74" x="621.65625"/>
       <use xlink:href="#DejaVuSans-69" x="660.865234"/>
       <use xlink:href="#DejaVuSans-6f" x="688.648438"/>
       <use xlink:href="#DejaVuSans-6e" x="749.830078"/>
       <use xlink:href="#DejaVuSans-5f" x="813.208984"/>
       <use xlink:href="#DejaVuSans-46" x="863.208984"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="text_23">
      <!-- CrystalNNFingerprint_mean_wt_CN_3 -->
      <g style="fill: #333333" transform="translate(102.526562 90.857508) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-43"/>
       <use xlink:href="#DejaVuSans-72" x="69.824219"/>
       <use xlink:href="#DejaVuSans-79" x="110.9375"/>
       <use xlink:href="#DejaVuSans-73" x="170.117188"/>
       <use xlink:href="#DejaVuSans-74" x="222.216797"/>
       <use xlink:href="#DejaVuSans-61" x="261.425781"/>
       <use xlink:href="#DejaVuSans-6c" x="322.705078"/>
       <use xlink:href="#DejaVuSans-4e" x="350.488281"/>
       <use xlink:href="#DejaVuSans-4e" x="425.292969"/>
       <use xlink:href="#DejaVuSans-46" x="500.097656"/>
       <use xlink:href="#DejaVuSans-69" x="550.367188"/>
       <use xlink:href="#DejaVuSans-6e" x="578.150391"/>
       <use xlink:href="#DejaVuSans-67" x="641.529297"/>
       <use xlink:href="#DejaVuSans-65" x="705.005859"/>
       <use xlink:href="#DejaVuSans-72" x="766.529297"/>
       <use xlink:href="#DejaVuSans-70" x="807.642578"/>
       <use xlink:href="#DejaVuSans-72" x="871.119141"/>
       <use xlink:href="#DejaVuSans-69" x="912.232422"/>
       <use xlink:href="#DejaVuSans-6e" x="940.015625"/>
       <use xlink:href="#DejaVuSans-74" x="1003.394531"/>
       <use xlink:href="#DejaVuSans-5f" x="1042.603516"/>
       <use xlink:href="#DejaVuSans-6d" x="1092.603516"/>
       <use xlink:href="#DejaVuSans-65" x="1190.015625"/>
       <use xlink:href="#DejaVuSans-61" x="1251.539062"/>
       <use xlink:href="#DejaVuSans-6e" x="1312.818359"/>
       <use xlink:href="#DejaVuSans-5f" x="1376.197266"/>
       <use xlink:href="#DejaVuSans-77" x="1426.197266"/>
       <use xlink:href="#DejaVuSans-74" x="1507.984375"/>
       <use xlink:href="#DejaVuSans-5f" x="1547.193359"/>
       <use xlink:href="#DejaVuSans-43" x="1597.193359"/>
       <use xlink:href="#DejaVuSans-4e" x="1667.017578"/>
       <use xlink:href="#DejaVuSans-5f" x="1741.822266"/>
       <use xlink:href="#DejaVuSans-33" x="1791.822266"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="text_24">
      <!-- GlobalSymmetryFeatures_spacegroup_num -->
      <g style="fill: #333333" transform="translate(60.942813 61.792746) scale(0.13 -0.13)">
       <use xlink:href="#DejaVuSans-47"/>
       <use xlink:href="#DejaVuSans-6c" x="77.490234"/>
       <use xlink:href="#DejaVuSans-6f" x="105.273438"/>
       <use xlink:href="#DejaVuSans-62" x="166.455078"/>
       <use xlink:href="#DejaVuSans-61" x="229.931641"/>
       <use xlink:href="#DejaVuSans-6c" x="291.210938"/>
       <use xlink:href="#DejaVuSans-53" x="318.994141"/>
       <use xlink:href="#DejaVuSans-79" x="382.470703"/>
       <use xlink:href="#DejaVuSans-6d" x="441.650391"/>
       <use xlink:href="#DejaVuSans-6d" x="539.0625"/>
       <use xlink:href="#DejaVuSans-65" x="636.474609"/>
       <use xlink:href="#DejaVuSans-74" x="697.998047"/>
       <use xlink:href="#DejaVuSans-72" x="737.207031"/>
       <use xlink:href="#DejaVuSans-79" x="778.320312"/>
       <use xlink:href="#DejaVuSans-46" x="837.5"/>
       <use xlink:href="#DejaVuSans-65" x="889.519531"/>
       <use xlink:href="#DejaVuSans-61" x="951.042969"/>
       <use xlink:href="#DejaVuSans-74" x="1012.322266"/>
       <use xlink:href="#DejaVuSans-75" x="1051.53125"/>
       <use xlink:href="#DejaVuSans-72" x="1114.910156"/>
       <use xlink:href="#DejaVuSans-65" x="1153.773438"/>
       <use xlink:href="#DejaVuSans-73" x="1215.296875"/>
       <use xlink:href="#DejaVuSans-5f" x="1267.396484"/>
       <use xlink:href="#DejaVuSans-73" x="1317.396484"/>
       <use xlink:href="#DejaVuSans-70" x="1369.496094"/>
       <use xlink:href="#DejaVuSans-61" x="1432.972656"/>
       <use xlink:href="#DejaVuSans-63" x="1494.251953"/>
       <use xlink:href="#DejaVuSans-65" x="1549.232422"/>
       <use xlink:href="#DejaVuSans-67" x="1610.755859"/>
       <use xlink:href="#DejaVuSans-72" x="1674.232422"/>
       <use xlink:href="#DejaVuSans-6f" x="1713.095703"/>
       <use xlink:href="#DejaVuSans-75" x="1774.277344"/>
       <use xlink:href="#DejaVuSans-70" x="1837.65625"/>
       <use xlink:href="#DejaVuSans-5f" x="1901.132812"/>
       <use xlink:href="#DejaVuSans-6e" x="1951.132812"/>
       <use xlink:href="#DejaVuSans-75" x="2014.511719"/>
       <use xlink:href="#DejaVuSans-6d" x="2077.890625"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 367.235469 638.149 
L 512.523469 638.149 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image82b0b82092" transform="scale(1 -1) translate(0 -578.16)" x="371.52" y="-43.2" width="136.8" height="578.16"/>
   <g id="text_25">
    <!-- SHAP Summary for coGN_ReadoutComponent5_Feature119_fold0 -->
    <g transform="translate(124.888469 21.789) scale(0.192 -0.192)">
     <defs>
      <path id="DejaVuSans-52" d="M 2841 2188 
Q 3044 2119 3236 1894 
Q 3428 1669 3622 1275 
L 4263 0 
L 3584 0 
L 2988 1197 
Q 2756 1666 2539 1819 
Q 2322 1972 1947 1972 
L 1259 1972 
L 1259 0 
L 628 0 
L 628 4666 
L 2053 4666 
Q 2853 4666 3247 4331 
Q 3641 3997 3641 3322 
Q 3641 2881 3436 2590 
Q 3231 2300 2841 2188 
z
M 1259 4147 
L 1259 2491 
L 2053 2491 
Q 2509 2491 2742 2702 
Q 2975 2913 2975 3322 
Q 2975 3731 2742 3939 
Q 2509 4147 2053 4147 
L 1259 4147 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-48" x="63.476562"/>
     <use xlink:href="#DejaVuSans-41" x="138.671875"/>
     <use xlink:href="#DejaVuSans-50" x="207.080078"/>
     <use xlink:href="#DejaVuSans-20" x="267.382812"/>
     <use xlink:href="#DejaVuSans-53" x="299.169922"/>
     <use xlink:href="#DejaVuSans-75" x="362.646484"/>
     <use xlink:href="#DejaVuSans-6d" x="426.025391"/>
     <use xlink:href="#DejaVuSans-6d" x="523.4375"/>
     <use xlink:href="#DejaVuSans-61" x="620.849609"/>
     <use xlink:href="#DejaVuSans-72" x="682.128906"/>
     <use xlink:href="#DejaVuSans-79" x="723.242188"/>
     <use xlink:href="#DejaVuSans-20" x="782.421875"/>
     <use xlink:href="#DejaVuSans-66" x="814.208984"/>
     <use xlink:href="#DejaVuSans-6f" x="849.414062"/>
     <use xlink:href="#DejaVuSans-72" x="910.595703"/>
     <use xlink:href="#DejaVuSans-20" x="951.708984"/>
     <use xlink:href="#DejaVuSans-63" x="983.496094"/>
     <use xlink:href="#DejaVuSans-6f" x="1038.476562"/>
     <use xlink:href="#DejaVuSans-47" x="1099.658203"/>
     <use xlink:href="#DejaVuSans-4e" x="1177.148438"/>
     <use xlink:href="#DejaVuSans-5f" x="1251.953125"/>
     <use xlink:href="#DejaVuSans-52" x="1301.953125"/>
     <use xlink:href="#DejaVuSans-65" x="1366.935547"/>
     <use xlink:href="#DejaVuSans-61" x="1428.458984"/>
     <use xlink:href="#DejaVuSans-64" x="1489.738281"/>
     <use xlink:href="#DejaVuSans-6f" x="1553.214844"/>
     <use xlink:href="#DejaVuSans-75" x="1614.396484"/>
     <use xlink:href="#DejaVuSans-74" x="1677.775391"/>
     <use xlink:href="#DejaVuSans-43" x="1716.984375"/>
     <use xlink:href="#DejaVuSans-6f" x="1786.808594"/>
     <use xlink:href="#DejaVuSans-6d" x="1847.990234"/>
     <use xlink:href="#DejaVuSans-70" x="1945.402344"/>
     <use xlink:href="#DejaVuSans-6f" x="2008.878906"/>
     <use xlink:href="#DejaVuSans-6e" x="2070.060547"/>
     <use xlink:href="#DejaVuSans-65" x="2133.439453"/>
     <use xlink:href="#DejaVuSans-6e" x="2194.962891"/>
     <use xlink:href="#DejaVuSans-74" x="2258.341797"/>
     <use xlink:href="#DejaVuSans-35" x="2297.550781"/>
     <use xlink:href="#DejaVuSans-5f" x="2361.173828"/>
     <use xlink:href="#DejaVuSans-46" x="2411.173828"/>
     <use xlink:href="#DejaVuSans-65" x="2463.193359"/>
     <use xlink:href="#DejaVuSans-61" x="2524.716797"/>
     <use xlink:href="#DejaVuSans-74" x="2585.996094"/>
     <use xlink:href="#DejaVuSans-75" x="2625.205078"/>
     <use xlink:href="#DejaVuSans-72" x="2688.583984"/>
     <use xlink:href="#DejaVuSans-65" x="2727.447266"/>
     <use xlink:href="#DejaVuSans-31" x="2788.970703"/>
     <use xlink:href="#DejaVuSans-31" x="2852.59375"/>
     <use xlink:href="#DejaVuSans-39" x="2916.216797"/>
     <use xlink:href="#DejaVuSans-5f" x="2979.839844"/>
     <use xlink:href="#DejaVuSans-66" x="3029.839844"/>
     <use xlink:href="#DejaVuSans-6f" x="3065.044922"/>
     <use xlink:href="#DejaVuSans-6c" x="3126.226562"/>
     <use xlink:href="#DejaVuSans-64" x="3154.009766"/>
     <use xlink:href="#DejaVuSans-30" x="3217.486328"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 521.603969 638.149 
L 529.233469 638.149 
L 529.233469 27.789 
L 521.603969 27.789 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAAsAAANPCAYAAAAMjSXBAAACZ0lEQVR4nO3by27jMBBEUctmAmR+fT55kMc22Yn0EDgVldYN4uJWqRXbynH7++/zdvK6nx2cHh630xCbMc5z7MNIHEYSRIZrY3kYUTduExxVBw4n2ugD6PuVmCAyjKhDtihiI3EY2UhNcHkYUYesgkx1CEYTzB5Glnligkj5GRsERhN8AoOwkakOwWiC6xhx6pBhRJ1SpFfiHhxvhI055j9GkV52nTyJQdiYZSYwZnbBTgzCxhzzg2jdGLtOTrQxx3wnmMeDKP94EKtgNkECwyj/uBMJIqF0FaxjGEWKTNAo/zgIG4mtUxI0+pzIPI4WaXEY2RtKkYwEkVAil7mhDilSpDoEw9hIkc/Bgyh/ZoIEs4JhJIj0OTLBYqwOK8ucuQcFjERmZH0hNhKZi7E8jDAzGB8GBmEjkbkYy8MIM4NBrIJIdcjrlIaNxNaN4/PDwBBsRCaIvFmN2GiR1jG6zBeHEWYlQeOLskh1SJH6o+0TGJtOTnymJLauRfqJ0SKtYhjqkCJlqjv/4Xhrnwkbk+reBWYlQaPPiXsDUTdmpnfaIN6FSGQex66TE21UXTpGE0zHaILrGMY/WTTBZzA2nZxoY/InP4IZUYcUKVQdgrHp5CuoQzA2nXwFdQjGppOvoI7AYP4q2HTy70+wGKvDSOuce1DASGRWimTYaPmfwdh08u9X1yJ5GInlb5GWhxOZa+P7hTD3Hlwe7hb9gWEkiGA0wUsNI+oUDKPPieqQ4UQbSOsQdYiNRHXIMGKjRVrHMNQhNkLVbTo50UZi6xB1CEYTvNRwEyzGfxlGmIuRPoyoQzASmRGML/pfLdqpQ6xuAAAAAElFTkSuQmCC" id="imagea941164d11" transform="scale(1 -1) translate(0 -609.84)" x="521.28" y="-27.36" width="7.92" height="609.84"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_21">
     <g id="line2d_25"/>
     <g id="text_26">
      <!-- Low -->
      <g transform="translate(532.733469 642.328141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-4c"/>
       <use xlink:href="#DejaVuSans-6f" x="53.962891"/>
       <use xlink:href="#DejaVuSans-77" x="115.144531"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_26"/>
     <g id="text_27">
      <!-- High -->
      <g transform="translate(532.733469 31.968141) scale(0.11 -0.11)">
       <use xlink:href="#DejaVuSans-48"/>
       <use xlink:href="#DejaVuSans-69" x="75.195312"/>
       <use xlink:href="#DejaVuSans-67" x="102.978516"/>
       <use xlink:href="#DejaVuSans-68" x="166.455078"/>
      </g>
     </g>
    </g>
    <g id="text_28">
     <!-- Feature value -->
     <g transform="translate(567.134406 373.933063) rotate(-90) scale(0.12 -0.12)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-65" x="52.019531"/>
      <use xlink:href="#DejaVuSans-61" x="113.542969"/>
      <use xlink:href="#DejaVuSans-74" x="174.822266"/>
      <use xlink:href="#DejaVuSans-75" x="214.03125"/>
      <use xlink:href="#DejaVuSans-72" x="277.410156"/>
      <use xlink:href="#DejaVuSans-65" x="316.273438"/>
      <use xlink:href="#DejaVuSans-20" x="377.796875"/>
      <use xlink:href="#DejaVuSans-76" x="409.583984"/>
      <use xlink:href="#DejaVuSans-61" x="468.763672"/>
      <use xlink:href="#DejaVuSans-6c" x="530.042969"/>
      <use xlink:href="#DejaVuSans-75" x="557.826172"/>
      <use xlink:href="#DejaVuSans-65" x="621.205078"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
  </g>
 </g>
 <defs>
  <clipPath id="p4f51af2810">
   <rect x="367.235469" y="27.789" width="145.288" height="610.36"/>
  </clipPath>
 </defs>
</svg>
